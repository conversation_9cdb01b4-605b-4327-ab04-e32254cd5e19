<template>
  <div>
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="返利结果变更证明文件"
      :close-on-click-modal="false"
      :visible="formVisable"
      width="50%"
      @close="close"
    >
      <el-row type="flex" class="filter-container">
        <el-col :span="24">
          <div class="imgbox">
            <div v-for="(url, index) in imageList" :key="index" class="block">
              <el-image
                style="width: 100%; height: 100%;object-fit: cover;"
                :src="url"
                :preview-src-list="getSrcList(index)"
              />
            </div>
          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="close()">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import RebateService from '@/api/rebate'
export default {
  data() {
    return {
      formVisable: false,
      imageList: [] // 图片列表
    }
  },
  created() {
  },
  methods: {
    init(id) {
      this.getRebateResultChangeCertificate(id)
      this.formVisable = true
    },
    getRebateResultChangeCertificate(id) {
      RebateService.GetRebateResultChangeCertificate({ id: id }).then((result) => {
        for (var i = 0; i < result.data.certificateImageList.length; i++) {
          this.imageList.push('data:image/png;base64,' + result.data.certificateImageList[i])
        }
      })
        .catch((error) => {
          console.log(error)
        })
    },
    getSrcList(index) {
      return this.imageList.slice(index).concat(this.imageList.slice(0, index))
    },
    close() {
      this.formVisable = false
      this.imageList = []
      this.$emit('success')
    }
  }

}
</script>
  <style scoped>
  .imgbox {
    display: flex;
  }
  .block {
    width: 180px;
    height: 180px;
    margin-right: 20px;
  }
  </style>
