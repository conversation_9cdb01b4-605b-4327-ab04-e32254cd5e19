<template>
  <div>
    <el-dialog custom-class="el-dialog-s" title="查看协议" width="80%" append-to-body :close-on-click-modal="false" :visible="showAddDialog" @close="closeAgreementViewDialog">
      <el-form
        ref="dataForm"
        :model="requestFormRebateAgreement"
        label-position="right"
        label-width="110px"
        class="el-dialogform"
      >
        <el-row type="flex" class="filter-container">
          <el-col :span="24">
            <el-form-item label="协议标题">
              <label>{{ requestFormRebateAgreement.name }}</label>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="代付方">
              <label>{{ requestFormRebateAgreement.rebateAgentName }}</label>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="返利接收方">
              <label>{{ requestFormRebateAgreement.rebateReceiverName }}</label>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="部门">
              <label>{{ requestFormRebateAgreement.departmentName }}</label>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="支付周期">
              <label>{{ requestFormRebateAgreement.enumPaymentCycleFlagsDesc }}</label>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="起始日期">
              <label>{{ requestFormRebateAgreement.startDate | parseTime('{y}-{m}-{d}') }}</label>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="截止日期">
              <label>{{ requestFormRebateAgreement.endDate | parseTime('{y}-{m}-{d}') }}</label>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="协议文本">
              <el-table
                :data="requestFormRebateAgreement.attachmentList"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
              >
                <el-table-column label="文件名称" min-width="180px" align="center">
                  <template slot-scope="{ row }">
                    <a
                      style="text-decoration:underline"
                      @click="handleDownLoadFile(row.id,row.fileName)"
                    >{{ row.fileName }}</a>
                  </template>
                </el-table-column>
                <el-table-column label="上传时间" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.createTime | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-collapse v-model="activeName" accordion style="margin-top:10px">
              <el-collapse-item v-for="item in requestFormRebateAgreement.requestFormRebateAgreementPolicyList" :key="item.rebateAgreementPolicyTypeId" :title="item.agreementPolicyTypeName" name="1" style="font-size: 12px;">
                <el-row type="flex" justify="end" style="margin-top:10px">
                  <el-col :span="span">
                    <el-form-item label="政策模板">
                      <span>{{ item.agreementPolicyTypeName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="span">
                    <el-form-item label="支付方式">
                      <span>{{ item.enumPaymentTypeDesc }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="span">
                    <el-form-item label="返利基数">
                      <span>{{ item.rebateBaseTypeName }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row style="margin-top:10px">
                  <el-col :span="span">
                    <el-form-item label="全部品种达标">
                      <span>{{ item.needAllStandardStr }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="span">
                    <el-form-item label="存在扣款条款">
                      <span>{{ item.hasDefaultClauseStr }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="span">
                    <el-form-item label="扣款条款">
                      <span>{{ item.defaultClauseRemark }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row style="margin-top:10px">
                  <el-col :span="24">
                    <el-form-item label="备注">
                      <span>{{ item.remark }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-divider />

                <el-row style="margin-top:10px">
                  <el-col :span="24">
                    <el-form-item label="品规列表">
                      <el-table
                        :data="item.requestFormRebateAgreementProductList"
                        stripe
                        border
                        fit
                        highlight-current-row
                        style="width: 100%;"
                        :default-sort="{prop: 'createTime', order: 'descending'}"
                        :header-cell-class-name="'tableStyle'"
                        :row-class-name="handleRowClass"
                      >
                        <el-table-column
                          fixed
                          label="序号"
                          type="index"
                          align="center"
                        />
                        <el-table-column label="产品名称" align="center" min-width="100px">
                          <template slot-scope="{ row }">
                            <span>{{ row.productNameCn }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="规格" align="center" min-width="100px">
                          <template slot-scope="{ row }">
                            <span>{{ row.spec }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="厂商" align="center" min-width="100px">
                          <template slot-scope="{ row }">
                            <span>{{ row.manufacturerName }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="供货价(元)" align="center" min-width="100px">
                          <template slot-scope="{ row }">
                            <span>{{ row.supplyPrice }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="销售下限" min-width="80px" align="center">
                          <template slot-scope="{ row }">
                            <span>{{ row.minimumPurchaseQuantity }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="折扣金额(元)" min-width="120px" align="center">
                          <template slot-scope="{ row }">
                            <span>{{ row.rebateUnitPrice }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="总指标" min-width="80px" align="center">
                          <template slot-scope="{ row }">
                            <span>{{ row.quotaQuantity }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="一季度指标" min-width="120px" align="center">
                          <template slot-scope="{ row }">
                            <span>{{ row.q1Quota }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="二季度指标" min-width="120px" align="center">
                          <template slot-scope="{ row }">
                            <span>{{ row.q2Quota }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="三季度指标" min-width="120px" align="center">
                          <template slot-scope="{ row }">
                            <span>{{ row.q3Quota }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="四季度指标" min-width="120px" align="center">
                          <template slot-scope="{ row }">
                            <span>{{ row.q4Quota }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column fixed="right" label="操作" align="center" header-align="center" width="80" class-name="small-padding fixed-width">
                          <template slot-scope="{ row }">
                            <i class="el-icon-document eltablei" title="查看明细" @click="reviewProductDetail(row)" />
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row style="margin-top:10px">
                  <el-col :span="24">
                    <el-form-item label="目标终端">
                      <el-table
                        :data="item.requestFormRebateAgreementTargetReceiverList"
                        stripe
                        border
                        fit
                        highlight-current-row
                        style="width: 100%;"
                        :default-sort="{prop: 'createTime', order: 'descending'}"
                        :header-cell-class-name="'tableStyle'"
                        :row-class-name="handleRowClass"
                      >
                        <el-table-column
                          fixed
                          label="序号"
                          type="index"
                          align="center"
                        />
                        <el-table-column label="编码" align="center" min-width="100px">
                          <template slot-scope="{ row }">
                            <span>{{ row.code }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="终端名称" align="center" min-width="100px">
                          <template slot-scope="{ row }">
                            <span>{{ row.targetReceiverName }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="省份" align="center" min-width="100px">
                          <template slot-scope="{ row }">
                            <span>{{ row.provinceNameCn }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="城市" align="center" min-width="100px">
                          <template slot-scope="{ row }">
                            <span>{{ row.cityNameCn }}</span>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row style="margin-top:10px">
                  <el-col :span="24">
                    <el-form-item label="计算公式">
                      <el-table
                        :data="item.requestFormRebateAgreementTermList"
                        stripe
                        border
                        fit
                        highlight-current-row
                        style="width: 100%;"
                        :default-sort="{prop: 'createTime', order: 'descending'}"
                        :header-cell-class-name="'tableStyle'"
                        :row-class-name="handleRowClass"
                      >
                        <el-table-column
                          fixed
                          label="序号"
                          type="index"
                          align="center"
                        />
                        <el-table-column label="条件" align="center" min-width="300px">
                          <template slot-scope="{ row }">
                            <span>{{ row.agreementTermFormula }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="结果" align="center" min-width="200px">
                          <template slot-scope="{ row }">
                            <span>{{ row.rebateCalculateFormula }}</span>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-collapse-item>
            </el-collapse>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeAgreementViewDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <AgreementProductView ref="refAgreementProductView" />
  </div>
</template>
<script>
import AgreementService from '@/api/agreement'
import FileService from '@/api/file'
import AgreementProductView from './agreementProductView'

export default {
  components: {
    AgreementProductView
  },
  data() {
    return {
      span: 8,
      title: '',
      showAddDialog: false,
      requestFormRebateAgreement: {
        enumPaymentCycleFlags: 0,
        requestFormRebateId: null,
        attachmentList: [],
        requestFormRebateAgreementPolicyList: []
      },
      activeName: '1'
    }
  },
  methods: {
    initPage(formId) {
      const para = { id: formId }
      AgreementService.GetRequestFormRebateAgreement(para)
        .then((result) => {
          this.requestFormRebateAgreement = result.data
        })
        .catch((error) => {
          console.log(error)
        })

      this.showAddDialog = true
    },
    closeAgreementViewDialog() {
      this.clear()
      this.showAddDialog = false
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    clear() {
      this.requestFormRebateAgreement = {
        attachmentList: []
      }
    },
    handleDownLoadFile(templateId, templateName) {
      FileService.downloadAttachment(templateId).then(res => {
        const fileDownload = require('js-file-download')
        var filename = templateName
        fileDownload(res.data, filename)
      })
        .catch(() => {
        })
    },
    reviewProductDetail(row) {
      this.$refs.refAgreementProductView.init(row)
    }
  }
}
</script>
<style scoped>
.el-collapse-item ::v-deep .el-collapse-item__header {
  font-size: 12px !important;
  font-weight: 600 !important;
}
</style>
