<template>
  <div>
    <div class="search-container-bg">
      <el-row type="flex" :gutter="10" class="filter-container minusBottom">
        <el-col :span="span">
          <el-input
            v-model="listQuery.jobNo"
            clearable
            placeholder="工号"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.displayName"
            clearable
            placeholder="员工姓名"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.departmentId"
            :loading="deptLoading"
            class="filter-item"
            placeholder="部门"
            clearable
            @change="deptChange"
          >
            <el-option
              v-for="item in deptList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.enumStatus"
            :loading="deptLoading"
            class="filter-item"
            placeholder="状态"
            clearable
            @change="deptChange"
          >
            <el-option
              v-for="item in employeeStatusList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'Employee_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Employee_Button_Export')"
            :loading="btnExportLoading"
            class="filter-item-button"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              header-align="center"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              sortable="custom"
              prop="jobNo"
              label="工号"
              header-align="center"
              align="center"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.jobNo }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="displayName"
              label="员工姓名"
              header-align="center"
              align="center"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.displayName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="departmentId"
              label="部门"
              header-align="center"
              align="center"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.departmentName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="title"
              label="职务"
              header-align="center"
              align="center"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.title }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="email"
              label="邮箱"
              header-align="center"
              align="center"
              min-width="200px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.email }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="mobile"
              label="电话"
              align="center"
              header-align="center"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.mobile }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="enumStatus"
              label="状态"
              header-align="center"
              align="center"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="User.EnumLockStatus"
              label="密码状态"
              header-align="center"
              align="center"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.userEnumLockStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="hireDate"
              label="同步时间"
              header-align="center"
              align="center"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span v-if="row.synchronizeTime">{{ row.synchronizeTime | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="hiredate"
              label="入职日期"
              header-align="center"
              align="center"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span v-if="row.hireDate">{{ row.hireDate | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="departureDate"
              label="离职日期"
              header-align="center"
              align="center"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span v-if="row.departureDate">{{ row.departureDate | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="100" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i v-if="$isPermitted($store.getters.user, 'Employee_Button_Reset')" class="el-icon-key eltablei" title="重置密码" @click="handleReset(row)" />
                <i class="el-icon-document eltablei" title="查看" @click="review(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>

    <el-dialog
      :close-on-click-modal="false"
      :title="textMap[dialogStatus]"
      :visible="dialogResetFormVisible"
      width="50%"
      @close="btnResetClose"
    >
      <el-form
        ref="dataResetForm"
        :rules="resetRules"
        :model="resetpwd"
        label-position="right"
        label-width="100px"
        class="el-dialogform"
      >
        <el-form-item label="工号" prop="username">
          <label>{{ temp.jobNo }}</label>
        </el-form-item>
        <el-form-item label="初始密码" prop="username">
          <label>KY@202105</label>
        </el-form-item>
        <el-form-item label="新密码" prop="newpwd">
          <el-input
            v-model="resetpwd.newpwd"
            type="password"
            clearable
            show-password
            placeholder="新密码"
            maxlength="100"
          />
        </el-form-item>
        <el-form-item label="确认新密码" prop="renewpwd">
          <el-input
            v-model="resetpwd.renewpwd"
            type="password"
            clearable
            show-password
            placeholder="确认新密码"
            maxlength="50"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnResetClose()"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="reset()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      title="员工"
      :visible="dialogRolePermissionVisible"
      width="70%"
      @close="btnRolePermissionClose"
    >
      <el-form
        label-position="right"
        label-width="100px"
        class="el-dialogform-detail"
      >
        <el-tabs v-model="activeName" @tab-click="tabClick">
          <el-tab-pane label="员工信息" name="first">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="工号">
                  <span style="padding-left:23px;">{{ employeeInfo.jobNo }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="姓名">
                  <span style="padding-left:23px;">{{ employeeInfo.displayName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="部门">
                  <span style="padding-left:23px;">{{ employeeInfo.departmentName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="职务">
                  <span style="padding-left:23px;">{{ employeeInfo.title }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别">
                  <span style="padding-left:23px;">{{ employeeInfo.enumGenderDesc }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="邮箱">
                  <span style="padding-left:23px;">{{ employeeInfo.email }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手机">
                  <span style="padding-left:23px;">{{ employeeInfo.mobile }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="状态">
                  <span style="padding-left:23px;">{{ employeeInfo.enumStatusDesc }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="同步时间">
                  <span v-if="employeeInfo.synchronizeTime" style="padding-left:23px;">{{ employeeInfo.synchronizeTime | parseTime('{y}-{m}-{d}') }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="入职日期">
                  <span v-if="employeeInfo.hireDate" style="padding-left:23px;">{{ employeeInfo.hireDate | parseTime('{y}-{m}-{d}') }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="离职日期">
                  <span v-if="employeeInfo.departureDate" style="padding-left:23px;">{{ employeeInfo.departureDate | parseTime('{y}-{m}-{d}') }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="角色" name="second">
            <el-row :gutter="10">
              <el-col :span="24">
                <el-table
                  :data="roleList"
                  stripe
                  border
                  fit
                  highlight-current-row
                  style="width: 100%;"
                  :default-sort="{prop: 'createTime', order: 'descending'}"
                  :header-cell-class-name="'tableStyle'"
                  :row-class-name="handleRowClass"
                  @sort-change="roleSortChange"
                >
                  <el-table-column
                    fixed
                    label="序号"
                    type="index"
                    header-align="center"
                    align="center"
                    :index="indexMethod"
                  />
                  <el-table-column
                    sortable="custom"
                    prop="name"
                    label="角色名称"
                    header-align="center"
                    align="center"
                    min-width="100px"
                  >
                    <template slot-scope="{ row }">
                      <span>{{ row.name }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    sortable="custom"
                    prop="remark"
                    label="角色描述"
                    header-align="center"
                    align="center"
                    min-width="100px"
                  >
                    <template slot-scope="{ row }">
                      <span>{{ row.remark }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="权限" name="third">
            <el-row :gutter="10">
              <el-col :span="24">
                <el-table
                  :data="permissionList"
                  stripe
                  border
                  fit
                  highlight-current-row
                  style="width: 100%;"
                  :default-sort="{prop: 'createTime', order: 'descending'}"
                  :header-cell-class-name="'tableStyle'"
                  :row-class-name="handleRowClass"
                  @sort-change="sortChange"
                >
                  <el-table-column
                    fixed
                    label="序号"
                    type="index"
                    header-align="center"
                    align="center"
                    :index="permissionIndexMethod"
                    width="150px"
                  />
                  <el-table-column
                    label="菜单"
                    header-align="center"
                    align="center"
                    min-width="300px"
                  >
                    <template slot-scope="{ row }">
                      <span>{{ row.parentName }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="按钮"
                    header-align="center"
                    align="center"
                    min-width="300px"
                  >
                    <template slot-scope="{ row }">
                      <span>{{ row.name }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
              <el-col class="el-colRight">
                <pagination v-show="permissionTotal > 0" :total="permissionTotal" :page.sync="permissionQuery.pageIndex" :limit.sync="permissionQuery.pageSize" @pagination="getEmployeePermission" />
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnRolePermissionClose()"> 关闭 </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出员工信息"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import MaintenanceService from '@/api/maintenance'
import CustomExport from '@/components/Export/CustomExport'
import MasterDataService from '@/api/masterData'

export default {
  name: 'Employee',
  components: {
    Pagination,
    CustomExport
  },
  data() {
    var regex = new RegExp('(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,30}')
    var validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else if (value.toString().length < 8 || value.toString().length > 30) {
        callback(new Error('密码长度为8 - 30个字符'))
      } else if (!regex.test(value.trim())) {
        callback(new Error('密码必须包含字母、数字、特殊字符'))
      } else {
        callback()
      }
    }
    var validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.resetpwd.newpwd) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      permissionTotal: 0,
      permissionQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      employeeRoleListQuery: {
        order: '-CreateTime'
      },
      listLoading: false,
      list: [],
      employeeId: '',
      btnExportLoading: false,
      dialogEditFormVisible: false,
      dialogResetFormVisible: false,
      showExportModal: false,
      deptLoading: false,
      dialogStatus: '',
      temp: {
        id: undefined,
        jobNo: '',
        displayName: '',
        mobile: '',
        email: ''
      },
      resetpwd: {
        userId: undefined,
        newpwd: '',
        renewpwd: ''
      },
      textMap: {
        update: '编辑厂商用户',
        create: '新增厂商用户',
        reset: '重置密码'
      },
      rules: {
        manufacturerId: [
          {
            required: true,
            type: 'string',
            message: '请选择厂商',
            trigger: 'change'
          }
        ],
        username: [
          {
            required: true,
            type: 'string',
            message: '请输入账号',
            trigger: 'blur'
          }
        ],
        displayName: [
          {
            required: true,
            type: 'string',
            message: '请输入姓名',
            trigger: 'blur'
          }
        ],
        email: [
          {
            type: 'email',
            message: '请输入正确的邮箱地址',
            trigger: ['blur', 'change']
          }
        ]
      },
      resetRules: {
        newpwd: [
          {
            required: true,
            validator: validatePass,
            trigger: 'blur'
          }
        ],
        renewpwd: [
          {
            required: true,
            validator: validatePass2,
            trigger: 'blur'
          }
        ]
      },
      modifyDialogTitle: '',
      modifyDialogIsReadonly: false,
      itemId: null,
      manufacturerList: [],
      columnDictionary: {},
      employeeStatusList: [],
      deptList: [],
      datatree: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      dialogRolePermissionVisible: false,
      roleList: [],
      activeName: 'first',
      employeeInfo: {},
      permissionList: []
    }
  },
  created() {
    this.initDept()
    this.initEmployeeStatus()
    this.getList()
  },
  mounted() {

  },
  methods: {
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch((error) => {
          this.deptLoading = false
          console.log(error)
        })
    },
    initEmployeeStatus() {
      var param = {
        enumType: 'EmployeeStatus'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.employeeStatusList = result.data.datas
        })
        .catch((error) => {
          console.log(error)
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      MaintenanceService.QueryEmployee(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    deptChange() {
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    permissionIndexMethod(index) {
      return (this.permissionQuery.pageIndex - 1) * this.permissionQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    onShowExportModal() {
      this.btnExportLoading = true
      MaintenanceService.GetEmployeeExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      this.listQuery.checkedColumns = checkColumns
      MaintenanceService.ExportEmployee(this.listQuery)
        .then(result => {
          this.listQuery.checkedColumns = null
          const fileDownload = require('js-file-download')
          const fileName = '员工列表.xlsx'
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    roleSortChange(column, prop, order) {
      // this.emoloyeeRoleListQuery.employeeId = this.employeeId
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.employeeRoleListQuery.order = orderSymbol + column.prop
      this.getEmployeeRole(this.employeeRoleListQuery)
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    btnClose() {
      this.dialogEditFormVisible = false
    },
    handleReset(row) {
      this.temp = Object.assign({}, row)
      this.resetpwd.userId = row.id
      this.resetpwd.newpwd = ''
      this.resetpwd.renewpwd = ''
      this.dialogStatus = 'reset'
      this.dialogResetFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataResetForm'].clearValidate()
      })
    },
    reset() {
      this.$refs['dataResetForm'].validate((valid) => {
        if (valid) {
          this.listLoading = true
          MaintenanceService.ResetEmployeePwd(this.resetpwd)
            .then((result) => {
              this.listLoading = false
              if (result.succeed) {
                this.dialogResetFormVisible = false
                this.getList()
                this.showMessage('重置成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
              this.listLoading = false
            })
        }
      })
    },
    btnResetClose() {
      this.dialogResetFormVisible = false
    },
    review(row) {
      this.employeeId = row.id
      this.employeeInfo = row
      this.dialogRolePermissionVisible = true
      this.permissionQuery.employeeId = this.employeeId
      this.getEmployeePermission()
      this.employeeRoleListQuery.employeeId = this.employeeId
      this.getEmployeeRole(this.employeeRoleListQuery)
    },
    getEmployeePermission() {
      MaintenanceService.QueryEmployeePermissions(this.permissionQuery).then(result => {
        if (result.succeed) {
          this.permissionList = result.data.datas
          this.permissionTotal = result.data.recordCount
          this.permissionQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    getEmployeeRole(id) {
      MaintenanceService.QueryEmployeeRoles(this.employeeRoleListQuery).then(result => {
        if (result.succeed) {
          this.roleList = result.data.datas
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    btnRolePermissionClose() {
      this.permissionQuery.pageIndex = 1
      this.dialogRolePermissionVisible = false
    },
    tabClick() {
    },
    exprot() {

    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },
    onHidden() {
      this.dialogEditFormVisible = false
    },
    onRefresh() {
      this.getList()
      this.dialogEditFormVisible = false
    }

  }
}
</script>
<style scoped>
.el-dialogform-detail
{
  width: 85%;
    margin-left: 50px;
}
</style>
