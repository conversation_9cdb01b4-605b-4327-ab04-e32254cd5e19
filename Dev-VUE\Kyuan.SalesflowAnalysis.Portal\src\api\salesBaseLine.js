import HttpApi from './libs/api.request'

const controller = 'SalesBaseLine'

const api = new HttpApi(controller)

export default {
  QuerySalesBaseLine(params) {
    return api.get('QuerySalesBaseLine', params)
  },
  GetSalesBaseLine(params) {
    return api.get('GetSalesBaseLine', params)
  },
  AddSalesBaseLine(params) {
    return api.post('AddSalesBaseLine', params)
  },
  UpdateSalesBaseLine(params) {
    return api.post('UpdateSalesBaseLine', params)
  },
  DeleteSalesBaseLine(params) {
    return api.post('DeleteSalesBaseLine', params)
  }
}
