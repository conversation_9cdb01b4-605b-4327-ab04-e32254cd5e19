<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10">
        <el-col :span="span">
          <el-input
            v-model="listQuery.distributorName"
            clearable
            placeholder="发货方名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.receiverName"
            clearable
            placeholder="收货方名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="6">
          <el-cascader
            ref="refProductAndSpec"
            v-model="manufacturerProductAndSpecId"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            placeholder="厂商 / 产品 / 规格"
            style="width:100%"
            clearable
            class="filter-item"
            :props="{ multiple: false, checkStrictly: true ,expandTrigger: 'hover' }"
            @change="handleProductChange"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.isHandle"
            class="filter-item"
            placeholder="是否已处理"
            clearable
          >
            <el-option
              v-for="item in isHandleList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'WarningStopPurchase_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column label="发货方名称" sortable="custom" header-align="center" align="left" min-width="100px" prop="Distributor.Name">
              <template slot-scope="{ row }">
                <span>{{ row.distributorName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="收货方名称" sortable="custom" header-align="center" align="left" min-width="100px" prop="Receiver.Name">
              <template slot-scope="{ row }">
                <span>{{ row.receiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="厂商" sortable="custom" header-align="center" align="left" min-width="100px" prop="ProductSpec.Product.Manufacturer.Name">
              <template slot-scope="{ row }">
                <span>{{ row.manufacturerName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="产品" sortable="custom" header-align="center" align="center" min-width="100px" prop="ProductSpec.Product.NameCn">
              <template slot-scope="{ row }">
                <span>{{ row.productName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="规格" sortable="custom" header-align="center" align="center" min-width="100px" prop="ProductSpec.Spec">
              <template slot-scope="{ row }">
                <span>{{ row.spec }}</span>
              </template>
            </el-table-column>
            <el-table-column label="所在月份" sortable="custom" header-align="center" align="center" min-width="100px" prop="YearMonthSplicing">
              <template slot-scope="{ row }">
                <span>{{ row.yearMonthSplicing }}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否已处理" sortable="custom" header-align="center" align="center" min-width="100px" prop="IsHandle">
              <template slot-scope="{ row }">
                <span>{{ row.isHandleDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i v-if="$isPermitted($store.getters.user, 'WarningStopPurchase_Button_Save') && !row.isHandle" title="处理" class="el-icon-edit eltablei" @click="handleSetOK(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import ReportService from '@/api/report'
import ProductService from '@/api/product'

export default {
  name: 'WarningStopPurchase',
  components: {
    Pagination
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      list: [],
      manufacturerProductAndSpecId: [],
      productAndSpecList: [],
      productAndSpecLoading: false,
      isHandleList: [
        {
          'value': true,
          'desc': '已处理'
        },
        {
          'value': false,
          'desc': '未处理'
        }
      ]
    }
  },
  created() {
    this.initProductAndSpec()
    this.handleFilter()
  },
  methods: {
    initProductAndSpec() {
      this.productAndSpecLoading = true
      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch(() => {
          this.productAndSpecLoading = false
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      if (this.manufacturerProductAndSpecId != null && this.manufacturerProductAndSpecId.length > 0) {
        this.listQuery.manufacturerId = this.manufacturerProductAndSpecId[0]
        this.listQuery.productId = this.manufacturerProductAndSpecId[1]
        this.listQuery.productSpecId = this.manufacturerProductAndSpecId[2]
      }

      ReportService.QueryWarningStopPurchase(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },

    handleSetOK(row) {
      this.$confirm('是否设置为已处理?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        ReportService.SetWarningStopPurchaseHandled(row).then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('处理成功', 'success')
          }
        })
          .catch(error => {
            this.listLoading = false
            console.log(error)
          })
      }).catch(error => {
        this.listLoading = false
        if (!error.succeed) {
          this.$notice.message('取消', 'info')
        }
      })
    },
    handleProductChange() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    }

  }
}
</script>
