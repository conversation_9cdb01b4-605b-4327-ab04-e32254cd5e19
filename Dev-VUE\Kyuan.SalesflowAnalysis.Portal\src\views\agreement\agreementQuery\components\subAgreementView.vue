<template>
  <div>
    <el-dialog
      custom-class="el-dialog-s"
      title="查看协议"
      width="60%"
      append-to-body
      :close-on-click-modal="false"
      :visible="showAddDialog"
      @close="closeAgreementViewDialog"
    >
      <el-form
        ref="dataForm"
        :model="rebateAgreement"
        label-position="left"
        label-width="100px"
        class="el-dialogform"
        style="width:95%"
      >
        <el-row type="flex" class="filter-container">
          <el-col :span="24" style="color:red;">
            <el-form-item v-if="rebateAgreement.enumStatus===rebateAgreementStatus.changed || rebateAgreement.enumStatus===rebateAgreementStatus.obsolete || rebateAgreement.changeAgreementCode" label="协议状态：">
              <label>协议{{ rebateAgreement.enumStatusDesc }}</label>
              <label v-if="rebateAgreement.enumStatus===rebateAgreementStatus.changed">，变更时间：{{ rebateAgreement.expiredDate }}</label>
              <label v-if="rebateAgreement.enumStatus===rebateAgreementStatus.obsolete">，作废日期：{{ rebateAgreement.obsoleteDate|parseTime('{y}-{m}-{d}') }}，作废原因：{{ rebateAgreement.obsoleteReason }}</label>
              <label v-if="rebateAgreement.changeAgreementCode">；此单据由原协议单号[{{ rebateAgreement.changeAgreementCode }}]变更生成</label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="协议编号：">
              {{ rebateAgreement.code }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="协议类型：">
              {{ rebateAgreement.rebateAgreementTemplateName }}
            </el-form-item>
          </el-col>
          <el-col v-if="!rebateAgreement.isMaster&&rebateAgreement.rebateAgreementTemplateTypeCode!=='changeChannels'" :span="24">
            <el-form-item label="主协议：">
              {{ rebateAgreement.masterRebateAgreementName }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="协议名称：">
              <label>{{ rebateAgreement.name }}</label>
            </el-form-item>
          </el-col>
          <el-col v-if="!rebateAgreement.isMaster&&rebateAgreement.rebateAgreementTemplateTypeCode!=='changeChannels'" :span="24">
            <el-form-item label="项目名称：">
              <label>{{ rebateAgreement.rebateProjectName }}</label>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="甲方：">
              {{ rebateAgreement.partyAName }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="乙方：">
              {{ rebateAgreement.rebateReceiverName }}
            </el-form-item>
          </el-col>
          <el-col v-if="rebateAgreement.enumSigningPartiesType >=2" :span="span">
            <el-form-item :label="rebateAgreement.enumSigningPartiesType ===3 || rebateAgreement.enumSigningPartiesType ===6?'丙方1：':'丙方：'">
              {{ rebateAgreement.partyCNameOne }}
            </el-form-item>
          </el-col>
          <el-col v-if="rebateAgreement.enumSigningPartiesType === 3||rebateAgreement.enumSigningPartiesType === 6" :span="12">
            <el-form-item label="丙方2：">
              {{ rebateAgreement.partyCNameTwo }}
            </el-form-item>
          </el-col>
          <el-col v-if="rebateAgreement.enumSigningPartiesType >=4" :span="span">
            <el-form-item :label="rebateAgreement.enumSigningPartiesType ===5?'丁方1：':'丁方：'">
              {{ rebateAgreement.partyDNameOne }}
            </el-form-item>
          </el-col>
          <el-col v-if="rebateAgreement.enumSigningPartiesType ===5" :span="span">
            <el-form-item label="丁方2：">
              {{ rebateAgreement.partyDNameTwo }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="起始日期：">
              <label>{{
                rebateAgreement.startDate | parseTime("{y}-{m}-{d}")
              }}</label>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="截止日期：">
              <label>{{
                rebateAgreement.endDate | parseTime("{y}-{m}-{d}")
              }}</label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="协议负责人：">
              <label>{{ rebateAgreement.principalDisplayName }}({{
                rebateAgreement.principalJobNo
              }})</label>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门：">
              <label>{{ rebateAgreement.departmentName }}</label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="filter-container">
          <el-col v-if="!rebateAgreement.isMaster&&rebateAgreement.rebateAgreementTemplateTypeCode!=='changeChannels'" :span="span">
            <el-form-item label="支付周期：">
              <label>{{ rebateAgreement.paymentCycleName }}</label>
            </el-form-item>
          </el-col>
          <el-col v-if="rebateAgreement.rebateAgreementTemplateTypeCode!=='changeChannels'" :span="span">
            <el-form-item label="支付方式：">
              <label>{{ rebateAgreement.paymentTypeName }}</label>
            </el-form-item>
          </el-col>
          <el-col v-if="rebateAgreement.rebateAgreementTemplateTypeCode!=='changeChannels'" :span="span">
            <el-form-item label="支付天数：">
              <label>{{ rebateAgreement.payWithinDaysName }}</label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="filter-container">
          <el-col
            v-if="rebateAgreement.enumSigningPartiesType === 2 ||rebateAgreement.enumSigningPartiesType === 3 ||
              rebateAgreement.enumSigningPartiesType === 4 ||rebateAgreement.enumSigningPartiesType === 5 || rebateAgreement.enumSigningPartiesType === 6"
            :span="span"
          >
            <el-form-item label="丙方代付：">
              <label>{{ rebateAgreement.partyCPaymentWithinDaysName }}</label>
            </el-form-item>
          </el-col>
          <el-col
            v-if="
              rebateAgreement.enumSigningPartiesType === 4 || rebateAgreement.enumSigningPartiesType === 5 || rebateAgreement.enumSigningPartiesType === 6
            "
            :span="span"
          >
            <el-form-item label="丁方代付：">
              <label>{{ rebateAgreement.partyDPaymentWithinDaysName }}</label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="filter-container">
          <el-form-item label="合同文件下载：">
            <i class="el-icon-download eltablei" title="协议下载" @click="handleDownLoadTemplate(rebateAgreement)" />
          </el-form-item>
        </el-row>
        <el-row v-if="rebateAgreement.rebateAgreementPolicyModel && rebateAgreement.rebateAgreementTemplateTypeCode!=='cover'">
          <el-col :span="24">
            <el-form-item label="政策条款：">
              <label v-show="rebateAgreement.rebateAgreementPolicyModel.enumCalculationTemplateType=== $constDefinition.calculationTemplateType.steppedType">
                当达成率大于等于{{ rebateAgreement.rebateAgreementPolicyModel.upperLimitAchievementRateDisplay }}%时,补偿系数为1;
                当{{ rebateAgreement.rebateAgreementPolicyModel.lowerLimitAchievementRateDisplay }}%≤达成率＜
                {{ rebateAgreement.rebateAgreementPolicyModel.upperLimitAchievementRateDisplay }}%时,补偿系数为：实际达成率；
                当达成率小于{{ rebateAgreement.rebateAgreementPolicyModel.lowerLimitAchievementRateDisplay }}%时，补偿系数为0;
              </label>
              <label v-show="rebateAgreement.rebateAgreementPolicyModel.enumCalculationTemplateType=== $constDefinition.calculationTemplateType.quantityReached">
                当达成率大于等于100%时,补偿系数为1;
                当{{ rebateAgreement.rebateAgreementPolicyModel.lowerLimitAchievementRateDisplay }}%≤达成率＜
                100%时,补偿系数为：实际达成率；
                当达成率小于{{ rebateAgreement.rebateAgreementPolicyModel.lowerLimitAchievementRateDisplay }}%时，补偿系数为0;
              </label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="rebateAgreement.rebateAgreementPolicyModel && rebateAgreement.rebateAgreementTemplateTypeCode==='cover'">
          <el-col :span="24">
            <el-form-item label="政策条款：">
              <label>
                （1）至少完成【{{ rebateAgreement.rebateAgreementPolicyModel.numberOfStores }} 】家门店的销售；
                （2）至少完成【{{ rebateAgreement.rebateAgreementPolicyModel.numberOfSpecPerStore }}】个规格产品的销售；
                （3）各品规{{ rebateAgreement.rebateAgreementPolicyModel.enumDistributionQuantityStatisticalTypeDesc }}销售量不低于【{{ rebateAgreement.rebateAgreementPolicyModel.distributionQuantity }}】盒。
              </label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="rebateAgreement.rebateAgreementPolicyModel" style="margin-top: 10px" class="table">
          <el-col
            v-if="
              rebateAgreement.rebateAgreementPolicyModel
                .rebateAgreementProductSpecList
            "
            :span="24"
          >
            <el-form-item label="产品指标">
              <el-table
                :data="
                  rebateAgreement.rebateAgreementPolicyModel
                    .rebateAgreementProductQuotaList
                "
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%"
                :header-cell-class-name="'tableStyle'"
              >
                <el-table-column
                  fixed
                  label="序号"
                  type="index"
                  align="center"
                />
                <el-table-column
                  label="产品(规格)"
                  prop="productNameCn"
                  align="center"
                  min-width="100px"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.productSpecName }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  label="补偿单价（元）"
                  prop="calculationPrice"
                  min-width="120px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.rebateUnitPrice | toTwoNum }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode!=='QuarterPayment'"
                  label="年度指标"
                  prop="totalQuota"
                  min-width="80px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.totalQuota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode!=='YearPayment'"
                  label="一季度指标"
                  prop="q1Quota"
                  min-width="100px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.q1Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode!=='YearPayment'"
                  label="二季度指标"
                  prop="q2Quota"
                  min-width="100px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.q2Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode!=='YearPayment'"
                  label="三季度指标"
                  prop="q3Quota"
                  min-width="100px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.q3Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode!=='YearPayment'"
                  label="四季度指标"
                  prop="q4Quota"
                  min-width="100px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.q4Quota }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="rebateAgreement.rebateAgreementTemplateTypeCode==='pureSales' && rebateAgreement.rebateAgreementPolicyModel" style="margin-top: 10px">
          <el-col :span="24">
            <el-form-item label="目标终端">
              <el-table
                :data="
                  rebateAgreement.rebateAgreementPolicyModel
                    .rebateAgreementTargetReceiverList
                "
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%"
                :default-sort="{ prop: 'createTime', order: 'descending' }"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
              >
                <el-table-column
                  fixed
                  label="序号"
                  type="index"
                  align="center"
                />
                <el-table-column
                  label="产品(规格)"
                  prop="productNameCn"
                  align="center"
                  min-width="100px"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.productName }}({{ row.spec }})</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode!=='QuarterPayment'"
                  label="年度指标"
                  prop="totalQuota"
                  min-width="80px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.totalQuota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode==='QuarterPayment'"
                  label="一季度指标"
                  prop="q1Quota"
                  min-width="100px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.q1Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode==='QuarterPayment'"
                  label="二季度指标"
                  prop="q2Quota"
                  min-width="100px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.q2Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode==='QuarterPayment'"
                  label="三季度指标"
                  prop="q3Quota"
                  min-width="100px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.q3Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode==='QuarterPayment'"
                  label="四季度指标"
                  prop="q4Quota"
                  min-width="100px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.q4Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  label="终端名称"
                  align="center"
                  min-width="100px"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.targetReceiverName }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeAgreementViewDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import AgreementService from '@/api/agreement'
import FileService from '@/api/file'

export default {

  data() {
    return {
      span: 12,
      title: '',
      showAddDialog: false,
      rebateAgreement: {
        enumPaymentCycleFlags: 0,
        requestFormRebateId: null,
        attachmentList: [],
        rebateAgreementPolicyModel: {
          rebateAgreementProductSpecList: []
        }
      },
      activeName: '1',
      method: 'UploadSealedContract',
      controller: 'RebateAgreement',
      rebateAgreementStatus: {
        changed: 80, // 已变更
        obsolete: 90 // 已作废
      }
    }
  },
  methods: {
    initPage(formId) {
      const para = { id: formId }
      AgreementService.GetRebateAgreement(para)
        .then((result) => {
          this.rebateAgreement = result.data
        })
        .catch((error) => {
          console.log(error)
        })

      this.showAddDialog = true
    },
    closeAgreementViewDialog() {
      this.clear()
      this.showAddDialog = false
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    clear() {
      this.rebateAgreement = {
        rebateAgreementPolicyModel: {
          rebateAgreementProductSpecList: []
        }
      }
    },
    handleDownLoadFile(templateId, templateName) {
      FileService.downloadAttachment(templateId)
        .then((res) => {
          const fileDownload = require('js-file-download')
          var filename = templateName
          fileDownload(res.data, filename)
        })
        .catch(() => {})
    },
    reviewProductDetail(row) {
      this.$refs.refAgreementProductView.init(row)
    },
    handleDownLoadTemplate(row) {
      var objectType = 'SealedRebateContract'
      if (!row.isUploadSealedContract) {
        objectType = 'RebateContract'
      }
      FileService.downloadAttachmentBusiness(row.id, objectType).then(res => {
        const fileDownload = require('js-file-download')
        fileDownload(res, `${row.name}.pdf`)
      })
        .catch(() => {
        })
    }
  }
}
</script>
<style scoped>
.el-collapse-item ::v-deep .el-collapse-item__header {
  font-size: 12px !important;
  font-weight: 600 !important;
}
</style>
