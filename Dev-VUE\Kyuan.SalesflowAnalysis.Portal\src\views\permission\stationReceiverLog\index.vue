<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="8">
          <el-date-picker
            v-model="listQuery.dateRange"
            clearable
            class="filter-item"
            style="width:100%"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.deptId"
            :loading="departmentLoading"
            class="filter-item"
            placeholder="部门"
            clearable
          >
            <el-option
              v-for="item in deptList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.stationName"
            clearable
            placeholder="岗位名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.receiverName"
            clearable
            placeholder="终端名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'StationReceiverLog_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'StationReceiverLog_Button_Export')"
            :loading="btnExportLoading"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column label="部门" align="center" sortable="custom" min-width="100px" prop="Station.Department.Name">
              <template slot-scope="{ row }">
                <span>{{ row.deptName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="上级岗位" align="center" sortable="custom" width="180px" prop="Station.Parent.Name">
              <template slot-scope="{ row }">
                <span>{{ row.stationParentName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="岗位名称" align="center" sortable="custom" width="180px" prop="Station.Name">
              <template slot-scope="{ row }">
                <span>{{ row.stationName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="负责省份" align="left" width="200px" header-align="center">
              <template slot-scope="{ row }">
                <span>{{ row.responsibleProvinceName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="终端名称" align="left" header-align="center" sortable="custom" min-width="120px" prop="Receiver.Name">
              <template slot-scope="{ row }">
                <span>{{ row.receiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="挂岗时间" align="center" sortable="custom" width="100px" prop="StartDate">
              <template slot-scope="{ row }">
                <span>{{ row.startDate | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="撤岗时间" align="center" sortable="custom" width="100px" prop="EndDate">
              <template slot-scope="{ row }">
                <span v-if="row.endDate">{{ row.endDate | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="80px" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i v-if="$isPermitted($store.getters.user, 'StationReceiverLog_Button_Edit')" class="el-icon-edit-outline eltablei" title="编辑" @click="handleEdit(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出终端在岗日志"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
    <editPage ref="editPage" @refreshData="getList" />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import CustomExport from '@/components/Export/CustomExport'
import MaintenanceService from '@/api/maintenance'
import StationReceiverService from '@/api/stationReceiver'
import editPage from './components/editPage.vue'

export default {
  name: 'StationReceiverLog',
  components: {
    Pagination,
    CustomExport,
    editPage
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      list: [],
      departmentLoading: false,
      btnExportLoading: false,
      showExportModal: false,
      deptList: [],
      columnDictionary: {}
    }
  },
  created() {
    this.initDepartment()
    this.handleFilter()
  },
  methods: {
    initDepartment() {
      this.departmentLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.departmentLoading = false
          this.deptList = result
        })
        .catch((error) => {
          this.departmentLoading = false
          console.log(error)
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      StationReceiverService.QueryStationReceiver(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      StationReceiverService.GetStationReceiverExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      this.listQuery.checkedColumns = checkColumns
      StationReceiverService.ExportStationReceiver(this.listQuery)
        .then(result => {
          this.listQuery.checkedColumns = null
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '终端与岗位日志记录.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '终端与岗位日志记录.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleEdit(row) {
      this.$refs.editPage.init(row)
    },
    handleExportCancel() {
      this.showExportModal = false
    }

  }
}
</script>
