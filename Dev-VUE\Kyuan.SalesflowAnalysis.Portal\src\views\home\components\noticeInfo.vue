<template>
  <div>
    <el-row>
      <el-col :span="24">
        <article style="margin:0 10px;width: 100%;padding-top: 15px;">
          <hgroup>
            <h1 class="headingStyle">{{ contentInfo.announcementTitle }}</h1>
            <h2 class="headingStyle">{{ contentInfo.announcementSubTitle }}</h2>
          </hgroup>
          <!-- 发布时间 -->
          <p class="primary_C"><span style="float: right">发布时间:{{ contentInfo.createTime }}</span></p>
          <div class="announcementContentStyle">
            <div class="ql-editor" v-html="contentInfo.content" />
          </div>
        </article>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import HomeService from '@/api/home'

export default {
  props: {
    announcementId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      span: 12,
      contentInfo: {}
    }
  },
  watch: {
    announcementId: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        if (val !== '') {
          this.announcementId = val
          this.getAnnouncementContent()
        }
      }
    }
  },
  methods: {
    getAnnouncementContent() {
      HomeService.GetAnnouncementContent({
        announcementId: this.announcementId
      }).then(res => {
        this.contentInfo = res.data
      }).catch(res => {})
    }
  }
}
</script>
<style>
.announcementContentStyle{
  width: 100%!important;
}
.announcementContentStyle p{
  width: 100%!important;
}
.announcementContentStyle img{
  display: block;
  width: 100%!important;
}
</style>

<style scoped>
.primary_C{
  clear: both;
  height: 30px;
}
.headingStyle{
  text-align: center;
}
</style>
