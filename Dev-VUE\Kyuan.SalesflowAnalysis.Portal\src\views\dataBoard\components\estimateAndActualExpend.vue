<!--预计支出/实际支出-->
<template>
  <div class="panel-group">
    <el-card style="height: 310px;">
      <div slot="header" class="clearfix cardHeader">
        <span>预计支出/实际支出</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleMore">详情</el-button>
      </div>
      <div ref="projectChart" style="width: 100%; height: 220px;" />
    </el-card>
    <!--弹出dialog:详情页-->
    <el-dialog append-to-body title="预计支出/实际支出查看" width="60%" :close-on-click-modal="false" :visible="showDialog" @close="cancleDialog()">
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="8">
            <el-input
              v-model="filter.projectName"
              clearable
              placeholder="项目名称"
              class="filter-item"
              @input="$forceUpdate()"
            />
          </el-col>
          <el-col :span="4">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-search"
              @click="search"
            >
              查询
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table
              :data="queryList"
              stripe
              fit
              border
              highlight-current-row
              style="width: 100%"
              :header-cell-class-name="'tableStyle'"
            >
              <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
              <el-table-column label="项目名称" min-width="150" header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.projectName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="预计收入" width="120" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.estimatedIncome | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column label="实际支出" width="120" prop="storesCount" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.actualInvestment | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column label="实际收入" width="120" prop="storesCount" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.actualIncome | toMoney }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleDialog()">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import echarts from 'echarts'
import DashboardService from '@/api/dashboard'

export default {
  data() {
    return {
      filter: {
        projectName: '',
        topList: []
      },
      projectData: [],
      projectedTotalIncomeData: [], // 预计收入
      actualTotalIncomeData: [], // 实际收入
      actualTotalInvestmentData: [], // 支出
      showDialog: false,
      dataList: [],
      queryList: [],
      topCount: 1
    }
  },
  methods: {
    initPage(param, count) {
      this.filter = param
      this.topCount = count
      this.queryProjectAchieved()
    },
    queryProjectAchieved() {
      DashboardService.QueryProjectAchieved(this.filter)
        .then((res) => {
          this.dataList = res
          this.projectData = []
          this.projectedTotalIncomeData = []
          this.actualTotalIncomeData = []// 实际收入
          this.actualTotalInvestmentData = [] // 支出

          this.topList = res.slice(0, this.topCount)

          this.topList.forEach(element => {
            this.projectData.push(element.projectName)
            this.projectedTotalIncomeData.push(element.estimatedIncome)
            this.actualTotalIncomeData.push(element.actualIncome)
            this.actualTotalInvestmentData.push(element.actualInvestment)
          })
          this.initChart()
        })
        .catch(() => {

        })
    },
    initChart() {
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        grid: {
          top: '20%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: this.projectData
        },
        series: [
          {
            name: '预计收入',
            type: 'bar',
            data: this.projectedTotalIncomeData
          },
          {
            name: '实际支出',
            type: 'bar',
            data: this.actualTotalInvestmentData
          },
          {
            name: '实际收入',
            type: 'bar',
            data: this.actualTotalIncomeData
          }
        ]
      }
      const myChartExpenditure = echarts.init(this.$refs.projectChart)
      myChartExpenditure.setOption(option)
    },
    cancleDialog() {
      this.showDialog = false
      this.queryList = []
      this.filter.projectName = ''
    },
    search() {
      this.queryList = this.dataList.filter(item => {
        return item.projectName.includes(this.filter.projectName)
      })
    },
    indexMethod(index) {
      return (
        index + 1
      )
    },
    handleMore() {
      this.showDialog = true
      this.queryList = this.dataList
    }
  }
}
</script>

<style lang="scss" scoped>
.cardHeader {
      height: 5px;
    }
</style>
