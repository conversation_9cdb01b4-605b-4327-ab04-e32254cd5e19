import HttpApi from './libs/api.request'

const controller = 'home'

const api = new HttpApi(controller)

export default {
  userLogin(data, processResult = true) {
    return api.post('UserLogin', { data, processResult })
  },
  userLogout(processResult = true) {
    return api.get('UserLogout', { processResult })
  },
  changeIdentity(processResult = true) {
    return api.get('ChangeIdentity', { processResult })
  },
  getCurrentUser(processResult = true) {
    return api.get('GetCurrentUser', { processResult })
  },
  updateUserPwd(data) {
    return api.post('ChangeMyPwd', { data })
  },
  QueryAnnouncement(params) {
    return api.get('QueryAnnouncement', params)
  },
  GetAnnouncementContent(params) {
    return api.get('GetAnnouncementContent', params)
  },
  QueryScehdulerTask(params) {
    return api.get('QueryScehdulerTask', params)
  },
  QueryWaitApprovalBonus(params) {
    return api.get('QueryWaitApprovalBonus', params)
  },
  GetStatisticCounts() {
    return api.get('GetStatisticCounts')
  },
  GetCurrentEmployee() {
    return api.get('GetCurrentEmployee')
  },
  GetCurrentEmployeeRole() {
    return api.get('GetCurrentEmployeeRole')
  },
  QuerySalesAchievedRate(params) {
    return api.get('QuerySalesAchievedRate', params)
  },
  QuerySalesRanking(params) {
    return api.get('QuerySalesRanking', params)
  },
  QuerySalesCompare(params) {
    return api.get('QuerySalesCompare', params)
  },
  firstLoginChangePwd(data) {
    return api.post('FirstLoginChangePwd', { data })
  },
  // 查询总监待办奖励审核
  QueryDepartmentWaitApprovalBonus(params) {
    return api.get('QueryDepartmentWaitApprovalBonus', params)
  }
}
