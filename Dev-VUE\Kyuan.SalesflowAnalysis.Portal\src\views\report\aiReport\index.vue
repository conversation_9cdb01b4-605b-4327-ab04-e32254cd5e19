<template>
  <div>
    <div class="search-container-bg">
      <el-form
        label-position="right"
        label-width="100px"
      >
        <el-row :gutter="10" class="filter-container" type="flex">
          <el-col :span="12">
            <el-form-item label="时间：">
              <el-date-picker
                v-model="filter.dateRange"
                style="font-size: 8px!important;"
                clearable
                class="filter-item"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始月份"
                end-placeholder="结束月份"
                format="yyyy-MM"
                value-format="yyyy-MM"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <span class="spanf">必填，请选择时间范围</span>
          </el-col>
          <el-col :span="12">
            <el-form-item label="需求描述：">
              <el-input
                v-model="filter.demand"
                clearable
                placeholder="请输入需求描述"
                class="filter-item"
                type="textarea"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <span class="spanf">必填，例：2025年Q1北区销售达成情况</span>
          </el-col>
          <el-col :span="12">
            <el-form-item label="条件：">
              <el-input
                v-model="filter.inputConditions"
                clearable
                placeholder="请输入条件"
                class="filter-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <span class="spanf">必填，例：部门：药品销售部；产品名称：来士普；大区：北区</span>
          </el-col>
          <el-col :span="12">
            <el-form-item label="列头：">
              <el-input
                v-model="filter.inputColumns"
                clearable
                placeholder="请输入报表显示的列头"
                class="filter-item"
                type="textarea"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <span class="spanf">必填，例：发货方名称，收货方名称，产品名称，销售数量，销售金额，达成率</span>
          </el-col>
          <el-col :span="9">
            <el-form-item label="排序：">
              <el-input
                v-model="filter.orderBy"
                clearable
                placeholder="请输入排序字段"
                class="filter-item"
              />

            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-select
              v-model="filter.order"
              class="filter-item"
              clearable
            >
              <el-option
                v-for="item in orderList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="12">
            <span class="spanf">请输入列头中包含的字段，例:发货方名称</span>
          </el-col>
        </el-row>
        <el-row type="flex" :gutter="10" justify="end">
          <el-col :span="3.5">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-search"
              @click="handleFilter"
            >
              查询
            </el-button>

          </el-col>
          <el-col :span="2" />
        </el-row>
      </el-form>
    </div>
    <div class="list-container">

      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
          >
            <el-table-column
              v-for="(column, index) in columns"
              :key="index"
              :label="column"
              :prop="index"
            />
          </el-table>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>

import ReportService from '@/api/report'

export default {
  name: 'AIReport',
  data() {
    return {
      span: 4,
      columns: [],
      orderList: [{
        value: '1',
        label: '升序'
      },
      {
        value: '2',
        label: '降序'
      }],
      filter: {
        inputConditions: '',
        demand: '',
        inputColumns: '',
        orderBy: ''
      },
      listLoading: false,
      list: [],
      jsonConditions: ''
    }
  },
  created() {
  },
  methods: {

    timeChange() {
      this.$forceUpdate()
    },
    parseStringToObject(input) {
      const result = {}
      const pairs = input.split(/[;；,，]/)
      pairs.forEach(pair => {
        const [key, value] = pair.split(/[:：]/)
        if (key && value) {
          result[key.trim()] = value.trim() // 去除两端空格
        }
      })
      return result
    },
    convertToJson(str) {
      try {
        // 调用解析方法生成对象
        const parsedObject = this.parseStringToObject(str)
        // 转换为 JSON 字符串并格式化显示
        this.jsonConditions = JSON.stringify(parsedObject, null, 2)
        return ''
      } catch (e) {
        return '请输正确格式的字符串'
      }
    },
    handleFilter() {
      if (this.filter.dateRange === undefined || this.filter.dateRange.length === 0) {
        this.$message.error('请选择时间范围')
        return
      }
      if (this.filter.demand === undefined || this.filter.demand.trim() === '') {
        this.$message.error('请输入需求描述')
        return
      }
      if (this.filter.inputConditions === undefined || this.filter.inputConditions.trim() === '') {
        this.$message.error('请输入条件')
        return
      }
      const error = this.convertToJson(this.filter.inputConditions)
      if (error !== '') {
        this.$message.error(error)
        return
      }
      if (this.jsonConditions === '{}') {
        this.$message.error('请输入正确格式的条件,例：部门：销售部；产品名称：来士普；大区：北区')
        return
      }

      if (this.filter.inputColumns === undefined || this.filter.inputColumns.trim() === '') {
        this.$message.error('请输入列头')
        return
      }
      var queryFilter = {}
      queryFilter.time_range = this.filter.dateRange.join('至')

      queryFilter.conditions = JSON.parse(this.jsonConditions)
      queryFilter.requirements = this.filter.demand

      queryFilter.columns = this.filter.inputColumns.split(/[;；,，]/)
      if (queryFilter.columns.length === 0) {
        this.$message.error('请输入正确格式的列头')
        return
      }

      if (this.filter.orderBy === undefined || this.filter.orderBy.trim() === '') {
        queryFilter.sort_rules = {}
      } else {
        queryFilter.sort_rules = { 'field': this.filter.orderBy, 'order': this.filter.order === '1' ? 'ASC' : 'DESC' }
      }

      this.getList(queryFilter)
    },
    getList(filter) {
      this.listLoading = true
      ReportService.QueryAIReport(filter).then(result => {
        if (result.data && result.data.success) {
          this.columns = Object.keys(result.data.data[0])
          this.$nextTick(() => {
            this.list = result.data.data
            this.listLoading = false
          })
        } else {
          this.listLoading = false
          this.$message.error(result.data.msg)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    }

  }
}
</script>
<style>
  .dataupdate{
    font-size: 14px;
    line-height: 25px;
    color: #606266;
    margin-right: 20px;
  }
  .el-form-item--small.el-form-item {
    margin-bottom: 1px !important;
}
.spanf{ font-size: 12px; color:#606266}
</style>
