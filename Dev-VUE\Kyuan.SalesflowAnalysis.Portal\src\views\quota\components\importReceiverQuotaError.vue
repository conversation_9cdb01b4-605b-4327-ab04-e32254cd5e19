<template>
  <div class="list-container">
    <el-row type="flex" justify="end" :gutter="10" style="margin-bottom:10px">
      <el-col :span="3.5">
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-download"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-table
          :data="list"
          stripe
          border
          fit
          highlight-current-row
          style="width: 100%;"
          :default-sort="{prop: 'createTime', order: 'descending'}"
          :header-cell-class-name="'tableStyle'"
          :row-class-name="handleRowClass"
          @sort-change="sortChange"
        >
          <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
          <el-table-column sortable="custom" prop="Month" label="月份" min-width="100px" align="center" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.month }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="DepartmentName" label="部门" min-width="100px" header-align="center" align="left">
            <template slot-scope="{ row }">
              <span>{{ row.departmentName }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="ReceiverCode" label="终端编码" min-width="160px" header-align="center" align="left">
            <template slot-scope="{ row }">
              <span>{{ row.receiverCode }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="ReceiverName" label="终端名称" min-width="160px" header-align="center" align="left">
            <template slot-scope="{ row }">
              <span>{{ row.receiverName }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="ManufacturerName" label="厂商名称" min-width="100px" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.manufacturerName }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="ProductName" label="产品名称" min-width="100px" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="Specification" label="规格" min-width="100px" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.specification }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="Qty" label="潜力数量" min-width="100px" align="right" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.qty }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="ErrorMessage" label="错误信息" min-width="160px" header-align="center" align="left" fixed="right">
            <template slot-scope="{ row }">
              <span>{{ row.errorMessage }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col class="el-colRight">
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.pageIndex"
          :limit.sync="listQuery.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
  </div>
</template>
<script>
import BonusService from '@/api/bonus'
import Pagination from '@/components/Pagination'

export default {
  components: {
    Pagination
  },
  props: {
    importMasterTempId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      btnExportLoading: false
    }
  },
  watch: {
    importMasterTempId: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        if (val !== '') {
          this.listQuery.importMasterTempId = val
          this.getList()
        }
      }
    }
  },
  methods: {
    getList() {
      BonusService.QueryImportReceiverQuotaError(this.listQuery).then(res => {
        this.list = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(res => {})
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    // 确认导出
    handleExport() {
      BonusService.ExportImportReceiverQuotaError(this.listQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '终端潜力错误数据.xlsx'

          fileDownload(result.data, filename)
        })
        .catch((error) => {
          console.log(error)
        })
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    }
  }
}
</script>
<style scoped>
  .el-dialog-s {
  z-index: 12;
}
</style>
