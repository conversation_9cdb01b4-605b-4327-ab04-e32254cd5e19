<template>
  <div>
    <el-dialog custom-class="el-dialog-s" title="编辑岗位潜力" width="90%" append-to-body :close-on-click-modal="false" :visible="showEditDialog" @close="closeEditQuotaDialog">
      <el-form
        ref="dataForm"
        :model="stationQuota"
        label-position="right"
        label-width="110px"
        class="el-dialogform-editQuota"
      >
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="月份" prop="quotaMonth">
              <el-date-picker
                v-model="stationQuota.quotaMonth"
                style="width:100%"
                type="month"
                disabled
                placeholder="月份"
                value-format="yyyy-MM"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="部门">
              <el-input
                v-model="stationQuota.departmentName"
                placeholder="部门"
                class="filter-item"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="岗位">
              <el-input
                v-model="stationQuota.stationName"
                placeholder="岗位"
                class="filter-item"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="厂商" prop="manufacturerId">
              <el-select
                v-model="stationQuota.manufacturerId"
                style="width: 100%"
                class="filter-item"
                placeholder="厂商"
                disabled
              >
                <el-option
                  v-for="item in manufacturers"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="产品/规格" prop="productAndSpecId">
              <el-cascader
                ref="refProductAndSpec"
                :key="productAndSpecKey"
                v-model="stationQuota.productAndSpecId"
                style="width:100%"
                :options="productAndSpecList"
                placeholder="产品/规格"
                disabled
                :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="潜力数量" prop="qty">
              <el-input
                v-model="stationQuota.qty"
                disabled
                placeholder="潜力数量"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="额外潜力数量" prop="otherQuotaQty">
              <el-input
                v-model="stationQuota.otherQuotaQty"
                clearable
                placeholder="额外潜力数量"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="额外潜力说明" prop="otherQuotaRemark">
              <el-input
                v-model="stationQuota.otherQuotaRemark"
                type="textarea"
                clearable
                placeholder="额外潜力说明"
                maxlength="500"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeEditQuotaDialog">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handlerSave"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import BonusSevices from '@/api/bonus'
import ProductService from '@/api/product'
import ManufacturerService from '@/api/manufacturer'

export default {
  data() {
    return {
      span: 12,
      manufacturers: [],
      distributorTypes: [],
      productAndSpecList: [],
      showEditDialog: false,
      stationQuota: {},
      quotas: [],
      productAndSpecKey: 0,
      allProductAndSpecList: []

    }
  },
  methods: {
    initPage(id) {
      this.showEditDialog = true
      this.initManufacturer()
      this.initProductAndSpec()
      this.getStationQuota(id)
    },
    initManufacturer() {
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturers = result
        })
        .catch(() => {
        })
    },
    initProductAndSpec() {
      ProductService.QueryProductAndSpecCascader()
        .then((result) => {
          this.allProductAndSpecList = result
          this.productAndSpecList = result
        })
        .catch(() => {
        })
    },
    getStationQuota(id) {
      BonusSevices.GetStationQuota({ id: id })
        .then((result) => {
          this.stationQuota = result.data
          this.productAndSpecList = this.allProductAndSpecList.filter(item => { return item.parentId === this.stationQuota.manufacturerId })
        })
        .catch(() => {
        })
    },
    handlerSave() {
      if (this.stationQuota.otherQuotaQty !== null && this.stationQuota.otherQuotaQty !== undefined && this.stationQuota.otherQuotaQty !== '') {
        if (!(/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/.test(this.stationQuota.otherQuotaQty))) {
          this.showMessage('额外潜力数量应为大于等于0的数字，支持两位小数。', 'error')
          return false
        }
      }
      if (this.stationQuota.otherQuotaQty === '' || this.stationQuota.otherQuotaQty === null || this.stationQuota.otherQuotaQty === undefined) {
        this.stationQuota.otherQuotaQty = null
      } else {
        this.stationQuota.otherQuotaQty = parseFloat(this.stationQuota.otherQuotaQty)
      }
      BonusSevices.UpdateStationQuota(this.stationQuota)
        .then((result) => {
          if (result.succeed) {
            this.showEditDialog = false
            this.$emit('success')
            this.showMessage('修改成功', 'success')
          } else {
            this.ShowTip(result)
          }
        })
        .catch(() => {
        })
    },
    closeEditQuotaDialog() {
      this.showEditDialog = false
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    }
  }
}
</script>
<style scoped>
  .el-dialog-s {
  z-index: 12;
  }
  .el-dialog-ls {
  z-index: 13;
  }
    .el-dialogform-editQuota {
  width: 90%;
  margin-left: 50px;
}
</style>
