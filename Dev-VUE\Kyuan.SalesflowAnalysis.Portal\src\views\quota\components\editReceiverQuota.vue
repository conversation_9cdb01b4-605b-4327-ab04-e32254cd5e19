<template>
  <div>
    <el-dialog custom-class="el-dialog-s" title="编辑终端潜力" width="70%" append-to-body :close-on-click-modal="false" :visible="showAddDialog" @close="closeEditQuotaDialog">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="receiverQuota"
        label-position="right"
        label-width="110px"
        class="el-dialogform-editQuota"
      >
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="月份" prop="quotaMonth">
              <el-date-picker
                v-model="receiverQuota.quotaMonth"
                style="width:100%"
                type="month"
                disabled
                placeholder="月份"
                value-format="yyyy-MM"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="部门" prop="departmentName">
              <el-input
                v-model="receiverQuota.departmentName"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="终端名称" prop="receiverName">
              <el-input
                v-model="receiverQuota.receiverName"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="所在岗位">
              <el-input
                v-model="receiverQuota.stationName"
                class="filter-item"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="省份">
              <el-input
                v-model="receiverQuota.provinceName"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="负责人">
              <el-input
                v-model="receiverQuota.responsiblePerson"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="厂商" prop="manufacturerId">
              <el-select
                v-model="receiverQuota.manufacturerId"
                style="width: 100%"
                class="filter-item"
                disabled
              >
                <el-option
                  v-for="item in manufacturers"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="产品/规格" prop="productAndSpecId">
              <el-cascader
                ref="refProductAndSpec"
                :key="productAndSpecKey"
                v-model="receiverQuota.productAndSpecId"
                style="width:100%"
                :options="productAndSpecList"
                disabled
                :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="潜力数量" prop="qty">
              <el-input
                v-model="receiverQuota.qty"
                clearable
                placeholder="潜力数量"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeEditQuotaDialog">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handlerSave"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import BonusSevices from '@/api/bonus'
import ProductService from '@/api/product'
import ManufacturerService from '@/api/manufacturer'

export default {
  data() {
    return {
      span: 12,
      manufacturers: [],
      distributorTypes: [],
      productAndSpecList: [],
      showAddDialog: false,
      receiverQuota: {},
      quotas: [],
      productAndSpecKey: 0,
      allProductAndSpecList: [],
      rules: {

        qty: [
          {
            required: true, trigger: ['blur', 'change'], message: '请填写潜力数量'
          },
          { pattern: /((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/, message: '潜力数量应为大于等于0的数字，支持两位小数' }
        ]
      }
    }
  },
  created() {
    this.initProductAndSpec()
  },
  methods: {
    initPage(id) {
      this.initManufacturer()
      this.GetReceiverQuota(id)
    },
    initManufacturer() {
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturers = result
        })
        .catch(() => {
        })
    },
    initProductAndSpec() {
      ProductService.QueryProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
        })
        .catch(() => {
        })
    },
    GetReceiverQuota(id) {
      BonusSevices.GetReceiverQuota({ id: id })
        .then((result) => {
          this.receiverQuota = result.data
          this.showAddDialog = true
        })
        .catch(() => {
        })
    },
    handlerSave() {
      // 保存
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          BonusSevices.UpdateReceiverQuota(this.receiverQuota)
            .then((result) => {
              if (result.succeed) {
                this.closeEditQuotaDialog()
                this.$emit('success')
                this.showMessage('修改成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
            })
        }
      })
    },
    closeEditQuotaDialog() {
      this.receiverQuota = {}
      this.showAddDialog = false
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    }
  }
}
</script>
<style scoped>
  .el-dialog-s {
  z-index: 12;
  }
  .el-dialog-ls {
  z-index: 13;
  }
    .el-dialogform-editQuota {
  width: 90%;
  margin-left: 50px;
}
</style>
