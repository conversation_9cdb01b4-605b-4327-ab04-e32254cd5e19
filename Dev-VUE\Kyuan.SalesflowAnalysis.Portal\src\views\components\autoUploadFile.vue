<template>
  <div>
    <el-upload
      ref="upload"
      class="upload-demo"
      action="customize"
      :multiple="false"
      :show-file-list="false"
      :auto-upload="true"
      :limit="1"
      :file-list="fileList"
      :http-request="uploadFile"
      :before-upload="beforeUpload"
    >
      <el-button v-if="showButton" size="small" type="primary" icon="el-icon-upload">选择文件</el-button>
      <span v-else>  {{ buttonDesc }}</span>
    </el-upload>
  </div>
</template>

<script>
import fileApi from '@/api/file'
export default {
  props: {
    controller: {
      type: String,
      default: ''
    },
    method: {
      type: String,
      default: ''
    },
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    showButton: {
      type: Boolean,
      default: true
    },
    buttonDesc: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fileList: [],
      accept: ['.xlsx'],
      baseSize: 1024,
      limitSize: 20
    }
  },
  computed: {
    importParams: function() {
      var params = new FormData()
      if (this.formData) {
        var keys = Object.keys(this.formData)
        keys.map(key => {
          params.append(key, this.formData[key])
        })
      }
      return params
    }
  },
  methods: {
    uploadFile(params) {
      const file = params.file
      this.$refs.upload.clearFiles()
      fileApi.uploadTemplate(file, this.importParams, this.controller, this.method)
        .then(result => {
          if (result.succeed) {
            this.$emit('uploadSuccess', result.data)
          }
        }).catch(res => {
          this.$emit('uploadFail', res)
        })
    },
    beforeUpload(file) {
      const ext = file.name.substring(file.name.lastIndexOf('.'))
      const isAccept = this.accept.findIndex(f => f === ext) > -1

      if (!isAccept) {
        this.$notice.message(`导入文件格式仅支持${this.accept.join(',')}`, 'error')
        return false
      }
      if (file.size === 0) {
        this.$notice.message('请不要上传空文件', 'error')
        return false
      }
      const isLessLimitSize = file.size / this.baseSize < this.baseSize * this.limitSize

      if (!isLessLimitSize) {
        this.$notice.message(`${'上传文件大小不能超过 '}${this.limitSize}${'M!'}`, 'error')
        return false
      }
      return true
    }
  }
}
</script>
