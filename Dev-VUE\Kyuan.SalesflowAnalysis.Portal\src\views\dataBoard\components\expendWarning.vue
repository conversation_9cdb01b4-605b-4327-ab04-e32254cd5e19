<template>
  <div class="panel-group">
    <el-card style="height: 380px;">
      <div slot="header" class="clearfix cardHeader">
        <div>
          <span>全年项目支出预警</span>
          <span class="waringCount">{{ showTotalCount }}</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="handleMore">详情</el-button>
        </div>
      </div>
      <el-table
        :data="topList"
        stripe
        fit
        highlight-current-row
        style="width:100%;overflow-y:auto;"
        max-height="360"
      >
        <el-table-column label="项目名称" show-overflow-tooltip header-align="center" align="left" min-width="150">
          <template slot-scope="{ row }">
            <span>{{ row.projectName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="全年预算" show-overflow-tooltip header-align="center" align="right" width="120">
          <template slot-scope="{ row }">
            <span>{{ row.budgetAmount | toMoney }}</span>
          </template>
        </el-table-column>
        <el-table-column label="全年预计支出" show-overflow-tooltip header-align="center" align="right" width="120">
          <template slot-scope="{ row }">
            <span>{{ row.expectedExpenditure | toMoney }}</span>
          </template>
        </el-table-column>
        <el-table-column label="全年实际支出" show-overflow-tooltip header-align="center" align="right" width="120">
          <template slot-scope="{ row }">
            <span>{{ row.actualExpenditure | toMoney }}</span>
          </template>
        </el-table-column>
        <el-table-column label="预警" show-overflow-tooltip header-align="center" align="center" width="80">
          <template slot-scope="{ row }">
            <span>{{ row.earlyWarning }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <el-dialog append-to-body title="全年项目支出预警" width="60%" :close-on-click-modal="false" :visible="showDialog" @close="cancleDialog()">
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="8">
            <el-input
              v-model="listQuery.keyWord"
              clearable
              placeholder="项目名称"
              class="filter-item"
            />
          </el-col>
          <el-col :span="3.5">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-search"
              @click="handleFilter"
            >
              查询
            </el-button>
          </el-col>
          <el-col :span="3.5">
            <el-button
              :loading="btnExportLoading"
              type="primary"
              icon="el-icon-download"
              @click="onShowExportModal"
            >
              导出
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table
              :data="dataList"
              stripe
              fit
              border
              highlight-current-row
              style="width: 100%"
              :header-cell-class-name="'tableStyle'"
            >
              <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
              <el-table-column label="项目名称" show-overflow-tooltip header-align="center" align="left" min-width="150">
                <template slot-scope="{ row }">
                  <span>{{ row.projectName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="全年预算" show-overflow-tooltip header-align="center" align="right" width="150">
                <template slot-scope="{ row }">
                  <span>{{ row.budgetAmount | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column label="全年预计支出" show-overflow-tooltip header-align="center" align="right" width="150">
                <template slot-scope="{ row }">
                  <span>{{ row.expectedExpenditure | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column label="全年实际支出" show-overflow-tooltip header-align="center" align="right" width="130">
                <template slot-scope="{ row }">
                  <span>{{ row.actualExpenditure | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column label="预警" show-overflow-tooltip header-align="center" align="right" width="80">
                <template slot-scope="{ row }">
                  <span>{{ row.earlyWarning }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col v-if="totalCount > 0" class="el-colRight">
            <pagination v-show="totalCount > 0" :total="totalCount" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="search" />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleDialog()">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出产品别名"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>
<script>

import Pagination from '@/components/Pagination'
import dashboardService from '@/api/dashboard'
import CustomExport from '@/components/Export/CustomExport'

export default {
  components: {
    Pagination,
    CustomExport
  },
  data() {
    return {
      showDialog: false,
      productAndSpecList: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      filter: {},
      dataList: [],
      topList: [],
      totalCount: 0, // 数据总数
      topCount: 1,
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: {},
      showTotalCount: 0
    }
  },

  methods: {
    initPage(filter, productAndSpecList, count) {
      this.filter = filter
      this.productAndSpecList = productAndSpecList
      this.topCount = count

      this.getTopList()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getTopList() {
      this.filter.pageIndex = 1
      this.filter.pageSize = this.topCount
      this.filter.isIndex = true
      dashboardService.QueryProjectExpectedExpenditureModel(this.filter)
        .then((res) => {
          this.topList = res.data.datas
          this.filter.pageSize = 10
          this.showTotalCount = res.data.recordCount
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.search()
    },
    search() {
      this.listQuery.timeRange = this.filter.timeRange
      this.listQuery.isIndex = false
      dashboardService.QueryProjectExpectedExpenditureModel(this.listQuery)
        .then((res) => {
          this.dataList = res.data.datas
          this.totalCount = res.data.recordCount
        })
    },
    handleMore() {
      this.showDialog = true
      this.listQuery.productSpecIds = this.filter.productSpecIds
      this.listQuery.departmentId = this.filter.departmentId
      this.listQuery.areaIds = this.filter.areaIds
      this.listQuery.provinceIds = this.filter.provinceIds
      this.listQuery.isSelectedAllProvince = this.filter.isSelectedAllProvince
      this.listQuery.pageIndex = 1
      this.search()
    },
    cancleDialog() {
      this.showDialog = false
      this.listQuery =
      {
        pageIndex: 1,
        pageSize: 10,
        receiverName: null
      }
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      dashboardService.GetProjectExpectedExpenditureExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      this.listQuery.checkedColumns = checkColumns
      dashboardService.ExportProjectExpectedExpenditure(this.listQuery)
        .then(result => {
          this.listQuery.checkedColumns = null
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '全年项目支出预警.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '全年项目支出预警.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    }
  }
}
</script>
<style lang="scss" scoped>
.cardHeader {
      height: 10px;
    }

.el-card ::v-deep .el-card__header {
  padding: 18px 20px;
  border-bottom: 1px solid #e6ebf5;
}

.el-card ::v-deep .el-card__body {
  padding: 0px 10px 0px 10px;
}
</style>

