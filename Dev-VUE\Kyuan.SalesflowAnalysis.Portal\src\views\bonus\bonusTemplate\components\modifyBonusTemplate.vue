<template>
  <div>
    <el-dialog
      :title="title"
      width="50%"
      :close-on-click-modal="false"
      :visible="true"
      @close="cancle()"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="tempData.tempFormModel"
        label-position="right"
        label-width="100px"
        class="el-dialogform"
      >
        <el-row :gutter="10">
          <el-col :span="span">
            <el-form-item label="部门" prop="departmentId">
              <span v-if="tempData.tempFormModel.id">{{ tempData.tempFormModel.departmentName }}</span>
              <span v-else>
                <el-select
                  v-model="tempData.tempFormModel.departmentId"
                  style="width: 100%"
                  :loading="departmentLoading"
                  class="filter-item"
                  placeholder="部门"
                  :disabled="viewModel"
                  @change="departmentChange"
                >
                  <el-option
                    v-for="item in deptList"
                    :key="item.key"
                    :label="item.value"
                    :value="item.key"
                  />
                </el-select>
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="奖励类型" prop="bonusCategoryId">
              <span v-if="tempData.tempFormModel.id">{{ tempData.tempFormModel.bonusCategoryName }}</span>
              <span v-else>
                <el-select
                  v-model="tempData.tempFormModel.bonusCategoryId"
                  style="width: 100%"
                  :loading="bonusCategoryLoading"
                  class="filter-item"
                  placeholder="奖励类型"
                  :disabled="viewModel"
                  @change="categoryChange"
                >
                  <el-option
                    v-for="item in calculateCategoryQueryList"
                    :key="item.key"
                    :label="item.value"
                    :value="item.key"
                  />
                </el-select>
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="模板名称" prop="templateName">
              <span v-if="tempData.tempFormModel.id">{{ tempData.tempFormModel.name }}</span>
              <span v-else>{{ tempData.tempFormModel.departmentName }}_{{ tempData.tempFormModel.categoryName }}_
                <el-input
                  v-model="tempData.tempFormModel.templateName"
                  placeholder="模板名称"
                  :disabled="viewModel"
                  style="width:60%;"
                />
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="最少月份" prop="minimumMonth">
              <span v-if="tempData.tempFormModel.id">{{ tempData.tempFormModel.minimumMonth }}</span>
              <span v-else>
                <el-input
                  v-model="tempData.tempFormModel.minimumMonth"
                  placeholder="最少月份"
                  :disabled="viewModel"
                  style="width: 25%;"
                />
                <div slot="tip" style="margin-left:10px; display:inline;">
                  <span class="info"><i class="el-alert__icon el-icon-info" /><span style="padding:0 8px">奖励计算时最少要选择月份数</span></span>
                </div>
              </span>
            </el-form-item>
          </el-col>
          <el-col v-if="tempData.tempFormModel.id" :span="24">
            <el-form-item label="文件列表">
              <div style="margin-top:10px">
                <el-table
                  :data="files"
                  stripe
                  border
                  fit
                  highlight-current-row
                  style="width: 100%;"
                  :default-sort="{prop: 'createTime', order: 'descending'}"
                  :header-cell-class-name="'tableStyle'"
                  :row-class-name="handleRowClass"
                >
                  <el-table-column label="文件名称" min-width="180px" align="center">
                    <template slot-scope="{ row }">
                      <a
                        style="text-decoration:underline"
                        @click="handleDownLoadTemplate(row.id,row.fileName)"
                      >{{ row.fileName }}</a>
                    </template>
                  </el-table-column>
                  <el-table-column label="上传时间" min-width="120px" align="center">
                    <template slot-scope="{ row }">
                      <span>{{ row.createTime | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="基础文件">
              <file-upload
                ref="upload"
                @getUploadFile="getUploadFileVariable"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="回传文件">
              <file-upload ref="upload" @getUploadFile="getUploadFileResult" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()"> 关闭 </el-button>
        <el-button
          :loading="btnSaveLoading"
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import MaintenanceService from '@/api/maintenance'
import FileUpload from '@/views/components/uploadFile'
import FileService from '@/api/file'
import BonusService from '@/api/bonus'

export default {
  components: { FileUpload },
  props: {
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    viewModel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      span: 24,
      rules: {
        departmentId: [
          { required: true, message: '请选择部门', trigger: 'change' }
        ],
        bonusCategoryId: [
          { required: true, message: '请选择奖励类型', trigger: 'change' }
        ],
        templateName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
        minimumMonth: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入最少月份'
          },
          {
            pattern: /((^[0-9]\d*))$/,
            message: '最少月份只能是整数',
            trigger: 'blur'
          }
        ]
      },
      deptList: [],
      tempData: { tempFormModel: { templateName: '' }},
      calculateCategoryLoading: false,
      calculateCategoryQueryList: [],
      btnSaveLoading: false,
      departmentLoading: false,
      bonusCategoryLoading: false,
      baseDataFile: null,
      formulaFile: null,
      addmethod: 'AddBonusTemplate',
      updateMethod: 'UpdateBonusTemplate',
      controller: 'Bonus',
      files: []
    }
  },
  watch: {
    id(val) {
      this.tempData.tempFormModel.id = val
    }
  },
  created() {
    this.initDepartment()
    this.initCategory()
    this.init()
  },
  methods: {
    init() {
      if (!this.id) {
        this.clear()
        this.tempData.tempFormModel.departmentName = '部门'
        this.tempData.tempFormModel.categoryName = '奖励类型'
        return
      }
      this.get(this.id)
    },
    initDepartment() {
      this.departmentLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.departmentLoading = false
          this.deptList = result
        })
        .catch(() => {
          this.departmentLoading = false
        })
    },
    initCategory() {
      this.bonusCategoryLoading = true
      BonusService.QueryBonusCategorySelect()
        .then((result) => {
          this.bonusCategoryLoading = false
          this.calculateCategoryQueryList = result
        })
        .catch((error) => {
          this.bonusCategoryLoading = false
          console.log(error)
        })
    },
    departmentChange(val) {
      this.tempData.tempFormModel.departmentName = this.deptList.find(item => item.key === val).value
    },
    categoryChange(val) {
      this.tempData.tempFormModel.categoryName = this.calculateCategoryQueryList.find(item => item.key === val).value
    },
    timeRangeChange() {
      this.$forceUpdate()
    },
    get(id) {
      this.btnSaveLoading = true
      BonusService.GetBonusTemplate({ id: id }).then(result => {
        this.tempData.tempFormModel = result.data
        this.tempData.tempFormModel.templateName = this.tempData.tempFormModel.name
        this.files = result.data.attachments
        this.btnSaveLoading = false
      }).catch(error => {
        this.btnSaveLoading = false
        console.log(error)
      })
    },
    save() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.tempData.tempFormModel.id) {
            this.addTemplate()
          } else {
            this.updateTemplate()
          }
        }
      })
    },
    addTemplate() {
      this.btnSaveLoading = true
      if (this.baseDataFile === null || this.formulaFile === null) {
        this.$notice.message('请选择文件。', 'error')
        this.btnSaveLoading = false
      } else {
        this.tempData.tempFormModel.name = this.tempData.tempFormModel.departmentName + '_' + this.tempData.tempFormModel.categoryName + '_' + this.tempData.tempFormModel.templateName
        const formData = new FormData()
        formData.append('formulaFile', this.formulaFile.raw)
        formData.append(
          'departmentId',
          this.tempData.tempFormModel.departmentId
        )
        formData.append('bonusCategoryId', this.tempData.tempFormModel.bonusCategoryId)
        formData.append('code', this.tempData.tempFormModel.code)
        formData.append('name', this.tempData.tempFormModel.name)
        formData.append(
          'minimumMonth',
          this.tempData.tempFormModel.minimumMonth
        )

        FileService.uploadTemplate(
          this.baseDataFile.raw,
          formData,
          this.controller,
          this.addmethod
        )
          .then((result) => {
            if (result.succeed) {
              this.$notice.message('保存成功', 'success')
              this.close()
            }
            this.btnSaveLoading = false
            this.baseDataFile = null
            this.formulaFile = null
            this.$refs.upload.handleRemove()
          })
          .catch((error) => {
            this.btnSaveLoading = false
            if (!error.processed) {
              this.$notice.message(error, 'error')
            }
          })
      }
    },
    updateTemplate() {
      this.btnSaveLoading = true
      if (this.baseDataFile === null && this.formulaFile === null) {
        this.$notice.message('请选择文件。', 'error')
        this.btnSaveLoading = false
      } else {
        this.$confirm('编辑模板信息会覆盖现有模板信息及关系映射?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            const formData = new FormData()
            if (this.formulaFile != null) {
              formData.append('formulaFile', this.formulaFile.raw)
            }
            formData.append(
              'departmentId',
              this.tempData.tempFormModel.departmentId
            )
            formData.append('id', this.tempData.tempFormModel.id)

            var baseDataFileRaw = null
            if (this.baseDataFile !== null) {
              baseDataFileRaw = this.baseDataFile.raw
            }
            FileService.uploadTemplate(
              baseDataFileRaw,
              formData,
              this.controller,
              this.updateMethod
            )
              .then((result) => {
                if (result.succeed) {
                  this.$notice.message('保存成功', 'success')
                  this.close()
                }
                this.btnSaveLoading = false
                this.baseDataFile = null
                this.formulaFile = null
                this.$refs.upload.handleRemove()
              })
              .catch((error) => {
                this.btnSaveLoading = false
                if (!error.processed) {
                  this.$notice.message(error, 'error')
                }
              })
          }).catch((error) => {
            this.btnSaveLoading = false
            if (!error.processed) {
              this.$notice.message(error, 'error')
            }
          })
      }
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.tempData.tempFormModel = {}
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    },
    getUploadFileVariable(val) {
      this.baseDataFile = val
    },
    getUploadFileResult(val) {
      this.formulaFile = val
    },
    handleDownLoadTemplate(templateId, templateName) {
      FileService.downloadAttachment(templateId).then(res => {
        const fileDownload = require('js-file-download')
        var filename = templateName
        fileDownload(res.data, filename)
      })
        .catch(() => {
        })
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    }
  }
}
</script>
<style scoped>
.info {
    width: 100%;
    padding: 8px 16px;
    margin: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
    opacity: 1;
    -webkit-transition: opacity .2s;
    transition: opacity .2s;
    background-color: #f4f4f5;
    color: #909399;
}
</style>
