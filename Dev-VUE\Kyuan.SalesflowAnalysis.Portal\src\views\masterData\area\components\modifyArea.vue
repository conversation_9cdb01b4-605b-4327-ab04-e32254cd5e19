<template>
  <div>
    <el-dialog :title="title" :close-on-click-modal="false" width="70%" :visible="true" @close="cancle()">
      <el-form ref="dataForm" :rules="rules" :model="areaModel" label-position="right" label-width="130px" class="el-dialogform">
        <el-row type="flex" class="filter-container">
          <el-col :span="12">
            <el-form-item label="部门" prop="departmentId">
              <el-select
                v-model="areaModel.departmentId"
                value-key="value"
                class="filter-item"
                placeholder="部门"
                clearable
              >
                <el-option
                  v-for="item in departments"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="大区名称" prop="name">
              <el-input v-model="areaModel.name" clearable placeholder="请输入大区名称" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="选择省份" prop="provinces">
              <el-checkbox v-model="checkAllProvince" :indeterminate="isIndeterminateProvince" @change="handleCheckAllProvinceChange">全选</el-checkbox>
              <el-checkbox-group v-model="checkedProvinces" @change="handleCheckedProvinceChange">
                <el-checkbox v-for="item in provinces" :key="item.id" :label="item.id">{{ item.nameCn }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          :loading="btnSaveLoading"
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>

import MaintenanceService from '@/api/maintenance'
import MasterDataService from '@/api/masterData'

export default {
  name: '',
  components: {
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      rules: {
        departmentId: [
          { required: true, message: '请选择部门', trigger: 'change' }
        ],
        name: [
          { required: true, message: '请输入大区名称', trigger: 'blur' },
          { max: 128, message: '厂商名称不允许超过128个字符', trigger: 'blur' }
        ]
      },
      areaModel: {
      },
      provinces: [],
      departments: [],
      btnSaveLoading: false,
      checkedProvinces: [],
      isIndeterminateProvince: false,
      checkAllProvince: []
    }
  },
  watch: {
    id(val) {
      this.areaModel.id = val
    }
  },
  mounted() {
  },
  created() {
    this.initDept()
    this.init()
    this.getProvinces()
  },
  methods: {
    init() {
      if (!this.id) {
        this.clear()
        return
      }
    },
    initDept() {
      MaintenanceService.QueryDept()
        .then((result) => {
          this.departments = result
        })
        .catch(() => {

        })
    },
    getProvinces() {
      MasterDataService.GetAllProvince().then(result => {
        if (result) {
          this.provinces = result.data
          if (this.id != null) {
            this.get(this.id)
          }
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    get(id) {
      this.btnSaveLoading = true
      MaintenanceService.GetArea({ id: id }).then(result => {
        this.btnSaveLoading = false
        this.areaModel = result.data
        this.checkedProvinces = result.data.provinceIds
        const checkedCount = this.checkedProvinces.length
        this.checkAllProvince = checkedCount === this.provinces.length
        this.isIndeterminateProvince = checkedCount > 0 && checkedCount < this.provinces.length
      }).catch(error => {
        this.btnSaveLoading = false
        console.log(error)
      })
    },
    handleCheckAllProvinceChange(val) {
      const provinceList = []
      this.provinces.forEach(e => {
        provinceList.push(e.id)
      })

      this.checkedProvinces = val ? provinceList : []
      this.isIndeterminateProvince = false
    },
    handleCheckedProvinceChange(value) {
      var provinceList = []
      value.forEach(e => {
        const pro = {
          id: e
        }
        provinceList.push(pro)
      })
      const checkedCount = value.length
      this.checkAllProvince = checkedCount === this.provinces.length
      this.isIndeterminateProvince = checkedCount > 0 && checkedCount < this.provinces.length
    },
    save() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.checkedProvinces.length === 0) {
            this.$notice.message('请选择省份', 'error')
          } else {
            this.areaModel.provinceIds = this.checkedProvinces
            if (!this.areaModel.id) {
              this.addNew()
            } else {
              this.update()
            }
          }
        }
      })
    },
    addNew() {
      this.btnSaveLoading = true
      MaintenanceService.AddArea(this.areaModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.areaModel = result.data
          this.$notice.message('新增成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('新增失败。', 'error')
          }
        })
    },
    update() {
      this.btnSaveLoading = true
      MaintenanceService.UpdateArea(this.areaModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.areaModel = result.data
          this.$notice.message('修改成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('修改失败。', 'error')
          }
        })
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    }
  }

}
</script>
