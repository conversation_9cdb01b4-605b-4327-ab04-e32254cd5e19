<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filter-container">
        <el-col :span="4">
          <el-input
            v-model="listQuery.requestFormRebateAgreementName"
            clearable
            placeholder="协议名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProductAndSpec"
            v-model="manufacturerProductAndSpecId"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            placeholder="厂商 / 产品 / 规格"
            style="width:100%"
            clearable
            class="filter-item"
            :props="{ multiple: true, checkStrictly: false ,expandTrigger: 'hover', emitPath: true }"
            @change="handleProductChange"
          />
        </el-col>
        <el-col :span="4">
          <el-cascader
            ref="refProvinceCity"
            v-model="listQuery.provinceCityId"
            :loading="provinceCityLoading"
            :options="provinceCityList"
            :props="{ checkStrictly: true ,expandTrigger: 'hover'}"
            placeholder="省份 / 城市"
            clearable
            style="width:100%"
            @change="provinceCitChange"
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="listQuery.rebateReceiverName"
            clearable
            placeholder="返利接收方"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="listQuery.rebateAgreementStatus"
            :loading="requestFormStatusLoading"
            class="filter-item"
            placeholder="状态"
            clearable
          >
            <el-option
              v-for="item in requestFormStatusList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
          >
            新增
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="rebateAgreementList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              fixed="left"
              label="申请单号"
              sortable="custom"
              min-width="120px"
              header-align="center"
              align="left"
              prop="RequestCode"
            >
              <template slot-scope="{ row }">
                <span>{{ row.requestCode }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="left"
              label="协议名称"
              sortable="custom"
              min-width="200px"
              header-align="center"
              align="left"
              prop="RequestFormRebateAgreement.Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.requestFormRebateAgreementName }}</span>
              </template>
            </el-table-column>

            <el-table-column
              label="部门"
              align="center"
              sortable="custom"
              min-width="100px"
              prop="RequestFormRebateAgreement.Department.Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.departmentName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="返利接收方"
              align="center"
              sortable="custom"
              min-width="120px"
              prop="RequestFormRebateAgreement.RebateReceiver.Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.rebateReceiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="品规"
              min-width="150px"
              align="center"
              prop="ProductSpecDesc"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productSpecDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" align="center" sortable="custom" min-width="100px" prop="EnumFormStatus">
              <template slot-scope="{ row }">
                <span>{{ row.enumFormStatus }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="120" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i class="el-icon-document eltablei" title="查看明细" @click="reviewDetail(row)" />
                <!-- <i v-if="row.enumStatus == 30" class="el-icon-circle-check eltablei" title="审批完成" @click="handleApproved(row)" /> -->
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

import RequestFormRebateService from '@/api/requestFormRebate'
import ProductService from '@/api/product'
import LocationService from '@/api/location'
import MasterDataService from '@/api/masterData'

export default {
  name: 'RequestForm',
  components: {
    Pagination
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      rebateAgreementList: [],
      requestFormStatusLoading: false,
      requestFormStatusList: [],
      provinceCityLoading: false,
      provinceCityList: [],
      manufacturerProductAndSpecId: [],
      productAndSpecList: [],
      productAndSpecLoading: false
    }
  },
  created() {
    this.initProductAndSpec()
    this.initProvinceCity()
    this.initRequestFormStatus()
    this.handleFilter()
  },
  methods: {
    initProductAndSpec() {
      this.productAndSpecLoading = true
      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch(() => {
          this.productAndSpecLoading = false
        })
    },
    initProvinceCity() {
      this.provinceCityLoading = true
      LocationService.QueryProvinceCityCascader()
        .then((result) => {
          this.provinceCityList = result
          this.provinceCityLoading = false
        })
        .catch((error) => {
          this.provinceCityLoading = false
          console.log(error)
        })
    },
    initRequestFormStatus() {
      var param = {
        enumType: 'FormStatus'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.requestFormStatusList = result.data.datas
          this.$forceupdate()
        })
        .catch(() => {
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      if (this.manufacturerProductAndSpecId) {
        this.listQuery.ProductSpecIds = this.manufacturerProductAndSpecId.map(element => element[2])
      }

      this.listLoading = true
      this.listQuery.quotaType = 1
      RequestFormRebateService.QueryRequestFormRebateAgreement(this.listQuery).then(res => {
        this.listLoading = false
        this.rebateAgreementList = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(() => {
        this.listLoading = true
      })
    },
    selectUpdate() {
      this.$forceUpdate()
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    reviewDetail(row) {
      this.$refs.refAgreementView.initPage(row.id)
    },
    provinceCitChange() {
      if (this.listQuery.provinceCityId !== undefined && this.listQuery.provinceCityId !== null && this.listQuery.provinceCityId.length > 0) {
        if (this.listQuery.provinceCityId.length > 1) {
          this.listQuery.cityId = this.listQuery.provinceCityId[1]
        } else {
          this.listQuery.cityId = null
        }
      } else {
        this.listQuery.cityId = null
      }
      this.$refs.refProvinceCity.dropDownVisible = false
    },
    handleProductChange() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    }
  }
}
</script>
<style scoped>
.flexwarp {
    display: flex;
    flex-wrap: wrap;
}

</style>

