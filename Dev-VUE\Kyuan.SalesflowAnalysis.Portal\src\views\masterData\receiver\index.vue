<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filter-container">
        <el-col :span="4">
          <el-cascader
            ref="refProvinceCityCounty"
            v-model="listQuery.provinceCityCountyId"
            :loading="provinceCityLoading"
            :options="provinceCityCountyList"
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            placeholder="省份 / 城市 / 区县"
            clearable
            class="filter-item"
            @change="closeProvinceCityCountyCascader"
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="listQuery.receiverName"
            clearable
            placeholder="收货方名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <!-- <el-col :span="4">
          <el-input
            v-model="listQuery.sapCode"
            clearable
            placeholder="SAPCode"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col> -->
        <el-col :span="4">
          <el-input
            v-model="listQuery.code"
            clearable
            placeholder="Code"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="listQuery.enumDistributorTypeItem"
            value-key="value"
            :loading="enumDistributorTypeLoading"
            class="filter-item"
            placeholder="经销商类型"
            clearable
          >
            <el-option
              v-for="item in enumDistributorTypeList"
              :key="item.value"
              :label="item.desc"
              :value="item"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-cascader
            ref="refReceiverTypeLevel"
            v-model="receiverTypeLevelIds"
            :loading="receiverTypeLevelLoading"
            :options="receiverTypeLevelList"
            placeholder="收货方一级类型 / 二级类型 / 三级类型"
            style="width:100%"
            clearable
            class="filter-item"
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            @change="handleReceiverTypeChange"
          />
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="listQuery.receiverFlag"
            :loading="enumReceiverFlagLoading"
            class="filter-item"
            placeholder="是否互联网医院"
            clearable
          >
            <el-option
              v-for="item in enumReceiverFlagList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="listQuery.isStopped"
            class="filter-item"
            placeholder="是否停用"
            clearable
          >
            <el-option
              v-for="item in receiverStoppedList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button v-if="$isPermitted($store.getters.user, 'Receiver_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Receiver_Button_BatchImport')"
            type="primary"
            icon="el-icon-upload2"
            @click="handleImport"
          >
            批量导入
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Receiver_Button_Add')"
            type="primary"
            icon="el-icon-plus"
            @click="handleCreate"
          >
            新增
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Receiver_Button_Export')"
            :loading="btnExportLoading"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              label="收货方名称"
              sortable="custom"
              min-width="200px"
              header-align="center"
              align="left"
              prop="Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="Code" align="center" sortable="custom" min-width="130px" prop="Code">
              <template slot-scope="{ row }">
                <span>{{ row.code }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column label="SAPCode" align="center" sortable="custom" min-width="100px" prop="SAPCode">
              <template slot-scope="{ row }">
                <span>{{ row.sapCode }}</span>
              </template>
            </el-table-column> -->
            <el-table-column label="负责人" align="center" sortable="custom" min-width="100px" prop="Employee.DisplayName">
              <template slot-scope="{ row }">
                <span>{{ row.employeeDisplayName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="省份" align="center" sortable="custom" min-width="100px" prop="Province.NameCn">
              <template slot-scope="{ row }">
                <span>{{ row.provinceNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column label="城市" align="center" sortable="custom" min-width="100px" prop="City.NameCn">
              <template slot-scope="{ row }">
                <span>{{ row.cityNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column label="区县" align="center" sortable="custom" min-width="100px" prop="County.Name">
              <template slot-scope="{ row }">
                <span>{{ row.countyName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="经销商类型" align="center" sortable="custom" min-width="110px" prop="EnumDistributorType">
              <template slot-scope="{ row }">
                <span>{{ row.enumDistributorTypeDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="收货方一级类型" align="center" sortable="custom" min-width="130px" prop="ReceiverTypeLevelOne.Name">
              <template slot-scope="{ row }">
                <span>{{ row.receiverTypeLevelOneName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="收货方二级类型" align="center" sortable="custom" min-width="130px" prop="ReceiverTypeLevelTwo.Name">
              <template slot-scope="{ row }">
                <span>{{ row.receiverTypeLevelTwoName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="收货方三级类型" align="center" sortable="custom" min-width="130px" prop="ReceiverTypeLevelThree.Name">
              <template slot-scope="{ row }">
                <span>{{ row.receiverTypeLevelThreeName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="上级单位" align="left" header-align="center" min-width="200px">
              <template slot-scope="{ row }">
                <span>{{ row.parentName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="曾用名" align="left" header-align="center" min-width="200px">
              <template slot-scope="{ row }">
                <span>{{ row.receiverFormers }}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否互联网医院" align="center" sortable="custom" min-width="150px" prop="EnumFlags">
              <template slot-scope="{ row }">
                <span>{{ row.enumFlagsDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="地址" align="left" header-align="center" sortable="custom" min-width="280px" prop="Address">
              <template slot-scope="{ row }">
                <span>{{ row.address }}</span>
              </template>
            </el-table-column>
            <el-table-column label="机构等级" align="center" header-align="center" sortable="custom" min-width="120px" prop="GradeLevel">
              <template slot-scope="{ row }">
                <span>{{ row.gradeLevel }}</span>
              </template>
            </el-table-column>
            <el-table-column label="机构经济类型" align="center" header-align="center" sortable="custom" min-width="120px" prop="EconomicType">
              <template slot-scope="{ row }">
                <span>{{ row.economicType }}</span>
              </template>
            </el-table-column>
            <el-table-column label="统一信用代码" align="center" header-align="center" sortable="custom" min-width="120px" prop="TaxRegistrationNo">
              <template slot-scope="{ row }">
                <span>{{ row.taxRegistrationNo }}</span>
              </template>
            </el-table-column>
            <el-table-column label="工商注册号" align="center" header-align="center" sortable="custom" min-width="120px" prop="RegistrationNo">
              <template slot-scope="{ row }">
                <span>{{ row.registrationNo }}</span>
              </template>
            </el-table-column>
            <el-table-column label="生效日期" align="center" sortable="custom" min-width="120px" prop="EffectiveDate">
              <template slot-scope="{ row }">
                <span v-if="row.effectiveDate">{{ row.effectiveDate |parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否停用" align="center" sortable="custom" min-width="100px" prop="IsStopped">
              <template slot-scope="{ row }">
                <span>{{ row.isStoppedDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="停用日期" align="center" sortable="custom" min-width="120px" prop="StopDate">
              <template slot-scope="{ row }">
                <span v-if="row.stopDate">{{ row.stopDate |parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="创建人" align="center" sortable="custom" min-width="100px" prop="Creator">
              <template slot-scope="{ row }">
                <span>{{ row.creator }}</span>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" sortable="custom" min-width="120px" prop="CreateTime">
              <template slot-scope="{ row }">
                <span>{{ row.createTime |parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="90" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i v-if="row.isStopped && $isPermitted($store.getters.user, 'Receiver_Button_Stop')" class="el-icon-circle-check eltablei" title="启用" @click="handleEnable(row)" />
                <i v-if="!row.isStopped && $isPermitted($store.getters.user, 'Receiver_Button_Stop')" class="el-icon-circle-close eltablei" title="停用" @click="handleDisable(row)" />
                <i v-if="!row.isStopped && $isPermitted($store.getters.user, 'Receiver_Button_Edit')" class="el-icon-edit-outline eltablei" title="更新" @click="handleUpdate(row)" />
                <i v-if="!row.isStopped && $isPermitted($store.getters.user, 'Receiver_Button_Del')" class="el-icon-delete eltablei" title="删除" @click="handleDelete(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      title="导入收货方"
      :close-on-click-modal="false"
      :visible="dialogImportVisible"
      width="80%"
      class="popup-search"
      @close="closeImportDialog"
    >
      <ImportReceiver ref="refImportReceiver" :show-import="dialogImportVisible" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeImportDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <modifyReceiver
      v-if="dialogEditFormVisible"
      :id="itemId"
      :title="modifyDialogTitle"
      :view-model="modifyDialogIsReadonly"
      @hidden="onHidden()"
      @refresh="onRefresh()"
    />
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出收货方"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
    <stopReceiver
      v-if="dialogDisableVisible"
      :id="itemId"
      :title="textMap['disable']"
      @hidden="onHiddenStopDialog()"
      @refresh="onCloesStopDialog()"
    />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import CustomExport from '@/components/Export/CustomExport'
import LocationService from '@/api/location'
import MasterDataService from '@/api/masterData'
import ReceiverService from '@/api/receiver'
import CRMInterfaceService from '@/api/crmInterface'
import ImportReceiver from './components/importReceiver'
import modifyReceiver from './components/modifyReceiver'
import stopReceiver from './components/stopReceiver'
import MaintenanceService from '@/api/maintenance'

export default {
  name: 'Receiver',
  components: {
    Pagination,
    ImportReceiver,
    CustomExport,
    modifyReceiver,
    stopReceiver
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      list: [],
      provinceCityLoading: false,
      provinceCityCountyList: [],
      enumDistributorTypeLoading: false,
      enumDistributorTypeList: [],
      receiverTypeLevelLoading: false,
      receiverTypeLevelList: [],
      receiverTypeLevelIds: [],
      enumReceiverFlagLoading: false,
      enumReceiverFlagList: [],
      receiverStoppedList: [
        {
          'value': true,
          'desc': '是'
        },
        {
          'value': false,
          'desc': '否'
        }
      ],
      btnExportLoading: false,
      dialogEditFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑收货方',
        create: '新增收货方',
        disable: '停用收货方'
      },
      modifyDialogTitle: '',
      modifyDialogIsReadonly: false,
      itemId: null,
      showExportModal: false,
      columnDictionary: {},
      dialogImportVisible: false,
      dialogDisableVisible: false
    }
  },
  created() {
    this.initProvinceCityCounty()
    this.initDistributorType()
    this.initReceiverTypeLevelList()
    this.initReceiverFlag()
    this.handleFilter()
  },
  methods: {
    initProvinceCityCounty() {
      this.provinceCityLoading = true
      LocationService.QueryProvinceCityCountyCascader()
        .then((result) => {
          this.provinceCityCountyList = result
          this.provinceCityLoading = false
        })
        .catch((error) => {
          this.provinceCityLoading = false
          console.log(error)
        })
    },
    initDistributorType() {
      this.enumDistributorTypeLoading = true
      MasterDataService.GetEnumInfos({ enumType: 'DistributorType' })
        .then((result) => {
          this.enumDistributorTypeLoading = false
          this.enumDistributorTypeList = result.data.datas
        })
        .catch((error) => {
          this.enumDistributorTypeLoading = false
          console.log(error)
        })
    },
    initReceiverTypeLevelList() {
      this.receiverTypeLevelLoading = true
      MaintenanceService.QueryReceiverTypeCascader().then(res => {
        this.receiverTypeLevelList = res
        this.receiverTypeLevelLoading = false
      })
        .catch((error) => {
          this.receiverTypeLevelLoading = false
          console.log(error)
        })
    },
    initReceiverFlag() {
      this.enumReceiverFlagLoading = true
      MasterDataService.GetEnumInfos({ enumType: 'ReceiverFlag' })
        .then((result) => {
          this.enumReceiverFlagLoading = false
          this.enumReceiverFlagList = result.data.datas
        })
        .catch((error) => {
          this.enumReceiverFlagLoading = false
          console.log(error)
        })
    },
    handleReceiverTypeChange() {
      this.$refs.refReceiverTypeLevel.dropDownVisible = false
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      if (this.listQuery.provinceCityCountyId) {
        const [provinceId, cityId, countyId] = this.listQuery.provinceCityCountyId
        this.listQuery.provinceId = provinceId
        this.listQuery.cityId = cityId
        this.listQuery.countyId = countyId
      }
      if (this.productAndSpecId) {
        const [productId, specId] = this.productAndSpecId
        this.listQuery.productId = productId
        this.listQuery.productSpecId = specId
      }
      if (this.listQuery.enumDistributorTypeItem) {
        this.listQuery.distributorType = this.listQuery.enumDistributorTypeItem.value
      } else {
        if (this.listQuery.distributorType) {
          delete this.listQuery.distributorType
        }
      }
      if (this.receiverTypeLevelIds) {
        const [receiverTypeLevelOneId, receiverTypeLevelTwoId, receiverTypeLevelThreeId] = this.receiverTypeLevelIds
        this.listQuery.receiverTypeLevelOneId = receiverTypeLevelOneId
        this.listQuery.receiverTypeLevelTwoId = receiverTypeLevelTwoId
        this.listQuery.receiverTypeLevelThreeId = receiverTypeLevelThreeId
      } else {
        delete this.listQuery.receiverTypeLevelOneId
        delete this.listQuery.receiverTypeLevelTwoId
        delete this.listQuery.receiverTypeLevelThreeId
      }
      this.listLoading = true
      ReceiverService.QueryReceiver(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    handleCreate() {
      this.itemId = null
      this.modifyDialogIsReadonly = false
      this.dialogEditFormVisible = true
      this.dialogStatus = 'create'
      this.modifyDialogTitle = this.textMap[this.dialogStatus]
    },
    handleUpdate(row) {
      this.itemId = row.id
      this.modifyDialogIsReadonly = false
      this.dialogEditFormVisible = true
      this.dialogStatus = 'update'
      this.modifyDialogTitle = this.textMap[this.dialogStatus]
    },
    handleDelete(row) {
      this.$confirm('确定删除此收货方吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        ReceiverService.DeleteReceiver(row).then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('删除成功', 'success')
          }
        })
          .catch(error => {
            this.listLoading = false
            console.log(error)
          })
      }).catch(error => {
        this.listLoading = false
        if (!error.succeed) {
          this.$notice.message('取消删除', 'info')
        }
      })
    },
    onHidden() {
      this.dialogEditFormVisible = false
    },
    onRefresh() {
      this.getList()
      this.dialogEditFormVisible = false
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      ReceiverService.GetReceiverExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      if (this.listQuery.provinceCityId) {
        const [provinceId, cityId] = this.listQuery.provinceCityId
        this.listQuery.provinceId = provinceId
        this.listQuery.cityId = cityId
      }
      if (this.productAndSpecId) {
        const [productId, specId] = this.productAndSpecId
        this.listQuery.productId = productId
        this.listQuery.productSpecId = specId
      }
      if (this.listQuery.enumDistributorTypeItem) {
        this.listQuery.distributorType = this.listQuery.enumDistributorTypeItem.value
      } else {
        if (this.listQuery.distributorType) {
          delete this.listQuery.distributorType
        }
      }
      this.listQuery.checkedColumns = checkColumns
      ReceiverService.ExportReceiver(this.listQuery)
        .then(result => {
          this.listQuery.checkedColumns = null
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '收货方.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '收货方.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    handleImport() {
      this.dialogImportVisible = true
    },
    closeImportDialog() {
      this.$refs.refImportReceiver.clear()
      this.dialogImportVisible = false
      this.handleFilter()
    },
    handleDisable(row) {
      this.itemId = row.id
      this.dialogStatus = 'disable'
      this.dialogDisableVisible = true
    },
    handleEnable(row) {
      ReceiverService.EnableReceiver(row).then(result => {
        if (result.succeed) {
          this.$notice.message('启用成功', 'success')
          this.getList()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          if (!error.processed) {
            this.$notice.message('启用失败。', 'error')
          }
        })
    },
    onHiddenStopDialog() {
      this.dialogDisableVisible = false
    },
    onCloesStopDialog() {
      this.getList()
      this.dialogDisableVisible = false
    },
    closeProvinceCityCountyCascader() {
      this.$refs.refProvinceCityCounty.dropDownVisible = false
    },
    handleSync() {
      CRMInterfaceService.SyncCRMReceiver()
    }
  }
}
</script>
<style scoped>
.flexwarp {
    display: flex;
    flex-wrap: wrap;
}

</style>

