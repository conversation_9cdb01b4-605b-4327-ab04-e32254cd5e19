<template>
  <div>
    <div class="card">
      <el-card class="rounded-card leftCard cardH">
        <!-- 卡片内容 -->
        <div class="icon-wrapper">
          <i class="el-icon-data-line icon" />
        </div>
        <div style="float: right;width: 80%">
          <div class="cardNumer">
            {{ tempModel.projectEstimatedSalesAmount | toMoney }}万
          </div>
          <div class="carTitle">
            全年预计销售金额
          </div>
        </div>
      </el-card>
    </div>
    <div class="card">
      <el-card icon="el-icon-coin" class="rounded-card leftCard cardH">
        <!-- 卡片内容 -->
        <div class="icon-wrapper">
          <i class="el-icon-s-operation icon" />
        </div>
        <div style="float: right;width: 80%">
          <div class="cardNumer">
            {{ tempModel.currentQuotaAmount | toMoney }}万
          </div>
          <div class="carTitle">
            当前周期预计销售额
          </div>
        </div>

      </el-card>
    </div>
    <div class="card">
      <el-card class="rounded-card leftCard cardH">
        <!-- 卡片内容 -->
        <div class="icon-wrapper">
          <i class="el-icon-s-data icon" />
        </div>
        <div style="float: right;width: 80%">
          <div class="cardNumer">
            {{ tempModel.currentSalesAmount | toMoney }}万
          </div>
          <div class="carTitle">
            当前周期实际销售金额
          </div>
        </div>
      </el-card>
    </div>
    <div class="card">
      <el-card class="rounded-card leftCard cardH">
        <!-- 卡片内容 -->
        <div class="icon-wrapper">
          <i class="el-icon-pie-chart icon" />
        </div>
        <div style="float: right;width: 80%">
          <div class="cardNumer">
            {{ tempModel.currentSalesAchievementRate | toTwoNum | toThousandFilter }}%
          </div>
          <div class="carTitle">
            当前周期达成率
          </div>
        </div>
      </el-card>
    </div>
    <div class="card">
      <el-card class="rounded-card leftCard cardH">
        <!-- 卡片内容 -->
        <div class="icon-wrapper">
          <i class="el-icon-potato-strips icon" />
        </div>
        <div style="float: right;width: 80%">
          <div class="cardNumer">
            {{ tempModel.salesAchievementRate | toTwoNum | toThousandFilter }}%
          </div>
          <div class="carTitle">
            全年达成率
          </div>
        </div>
      </el-card>
    </div>
    <div class="card">
      <el-card class="rounded-card cardH">
        <!-- 卡片内容 -->
        <div class="icon-wrapper">
          <i class="el-icon-odometer icon" />
        </div>
        <div style="float: right;width: 80%">
          <div class="cardNumer">
            {{ tempModel.salesGrowthRate | toTwoNum | toThousandFilter }}%
          </div>
          <div class="carTitle">
            当前周期同比增长率
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>
<script>
import DashboardService from '@/api/dashboard'

export default {
  data() {
    return {
      tempModel: {}
    }
  },
  methods: {
    initPage(filter) {
      DashboardService.InitDashboardSalesAmount(filter).then((result) => {
        if (result.succeed) {
          this.tempModel = result.data
        }
      }).catch(() => {
        this.deptLoading = false
      })
    }
  }
}
</script>
<style scoped>
.card {
    width: 16.66%;
    float: left;
    margin: 10px 0;
    height: 80px;
  }

  .leftCard {
    margin-right: 10px;
  }
  .cardH {
    height: 100%;
  }
  .icon-wrapper {
    top: -15;
    position: relative;
    width: 20%; /* 或者你需要的尺寸 */
    height: 40px; /* 或者你需要的尺寸 */
    border-radius: 50%; /* 使得div成为圆形 */
    background-color: #c4d9f2; /* 圆的背景颜色 */
    display: flex;
    align-items: center;
    justify-content: center;
    float: left;
}

.icon {
  font-size: 28px; /* 或者你需要的图标大小 */
  color: #516e92; /* 图标的颜色 */
}

.cardNumer {
    width: 100%;
    height:50%;
    font-size: 20px;
    font-weight: bold;
    color: #516e92;
    text-align: center;
}
.carTitle{
    width: 100%;
    height:50%;
    font-size: 13px;
    text-align: center;
}
</style>
