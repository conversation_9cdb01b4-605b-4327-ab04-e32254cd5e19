import HttpApi from './libs/api.request'

const controller = 'Project'

const api = new HttpApi(controller)

export default {
  QueryRebateProject(params) {
    return api.get('QueryRebateProject', params)
  },
  AddRebateProject(params) {
    return api.post('AddRebateProject', params)
  },
  UpdateRebateProject(params) {
    return api.post('UpdateRebateProject', params)
  },
  DeleteRebateProject(params) {
    return api.post('DeleteRebateProject', params)
  },
  GetRebateProject(params) {
    return api.get('GetRebateProject', params)
  },
  GetProjectExportColumn(params) {
    return api.get('GetProjectExportColumn', params)
  },
  ExportProject(params) {
    return api.post('ExportProject', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  GetAreaByDepartmentAndProvince(params) {
    return api.get('GetAreaByDepartmentAndProvince', params)
  },
  GetStation(params) {
    return api.get('GetStation', params)
  }
}
