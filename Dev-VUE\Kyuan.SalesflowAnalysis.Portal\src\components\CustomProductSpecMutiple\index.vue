<template>
  <div style="width: 100%" class="ivuInput">
    <el-popover
      placement="bottom-start"
      trigger="hover"
      @hide="chooseEnd"
    >
      <div class="tree-slot-content">
        <el-tree
          ref="tree"
          :data="productSpecs"
          show-checkbox
          node-key="value"
          highlight-current
          check-on-click-node
          default-expand-all
          :expand-on-click-node="false"
          :render-content="renderContent"
          @node-expand="handleExpand"
          @check="checkChange"
        />
      </div>
      <el-input
        slot="reference"
        v-model="checkProductSpecNames"
        readonly
        placeholder="厂商 / 产品 / 规格"
      />
    </el-popover>
  </div>
</template>

<script>
import ProductService from '@/api/product'

export default {
  data() {
    return {
      checkProductSpecNames: '',
      productSpecs: [],
      checkedNodes: [],
      isCheckChanged: false,
      localAllChecked: true,
      productSpecCount: 0
    }
  },
  created() {
    this.initProductSpec()
  },
  methods: {
    initProductSpec() {
      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then(result => {
          this.productSpecs = result
          this.setTreeNodeAllChecked()
        })
        .catch(() => {
        })
    },
    setTreeNodeAllChecked() {
      const allKeys = this.productSpecs.map(item => item.value)
      this.$refs.tree.setCheckedKeys(allKeys)
      let checkedSpecs = []
      for (var i = 0; i < this.productSpecs.length; i++) {
        for (var j = 0; j < this.productSpecs[i].children.length; j++) {
          checkedSpecs = checkedSpecs.concat(this.productSpecs[i].children[j].children)
        }
      }
      this.checkProductSpecNames = checkedSpecs.map(item => { return item.remark }).join(',')
      const checkNodesID = checkedSpecs.map(item => { return item.value })
      this.productSpecCount = checkedSpecs.length
      this.$emit('input', checkNodesID, this.localAllChecked)
      this.handleExpand()
    },
    checkChange(currentNode, checkParams) {
      this.isCheckChanged = true
      this.checkedNodes = checkParams.checkedNodes.filter(item => {
        return !item.children
      })
      this.checkProductSpecNames = this.checkedNodes.map(item => { return item.remark }).join(',')
    },
    chooseEnd() {
      if (this.isCheckChanged) {
        const checkNodesID = this.checkedNodes.map(item => { return item.value })
        if (checkNodesID.length !== this.productSpecCount) {
          this.localAllChecked = false
        } else {
          this.localAllChecked = true
        }
        this.$emit('input', checkNodesID, this.localAllChecked)
      }
    },
    // 一行显示多条数据
    handleExpand() {
    // 节点被展开时触发的事件
    // 因为该函数执行在renderContent函数之前，所以得加this.$nextTick()
      this.$nextTick(() => {
        this.changeCss()
      })
    },
    // 改变tree节点样式
    changeCss() {
      var levelName = document.getElementsByClassName('foo') // levelname是上面的最底层节点的名字
      for (var i = 0; i < levelName.length; i++) {
      // cssFloat 兼容 ie6-8  styleFloat 兼容ie9及标准浏览器
      // @ts-ignore
        levelName[i].parentNode.style.cssFloat = 'left' // 最底层的节点，包括多选框和名字都让他左浮动
        // @ts-ignore
        levelName[i].parentNode.style.styleFloat = 'left'
        // @ts-ignore
        levelName[i].parentNode.onmouseover = function() {
        // @ts-ignore
          this.style.backgroundColor = '#fff'
        }
      }
    },
    // 一行显示多条
    // @ts-ignore
    renderContent(h, { node, data, store }) {
    // console.log('信息',h,node,data,store)
      let classname = ''
      // perms这个是后台数据区分普通tree节点和横向tree节点的标志  各位要根据实际情况做相对应的修改
      // 由于项目中有三级菜单也有四级级菜单，就要在此做出判断
      if (node.level === 4) {
        classname = 'foo'
      }
      if (node.level === 3 && node.childNodes.length === 0) {
        classname = 'foo'
      }
      if (node.level === 2 && node.childNodes.length === 0) {
        classname = 'foo'
      }
      return h(
        'p',
        {
          class: classname
        },
        node.label
      )
    }
  }
}
</script>

<style lang="scss" scoped>
  .tree-slot-content > .el-tree > .el-tree-node .el-tree-node__expand-icon {
  display: none !important;
}

.tree-slot-content
  > .el-tree
  > .el-tree-node
  > .el-tree-node__children
  > .el-tree-node
  > .el-tree-node__children {
  display: flex !important;
  flex-direction: row !important;
  justify-content: start;
  flex-wrap: wrap;
}
.tree-slot-content {
  min-width: 500px;
  //min-height: 100px;
  height: 300px;
  overflow-y: scroll;
  overflow-x: hidden;
}
.tree-slot-content
  > .el-tree
  > .el-tree-node
  > .el-tree-node__children
  > .el-tree-node {
  border-bottom: 1px solid #ccc;
}
.tree-slot-content > .el-tree > .el-tree-node > .el-tree-node__content {
  font-weight: bolder;
}
.tree-slot-content
  > .el-tree
  > .el-tree-node
  > .el-tree-node__children
  > .el-tree-node
  > .el-tree-node__content {
  font-weight: 600;
}
  </style>
