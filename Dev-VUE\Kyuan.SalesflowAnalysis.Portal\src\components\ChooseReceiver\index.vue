<template>
  <div>
    <el-row>
      <el-col>
        <el-input v-if="showType === 'input' && chooseType === 'single'" ref="refInput" :value="inputValue" :placeholder="placeholder" prefix-icon="el-icon-search" @focus="handleShowSelect" @clear="clearSelect" />
        <i
          v-else
          class="el-icon-user-solid"
          style="
            font-size: 1.3rem;
            cursor: pointer;
            line-height: 170%;
            color: #304156;
          "
          @click="handleShowSelect"
        />
      </el-col>
    </el-row>

    <el-dialog append-to-body :title="title" width="80%" :close-on-click-modal="false" :visible="showDialog" @close="cancleDialog()">
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filiter-container">
          <el-cascader
            ref="refProvinceCity"
            v-model="listQuery.provinceCityId"
            :options="provinceCityList"
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            placeholder="省份城市"
            clearable
            @change="provinceCitChange"
          />
          </el-col>
          <el-col :span="span">
            <el-input
              v-model="listQuery.receiverName"
              clearable
              placeholder="经销商名称"
              class="filter-item"
              @keyup.enter.native="handleFilter"
            />
          </el-col>
          <el-col :span="span">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-search"
              @click="handleFilter"
            >
              查询
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="dataList-container">
        <el-row :class="showSelectAll && chooseType === 'multiple' ? '' : 'components-dialog'">
          <el-col :span="24">
            <el-table
              ref="refTable"
              :data="dataList"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%"
              :default-sort="{ prop: 'JobNo', order: 'ascending' }"
              :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass"
              @select-all="handleSelectionAll"
              @select="handleSelectionRow"
              @sort-change="sortChange"
            >
              <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
              <el-table-column sortable="custom" prop="Name" label="经销商Code" min-width="200px" align="left" header-align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.code }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="Name" label="经销商名称" min-width="200px" align="left" header-align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="Employee.DisplayName" label="负责人" min-width="80px" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.employeeDisplayName }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="Province.NameCn" label="省份" min-width="80px" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.provinceNameCn }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="City.NameCn" label="城市" min-width="80px" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.cityNameCn }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="EnumDistributorType" label="经销商类型" min-width="100px" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.enumDistributorTypeDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="ReceiverTypeLevelOne.Name" label="收货方类型" min-width="100px" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.receiverTypeLevelOneName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="曾用名" min-width="180px" align="left" header-align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.receiverFormers }}</span>
                </template>
              </el-table-column>

              <el-table-column v-if="chooseType === 'single'" fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <i style="color: #13ce66" class="el-icon-circle-check eltablei" title="选择" @click="handleSingleCheck(row)" />
                </template>
              </el-table-column>
              <el-table-column v-else type="selection" width="55" />
            </el-table>
          </el-col>
          <el-col v-if="showPagination" class="el-colRight">
            <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleDialog()">
          关闭
        </el-button>
        <el-button v-if="chooseType === 'multiple'" type="primary" icon="el-icon-check" @click="submit">
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import props from './props'
import Pagination from '@/components/Pagination'
import LocationService from '@/api/location'
import ReceiverService from '@/api/receiver'

export default {
  components: {
    Pagination
  },
  props: props,
  data() {
    return {
      span: 4,
      showDialog: false,
      showPagination: false,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      dataList: [],
      provinceCityList: [],
      displayName: this.inputValue,
      selectedData: [],
      resultData: []
    }
  },

  created() {

  },
  methods: {
    initProvinceCity() {
      LocationService.QueryProvinceCityCascader()
        .then((result) => {
          this.provinceCityList = result
        })
        .catch((error) => {
          console.log(error)
        })
    },
    handleShowSelect(e) {
      this.showDialog = true
      this.getList()
      this.initProvinceCity()
      this.$refs.refInput.blur()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList() {
      if (this.distributorTypes) { this.listQuery.distributorTypes = this.distributorTypes }
      if (this.targetReceiverId) { this.listQuery.targetReceiverId = this.targetReceiverId }
      if (this.departmentId) { this.listQuery.departmentId = this.departmentId }
      if (this.isStopped != null) {
        this.listQuery.isStopped = this.isStopped
      }
      ReceiverService.QueryReceiverList(this.listQuery).then(res => {
        this.dataList = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
        this.showPagination = this.total > this.listQuery.pageSize
        // this.initSelect()
      }).catch(res => {})
    },
    initSelect() {
      // this.$nextTick(() => {
      //   if (this.resultData.length > 0) {
      //     this.resultData.map(item => {
      //       if (this.dataList.some(s => { return (item.id && item.id === s.id) || (item.memberId && item.memberId === s.memberId) })) {
      //         var scitem = this.dataList.find(f => { return (item.id && item.id === f.id) || (item.memberId && item.memberId === f.memberId) })

      //         this.$refs.refTable.toggleRowSelection(scitem, true)
      //       } else {
      //         this.$refs.refTable.toggleRowSelection(item, false)
      //       }
      //     })
      //   }
      // })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    provinceCitChange() {
      if (this.listQuery.provinceCityId !== undefined && this.listQuery.provinceCityId !== null && this.listQuery.provinceCityId.length > 0) {
        this.listQuery.provinceId = this.listQuery.provinceCityId[0]
        if (this.listQuery.provinceCityId.length > 1) {
          this.listQuery.cityId = this.listQuery.provinceCityId[1]
        } else {
          this.listQuery.cityId = null
        }
      } else {
        this.listQuery.provinceId = null
        this.listQuery.cityId = null
      }
      this.$refs.refProvinceCity.dropDownVisible = false
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    // 单选
    handleSingleCheck(row) {
      this.displayName = row[this.showColumn]
      // this.$emit('update', this.displayName)
      this.$emit('change', row)
      this.cancleDialog()
    },
    // 多选(全选)
    handleSelectionAll(selections) {
      if (selections.length > 0) {
        selections.map(sel => {
          if (!this.resultData.some(res => { return res.id === sel.id })) {
            this.resultData.push(sel)
          }
        })
      } else {
        this.dataList.map(sel => {
          const index = this.resultData.findIndex((item) => {
            return item.id === sel.id
          })
          this.resultData.splice(index, 1)
        })
      }
    },
    // 多选(单选)
    handleSelectionRow(selection, row) {
      if (selection.some(sel => { return sel.id === row.id })) {
        if (!this.resultData.some(res => { return res.id === row.id })) {
          this.resultData.push(row)
        }
      } else {
        const index = this.resultData.findIndex((item) => {
          return item.id === row.id
        })
        this.resultData.splice(index, 1)
      }
    },
    submit() {
      if (this.resultData.length > 0) {
        this.$emit('change', this.resultData)
        this.cancleDialog()
      } else {
        this.$message.error('至少选择一个')
      }
    },
    clearSelect() {
    },
    cancleDialog() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
      this.showDialog = false
      this.resultData = []
      this.dataList = []
    }
  }

}
</script>

<style scoped>
::v-deep .el-input__validateIcon {
  display: none;
}
.components-dialog ::v-deep th .el-checkbox {
  display: none !important;
}
</style>
