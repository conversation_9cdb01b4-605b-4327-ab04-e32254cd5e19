<template>
  <div class="app-container" style="text-align:center;background-color: #f5f6f8;">
    <div style="width:50%; display:inline-block">
      <el-row :gutter="20">
        <el-col :span="24" :xs="24">
          <el-card class="box-card" shadow="never">
            <el-form
              ref="datafrom"
              :rules="pwdRules"
              :model="password"
              label-position="right"
              label-width="120px"
              style="margin-right:20px;"
            >
              <el-form-item label="原密码" prop="oldPwd">
                <el-input v-model="password.oldPwd" maxlength="30" show-password placeholder="请输入原密码" />
              </el-form-item>
              <el-form-item label="新密码" prop="newPwd">
                <el-input v-model="password.newPwd" maxlength="30" show-password placeholder="请输入新密码" />
              </el-form-item>
              <el-form-item label="重复新密码" prop="repeatNewPwd">
                <el-input v-model="password.repeatNewPwd" maxlength="30" show-password placeholder="请输入重复新密码" />
              </el-form-item>

              <el-form-item style="padding-top:15px;">
                <el-button type="primary" icon="el-icon-check" @click="btnSave()">
                  保存
                </el-button>

              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
    </div>

  </div>
</template>
<script>

import homeService from '@/api/home'
import store from '@/store'

export default {
  name: 'EditPwd',
  data() {
    return {
      employee: {
        id: '',
        displayName: '',
        uid: '',
        email: '',
        mobile: '',
        enumStatus: ''

      },
      pwdRules: {
        oldPwd: [
          { required: true, message: '请输入原密码', trigger: 'blur' }
        ],
        newPwd: [
          { required: true, message: '请输入新密码', trigger: 'blur' }
        ],
        repeatNewPwd: [
          { required: true, message: '请输入重复新密码', trigger: 'blur' }
        ]
      },
      password: {
        oldPwd: '',
        newPwd: '',
        repeatNewPwd: ''
      }
    }
  },
  created() {
    // this.getUserProfile()
  },
  methods: {

    getUserProfile() {
      this.$nextTick(() => {
        this.$refs['datafrom'].clearValidate()
      })

      homeService.getCurrentUser().then(res => {
        this.employee = res.data
      })
    },

    btnSave() {
      this.$refs['datafrom'].validate((valid) => {
        if (valid) {
          var regex = new RegExp('(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,30}')
          if (this.password.oldPwd.trim() === this.password.newPwd.trim()) {
            this.$alert('原密码不能与新密码相同', '提示', {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确定',
              type: 'error'
            })
          } else if (this.password.newPwd.trim() !== this.password.repeatNewPwd.trim()) {
            this.$alert('重复新密码必须与新密码一致', '提示', {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确定',
              type: 'error'
            })
          } else if (!regex.test(this.password.newPwd.trim())) {
            this.$alert('您的新密码复杂度太低（密码中必须包含字母、数字、特殊字符，至少8个字符，最多30个字符），请修改新密码！', '提示', {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确定',
              type: 'error'
            })
          } else {
            var param = {
              oldPwd: this.password.oldPwd,
              newPwd: this.password.newPwd,
              rptNewPwd: this.password.repeatNewPwd
            }

            homeService.updateUserPwd(param).then(res => {
              if (res.succeed) {
                const router = this.$router
                const cfg = this.$cfg
                this.dialogPwdVisible = false
                this.$message({
                  showClose: true,
                  message: '密码修改成功',
                  type: 'success',
                  duration: 1500,
                  onClose: function() {
                    store.dispatch('user/resetToken').then(() => {
                      router.push({ name: cfg.loginName })
                    })
                  }
                })
              } else {
                this.$alert(res.messages.toString().replace(/,/g, '<br/>'), '提示', {
                  dangerouslyUseHTMLString: true,
                  confirmButtonText: '确定',
                  type: 'error'
                })
              }
            })
          }
        }
      })
    }
  }
}
</script>
<style>

#left-card{
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif !important;
  font-size: 14px;
  color: #606266;
}

#left-card .el-card__body{
  padding-top: 5px !important;
}
#left-card .el-card__header{
  padding-top: 8px !important;
  padding-bottom: 8px !important;
}

.title{
  line-height: 30px !important;
  padding-top: 5px;
  font-weight: 700;
}
#left-card .content{
  line-height: 20px !important;
  padding-left: 20px;
  font-size: 15px;
}
</style>
