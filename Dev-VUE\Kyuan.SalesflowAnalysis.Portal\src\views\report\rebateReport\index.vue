<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="span">
          <el-date-picker
            v-model="listQuery.year"
            class="filter-item"
            placeholder="年"
            type="year"
            :clearable="false"
            format="yyyy"
            value-format="yyyy"
            @change="timeChange"
            @blur="timeChange"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.projectName"
            clearable
            placeholder="项目名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.departmentId"
            :loading="deptLoading"
            class="filter-item"
            placeholder="部门"
            clearable
          >
            <el-option
              v-for="item in deptList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProductAndSpec"
            v-model="manufacturerProductAndSpecId"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            placeholder="厂商 / 产品 / 规格"
            style="width:100%"
            clearable
            class="filter-item"
            :props="{ checkStrictly: false ,expandTrigger: 'hover', emitPath: true }"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.receiverName"
            clearable
            placeholder="客户名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Rebate_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Rebate_Button_Export')"
            :loading="btnExportLoading"
            class="filter-item-button"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              label="项目名称"
              min-width="200px"
              header-align="center"
              align="left"
              prop="ProjectName"
            >
              <template slot-scope="{ row }">
                <span>{{ row.projectName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="部门" min-width="100px" prop="DepartmentName" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.departmentName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="产品/规格" min-width="120px" prop="spec" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.productName }}({{ row.spec }})</span>
              </template>
            </el-table-column>
            <el-table-column label="客户编码" min-width="100px" prop="code" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.code }}</span>
              </template>
            </el-table-column>
            <el-table-column label="客户名称" min-width="150px" prop="ReceiverName" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.receiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="项目返利单价" min-width="100px" prop="RebatePrice" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.rebatePrice }}</span>
              </template>
            </el-table-column>
            <el-table-column label="1月" min-width="80px" prop="January" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.january }}</span>
              </template>
            </el-table-column>
            <el-table-column label="2月" min-width="80px" prop="February" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.february }}</span>
              </template>
            </el-table-column>
            <el-table-column label="3月" min-width="80px" prop="March" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.march }}</span>
              </template>
            </el-table-column>
            <el-table-column label="4月" min-width="80px" prop="April" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.april }}</span>
              </template>
            </el-table-column>
            <el-table-column label="5月" min-width="80px" prop="May" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.may }}</span>
              </template>
            </el-table-column>
            <el-table-column label="6月" min-width="80px" prop="June" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.june }}</span>
              </template>
            </el-table-column>
            <el-table-column label="7月" min-width="80px" prop="July" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.july }}</span>
              </template>
            </el-table-column>
            <el-table-column label="8月" min-width="80px" prop="August" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.august }}</span>
              </template>
            </el-table-column>
            <el-table-column label="9月" min-width="80px" prop="September" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.september }}</span>
              </template>
            </el-table-column>
            <el-table-column label="10月" min-width="80px" prop="October" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.october }}</span>
              </template>
            </el-table-column>
            <el-table-column label="11月" min-width="80px" prop="November" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.november }}</span>
              </template>
            </el-table-column>
            <el-table-column label="12月" min-width="80px" prop="December" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.december }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出报表"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import CustomExport from '@/components/Export/CustomExport'
import ProductService from '@/api/product'
import ReportService from '@/api/report'
import MaintenanceService from '@/api/maintenance'

export default {
  components: {
    Pagination,
    CustomExport
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        year: new Date().getFullYear().toString()
      },
      listLoading: false,
      list: [],
      productAndSpecLoading: false,
      productAndSpecList: null,
      productAndSpecId: [],
      manufacturerProductAndSpecId: [],
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: {},
      deptList: [],
      deptLoading: false
    }
  },
  created() {
    this.initProductAndSpec()
    this.initDept()
    this.handleFilter()
  },
  methods: {
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch(() => {
          this.deptLoading = false
        })
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch(() => {
          this.productAndSpecLoading = false
        })
    },
    timeChange() {
      this.$forceUpdate()
    },
    handleFilter() {
      if (this.listQuery.year) {
        this.listQuery.pageIndex = 1
        this.getList()
      } else {
        this.$notice.message('请选择年度', 'error')
      }
    },
    getList() {
      if (this.productAndSpecId) {
        const [productId, productSpecId] = this.productAndSpecId
        this.listQuery.productId = productId
        this.listQuery.productSpecId = productSpecId
      }
      if (this.manufacturerProductAndSpecId) {
        this.listQuery.productSpecId = this.manufacturerProductAndSpecId[2]
        // this.listQuery.ProductSpecIds = this.manufacturerProductAndSpecId.map(element => element[2])
      }
      this.listLoading = true
      ReportService.QueryRebateReport(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    productChange() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    // 显示导出model
    onShowExportModal() {
      if (!this.listQuery.year) {
        this.$notice.message('请选择年度', 'error')
        return
      }

      this.btnExportLoading = true
      ReportService.GetRebateReportExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      var exportParam = JSON.parse(JSON.stringify(this.listQuery))
      exportParam.checkedColumns = checkColumns
      ReportService.ExportRebateReport(exportParam)
        .then(result => {
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '返利报表.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '返利报表.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    }

  }
}
</script>
