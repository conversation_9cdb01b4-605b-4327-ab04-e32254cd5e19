<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filiter-container">
        <el-col :span="span">
          <el-input
            v-model="listQuery.name"
            clearable
            placeholder="产品名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            ref="dataTable"
            v-loading="listLoading"
            :data="list"
            stripe
            max-height="400"
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              width="55"
            />
            <el-table-column
              sortable
              prop="ManufacturerName"
              label="厂商名称"
              min-width="100px"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.manufacturerName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable
              prop="ProductNameCn"
              label="产品名称"
              min-width="100px"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable
              prop="ProductCommonName"
              label="通用名"
              min-width="100px"
              header-align="center"
              align="left"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productCommonName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable
              prop="Spec"
              label="规格"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.spec }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>

import ProductService from '@/api/product'
// import Pagination from '@/components/Pagination'

export default {
  components: {

  },
  props: {
    showDialog: {
      type: Boolean,
      default: false
    },
    selected: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      span: 4,
      listLoading: true,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 99999,
        order: '-CreateTime'
      },
      list: [],
      multipleSelection: []
    }
  },
  watch: {
    showDialog: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        if (val === true) {
          this.getList(this.selected)
        }
      }
    }
  },
  methods: {

    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList(selected) {
      this.listLoading = true
      ProductService.QueryProductSpecs(this.listQuery)
        .then((result) => {
          this.listLoading = false

          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex

          this.$nextTick(() => {
          // 标记已选择的集合
            const datalist = []
            this.list.forEach((item) => {
              selected.forEach(val => {
                if (val.productSpecId === item.id) {
                  datalist.push(item)
                }
              })
            })
            // 根据已选集合标记选中状态
            if (datalist) {
              datalist.forEach((row) => {
                this.$refs.dataTable.toggleRowSelection(row, true)
              })
            }
          })
        })
        .catch((error) => {
          console.log(error)
          this.loading = false
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleCheck() {
      this.$emit('success', JSON.parse(JSON.stringify(this.multipleSelection)))
    },
    clear() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 9999,
        order: '-CreateTime'
      }
    },
    handleSortChange({ column, prop, order }) {
      this.list.sort((a, b) => a[prop].localeCompare(b[prop]));
    }
  }
}
</script>
<style scoped>

</style>
