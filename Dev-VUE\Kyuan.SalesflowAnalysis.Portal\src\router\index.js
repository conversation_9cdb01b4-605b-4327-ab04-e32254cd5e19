import Vue from 'vue'
import Router from 'vue-router'
import { Message } from 'element-ui'
import store from '@/store'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'
import { constantRoutes, endRoutes } from '@/router/routes'
import cfg from '@cfg'

const { homeName, loginName } = cfg
const HOME_PAGE_NAME = homeName
const LOGIN_PAGE_NAME = loginName

Vue.use(Router)

function createRouter() {
  const router = new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
  })

  return router
}

const router = createRouter()

NProgress.configure({ showSpinner: false }) // NProgress Configuration

router.beforeEach(async(to, from, next) => {
  // start progress bar
  NProgress.start()

  // set page title
  document.title = getPageTitle(to.meta.title)

  // determine whether the user has logged in
  const token = getToken()

  if (to.meta && to.meta.anonymous) {
    // 可以匿名访问的页面
    next() // 跳转
  } else if (!token) {
    if (to.name !== LOGIN_PAGE_NAME) {
      // 未登录且要跳转的页面不是登录页
      next({
        name: LOGIN_PAGE_NAME, // 跳转到登录页
        query: {
          redirect: to.path
        }
      })
    } else {
      // 未登陆且要跳转的页面是登录页
      next() // 跳转
    }
  } else if (token) {
    if (to.name === LOGIN_PAGE_NAME) {
      // 已登录且要跳转的页面是登录页
      next({
        name: HOME_PAGE_NAME // 跳转到homeName页
      })
    } else {
      // determine whether the user has obtained his permission roles through getInfo
      let user = store.getters.user
      if (user) {
        next()
      } else {
        try {
          // get user info
          // note: 获取用户信息的同时，会重置路由
          user = await store.dispatch('user/getInfo')

          if (!user) {
            next({
              name: LOGIN_PAGE_NAME,
              query: {
                redirect: to.path
              }
            })
          } else {
            // set the replace: true, so the navigation will not leave a history record
            next({ ...to, replace: true })
          }
        } catch (error) {
          // remove token and go to login page to re-login
          await store.dispatch('user/resetToken')
          console.log(error)
          Message.error(error.message || '系统错误')
          next({
            name: LOGIN_PAGE_NAME,
            query: {
              redirect: to.path
            }
          })
        }
      }
    }
  }
  NProgress.done()
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
  window.scrollTo(0, 0)
})

export function resetRouter(accessedRoutes) {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router

  if (accessedRoutes) {
    router.addRoutes(accessedRoutes)
    router.addRoutes(endRoutes)
  }
}

export default router
