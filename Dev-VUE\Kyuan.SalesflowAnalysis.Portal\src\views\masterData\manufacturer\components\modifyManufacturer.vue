<template>
  <div>
    <el-dialog :title="title" :close-on-click-modal="false" :visible="true" @close="cancle()">
      <el-form ref="dataForm" :rules="rules" :model="tempData.tempFormModel" label-position="right" label-width="200px">
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="厂商名称" prop="name">
              <el-input v-model="tempData.tempFormModel.name" style="width:80%" clearable :readonly="viewModel" placeholder="请输入厂商名称" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          v-if="!viewModel"
          :loading="btnSaveLoading"
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>

import manufacturerApi from '@/api/manufacturer'

export default {
  name: '',
  components: {
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    viewModel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rules: {
        name: [
          { required: true, message: '请输入厂商名称', trigger: 'blur' },
          { max: 300, message: '厂商名称不允许超过300个字符', trigger: 'blur' }
        ]
      },
      tempData: { tempFormModel: {}},
      btnSaveLoading: false
    }
  },
  watch: {
    id(val) {
      this.tempData.tempFormModel.id = val
    }
  },
  mounted() {
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      if (!this.id) {
        this.clear()
        return
      }
      this.get(this.id)
    },
    get(id) {
      this.btnSaveLoading = true
      manufacturerApi.GetManufacturer({ id: id }).then(result => {
        this.btnSaveLoading = false
        this.tempData.tempFormModel = result.data
      }).catch(error => {
        this.btnSaveLoading = false
        console.log(error)
      })
    },
    save() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.tempData.tempFormModel.id) {
            this.addNew()
          } else {
            this.update()
          }
        }
      })
    },
    addNew() {
      this.btnSaveLoading = true
      manufacturerApi.AddManufacturer(this.tempData.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempData.tempFormModel = result.data
          this.$notice.message('新增成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('新增失败。', 'error')
          }
        })
    },
    update() {
      this.btnSaveLoading = true
      manufacturerApi.UpdateManufacturer(this.tempData.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempData.tempFormModel = result.data
          this.$notice.message('修改成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('修改失败。', 'error')
          }
        })
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    }
  }

}
</script>
