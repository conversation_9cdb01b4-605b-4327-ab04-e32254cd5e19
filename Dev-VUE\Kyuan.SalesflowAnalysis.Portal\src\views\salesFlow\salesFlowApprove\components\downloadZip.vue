<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      title="申诉附件下载"
      :visible="dialogFormVisible"
      width="45%"
      @close="handleHidden"
    >
      <el-form
        label-position="right"
        label-width="160px"
        class="el-dialogform"
      >
        <el-form-item label="选择月份">
          <el-date-picker
            v-model="selectMonth"
            style="width:100%"
            type="month"
            clearable
            placeholder="月份"
            value-format="yyyy-MM"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleHidden()"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleDownload"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import SalesFlowComplaintsRequestService from '@/api/salesFlowComplaintsRequest'

export default {
  data() {
    return {
      selectMonth: '',
      dialogFormVisible: false
    }
  },
  methods: {
    initPage() {
      this.dialogFormVisible = true
    },
    handleHidden() {
      this.dialogFormVisible = false
      this.selectMonth = ''
    },
    handleDownload() {
      if (this.selectMonth === '') {
        this.$notice.message('请选择月份', 'error')
        return
      }
      SalesFlowComplaintsRequestService.DownloadComplaintsZip({ month: this.selectMonth })
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = `${this.selectMonth}申诉文件.zip`
          fileDownload(result.data, filename)
          this.dialogFormVisible = false
        })
        .catch(() => {
        })
    }
  }
}
</script>
