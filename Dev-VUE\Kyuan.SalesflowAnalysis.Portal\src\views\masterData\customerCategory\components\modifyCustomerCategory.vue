<template>
  <div>
    <el-dialog :title="title" width="50%" :close-on-click-modal="false" :visible="true" @close="cancle()">
      <el-form ref="dataForm" :rules="rules" :model="tempFormModel" label-position="right" label-width="100px" class="el-dialogform">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="客户分类名称" prop="name">
              <el-col :span="18" style="padding-left: 0px; padding-right: 0px;">
                <el-input
                  v-model="tempFormModel.name"
                  placeholder="客户分类名称"
                />
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="客户类型" prop="customerCategoryId">
              <el-select
                v-model="tempFormModel.customerCategoryId"
                class="filter-item"
                placeholder="客户类型"
                clearable
              >
                <el-option
                  v-for="item in customerCategoryList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          :loading="btnSaveLoading"
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="选择收货方"
      :close-on-click-modal="false"
      :visible="dialogReceiverVisible"
      width="90%"
      class="popup-search"
      @close="closeReceiverDialog"
    >
      <SelectReceiver
        ref="refSelectReceiver"
        :show-dialog="dialogReceiverVisible"
        :distributor-types="distributorTypes"
        :is-stopped="false"
        @success="selectReceiverSuccess"
      />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeReceiverDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import SelectReceiver from '@/views/components/selectReceiver'
import MaintenanceService from '@/api/maintenance'
import ProductService from '@/api/product'
import TargetReceiverService from '@/api/targetReceiver'

export default {
  name: '',
  components: {
    SelectReceiver
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    viewModel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      span: 12,
      rules: {
        manufacturerProductAndSpecId: [
          { required: true, message: '请选择厂商产品规格', trigger: 'change' }
        ],
        departmentId: [
          { required: true, message: '请选择部门', trigger: 'change' }
        ],
        receiverName: [
          { required: true, message: '请选择客户', trigger: 'change' }
        ],
        startDate: [
          { required: true, message: '请选择起始日期', trigger: 'change' }
        ],
        customerCategoryId: [
          { required: true, message: '请选择客户类型', trigger: 'change' }
        ]
      },
      tempFormModel: { distributorName: '', receiverName: '' },
      btnSaveLoading: false,
      productAndSpecLoading: false,
      productAndSpecList: [],
      manufacturerProductAndSpecId: [],
      productAndSpecKey: 0,
      deptLoading: false,
      deptList: [],
      distributorTypes: [],
      customerCategoryLoading: false,
      customerCategoryList: [],
      dialogDistributorVisible: false,
      dialogReceiverVisible: false,
      endDateString: '2099-12-31 00:00:00'
    }
  },
  watch: {
    id(val) {
      this.tempFormModel.id = val
    }
  },
  mounted() {
  },
  created() {
    this.initProductAndSpec()
    this.initDept()
    this.initCustomerCategory()
    this.init()
  },
  methods: {
    initProductAndSpec() {
      this.productAndSpecLoading = true
      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch(() => {
          this.deptLoading = false
        })
    },
    initCustomerCategory() {
      this.customerCategoryLoading = true
      TargetReceiverService.QueryCustomerCategorySelect().then(res => {
        this.customerCategoryLoading = false
        this.customerCategoryList = res
      })
        .catch(
          this.customerCategoryLoading = false
        )
    },
    init() {
      if (!this.id) {
        this.clear()
        return
      }
      this.get(this.id)
    },
    get(id) {
      this.btnSaveLoading = true
      TargetReceiverService.GetTargetReceiver({ id: id }).then(result => {
        this.tempFormModel = result.data
        this.tempFormModel.manufacturerProductAndSpecId = [result.data.manufacturerId, result.data.productId, result.data.productSpecId]
        this.tempFormModel.timeRange = [new Date(result.data.startDate), new Date(result.data.endDate)]
        this.btnSaveLoading = false

        if (this.tempFormModel.endDate === this.endDateString) {
          this.tempFormModel.endDate = ''
        }

        this.initProductAndSpec()
      }).catch(error => {
        this.btnSaveLoading = false
        console.log(error)
      })
    },
    save() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.tempFormModel.id) {
            this.addNew()
          } else {
            this.update()
          }
        }
      })
    },
    addNew() {
      this.btnSaveLoading = true
      TargetReceiverService.AddTargetReceiver(this.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempFormModel = result.data
          this.$notice.message('新增成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('新增失败。', 'error')
          }
        })
    },
    update() {
      this.btnSaveLoading = true
      TargetReceiverService.UpdateTargetReceiver(this.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempFormModel = result.data
          this.$notice.message('修改成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('修改失败。', 'error')
          }
        })
    },
    handleSelectReceiver() {
      this.distributorTypes = [10, 20, 30]
      this.dialogReceiverVisible = true
    },
    closeReceiverDialog() {
      this.dialogReceiverVisible = false
      this.$refs.refSelectReceiver.clear()
      this.distributorTypes = []
    },
    selectReceiverSuccess(val) {
      if (val.id === this.tempFormModel.distributorId) {
        this.$notice.message('收货方不能和发货方相同', 'error')
      } else {
        // 解决弹出框打开后，不输入任何值，只选择收货方名称时，验证失效的问题
        this.$set(this.tempFormModel, 'receiverName', val.name)
        // this.tempFormModel.receiverName = val.name
        this.tempFormModel.receiverId = val.id
        this.closeReceiverDialog()
      }
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.tempFormModel = {}
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    },
    change() {
      this.$forceUpdate()
    }
  }

}
</script>

