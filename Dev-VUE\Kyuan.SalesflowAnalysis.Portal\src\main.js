import Vue from 'vue'
import Cookies from 'js-cookie'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

import Element from 'element-ui'
import '@/styles/element-variables.scss'

import '@/styles/index.scss' // global css

import '@/styles/databorad.scss'

import App from './App'
import store from '@/store'
import router from '@/router'
import cfg from '@cfg'

import './icons' // icon
import './utils/error-log' // error log

import * as filters from './filters' // global filters

import notice from '@/utils/notice'
import { encryptCycle, decryptCycle } from '@/utils/json-cycle'

import { deepClone } from '@/utils/deepCopy'
import constDefinition from '@/utils/constDefinition'

import echarts from 'echarts'
import { isUserPermitted } from '@/utils/auth'
Vue.prototype.$echarts = echarts
Vue.prototype.$isPermitted = isUserPermitted

import TreeSelect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
Vue.component('TreeSelect', TreeSelect)

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
// if (process.env.NODE_ENV === 'production') {
//   const { mockXHR } = require('../mock')
//   mockXHR()
// }

if (typeof JSON.encryptCycle !== 'function') {
  JSON.encryptCycle = encryptCycle
}

if (typeof JSON.decryptCycle !== 'function') {
  JSON.decryptCycle = decryptCycle
}

Vue.use(Element, {
  size: Cookies.get('size') || 'small' // set element-ui default size
})

// register global utility filters
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

/**
 * @description 生产环境关掉提示
 */
Vue.config.productionTip = false

/**
 * @description 全局注册应用配置
 */
Vue.prototype.$cfg = cfg

/**
 * @description 全局注册 自定义提示
 */
Vue.prototype.$notice = notice

Vue.prototype.$deepClone = deepClone
Vue.prototype.$constDefinition = constDefinition
new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
