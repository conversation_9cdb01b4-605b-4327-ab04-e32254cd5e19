<template>
  <div>
    <el-form
      label-position="right"
      label-width="90px"
    >
      <el-row>
        <el-col :span="span">
          <el-form-item label="产品名称">
            {{ productInfo.nameCn }}
          </el-form-item>
        </el-col>
        <el-col :span="span">
          <el-form-item label="通用名">
            {{ productInfo.commonName }}
          </el-form-item>
        </el-col>
        <el-col :span="span">
          <el-form-item label="英文名称">
            {{ productInfo.nameEn }}
          </el-form-item>
        </el-col>
        <el-col :span="span">
          <el-form-item label="厂商名称">
            {{ productInfo.manufacturerName }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="包装规格">
            <el-table
              v-loading="listLoading"
              :data="list"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%;"
              :default-sort="{prop: 'createTime', order: 'descending'}"
              :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass"
              @sort-change="sortChange"
            >
              <el-table-column fixed label="序号" type="index" align="center" />
              <el-table-column sortable="custom" prop="Code" label="规格编码" min-width="80px" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.code }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="Spec" label="包装规格" min-width="120px" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.spec }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="Unit" label="包装单位" min-width="80px" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.unit }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="PharmaceuticalFactory" label="生产厂家" min-width="200px" header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.pharmaceuticalFactory }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="DosageFormCode" label="剂型" min-width="80px" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.dosageFormName }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>
  </div>
</template>
<script>
import ProductService from '@/api/product'

export default {
  props: {
    productId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      span: 12,
      productInfo: {},
      listLoading: true,
      list: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 1000,
        order: '-CreateTime'
      }
    }
  },
  watch: {
    productId: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        if (val !== '') {
          this.productId = val
          this.getProduct(val)
          this.QueryProductSpecs(val)
        }
      }
    }
  },
  methods: {
    getProduct(productId) {
      ProductService.GetProduct({
        id: productId
      }).then(res => {
        this.productInfo = res.data
      }).catch(res => {})
    },
    QueryProductSpecs(productId) {
      this.listLoading = true
      this.listQuery.productId = productId
      ProductService.QueryProductSpecs(this.listQuery).then(res => {
        this.listLoading = false
        this.list = res.data.datas
      }).catch(res => {})
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.QueryProductSpecs(this.listQuery.productId)
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    }
  }
}
</script>
