<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filiter-container">
        <el-col :span="span">
          <el-input
            v-model="listQuery.displayName"
            clearable
            placeholder="姓名"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.jobNo"
            clearable
            placeholder="工号"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
            <el-table-column
              sortable="custom"
              prop="Spec"
              label="工号"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.jobNo }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Spec"
              label="姓名"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.displayName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Spec"
              label="部门"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.departmentName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Spec"
              label="职务"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.title }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Spec"
              label="性别"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumGenderDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Spec"
              label="状态"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="70"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i class="el-icon-circle-check eltablei" title="选择" @click="handleCheck(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>

import MaintenanceService from '@/api/maintenance'
import Pagination from '@/components/Pagination'

export default {
  components: {
    Pagination
  },
  props: {
    showDialog: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      span: 4,
      listLoading: true,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        enumStatus: 1,
        order: '-CreateTime'
      },
      list: []
    }
  },
  watch: {
    showDialog: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        if (val === true) {
          this.getList()
        }
      }
    }
  },
  methods: {
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList() {
      this.listLoading = true
      MaintenanceService.QueryEmployee(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleCheck(row) {
      this.$emit('success', row)
    },
    clear() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
    }
  }
}
</script>
<style scoped>

</style>
