
import Layout from '@/layout'

const databorad = [
  {
    path: '/databorad',
    component: Layout,
    redirect: '/databorad/index',
    hidden: false,
    meta: {
      title: '数据看板',
      sort: 75,
      icon: 'setting'
    },
    children: [
      {
        name: 'databorad',
        path: 'databorad',
        component: () => import('@/views/dataBoard/index'),
        meta: {
          title: '销售数据看板',
          icon: 'list',
          sort: 1,
          noCache: false,
          permissions: ['Dashboard_Query']
        }
      },
      {
        name: 'expendDashboard',
        path: 'expendDashboard',
        component: () => import('@/views/dataBoard/expendDashboard'),
        meta: {
          title: '支出数据看板',
          icon: 'list',
          sort: 1,
          noCache: false,
          permissions: ['ExpendDashboard_Query']
        }
      }
    ]
  }
]

export default databorad
