<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filiter-container">
        <el-col :span="span">
          <el-input
            v-model="filter.name"
            clearable
            placeholder="模板名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              width="55"
            />
            <el-table-column
              sortable="custom"
              prop="Name"
              label="模板类型名称"
              min-width="100px"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Code"
              label="模板编码"
              min-width="100px"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.code }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="EnumRebateAgreementGroupType"
              label="模板大类"
              min-width="100px"
              header-align="center"
              align="left"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumRebateAgreementGroupTypeDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="isMasterDesc"
              label="是否主协议"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.isMasterDesc }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>

import AgreementService from '@/api/agreement'

export default {
  components: {

  },
  props: {
    showDialog: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      span: 4,
      listLoading: true,
      total: 0,
      filter: {
        pageIndex: 1,
        pageSize: 99999,
        order: '-CreateTime'
      },
      list: [],
      multipleSelection: []
    }
  },
  watch: {
    showDialog: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        if (val === true) {
          this.getList()
        }
      }
    }
  },
  methods: {
    indexMethod(index) {
      return (
        (this.filter.pageIndex - 1) * this.filter.pageSize + index + 1
      )
    },
    getList() {
      this.listLoading = true
      // 只筛选需要计算的
      this.filter.isNeedCalculated = true
      AgreementService.QueryAgreementTemplateType(this.filter)
        .then((result) => {
          this.listLoading = false

          this.list = result.data.datas
          this.total = result.data.recordCount
          this.filter.pageIndex = result.data.pageIndex
        })
        .catch((error) => {
          console.log(error)
          this.loading = false
        })
    },
    handleFilter() {
      this.filter.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.filter.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.filter.order = orderSymbol + column.prop
      this.getList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleCheck() {
      this.$emit('success', this.multipleSelection)
    },
    clear() {
      this.filter = {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
    }
  }
}
</script>
<style scoped>

</style>
