/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    permissions: ['admin','editor']    control the page permissions (you can set multiple permissions)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

import Layout from '@/layout'

const reportRoutes = [
  {
    path: '/report',
    component: Layout,
    redirect: '/report/index',
    hidden: false,
    meta: {
      title: '报表查询',
      sort: 70,
      icon: 'report'
    },
    children: [
      {
        name: 'SalesAchievement',
        path: 'SalesAchievement',
        component: () => import('@/views/report/salesAchievementReport/index'),
        meta: {
          title: '销量达成报表',
          icon: 'report-increase',
          sort: 5,
          noCache: false,
          permissions: ['Report_SalesAchievement']
        }
      },
      {
        name: 'MedicalCustomer',
        path: 'medicalCustomer',
        component: () => import('@/views/report/medicalCustomerReport/index'),
        meta: {
          title: '医疗客户分析',
          icon: 'brokenline',
          sort: 7,
          noCache: false,
          permissions: ['Report_MedicalCustomer']
        }
      },
      {
        name: 'RetailCustomerReport',
        path: 'retailCustomerReport',
        component: () => import('@/views/report/retailCustomerReport/index'),
        meta: {
          title: '零售客户分析',
          icon: 'table',
          sort: 8,
          noCache: false,
          permissions: ['Report_SalesAchievement']
        }
      },
      {
        name: 'HeadquartersCoverageReport',
        path: 'headquartersCoverageReport',
        component: () => import('@/views/report/headquartersCoverage/index'),
        meta: {
          title: '连锁总部覆盖率',
          icon: 'report-wave',
          sort: 9,
          noCache: false,
          permissions: ['Report_HeadquartersCoverage']
        }
      },
      {
        name: 'RebateReport',
        path: 'RebateReport',
        component: () => import('@/views/report/rebateReport/index'),
        meta: {
          title: '返利政策',
          icon: 'report-1',
          sort: 9,
          noCache: false,
          permissions: ['Report_Rebate']
        }
      }
    ]
  }
]

export default reportRoutes
