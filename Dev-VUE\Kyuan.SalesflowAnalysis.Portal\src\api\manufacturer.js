import HttpApi from './libs/api.request'

const controller = 'Manufacturer'

const api = new HttpApi(controller)

export default {
  QueryManufacturerSelect(params) {
    return api.get('QueryManufacturerSelect', params)
  },
  QueryManufacturer(params) {
    return api.get('QueryManufacturer', params)
  },
  GetManufacturer(params) {
    return api.get('GetManufacturer', params)
  },
  AddManufacturer(params) {
    return api.post('AddManufacturer', params)
  },
  UpdateManufacturer(params) {
    return api.post('UpdateManufacturer', params)
  },
  DeleteManufacturer(params) {
    return api.post('DeleteManufacturer', params)
  },
  GetManufacturerExportColumn() {
    return api.get('GetManufacturerExportColumn')
  },
  ExportManufacturer(params) {
    return api.post('ExportManufacturer',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  QueryManufacturerUser(params) {
    return api.get('QueryManufacturerUser', params)
  },
  GetManufacturerUser(params) {
    return api.get('GetManufacturerUser', params)
  },
  AddManufacturerUser(params) {
    return api.post('AddManufacturerUser', params)
  },
  UpdateManufacturerUser(params) {
    return api.post('UpdateManufacturerUser', params)
  },
  DeleteManufacturerUser(params) {
    return api.post('DeleteManufacturerUser', params)
  },
  ResetManufacturerUserPwd(params) {
    return api.post('ResetManufacturerUserPwd', params)
  },
  GetManufacturerUserExportColumn() {
    return api.get('GetManufacturerUserExportColumn')
  },
  ExportManufacturerUser(params) {
    return api.post('ExportManufacturerUser',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  }
}
