<template>
  <div class="search-container-bg">
    <el-row type="flex" :gutter="10" class="home-flex-container">
      <el-col :span="4">
        <el-date-picker
          v-model="selectYear"
          class="filter-item"
          style="width:100%"
          type="year"
          :clearable="false"
          placeholder="年份"
          @change="yearChange"
        />
      </el-col>
      <el-col :span="4">
        <customProductSpecMutiple @input="handleProductSpecChange" />
      </el-col>
      <el-col :span="4">
        <el-select
          v-model="filter.departmentId"
          value-key="value"
          class="filter-item"
          placeholder="部门"
          style="width:100%"
          :clearable="isManager && deptList.length>1"
          @change="departmentChange"
        >
          <el-option
            v-for="item in deptList"
            :key="item.key"
            :label="item.name"
            :value="item.key"
          />
        </el-select>
      </el-col>
      <el-col v-if="isManager || isDepartmentManager" :span="3">
        <el-select
          v-model="filter.areaIds"
          class="filter-item"
          placeholder="大区"
          multiple
          style="width:100%"
          :disabled="filter.departmentId === null || filter.departmentId ===undefined || filter.departmentId === ''"
          clearable
          @change="areaChange"
        >
          <el-option
            v-for="item in areaList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-col>
      <el-col :span="5">
        <customProvinceMutiple
          v-model="filter.selectProvinceIds"
          :province-list="provinceList"
          @input="handleProvinceChange"
        />
      </el-col>
      <el-col :span="2">
        <el-button v-if="$isPermitted($store.getters.user, 'ExpendDashboard_Button_Query')" :loading="dataLoading" class="filter-item-button" type="primary" icon="el-icon-search" @click="search">
          查询
        </el-button>
      </el-col>
    </el-row>
    <el-row>
      <el-col v-if="$isPermitted($store.getters.user, 'ExpendDashboard_Button_ClearCache')" :span="24" style="text-align: right;">
        <span style="font-size: 11px;padding-right:5px; line-height: 32px;">数据更新至： {{ salesFlowCutoffDate }}</span>
        <el-button v-if="$isPermitted($store.getters.user, 'ExpendDashboard_Button_ClearCache')" icon="el-icon-refresh" style="float: right;" :loading="dataLoading" class="filter-item-button" type="primary" @click="clearCache">
          清除缓存
        </el-button>
      </el-col>
      <el-col v-if="!$isPermitted($store.getters.user, 'ExpendDashboard_Button_ClearCache')" :span="24" style="text-align: right;">
        <span style="float: right;font-size: 11px;padding-right:5px; line-height: 32px;">数据更新至： {{ salesFlowCutoffDate }}</span>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <expendExpenses ref="refExpendExpenses" />
      </el-col>
    </el-row>
    <el-row :gutter="10" type="flex" class="home-flex-container">
      <el-col :span="12">
        <expendWarning ref="refExpendWarning" @success="success" />
      </el-col>
      <el-col v-if="isManager||isRegionalManager" :span="12">
        <RevenueAndExpenditureDetail ref="refRevenueAndExpenditureDetail" />
      </el-col>
      <el-col v-if="isManager" :span="12">
        <projectExpendDetail ref="refProjectExpendDetail" />
      </el-col>
      <el-col v-if="!isRegionalManager" :span="12">
        <inputOutputRatio ref="refInputOutputRatio" />
      </el-col>
      <el-col v-if="isRegionalManager" :span="24">
        <inputOutputRatio ref="refInputOutputRatio" />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import ProductService from '@/api/product'
import MaintenanceService from '@/api/maintenance'
import LocationService from '@/api/location'
import dashboardService from '@/api/dashboard'
import ReportService from '@/api/report'
import HomeService from '@/api/home'

// 新组件
import RevenueAndExpenditureDetail from './components/revenueAndExpenditureDetail'
import expendWarning from './components/expendWarning'
import inputOutputRatio from './components/inputOutputRatio'
import expendExpenses from './components/expendExpenses'
import projectExpendDetail from './components/projectExpendDetail'

import customProductSpecMutiple from '@/components/CustomProductSpecMutiple'
import customProvinceMutiple from '@/components/CustomProvinceMutiple'

export default {
  name: 'Home',
  components: {
    RevenueAndExpenditureDetail,
    expendWarning,
    expendExpenses,
    projectExpendDetail,
    inputOutputRatio,
    customProductSpecMutiple,
    customProvinceMutiple
  },
  data() {
    return {
      dataLoading: false,
      productAndSpecList: [],
      filter: {
        productAndSpecList: [],
        timeRange: [],
        isSelectedAllProvince: true
      },
      selectYear: '2023',
      deptList: [],
      areaList: [],
      allAreaList: [],
      provinceList: [],
      analyticsCardData: {},
      analyticsCardDataLoading: false,
      salesFlowCutoffDate: '',
      employeeRole: {},
      isManager: false,
      isDepartmentManager: false,
      isRegionalManager: false,
      topCount: 1,
      productSpecAllChecked: true,
      productSpecIds: []
    }
  },
  created() {
    this.getSalesFlowUpdateDate()
  },
  async mounted() {
    const date = new Date()
    const year = date.getFullYear()
    this.selectYear = year + ''
    this.filter.timeRange = [`${year}-${'01'}`, `${year}-${'12'}`]

    await this.initProductAndSpec()
    await this.getSysSettingTopCount()
    await this.getEmployeeRole()
    await this.initDept()
    await this.initArea()
    await this.search()
  },
  methods: {
    async getSysSettingTopCount() {
      await dashboardService.GetSysSettingTopCount(this.filter)
        .then((res) => {
          this.topCount = res.data
        })
    },
    async getEmployeeRole() {
      await HomeService.GetCurrentEmployeeRole().then(async res => {
        this.employeeRole = res.data
        if (this.employeeRole.isSFE ||
        this.employeeRole.isSeniorManagement ||
        this.employeeRole.isSysAdmin ||
        this.employeeRole.isDataAdmin ||
        this.employeeRole.isDepartmentManager
        ) {
          this.isManager = true
        }
        // 部门总监
        this.isDepartmentManager = this.employeeRole.isDepartmentManager
        this.isRegionalManager = this.employeeRole.isRegionalManager
      })
    },
    success(value) {
      if (value) {
        this.$nextTick(() => {
          this.$refs.refSalesGrowthRate.initPage(this.filter)
        })
      } else {
        this.$nextTick(() => {
          this.$refs.refSalesGrowthRate1.initPage(this.filter)
        })
      }
    },
    async search() {
      if (this.filter.selectProvinceIds !== undefined && this.filter.selectProvinceIds !== null) {
        this.filter.provinceIds = this.filter.selectProvinceIds
      } else {
        this.$notice.message('省份不可为空，请选择省份信息', 'error')
        return
      }

      if (this.productSpecIds === undefined || this.productSpecIds.length <= 0) {
        this.$notice.message('产品规格不可为空，请选择品规信息', 'error')
        return
      } else if (this.productSpecAllChecked) {
        this.filter.productSpecIds = []
      } else {
        this.filter.productSpecIds = this.productSpecIds
      }

      if (this.filter.selectProvinceIds.length === this.provinceList.length) {
        this.filter.isSelectedAllProvince = true
      } else {
        this.filter.isSelectedAllProvince = false
      }

      this.initDashboard()
    },
    getSalesFlowUpdateDate() {
      ReportService.GetSalesFlowUpdateDate()
        .then((res) => {
          this.salesFlowCutoffDate = res.data
        })
    },
    async initDept() {
      await MaintenanceService.QueryDepartmentForReport()
        .then(async result => {
          this.deptList = result
          if (!this.isManager) {
            if (this.deptList.length > 0) {
              this.filter.departmentId = this.deptList[0].key
            }
          } else {
            if (this.deptList.length === 1) {
              this.filter.departmentId = this.deptList[0].key
            }
          }
          this.queryProvinceByDepartmentAndAreas()
        })
        .catch(() => {
        })
    },
    async initArea() {
      await MaintenanceService.QueryAreasForReport()
        .then(async result => {
          this.allAreaList = result
          if (this.isDepartmentManager) {
            this.filter.areaIds = []
            this.areaList = this.allAreaList.filter(item => { return item.departmentId === this.filter.departmentId })
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    async initProductAndSpec() {
      await ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then(result => {
          this.productAndSpecList = result
        })
        .catch(() => {
        })
    },
    initDashboard() {
      dashboardService.PreloadSalesData(this.filter)
        .then((res) => {
          this.$nextTick(() => {
            const filterTemp = JSON.parse(JSON.stringify(this.filter))
            this.$refs.refExpendExpenses.initPage(filterTemp)
            this.$refs.refExpendWarning.initPage(filterTemp, this.productAndSpecList, this.topCount)
            if (this.isRegionalManager || this.isManager) {
              this.$refs.refRevenueAndExpenditureDetail.initPage(filterTemp, filterTemp.departmentId !== undefined && filterTemp.departmentId !== '' ? this.deptList.filter(o => o.key === filterTemp.departmentId) : this.deptList)
            }
            this.$refs.refInputOutputRatio.initPage(filterTemp, filterTemp.departmentId !== undefined && filterTemp.departmentId !== '' ? this.deptList.filter(o => o.key === filterTemp.departmentId) : this.deptList, this.productAndSpecList, this.topCount)
            if (this.isManager) {
              this.$refs.refProjectExpendDetail.initPage(filterTemp)
            }
          })
        })
        .catch(() => {
          this.analyticsCardDataLoading = false
        })
    },
    departmentChange() {
      this.filter.selectProvinceIds = []
      if (this.isManager || this.isDepartmentManager) {
        this.filter.areaIds = []
        this.areaList = this.allAreaList.filter(item => { return item.departmentId === this.filter.departmentId })
      }
      this.queryProvinceByDepartmentAndAreas()
    },
    areaChange() {
      this.filter.selectProvinceIds = []
      if (this.filter.areaIds === null || this.filter.areaIds === undefined || this.filter.areaIds.length === 0) {
        this.provinceList = []
      }
      this.queryProvinceByDepartmentAndAreas()
    },
    queryProvinceByDepartmentAndAreas() {
      this.dataLoading = true
      LocationService.QueryProvinceSelectForDataReport(this.filter)
        .then((result) => {
          this.provinceList = result
          this.filter.selectProvinceIds = result.map(item => { return item.key })
          this.dataLoading = false
        })
        .catch((error) => {
          console.log(error)
          this.dataLoading = false
        })
    },
    handleProvinceChange(provinceIDs) {
      this.filter.selectProvinceIds = provinceIDs
    },
    handleProductSpecChange(specIds, isCheckChanged) {
      this.productSpecIds = specIds
      this.productSpecAllChecked = isCheckChanged
    },
    clearCache() {
      dashboardService.ClearDashboardRedis()
    },
    yearChange() {
      var year = this.selectYear.getFullYear()
      this.filter.timeRange = [`${year}-${'01'}`, `${year}-${'12'}`]
    }
  }
}
</script>
  <style scoped>
  .box ul {
    display: grid;
    justify-content: space-evenly;
    padding: 10px;
    grid-template-columns: repeat(auto-fill, 160px);
    grid-gap: 10px;
    text-align: center;
  }
  .box li {
    width:160px;
    list-style: none;
    margin-bottom: 10px;
  }

  .scrollable-div {
    background-color: white;
    height: 50%; /* 设置div的高度 */
    overflow-y: auto; /* 当内容超出垂直高度时显示滚动条 */
}

  .appImgContainer{
    height: 120px;
    padding :5px 0px;
  }

  .appImgContainer img{
    width: 100px;
    height: 100px;
  }

  .appName{
    font-size: 18px;
    font-weight: bold;
  }
  .home-flex-container{
    flex-wrap: wrap;
  }

   .home-flex-container .el-col{
      margin-bottom: 10px;
    }

  </style>
