<template>
  <div class="login-container">
    <div class="login-form-container">
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        autocomplete="on"
        label-position="left"
      >
        <div style="background-color: #022140; padding: 40px">
          <div class="title-container">
            <h3 class="title">{{ title }}</h3>
          </div>

          <el-form-item prop="username">
            <span class="svg-container">
              <svg-icon icon-class="user" />
            </span>
            <el-input
              ref="username"
              v-model="loginForm.username"
              placeholder="用户名"
              name="username"
              type="text"
              tabindex="1"
              autocomplete="on"
            />
          </el-form-item>

          <el-tooltip v-model="capsTooltip" content="Caps lock is On" placement="right" manual>
            <el-form-item prop="password" style="margin-bottom: 0px !important">
              <span class="svg-container">
                <svg-icon icon-class="password" />
              </span>
              <el-input
                :key="passwordType"
                ref="password"
                v-model="loginForm.password"
                :type="passwordType"
                placeholder="密码"
                name="password"
                tabindex="2"
                autocomplete="on"
                @keyup.native="checkCapslock"
                @blur="capsTooltip = false"
                @keyup.enter.native="handleLogin"
              />
              <span class="show-pwd" @click="showPwd">
                <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
              </span>
            </el-form-item>
          </el-tooltip>

          <el-button
            :loading="loading"
            type="primary"
            style="width: 100%; margin-bottom: 30px; margin-top: 22px"
            @click.native.prevent="handleLogin"
          >登 录</el-button>
        </div>
      </el-form>

    </div>
    <el-dialog
      :close-on-click-modal="false"
      title="修改密码"
      :visible="dialogEditFormVisible"
      width="35%"
      @close="cancle()"
    >
      <el-alert
        title="提示：首次登录您需要重新设置新密码，旧密码将会被锁定，无法使用旧密码再次登录。"
        type="success"
        class="alert"
        :closable="false"
        show-icon
      />
      <el-form
        ref="dataForm"
        :rules="rulePasswords"
        :model="passwordModel"
        label-position="right"
        label-width="90px"
        class="el-dialogform"
      >

        <el-form-item label="新密码" prop="newPwd">
          <el-input
            v-model="passwordModel.newPwd"
            maxlength="30"
            show-password
            placeholder="请输入新密码"
          />
        </el-form-item>
        <el-form-item label="重复新密码" prop="repeatNewPwd">
          <el-input
            v-model="passwordModel.repeatNewPwd"
            maxlength="30"
            show-password
            placeholder="请输入重复新密码"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" icon="el-icon-check" @click="btnSave()">
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import cfg from '@cfg'
export default {
  name: 'Login',
  data() {
    const validateUsername = (rule, value, callback) => {
      if (value.length === 0) {
        callback(new Error('请输入用户名'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length === 0) {
        callback(new Error('请输入密码'))
      } else if (value.trim() === '') {
        callback(new Error('请输入正确的密码'))
      } else {
        callback()
      }
    }
    return {
      title: cfg.title,
      loginForm: {
        username: process.env.NODE_ENV === 'development' ? 'sysadmin' : '',
        password: process.env.NODE_ENV === 'development' ? '123456' : ''
      },
      loginRules: {
        username: [{
          required: true,
          trigger: 'blur',
          validator: validateUsername
        }],
        password: [{
          required: true,
          trigger: 'blur',
          validator: validatePassword
        }]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      redirect: undefined,
      otherQuery: {},
      dialogEditFormVisible: false,
      rulePasswords: {
        newPwd: [{
          required: true,
          message: '请输入新密码',
          trigger: 'blur'
        }],
        repeatNewPwd: [{
          required: true,
          message: '请输入重复新密码',
          trigger: 'blur'
        }]
      },
      passwordModel: {
        newPwd: '',
        repeatNewPwd: ''
      }
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  created() {
    // window.addEventListener('storage', this.afterQRScan)
  },
  mounted() {
    if (this.loginForm.username === '') {
      this.$refs.username.focus()
    } else if (this.loginForm.password === '') {
      this.$refs.password.focus()
    }
  },
  destroyed() {
    // window.removeEventListener('storage', this.afterQRScan)
  },
  methods: {
    checkCapslock(e) {
      const {
        key
      } = e
      this.capsTooltip = key && key.length === 1 && key >= 'A' && key <= 'Z'
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true
          this.$store
            .dispatch('user/login', this.loginForm)
            .then((data) => {
              if (data.lockStatus === 1) {
                this.dialogEditFormVisible = true
              } else {
                this.$router.push({
                  path: this.redirect || '/',
                  query: this.otherQuery
                })
              }
              this.loading = false
            })
            .catch((error) => {
              console.log(error)
              this.loading = false
            })
        } else {
          return false
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    },
    cancle() {
      this.dialogEditFormVisible = false
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.passwordModel = {
        newPwd: '',
        repeatNewPwd: ''
      }
    },
    btnSave() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          var regex = new RegExp('(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,30}')
          if (this.passwordModel.newPwd.trim() !== this.passwordModel.repeatNewPwd.trim()) {
            this.$alert('重复新密码必须与新密码一致', '提示', {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确定',
              type: 'error'
            })
          } else if (!regex.test(this.passwordModel.newPwd.trim())) {
            this.$alert('您的新密码复杂度太低（密码中必须包含字母、数字、特殊字符，至少8个字符，最多30个字符），请修改新密码！', '提示', {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确定',
              type: 'error'
            })
          } else {
            var param = {
              username: this.loginForm.username,
              newPwd: this.passwordModel.newPwd
            }
            this.$store
              .dispatch('user/firstLoginChangePwd', param)
              .then((data) => {
                this.$router.push({
                  path: this.redirect || '/',
                  query: this.otherQuery
                })
                this.dialogEditFormVisible = false
              })
              .catch((error) => {
                console.log(error)
                this.loading = false
              })
          }
        }
      })
    }

  }
}

</script>

<style lang="scss">
  /* 修复input 背景不协调 和光标变色 */
  /* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

  $bg: #283443;
  $light_gray: #fff;
  $cursor: #fff;

  @supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
    .login-form-container .el-input input {
      color: $cursor;
    }
  }

  /* reset element-ui css */
  .login-form-container {
    .el-input {
      display: inline-block;
      height: 47px;
      width: 85%;

      input {
        background: transparent;
        border: 0px;
        -webkit-appearance: none;
        border-radius: 0px;
        padding: 12px 5px 12px 15px;
        color: $light_gray;
        height: 47px;
        caret-color: $cursor;

        &:-webkit-autofill {
          box-shadow: 0 0 0px 1000px $bg inset !important;
          -webkit-text-fill-color: $cursor !important;
        }
      }
    }

    .el-form-item {
      border: 1px solid rgba(255, 255, 255, 0.3);
      background: rgba(0, 0, 0, 0.1);
      border-radius: 5px;
      color: #454545;
    }
  }

</style>

<style lang="scss" scoped>
  $bg: #2d3a4b;
  $dark_gray: #889aa4;
  $light_gray: #eee;

  .login-container {
    min-height: 100%;
    width: 100%;
    background: url("~@/assets/login/loginBackground.png");
    background-position: center;
    background-size: 100%;
    background-repeat: no-repeat;
    overflow: hidden;

    .login-form {
      position: relative;
      width: 450px;
      max-width: 100%;
      padding: 200px 35px 0;
      margin: 0px 60px;
      overflow: hidden;
    }

    .tips {
      font-size: 14px;
      color: #fff;
      margin-bottom: 10px;

      span {
        &:first-of-type {
          margin-right: 16px;
        }
      }
    }

    .svg-container {
      padding: 6px 5px 6px 15px;
      color: $dark_gray;
      vertical-align: middle;
      width: 30px;
      display: inline-block;
    }

    .title-container {
      position: relative;

      .title {
        font-size: 22px;
        color: $light_gray;
        margin: 0px auto 40px auto;
        text-align: center;
        font-weight: bold;
      }
    }

    .show-pwd {
      position: absolute;
      right: 10px;
      top: 7px;
      font-size: 16px;
      color: $dark_gray;
      cursor: pointer;
      user-select: none;
    }

    .thirdparty-button {
      position: absolute;
      right: 0;
      bottom: 6px;
    }

    @media only screen and (max-width: 470px) {
      .thirdparty-button {
        display: none;
      }
    }

    .alert{
      background-color: #f5f6f8;
      color: #409eff;
      margin-bottom: 15px;
    }
}

</style>
