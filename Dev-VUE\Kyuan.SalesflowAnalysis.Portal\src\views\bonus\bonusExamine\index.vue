<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="8">
          <el-date-picker
            v-model="listQuery.timeRange"
            clearable
            class="filter-item"
            style="width:100%"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            format="yyyy-MM"
            value-format="yyyy-MM"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.bonusCategoryId"
            :loading="calculateCategoryLoading"
            class="filter-item"
            placeholder="奖励类型"
            clearable
            @change="selectUpdate"
          >
            <el-option
              v-for="item in calculateCategoryQueryList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.enumCalculateStatus"
            :loading="deptLoading"
            class="filter-item"
            placeholder="状态"
            clearable
          >
            <el-option
              v-for="item in calculateStatusList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'MyBonusExamine_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              header-align="center"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              label="计算周期"
              header-align="center"
              align="center"
              min-width="120px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.startDate | parseTime('{y}.{m}') }}-{{ row.endDate | parseTime('{y}.{m}') }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="EnumCalculateType"
              label="奖励类型"
              header-align="center"
              align="center"
              min-width="80px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumCalculateTypeDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="TotalAmount"
              label="奖励总金额（元）"
              header-align="center"
              align="right"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.totalAmount | toTwoNum | toThousandFilter }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="EnumCalculateStatus"
              label="状态"
              header-align="center"
              align="center"
              min-width="80px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumCalculateStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="CalculateTime"
              label="计算时间"
              align="center"
              min-width="120px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.calculateTime }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="120" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i v-if="row.enumCalculateStatus !== 1 && row.enumCalculateStatus !== 2 && row.enumCalculateStatus !== 20" class="el-icon-document eltablei" title="查看明细" @click="reviewDetail(row)" />
                <i v-if="row.enumCalculateStatus == 30" class="el-icon-circle-check eltablei" title="审批完成" @click="handleApproved(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>

    <el-dialog
      :close-on-click-modal="false"
      title="奖励明细"
      :visible="dialogBonusDetailFormVisible"
      width="80%"
      class="popup-search"
      @close="btnBonusDetailClose"
    >
      <div class="search-container-bg">
        <el-row :gutter="10">
          <el-col :span="span">
            <el-input
              v-model="employeeListQuery.displayName"
              clearable
              placeholder="员工姓名"
              class="filter-item"
              @keyup.enter.native="getBonusSummaryDetailList"
            />
          </el-col>
          <el-col :span="span">
            <el-select
              v-model="employeeListQuery.isGrant"
              style="width: 100%"
              class="filter-item"
              placeholder="是否发放"
              clearable
            >
              <el-option
                v-for="item in grantList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="span">
            <el-select
              v-model="employeeListQuery.isWaring"
              style="width: 100%"
              class="filter-item"
              placeholder="是否警告"
              clearable
            >
              <el-option
                v-for="item in warningList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="span">
            <el-select
              v-model="employeeListQuery.enumBonusApprovalStatus"
              :loading="enumBonusApprovalStatusListLoading"
              class="filter-item"
              placeholder="审核状态"
              clearable
            >
              <el-option
                v-for="item in enumBonusApprovalStatusList"
                :key="item.value"
                :label="item.desc"
                :value="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button class="filter-item" type="primary" icon="el-icon-search" @click="getBonusSummaryDetailList">
              查询
            </el-button>
          </el-col>
        </el-row>
        <el-row type="flex" :gutter="10" justify="end" class="mainButtonRow">
          <el-col :span="3.5">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-download"
              @click="onShowExportModal"
            >
              导出
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table
              :data="bonusResultEmployeeList"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%;"
              :default-sort="{prop: 'createTime', order: 'descending'}"
              :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass"
              @sort-change="bonusEmployeeSortChange"
            >
              <el-table-column fixed label="序号" :index="indexMethodEmployee" type="index" align="center" />
              <el-table-column sortable="custom" prop="Employee.JobNo" label="工号" align="center" min-width="80px">
                <template slot-scope="{ row }">
                  <span>{{ row.employeeJobNo }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="Employee.DisplayName" label="员工姓名" align="center" min-width="100px">
                <template slot-scope="{ row }">
                  <span>{{ row.employeeDisplayName }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="ActualAmount" label="金额（元）" align="right" header-align="center" min-width="100px">
                <template slot-scope="{ row }">
                  <span>{{ row.actualAmount | toTwoNum | toThousandFilter }}</span>
                </template>
              </el-table-column>
              <el-table-column label="是否发放" align="center" min-width="80px">
                <template slot-scope="{ row }">
                  <span>{{ row.isGrant ? '是' : '否' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="审核状态" align="center" min-width="120px">
                <template slot-scope="{ row }">
                  <span>{{ row.enumBonusApprovalStatusDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column label="警告原因" align="center" min-width="130px">
                <template slot-scope="{ row }">
                  <span>{{ row.enumBonusWaringFlagsDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column label="备注" align="center" min-width="130px">
                <template slot-scope="{ row }">
                  <span>{{ row.adjustReason }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                header-align="center"
                width="120"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="{ row }">
                  <i class="el-icon-document eltablei" title="查看明细" @click="reviewPersonalDetails(row)" />
                  <i v-if="row.enumBonusApprovalStatus == 10 && $isPermitted($store.getters.user, 'MyBonusExamine_Button_Approve_FinishApprove')" class="el-icon-circle-check eltablei" title="审批" @click="handleShowApproveEmployeeBonus(row)" />
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col class="el-colRight">
            <pagination v-show="bonusResultEmployeeTotal > 0" :total="bonusResultEmployeeTotal" :page.sync="employeeListQuery.pageIndex" :limit.sync="employeeListQuery.pageSize" @pagination="getBonusSummaryDetailList" />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnBonusDetailClose()"> 关闭 </el-button>
      </div>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      title="人员奖励明细"
      :visible="dialogPersonalDetailFormVisible"
      width="80%"
      @close="btnPersonalDetailsClose"
    >
      <el-row class="query-title" type="flex" :gutter="10">
        <el-col :span="5">
          <label>工号：{{ jobNo }}</label>
        </el-col>
        <el-col :span="6">
          <label>员工姓名：{{ employeeDisplayName }}</label>
        </el-col>
      </el-row>
      <el-row style="margin-bottom:20px;">
        <el-col :span="24">
          <el-table
            :data="bonusResultProjectList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
          >
            <el-table-column fixed label="序号" type="index" align="center" />

            <el-table-column label="计算月份" align="center" min-width="120px">
              <template slot-scope="{ row }">
                <span>{{ row.bonusResultStartDate | parseTime('{y}.{m}') }}-{{ row.bonusResultEndDate | parseTime('{y}.{m}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="实发总奖励金额（元）" align="right" header-align="center" min-width="120px">
              <template slot-scope="{ row }">
                <span>{{ row.bonusAmount | toTwoNum | toThousandFilter }}</span>
              </template>
            </el-table-column>
            <el-table-column label="应发项目奖励金额（元）" align="left" header-align="center" min-width="160px">
              <template slot-scope="{ row }">
                <span v-html="row.projectAmountStr" />
              </template>
            </el-table-column>
            <el-table-column label="岗位" align="center" min-width="120px">
              <template slot-scope="{ row }">
                <span>{{ row.stationNames }}</span>
              </template>
            </el-table-column>
            <el-table-column label="计算开始日期" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span v-if="row.stationStartDate">{{ row.stationStartDate | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="计算截止日期" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span v-if="row.stationEndDate">{{ row.bonusResultEndDate | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否舍弃" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span>{{ row.isUseDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="警告原因" align="center" min-width="150px">
              <template slot-scope="{ row }">
                <span>{{ row.enumBonusWaringFlagDesc }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnPersonalDetailsClose()"> 关闭 </el-button>
      </div>
    </el-dialog>

    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      :visible="showExportModal"
      :close-on-click-modal="false"
      title="导出奖励明细"
      width="800"
      @close="handleExportCancel"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      title="奖励审批"
      :visible="dialogApploveEmployeeBonusVisible"
      width="80%"
      @close="handleCloseApproveEmployeeBonus"
    >
      <el-row class="query-title" type="flex" :gutter="10">
        <el-col :span="5">
          <label>工号：{{ jobNo }}</label>
        </el-col>
        <el-col :span="6">
          <label>员工姓名：{{ employeeDisplayName }}</label>
        </el-col>
      </el-row>

      <el-row style="margin-bottom:20px;">
        <el-col :span="24">
          <el-table
            :data="bonusHistoryList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
          >
            <el-table-column fixed label="序号" type="index" align="center" />

            <el-table-column label="操作人" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span>{{ row.userDisplayName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="处理时间" align="center" min-width="120px">
              <template slot-scope="{ row }">
                <span>{{ row.handleTime | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="处理内容" align="left" header-align="center" min-width="120px">
              <template slot-scope="{ row }">
                <span v-if="row.oldValue">{{ row.enumBonusHandleTypeDesc }}由{{ row.oldValue }}变更为{{ row.newValue }}</span>
                <span v-if="!row.oldValue">{{ row.enumBonusHandleTypeDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="备注" align="left" header-align="center" min-width="150px">
              <template slot-scope="{ row }">
                <span>{{ row.remark }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-form
        ref="approveForm"
        :rules="approveRules"
        :model="approve"
        label-position="right"
        label-width="115px"
        class="el-dialogform"
      >
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="approve.remark"
                type="textarea"
                :rows="2"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleCloseApproveEmployeeBonus()"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleApproveEmployeeBonusSave()"
        >
          审核
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleRejectEmployeeBonusSave()"
        >
          拒绝
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import ManufacturerService from '@/api/manufacturer'
import MaintenanceService from '@/api/maintenance'
import MasterDataService from '@/api/masterData'
import BonusService from '@/api/bonus'
import CustomExport from '@/components/Export/CustomExport'

export default {
  components: {
    Pagination,
    CustomExport
  },
  data() {
    return {
      span: 4,
      total: 0,
      bonusResultEmployeeTotal: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      employeeListQuery: {
        displayName: '',
        isGrant: '',
        isWaring: '',
        enumBonusApprovalStatus: '',
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      list: [],
      bonusResultId: undefined,
      bonusResultEmployee: {
        employeeDisplayName: '',
        positionName: '',
        provinces: '',
        activeName: ''
      },
      bonusResultEmployeeList: [],
      bonusResultProjectList: [],
      bonusHistoryList: [],
      jobNo: '',
      employeeDisplayName: '',
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: {},
      dialogEditFormVisible: false,
      dialogBonusDetailFormVisible: false,
      dialogPersonalDetailFormVisible: false,
      manufacturerLoading: false,
      deptLoading: false,
      calculateTypeList: [],
      bonusCalculation: {
        months: '',
        departmentId: undefined,
        enumCalculateType: undefined
      },
      manufacturerList: [],
      deptList: [],
      calculateStatusList: [],
      isDisabled: false,
      adjust: {
        id: undefined,
        newValue: '',
        remark: ''
      },
      activeName: 'first',
      grantList: [{
        value: 'true',
        label: '是'
      },
      {
        value: 'false',
        label: '否'
      }],
      warningList: [
        {
          value: 'true',
          label: '是'
        },
        {
          value: 'false',
          label: '否'
        }
      ],
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6
        }
      },
      enumCalculateType_SalesBonus: 10, // 销量奖励
      enumCalculateType_IncrementBonus: 20, // 增量奖励
      bonusResult: null,

      enumCalculateStatus_Calculated: 10, // 已计算
      enumCalculateStatus_WaitingReview: 33, // 待审核(部门总监已审核)
      enumCalculateStatus_NotApproved: 35, // 审核未通过
      isAllowAdjust: false,
      enumBonusApprovalStatusListLoading: false,
      enumBonusApprovalStatusList: [],
      dialogApploveEmployeeBonusVisible: false,
      approve: { remark: undefined },
      approveRules: {
        remark: [
          { max: 500, message: '备注长度必须小于等于 500 个字符', trigger: 'blur' }
        ]
      },
      calculateCategoryLoading: false,
      calculateCategoryQueryList: []
    }
  },
  created() {
    if (this.$route.query.fromPage) {
      this.listQuery.enumCalculateStatus = this.$constDefinition.calculateStatus.WaitingReview
    }
    this.initManufacturer()
    this.getList()
    this.initDept()
    this.initCalculateStatus()
    this.initCalculateType()
    this.initBonusApprovalStatus()
  },
  methods: {
    initManufacturer() {
      this.manufacturerLoading = true
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturerLoading = false
          this.manufacturerList = result
        })
        .catch(() => {
          this.manufacturerLoading = false
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch(() => {
          this.deptLoading = false
        })
    },
    initCalculateType() {
      this.calculateCategoryLoading = true
      BonusService.QueryBonusCategorySelect()
        .then((result) => {
          this.calculateCategoryLoading = false
          this.calculateCategoryQueryList = JSON.parse(JSON.stringify(result))
        })
        .catch((error) => {
          this.calculateCategoryLoading = false
          console.log(error)
        })
    },
    initCalculateStatus() {
      var param = {
        enumType: 'DepartmentCalculateStatus'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.calculateStatusList = result.data.datas
        })
        .catch(() => {
        })
    },
    initBonusApprovalStatus() {
      this.enumBonusApprovalStatusListLoading = true
      var param = {
        enumType: 'BonusApprovalStatus'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.enumBonusApprovalStatusListLoading = false
          this.enumBonusApprovalStatusList = result.data.datas
        })
        .catch((error) => {
          this.enumBonusApprovalStatusListLoading = false
          console.log(error)
        })
    },
    getList() {
      this.listLoading = true
      BonusService.QueryDepartBonusResult(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(() => {
        this.listLoading = false
      })
    },
    getBonusResultEmployee(id) {
      BonusService.GetBonusResultEmployee({ id: id })
        .then((result) => {
          this.bonusResultEmployee = Object.assign({}, result.data)
        })
        .catch(() => {
        })
    },
    getBonusSummaryDetailList() {
      this.listLoading = true
      this.employeeListQuery.bonusResultId = this.bonusResultId
      BonusService.QueryBonusResultEmployee(this.employeeListQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.bonusResultEmployeeList = result.data.datas
          this.bonusResultEmployeeTotal = result.data.recordCount
          this.employeeListQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(() => {
        this.listLoading = false
      })
    },
    getProjectBonusList(id) {
      this.listLoading = true
      BonusService.QueryBonusResultEmployeeDetail({ bonusResultEmployeeId: id }).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.bonusResultProjectList = result.data.datas
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(() => {
        this.listLoading = false
      })
    },
    getBonusHistoryList(id) {
      this.listLoading = true
      BonusService.QueryBonusResultEmployeeHistory({ bonusResultEmployeeId: id }).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.bonusHistoryList = result.data.datas
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(() => {
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    indexMethodEmployee(index) {
      return (this.employeeListQuery.pageIndex - 1) * this.employeeListQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    bonusEmployeeSortChange(column, prop, order) {
      this.employeeListQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.employeeListQuery.order = orderSymbol + column.prop
      this.getBonusSummaryDetailList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    reviewDetail(row) {
      this.dialogBonusDetailFormVisible = true
      this.bonusResult = row
      this.isAllowAdjust = false
      if (this.bonusResult.enumCalculateStatus === this.enumCalculateStatus_Calculated ||
      this.bonusResult.enumCalculateStatus === this.enumCalculateStatus_NotApproved) {
        this.isAllowAdjust = true
      }
      this.bonusResultId = row.id
      this.employeeListQuery.pageIndex = 1
      this.$nextTick(() => {
        this.getBonusSummaryDetailList()
      })
    },
    btnBonusDetailClose() {
      this.employeeListQuery.displayName = ''
      this.employeeListQuery.isGrant = ''
      this.employeeListQuery.isWaring = ''
      this.employeeListQuery.enumBonusApprovalStatus = ''
      this.dialogBonusDetailFormVisible = false
    },
    handleApproved(row) {
      this.$confirm('确定审核完毕?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        BonusService.ApproveDepartmentBonus(row).then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('奖励已审核通过', 'success')
          }
        })
          .catch(() => {
            this.listLoading = false
          })
      })
    },
    reviewPersonalDetails(row) {
      this.dialogPersonalDetailFormVisible = true
      this.jobNo = row.employeeJobNo
      this.employeeDisplayName = row.employeeDisplayName
      this.$nextTick(() => {
        this.getProjectBonusList(row.id)
      })
    },
    btnPersonalDetailsClose() {
      this.dialogPersonalDetailFormVisible = false
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      BonusService.GetBonusResultEmployeeDetailExportColumn({ bonusResultId: this.bonusResult.id }).then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      this.employeeListQuery.checkedColumns = checkColumns

      BonusService.ExportBonusResultEmployeeDetail(this.employeeListQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '奖励明细.xlsx'

          this.employeeListQuery.checkedColumns = null
          fileDownload(result.data, filename)
        })
        .catch(() => {
          this.employeeListQuery.checkedColumns = null
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    handleShowApproveEmployeeBonus(row) {
      this.jobNo = row.employeeJobNo
      this.employeeDisplayName = row.employeeDisplayName
      this.getBonusHistoryList(row.id)
      this.bonusResultEmployee.id = row.id
      this.getBonusResultEmployee(row.id)
      this.dialogApploveEmployeeBonusVisible = true
    },
    handleCloseApproveEmployeeBonus() {
      this.dialogApploveEmployeeBonusVisible = false
      this.approve.remark = ''
      this.$refs['approveForm'].resetFields()
    },
    handleApproveEmployeeBonusSave() {
      this.$refs['approveForm'].validate((valid) => {
        if (valid) {
          this.approve.bonusResultEmployeeId = this.bonusResultEmployee.id
          BonusService.ApproveDepartmentEmployeeBonus(this.approve)
            .then((result) => {
              if (result.succeed) {
                this.handleCloseApproveEmployeeBonus()
                this.getBonusSummaryDetailList()
                this.showMessage('审批成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
            })
        }
      })
    },
    handleRejectEmployeeBonusSave() {
      if (!this.approve.remark) {
        this.$notice.message('拒绝时必须填写备注', 'error')
        return
      }
      this.$refs['approveForm'].validate((valid) => {
        if (valid) {
          this.approve.bonusResultEmployeeId = this.bonusResultEmployee.id
          BonusService.RejectDepartmentEmployeeBonus(this.approve)
            .then((result) => {
              if (result.succeed) {
                this.handleCloseApproveEmployeeBonus()
                this.getBonusSummaryDetailList()
                this.showMessage('拒绝成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
            })
        }
      })
    },
    selectUpdate() {
      this.$forceUpdate()
    }
  }
}
</script>
<style>
.query-title{
  margin-bottom: 17px;
}
.filter-item{
  margin: 0 10px 5px 0;
}
.table-title{
  font-weight:bold;
  font-size:13px;
  margin-bottom: 20px;
}

.timeline-title{
  font-weight:bold;
}
</style>
