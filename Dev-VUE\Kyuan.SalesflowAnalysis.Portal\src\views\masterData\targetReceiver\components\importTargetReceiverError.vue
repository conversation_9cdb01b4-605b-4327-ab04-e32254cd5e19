<template>
  <div class="list-container">
    <el-row type="flex" justify="end" :gutter="10" style="margin-bottom:10px">
      <el-col :span="3.5">
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-download"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-table
          :data="list"
          stripe
          border
          fit
          highlight-current-row
          style="width: 100%;"
          :default-sort="{prop: 'createTime', order: 'descending'}"
          :header-cell-class-name="'tableStyle'"
          :row-class-name="handleRowClass"
          @sort-change="sortChange"
        >
          <el-table-column align="center" fixed label="序号" :index="indexMethod" type="index" />
          <el-table-column align="left" sortable="custom" prop="DepartmentName" label="部门" min-width="110px" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.departmentName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="left" sortable="custom" prop="ManufacturerName" label="厂商" min-width="180px" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.manufacturerName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="ProductNameCn" label="产品" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.productNameCn }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="Specification" label="规格" min-width="120px">
            <template slot-scope="{ row }">
              <span>{{ row.specification }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="ReceiverName" label="客户" min-width="220px">
            <template slot-scope="{ row }">
              <span>{{ row.receiverName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="CustomerCategoryName" label="客户类型" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.customerCategoryName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="StartDate" label="开始日期" min-width="110px">
            <template slot-scope="{ row }">
              <span>{{ row.startDate }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="EndDate" label="结束日期" min-width="130px">
            <template slot-scope="{ row }">
              <span>{{ row.endDate }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="EmployeeName" label="人员" min-width="130px">
            <template slot-scope="{ row }">
              <span>{{ row.employeeName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="StationName" label="岗位" min-width="130px">
            <template slot-scope="{ row }">
              <span>{{ row.stationName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="left" sortable="custom" prop="ErrorMessage" label="错误信息" min-width="180px" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.errorMessage }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col class="el-colRight">
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.pageIndex"
          :limit.sync="listQuery.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
  </div>
</template>
<script>
import TargetReceiverService from '@/api/targetReceiver'
import Pagination from '@/components/Pagination'

export default {
  components: {
    Pagination
  },
  props: {
    importMasterTempId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        importMasterTempId: null,
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
    }
  },
  watch: {
    importMasterTempId: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        this.listQuery.importMasterTempId = val
        this.getList()
      }
    }
  },
  methods: {
    getList() {
      TargetReceiverService.QueryImportTargetReceiverError(this.listQuery).then(res => {
        this.list = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(res => {})
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    // 确认导出
    handleExport() {
      TargetReceiverService.ExportImportTargetReceiverError(this.listQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '目标客户错误数据.xlsx'

          fileDownload(result.data, filename)
        })
        .catch((error) => {
          console.log(error)
        })
    }
  }
}
</script>
  <style scoped>
    .el-dialog-s {
    z-index: 12;
  }
  </style>

