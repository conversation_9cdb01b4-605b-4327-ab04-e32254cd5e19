<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="span">
          <el-select
            v-model="listQuery.manufacturerId"
            :loading="manufacturerLoading"
            class="filter-item"
            placeholder="厂商名称"
            clearable
            @change="manufacturerChange"
          >
            <el-option
              v-for="item in manufacturerList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProductAndSpec"
            v-model="productAndSpecId"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            placeholder="产品"
            class="filter-item"
            clearable
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            @change="productChange"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'ProductAlias_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'ProductAlias_Button_Add')"
            type="primary"
            icon="el-icon-plus"
            @click="handleCreate"
          >
            新增
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'ProductAlias_Button_Export')"
            :loading="btnExportLoading"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              label="厂商名称"
              sortable="custom"
              min-width="100px"
              header-align="center"
              align="center"
              prop="Manufacturer.Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.manufacturerName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="产品名称"
              sortable="custom"
              min-width="150px"
              header-align="center"
              align="center"
              prop="ProductSpec.Product.NameCn"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="通用名"
              sortable="custom"
              min-width="150px"
              header-align="center"
              align="left"
              prop="ProductSpec.Product.CommonName"
            >
              <template slot-scope="{ row }">
                <span>{{ row.commonName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="规格" align="center" sortable="custom" min-width="100px" prop="ProductSpec.Spec">
              <template slot-scope="{ row }">
                <span>{{ row.spec }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="通用名别名"
              sortable="custom"
              min-width="150px"
              header-align="center"
              align="left"
              prop="ProductAliasName"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productAliasName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="规格别名" align="center" sortable="custom" min-width="100px" prop="ProductSpecAlias">
              <template slot-scope="{ row }">
                <span>{{ row.productSpecAlias }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i v-if="$isPermitted($store.getters.user, 'ProductAlias_Button_Edit')" class="el-icon-edit-outline eltablei" @click="handleUpdate(row)" />
                <i v-if="$isPermitted($store.getters.user, 'ProductAlias_Button_Del')" class="el-icon-delete eltablei" @click="handleDelete(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
    <modifyProductAlias
      v-if="dialogEditFormVisible"
      :id="itemId"
      :title="modifyDialogTitle"
      :view-model="modifyDialogIsReadonly"
      @hidden="onHidden()"
      @refresh="onRefresh()"
    />
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出产品别名"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import CustomExport from '@/components/Export/CustomExport'
import ManufacturerService from '@/api/manufacturer'
import ProductService from '@/api/product'
import ProductAliasService from '@/api/productAlias'
import modifyProductAlias from './components/modifyProductAlias'

export default {
  name: 'ProductAlias',
  components: {
    Pagination,
    CustomExport,
    modifyProductAlias
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      list: [],
      manufacturerLoading: false,
      productAndSpecLoading: false,
      manufacturerList: [],
      productAndSpecList: null,
      productAndSpecId: [],
      btnExportLoading: false,
      dialogEditFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑产品别名',
        create: '新增产品别名'
      },
      modifyDialogTitle: '',
      modifyDialogIsReadonly: false,
      itemId: null,
      showExportModal: false,
      columnDictionary: {}
    }
  },
  created() {
    this.initManufacturer()
    this.initProductAndSpec()
    this.handleFilter()
  },
  methods: {
    initManufacturer() {
      this.manufacturerLoading = true
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturerLoading = false
          this.manufacturerList = result
        })
        .catch((error) => {
          this.manufacturerLoading = false
          console.log(error)
        })
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      const para = { manufacturerId: this.listQuery.manufacturerId }
      ProductService.QueryProductAndSpecCascader(para)
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    manufacturerChange() {
      this.initProductAndSpec()
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      if (this.productAndSpecId && this.productAndSpecId.length > 0) {
        if (this.productAndSpecId.length === 1) {
          this.listQuery.productId = this.productAndSpecId[0]
        } if (this.productAndSpecId.length > 1) {
          this.listQuery.productSpecId = this.productAndSpecId[1]
        }
      } else {
        this.listQuery.productId = null
        this.listQuery.productSpecId = null
      }
      this.listLoading = true
      ProductAliasService.QueryProductAlias(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    handleCreate() {
      this.itemId = null
      this.modifyDialogIsReadonly = false
      this.dialogEditFormVisible = true
      this.dialogStatus = 'create'
      this.modifyDialogTitle = this.textMap[this.dialogStatus]
    },
    handleUpdate(row) {
      this.itemId = row.id
      this.modifyDialogIsReadonly = true
      this.dialogEditFormVisible = true
      this.dialogStatus = 'update'
      this.modifyDialogTitle = this.textMap[this.dialogStatus]
    },
    handleDelete(row) {
      this.$confirm('确定删除此产品别名吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        ProductAliasService.DeleteProductAlias(row).then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('删除成功', 'success')
          }
        })
          .catch(error => {
            this.listLoading = false
            console.log(error)
          })
      }).catch(error => {
        this.listLoading = false
        if (!error.succeed) {
          this.$notice.message('取消删除', 'info')
        }
      })
    },
    onHidden() {
      this.dialogEditFormVisible = false
    },
    onRefresh() {
      this.getList()
      this.dialogEditFormVisible = false
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      ProductAliasService.GetProductAliasExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      if (this.productAndSpecId && this.productAndSpecId.length > 0) {
        if (this.productAndSpecId.length === 1) {
          this.listQuery.productId = this.productAndSpecId[0]
        } if (this.productAndSpecId.length > 1) {
          this.listQuery.productSpecId = this.productAndSpecId[1]
        }
      } else {
        this.listQuery.productId = null
        this.listQuery.productSpecId = null
      }
      this.listQuery.checkedColumns = checkColumns
      ProductAliasService.ExportProductAlias(this.listQuery)
        .then(result => {
          this.listQuery.checkedColumns = null
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '产品别名.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '产品别名.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    productChange() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    }
  }
}
</script>
