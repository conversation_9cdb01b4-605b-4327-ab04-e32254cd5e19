import HttpApi from './libs/api.request'

const controller = 'AccountingPrice'

const api = new HttpApi(controller)

export default {
  QueryAccountingPrice(params) {
    return api.get('QueryAccountingPrice', params)
  },
  GetAccountingPrice(params) {
    return api.get('GetAccountingPrice', params)
  },
  AddAccountingPrice(params) {
    return api.post('AddAccountingPrice', params)
  },
  UpdateAccountingPrice(params) {
    return api.post('UpdateAccountingPrice', params)
  },
  DeleteAccountingPrice(params) {
    return api.post('DeleteAccountingPrice', params)
  },
  GetAccountingPriceExportColumn() {
    return api.get('GetAccountingPriceExportColumn')
  },
  ExportAccountingPrice(params) {
    return api.post('ExportAccountingPrice',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  ExportAccountingPriceError(params) {
    return api.post('ExportAccountingPriceError', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryImportAccountingPriceError(params) {
    return api.get('QueryImportAccountingPriceError', params)
  },
  QueryImportAccountingPriceMasterTemp(params) {
    return api.get('QueryImportAccountingPriceMasterTemp', params)
  }
}
