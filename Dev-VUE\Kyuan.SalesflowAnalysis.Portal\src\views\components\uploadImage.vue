<template>
  <div>
    <el-upload
      ref="upload"
      class="upload-demo"
      action="customize"
      list-type="picture-card"
      :auto-upload="false"
      :on-change="(file,fileList)=>{return onChangeFile(file,fileList)}"
      :file-list="fileList"
      :limit="10"
    >
      <i slot="default" class="el-icon-plus" />
      <div slot="file" slot-scope="{file}">
        <img
          class="el-upload-list__item-thumbnail"
          :src="file.url"
          alt=""
        >
        <span class="el-upload-list__item-actions">
          <span
            class="el-upload-list__item-preview"
            @click="handlePictureCardPreview(file)"
          >
            <i class="el-icon-zoom-in" />
          </span>

          <span
            v-if="!disabled"
            class="el-upload-list__item-delete"
            @click="handleRemove(file)"
          >
            <i class="el-icon-delete" />
          </span>
        </span>
      </div>
      <div slot="tip" style="margin-top:5px;">
        <span class="info"><i class="el-alert__icon el-icon-info" /><span style="padding:0 8px">仅支持.jpg、png，且不超过5M</span></span>
      </div>
    </el-upload>
    <el-dialog :modal="false" :close-on-click-modal="false" :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dialogImageUrl: '',
      dialogVisible: false,
      disabled: false,
      fileList: [],
      accept: ['.jpg', '.png'],
      baseSize: 1024,
      limitSize: 5
    }
  },
  methods: {
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    onChangeFile(file, fileList) {
      const ext = file.name.substring(file.name.lastIndexOf('.'))
      const isAccept = this.accept.findIndex(f => f === ext) > -1
      const index = this.$refs.upload.uploadFiles.findIndex(item => item.uid === file.uid)
      if (!isAccept) {
        // 删除文件
        this.$refs.upload.uploadFiles.splice(index, 1)

        this.$notice.message(`上传图片格式仅支持${this.accept.join(',')}`, 'error')
      } else {
        if (file.size === 0) {
          // 删除文件
          this.$refs.upload.uploadFiles.splice(index, 1)
          this.$notice.message('请不要上传空图片', 'error')
        } else {
          const isLessLimitSize = file.size / this.baseSize < this.baseSize * this.limitSize

          if (!isLessLimitSize) {
            // 删除文件
            this.$refs.upload.uploadFiles.splice(index, 1)
            this.$notice.message(`${'上传图片大小不能超过 '}${this.limitSize}${'M!'}`, 'error')
          } else {
            this.fileList.push(file)
            this.$emit('getUploadFile', this.fileList)
          }
        }
      }
    },
    handleRemove(file, fileList) {
      const index = this.fileList.findIndex(item => item.uid === file.uid)
      // 删除文件
      this.fileList.splice(index, 1)
      this.$emit('getUploadFile', this.fileList)
    },
    handleClear() {
      this.fileList = []
    }
  }
}
</script>
<style scoped>
.info {
    width: 100%;
    padding: 8px 16px;
    margin: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
    opacity: 1;
    -webkit-transition: opacity .2s;
    transition: opacity .2s;
    background-color: #f4f4f5;
    color: #909399;
}
</style>
