<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filter-container">
        <el-col :span="8">
          <el-date-picker
            v-model="listQuery.calculateTime"
            clearable
            class="filter-item"
            type="monthrange"
            range-separator="至"
            start-placeholder="生成开始月份"
            end-placeholder="生成结束月份"
            format="yyyy-MM"
            value-format="yyyy-MM"
            :picker-options="datePickerOptions"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" justify="end" :gutter="10">
        <el-col :span="3.5">
          <el-button
            class="filter-item-button"
            type="primary"
            icon="el-icon-document"
            @click="handleGenerateDialog"
          >
            生成
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row type="flex" class="filter-container">
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            @sort-change="sortChange"
          >
            <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
            <el-table-column sortable="custom" label="生成开始月份" min-width="140px" align="center" prop="StartDate">
              <template slot-scope="{ row }">
                <span>{{ row.startDate | parseTime('{y}-{m}') }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" label="生成截止月份" min-width="140px" align="center" prop="EndDate">
              <template slot-scope="{ row }">
                <span>{{ row.endDate | parseTime('{y}-{m}') }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" label="状态" min-width="100px" align="center" prop="EnumGenerateStatus" header-align="center">
              <template slot-scope="{ row }">
                <span>{{ row.enumGenerateStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" label="操作人" min-width="100px" align="center" prop="Creator">
              <template slot-scope="{ row }">
                <span>{{ row.creator }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" label="生成时间" min-width="100px" align="center" prop="CreateTime" header-align="center">
              <template slot-scope="{ row }">
                <span>{{ row.createTime }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="生成"
      :close-on-click-modal="false"
      :visible="generateRecordVisable"
      width="40%"
      @close="closeGenerateToMongoRecordDialog"
    >
      <el-form
        ref="generateByCommercialForm"
        :model="generateToMongoRecord"
        label-position="right"
        label-width="90px"
        class="el-dialogform"
      >
        <el-form-item label="计算月份" prop="months">
          <el-date-picker
            v-model="generateToMongoRecord.generateDate"
            clearable
            class="filter-item"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            format="yyyy-MM"
            value-format="yyyy-MM"
            :picker-options="datePickerOptions"
          />
        </el-form-item>
        <label style="color:red">备注：{{ filterStartDate }}之前的数据已由服务生成，可选择之后的月份计算</label>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeGenerateToMongoRecordDialog">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="saveGenerateToMongoRecord()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import SalesFlowService from '@/api/salesFlow'
import Pagination from '@/components/Pagination'

export default {
  components: {
    Pagination
  },
  props: {
    showImport: {
      type: Boolean,
      default: null
    }
  },
  data() {
    return {
      span: 6,
      attachment: {},
      listLoading: true,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      list: [],
      filterStartDate: null,
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6
        }
      },
      generateReportVisable: false,
      generateRecordVisable: false,
      generateToMongoRecord: {}
    }
  },
  watch: {
    showImport: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        this.generateReportVisable = val
        if (this.generateReportVisable === true) {
          this.getList()
        }
      }
    }
  },
  methods: {
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList() {
      this.listLoading = true
      SalesFlowService.QueryGenerateSalesFlowToMongoRecord(this.listQuery).then(res => {
        this.listLoading = false
        this.list = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(res => {})
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    clear() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
    },
    handleGenerateDialog() {
      this.generateRecordVisable = true
      this.handelGetGenerateStartDate()
    },
    handelGetGenerateStartDate() {
      SalesFlowService.GetGenerateStartDate().then(result => {
        if (result.succeed) {
          this.filterStartDate = result.data
        } else {
          this.$notice.resultTip(result)
        }
      })
    },
    closeGenerateToMongoRecordDialog() {
      this.generateRecordVisable = false
    },
    saveGenerateToMongoRecord() {
      var params = { startDate: this.generateToMongoRecord.generateDate[0], endDate: this.generateToMongoRecord.generateDate[1] }
      SalesFlowService.ManualGenerateSalesFlowDataToMongo(params).then(result => {
        if (result.succeed) {
          this.$notice.message('生成任务已执行', 'success')
        } else {
          this.$notice.resultTip(result)
        }

        this.generateRecordVisable = false
        this.getList()
      })
    }
  }
}
</script>
    <style scoped>
      .el-dialog-s {
      z-index: 11;
    }
    </style>

