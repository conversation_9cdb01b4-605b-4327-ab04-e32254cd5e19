<template>
  <div class="list-container">
    <el-row type="flex" justify="end" :gutter="10" style="margin-bottom:10px">
      <el-col :span="3.5">
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-download"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-table
          v-if="importType === 1"
          :data="list"
          stripe
          border
          fit
          highlight-current-row
          style="width: 100%;"
          :default-sort="{prop: 'createTime', order: 'descending'}"
          :header-cell-class-name="'tableStyle'"
          :row-class-name="handleRowClass"
          @sort-change="sortChange"
        >
          <el-table-column align="center" fixed label="序号" :index="indexMethod" type="index" />
          <el-table-column align="left" sortable="custom" prop="RebateAgreementTemplateName" label="协议类型" min-width="180px" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.rebateAgreementTemplateName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="DepartmentName" label="部门" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.departmentName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="PrincipalNo" label="负责人工号" min-width="110px">
            <template slot-scope="{ row }">
              <span>{{ row.principalNo }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="RebateReceiverName" label="乙方" min-width="180px">
            <template slot-scope="{ row }">
              <span>{{ row.rebateReceiverName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="PaymentType" label="支付类型" min-width="110px">
            <template slot-scope="{ row }">
              <span>{{ row.paymentType }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="PayWithinDays" label="支付天数" min-width="130px">
            <template slot-scope="{ row }">
              <span>{{ row.payWithinDays }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="PartyCOneName" label="丙方1" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.partyCOneName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="PartyCOneName" label="丙方2" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.partyCTwoName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="PartyCPaymentWithinDays" label="丙方代付天数" min-width="130px">
            <template slot-scope="{ row }">
              <span>{{ row.partyCPaymentWithinDays }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="PartyDOneName" label="丁方1" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.partyDOneName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="PartyDOneName" label="丁方2" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.partyDTwoName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="PartyDPaymentWithinDays" label="丁方代付天数" min-width="130px">
            <template slot-scope="{ row }">
              <span>{{ row.partyDPaymentWithinDays }}</span>
            </template>
          </el-table-column>
          <el-table-column label="开始日期" align="center" header-align="center" sortable="custom" min-width="120px" prop="StartDate">
            <template slot-scope="{ row }">
              <span>{{ row.startDate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="结束日期" align="center" header-align="center" sortable="custom" min-width="120px" prop="EndDate">
            <template slot-scope="{ row }">
              <span>{{ row.endDate }}</span>
            </template>
          </el-table-column>
          <el-table-column align="left" sortable="custom" prop="ErrorMessage" label="错误信息" min-width="180px" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.errorMessage }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-table
          v-if="importType === 2"
          :data="list"
          stripe
          border
          fit
          highlight-current-row
          style="width: 100%;"
          :default-sort="{prop: 'createTime', order: 'descending'}"
          :header-cell-class-name="'tableStyle'"
          :row-class-name="handleRowClass"
          @sort-change="sortChange"
        >
          <el-table-column align="center" fixed label="序号" :index="indexMethod" type="index" />
          <el-table-column align="left" sortable="custom" prop="Code" label="协议编号" min-width="110px" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.code }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="MainAgreementCode" label="主协议编号" min-width="130px">
            <template slot-scope="{ row }">
              <span>{{ row.mainAgreementCode }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="MainAgreementName" label="主协议名称" min-width="110px">
            <template slot-scope="{ row }">
              <span>{{ row.mainAgreementName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="RebateAgreementTemplateName" label="协议模版" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.rebateAgreementTemplateName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="政策模版" align="left" header-align="center" sortable="custom" min-width="100px" prop="RebateAgreementPolicyTemplateName">
            <template slot-scope="{ row }">
              <span>{{ row.rebateAgreementPolicyTemplateName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="项目名称" align="center" header-align="center" sortable="custom" min-width="120px" prop="RebateProjectName">
            <template slot-scope="{ row }">
              <span>{{ row.rebateProjectName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="支付周期" align="center" header-align="center" sortable="custom" min-width="120px" prop="PaymentCycle">
            <template slot-scope="{ row }">
              <span>{{ row.paymentCycle }}</span>
            </template>
          </el-table-column>
          <el-table-column label="达成率上限" align="center" header-align="center" sortable="custom" min-width="120px" prop="UpperLimitAchievementRate">
            <template slot-scope="{ row }">
              <span>{{ row.upperLimitAchievementRate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="达成率下限" align="center" header-align="center" sortable="custom" min-width="120px" prop="LowerLimitAchievementRate">
            <template slot-scope="{ row }">
              <span>{{ row.lowerLimitAchievementRate }}</span>
            </template>
          </el-table-column>

          <el-table-column label="至少完成多少家门店" align="center" header-align="center" sortable="custom" min-width="155px" prop="NumberOfStores">
            <template slot-scope="{ row }">
              <span>{{ row.numberOfStores }}</span>
            </template>
          </el-table-column>
          <el-table-column label="每家门店至少销售多少品规" align="center" header-align="center" sortable="custom" min-width="130px" prop="NumberOfSpecPerStore">
            <template slot-scope="{ row }">
              <span>{{ row.numberOfSpecPerStore }}</span>
            </template>
          </el-table-column>
          <el-table-column label="销售盒数统计方式" align="center" header-align="center" sortable="custom" min-width="150px" prop="DistributionQuantityStatisticalType">
            <template slot-scope="{ row }">
              <span>{{ row.distributionQuantityStatisticalType }}</span>
            </template>
          </el-table-column>
          <el-table-column label="销售盒数" align="center" header-align="center" sortable="custom" min-width="120px" prop="DistributionQuantity">
            <template slot-scope="{ row }">
              <span>{{ row.distributionQuantity }}</span>
            </template>
          </el-table-column>
          <el-table-column label="开始日期" align="center" header-align="center" sortable="custom" min-width="120px" prop="StartDate">
            <template slot-scope="{ row }">
              <span>{{ row.startDate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="结束日期" align="center" header-align="center" sortable="custom" min-width="120px" prop="EndDate">
            <template slot-scope="{ row }">
              <span>{{ row.endDate }}</span>
            </template>
          </el-table-column>
          <el-table-column align="left" sortable="custom" prop="ErrorMessage" label="错误信息" min-width="180px" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.errorMessage }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-table
          v-if="importType === 3"
          :data="list"
          stripe
          border
          fit
          highlight-current-row
          style="width: 100%;"
          :default-sort="{prop: 'createTime', order: 'descending'}"
          :header-cell-class-name="'tableStyle'"
          :row-class-name="handleRowClass"
          @sort-change="sortChange"
        >
          <el-table-column align="center" fixed label="序号" :index="indexMethod" type="index" />
          <el-table-column align="left" sortable="custom" prop="Code" label="协议编号" min-width="110px" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.code }}</span>
            </template>
          </el-table-column>
          <el-table-column label="产品名称" align="center" header-align="center" sortable="custom" min-width="120px" prop="ProductNameCn">
            <template slot-scope="{ row }">
              <span>{{ row.productNameCn }}</span>
            </template>
          </el-table-column>
          <el-table-column label="规格" align="center" header-align="center" sortable="custom" min-width="120px" prop="Specification">
            <template slot-scope="{ row }">
              <span>{{ row.specification }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补偿单价" align="center" header-align="center" sortable="custom" min-width="120px" prop="RebateUnitPrice">
            <template slot-scope="{ row }">
              <span>{{ row.rebateUnitPrice }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Q1指标" align="center" header-align="center" sortable="custom" min-width="120px" prop="Q1Quota">
            <template slot-scope="{ row }">
              <span>{{ row.q1Quota }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Q2指标" align="center" header-align="center" sortable="custom" min-width="120px" prop="Q2Quota">
            <template slot-scope="{ row }">
              <span>{{ row.q2Quota }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Q3指标" align="center" header-align="center" sortable="custom" min-width="120px" prop="Q3Quota">
            <template slot-scope="{ row }">
              <span>{{ row.q3Quota }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Q4指标" align="center" header-align="center" sortable="custom" min-width="120px" prop="Q4Quota">
            <template slot-scope="{ row }">
              <span>{{ row.q4Quota }}</span>
            </template>
          </el-table-column>
          <el-table-column label="年度指标" align="center" header-align="center" sortable="custom" min-width="120px" prop="TotalQuota">
            <template slot-scope="{ row }">
              <span>{{ row.totalQuota }}</span>
            </template>
          </el-table-column>
          <el-table-column align="left" sortable="custom" prop="ErrorMessage" label="错误信息" min-width="180px" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.errorMessage }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-table
          v-if="importType===4"
          :data="list"
          stripe
          border
          fit
          highlight-current-row
          style="width: 100%;"
          :default-sort="{prop: 'createTime', order: 'descending'}"
          :header-cell-class-name="'tableStyle'"
          :row-class-name="handleRowClass"
          @sort-change="sortChange"
        >
          <el-table-column align="center" fixed label="序号" :index="indexMethod" type="index" />
          <el-table-column align="left" sortable="custom" prop="Code" label="协议编号" min-width="110px" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.code }}</span>
            </template>
          </el-table-column>
          <el-table-column label="产品名称" align="center" header-align="center" sortable="custom" min-width="120px" prop="ProductNameCn">
            <template slot-scope="{ row }">
              <span>{{ row.productNameCn }}</span>
            </template>
          </el-table-column>
          <el-table-column label="规格" align="center" header-align="center" sortable="custom" min-width="120px" prop="Specification">
            <template slot-scope="{ row }">
              <span>{{ row.specification }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="importType===3" label="补偿单价" align="center" header-align="center" sortable="custom" min-width="120px" prop="RebateUnitPrice">
            <template slot-scope="{ row }">
              <span>{{ row.rebateUnitPrice }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Q1指标" align="center" header-align="center" sortable="custom" min-width="120px" prop="Q1Quota">
            <template slot-scope="{ row }">
              <span>{{ row.q1Quota }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Q2指标" align="center" header-align="center" sortable="custom" min-width="120px" prop="Q2Quota">
            <template slot-scope="{ row }">
              <span>{{ row.q2Quota }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Q3指标" align="center" header-align="center" sortable="custom" min-width="120px" prop="Q3Quota">
            <template slot-scope="{ row }">
              <span>{{ row.q3Quota }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Q4指标" align="center" header-align="center" sortable="custom" min-width="120px" prop="Q4Quota">
            <template slot-scope="{ row }">
              <span>{{ row.q4Quota }}</span>
            </template>
          </el-table-column>
          <el-table-column label="年度指标" align="center" header-align="center" sortable="custom" min-width="120px" prop="TotalQuota">
            <template slot-scope="{ row }">
              <span>{{ row.totalQuota }}</span>
            </template>
          </el-table-column>
          <el-table-column label="终端编码" align="center" header-align="center" sortable="custom" min-width="120px" prop="ReceiverCode">
            <template slot-scope="{ row }">
              <span>{{ row.receiverCode }}</span>
            </template>
          </el-table-column>
          <el-table-column label="终端名称" align="center" header-align="center" sortable="custom" min-width="120px" prop="ReceiverName">
            <template slot-scope="{ row }">
              <span>{{ row.receiverName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="left" sortable="custom" prop="ErrorMessage" label="错误信息" min-width="180px" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.errorMessage }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-table
          v-if="importType === 5"
          :data="list"
          stripe
          border
          fit
          highlight-current-row
          style="width: 100%;"
          :default-sort="{prop: 'createTime', order: 'descending'}"
          :header-cell-class-name="'tableStyle'"
          :row-class-name="handleRowClass"
          @sort-change="sortChange"
        >
          <el-table-column align="center" fixed label="序号" :index="indexMethod" type="index" />
          <el-table-column label="部门名称" align="center" header-align="center" min-width="100px" prop="ProductNameCn">
            <template slot-scope="{ row }">
              <span>{{ row.departmentName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="上游商业" align="center" header-align="center" min-width="120px" prop="Specification">
            <template slot-scope="{ row }">
              <span>{{ row.distributorName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="客户名称" align="center" header-align="center" min-width="120px" prop="RebateUnitPrice">
            <template slot-scope="{ row }">
              <span>{{ row.rebateReceiverName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="项目" align="center" header-align="center" min-width="100px" prop="Q1Quota">
            <template slot-scope="{ row }">
              <span>{{ row.projectName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="产品" align="center" header-align="center" min-width="80px" prop="Q2Quota">
            <template slot-scope="{ row }">
              <span>{{ row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="规格" align="center" header-align="center" min-width="100px" prop="Q3Quota">
            <template slot-scope="{ row }">
              <span>{{ row.spec }}</span>
            </template>
          </el-table-column>
          <el-table-column label="政策类型" align="center" header-align="center" min-width="100px" prop="Q4Quota">
            <template slot-scope="{ row }">
              <span>{{ row.rebateAgreementTemplateName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="总协议量(盒)" align="center" header-align="center" min-width="120px" prop="TotalQuota">
            <template slot-scope="{ row }">
              <span>{{ row.totalQuantity }}</span>
            </template>
          </el-table-column>
          <el-table-column label="开始日期" align="center" header-align="center" min-width="100px" prop="TotalQuota">
            <template slot-scope="{ row }">
              <span>{{ row.startDate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="结束日期" align="center" header-align="center" min-width="100px" prop="TotalQuota">
            <template slot-scope="{ row }">
              <span>{{ row.endDate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补偿金额(单价)" align="center" header-align="center" min-width="130px" prop="TotalQuota">
            <template slot-scope="{ row }">
              <span>{{ row.price }}</span>
            </template>
          </el-table-column>
          <el-table-column label="最低达成率" align="center" header-align="center" min-width="120px" prop="TotalQuota">
            <template slot-scope="{ row }">
              <span>{{ row.minAchievementRate*100 }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="核算周期" align="center" header-align="center" min-width="100px" prop="TotalQuota">
            <template slot-scope="{ row }">
              <span>{{ row.paymentCycle }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" align="center" header-align="center" min-width="80px" prop="TotalQuota">
            <template slot-scope="{ row }">
              <span>{{ row.remark }}</span>
            </template>
          </el-table-column>
          <el-table-column label="终端" align="center" header-align="center" min-width="120px" prop="TotalQuota">
            <template slot-scope="{ row }">
              <span>{{ row.terminal }}</span>
            </template>
          </el-table-column>
          <el-table-column label="邮件申请政策理由" align="center" header-align="center" min-width="145px" prop="TotalQuota">
            <template slot-scope="{ row }">
              <span>{{ row.reason }}</span>
            </template>
          </el-table-column>
          <el-table-column align="left" sortable="custom" prop="ErrorMessage" label="错误信息" min-width="180px" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.errorMessage }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col class="el-colRight">
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.pageIndex"
          :limit.sync="listQuery.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
  </div>
</template>
<script>
import RebateAgreementService from '@/api/agreement'
import Pagination from '@/components/Pagination'

export default {
  components: {
    Pagination
  },
  props: {
    importMasterTempId: {
      type: String,
      default: ''
    },
    importType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        importMasterTempId: null,
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
    }
  },
  watch: {
    importMasterTempId: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        this.listQuery.importMasterTempId = val
        this.getList()
      }
    }
  },
  methods: {
    getList() {
      this.listQuery.importType = this.importType
      RebateAgreementService.QueryImportAgreementError(this.listQuery).then(res => {
        if (this.importType === 1) {
          this.list = res.data.datas[0].importRebateAgreementTempQueryList
        } else if (this.importType === 2) {
          this.list = res.data.datas[0].importSubRebateAgreementTempQueryList
        } else if (this.importType === 3) {
          this.list = res.data.datas[0].importSubAgreementProductQuotaTempQueryList
        } else if (this.importType === 4) {
          this.list = res.data.datas[0].importSubAgreementTargetReceiverTempQueryList
        } else if (this.importType === 5) {
          this.list = res.data.datas[0].importRebateAgreementSummaryTempQueryList
        }
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(res => {})
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    // 确认导出
    handleExport() {
      RebateAgreementService.ExportImportAgreementError(this.listQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '协议错误数据.xlsx'

          fileDownload(result.data, filename)
        })
        .catch((error) => {
          console.log(error)
        })
    }
  }
}
</script>
<style scoped>
  .el-dialog-s {
  z-index: 12;
}
</style>
