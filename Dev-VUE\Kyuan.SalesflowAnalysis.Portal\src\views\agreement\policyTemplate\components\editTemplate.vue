<template>
  <div>
    <el-dialog :title="title" width="70%" append-to-body :close-on-click-modal="false" :visible="editRebateAgreementPolicyVisible" @close="cancle()">
      <el-form ref="dataForm" :rules="rules" :model="agreementPolicyTypeModel" label-position="right" label-width="100px" class="myself-dialogform">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="模板名称" prop="name">
              <el-input
                v-model="agreementPolicyTypeModel.name"
                clearable
                placeholder="模板名称"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="agreementPolicyTypeModel.code!= null" :span="12">
            <el-form-item label="编码" prop="code">
              <el-input
                v-model="agreementPolicyTypeModel.code"
                clearable
                disabled
                placeholder="编码自动生成"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="政策条款类型" prop="enumCalculationTemplateType">
              <el-select
                v-model="agreementPolicyTypeModel.enumCalculationTemplateType"
                style="width: 100%"
                :disabled="isCover"
                class="filter-item"
                placeholder="政策条款类型"
                clearable
                @change="selectUpdate"
              >
                <el-option
                  v-for="item in calculationTemplateTypeList"
                  :key="item.value"
                  :label="item.desc"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="模板类型">
              <el-col :span="3.5" class="el-colRight">

                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  @click="handleAddTemplateType"
                >
                  选择模板类型
                </el-button>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="适用模板范围" prop="scope">
              <el-tag
                v-for="tag in agreementPolicyTypeModel.rebateAgreementTemplateTypes"
                :key="tag.id"
                closable
                :disable-transitions="false"
                @close="handleDeleteTemplateScope(tag)"
              >
                {{ tag.name }}
              </el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                v-model="agreementPolicyTypeModel.remark"
                type="textarea"
                clearable
                placeholder="备注"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="政策条款" prop="content">
              <el-input
                id="policyTerms"
                v-model="agreementPolicyTypeModel.content"
                type="textarea"
                rows="5"
                clearable
                placeholder="政策条款"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" :gutter="10" justify="end">
          <el-col :span="3.5">
            <el-select
              v-model="agreementPolicyTypeModel.policyTermsCode"
              style="width: 180px"
              class="filter-item"
              placeholder="请选择变量"
              clearable
              @change="selectUpdate"
            >
              <el-option
                v-for="item in policyTermsVariableList"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="3.5">
            <el-button
              type="primary"
              @click="insertVariable('policyTerms')"
            >插入变量</el-button>
          </el-col>
        </el-row>
        <el-row style="margin-top:40px;">
          <el-col :span="24">
            <el-form-item label="政策条款公式">
              <el-card class="box-card" shadow="never">
                <div slot="header">
                  <el-row type="flex" :gutter="10" justify="end">
                    <el-col :span="4.5">   <el-button type="primary" @click="handleAddFormula">新增政策条款公式</el-button></el-col>
                  </el-row>
                </div>
                <div style="margin-top: 10px;">
                  <el-table
                    :data="agreementPolicyTypeModel.rebateAgreementTermModelList"
                    stripe
                    border
                    fit
                    highlight-current-row
                    style="width: 100%;"
                    :header-cell-class-name="'tableStyle'"
                  >
                    <el-table-column
                      fixed
                      label="序号"
                      type="index"
                      align="center"
                    />
                    <el-table-column label="条件" align="center" min-width="100px" prop="conditionalDescription">
                      <template slot-scope="{ row }">
                        <span>{{ row.conditionalDescription }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="结果" align="center" min-width="110px" prop="calculatedDescription">
                      <template slot-scope="{ row }">
                        <span>{{ row.calculatedDescription }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" align="center" header-align="center" width="90" class-name="small-padding fixed-width">
                      <template slot-scope="{ row }">
                        <i class="el-icon-delete eltablei" title="删除" @click="handleDelete(row)" />
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-card>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="el-dialog-ls"
      append-to-body
      title="选择协议模板类型"
      :close-on-click-modal="false"
      :visible="dialogSelectTemplateTypeVisible"
      width="60%"
      class="popup-search"
      @close="handleCloseSelectTemplateTypeDialog"
    >
      <SelectTemplateType ref="refSelectTemplateType" :show-dialog="dialogSelectTemplateTypeVisible" @success="chooseSelectTemplateType_callback" />
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleChooseSelectTemplateType"
        >
          确认
        </el-button>
        <el-button icon="el-icon-close" @click="handleCloseSelectTemplateTypeDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <ConditionConfig ref="refConditionConfig" @add="resultAddRebateAgreementTerm" />
  </div>
</template>
<script>
import ConditionConfig from './conditionConfig'
import MasterDataService from '@/api/masterData'
import AgreementService from '@/api/agreement'
import SelectTemplateType from './selectTemplateType'

export default {
  name: '',
  components: {
    ConditionConfig,
    SelectTemplateType
  },
  props: {
  },
  data() {
    return {
      rules: {
        name: [
          {
            required: true,
            type: 'string',
            message: '请输入模板名称',
            trigger: 'change'
          }
        ],
        enumProvisionType: [
          { required: true, message: '请选择预提参数类型', trigger: 'change' }
        ],
        enumCalculationTemplateType: [
          { required: true, message: '请选择模板类型', trigger: 'change' }
        ],
        content: [
          {
            required: true,
            type: 'string',
            message: '请输入政策条款',
            trigger: 'change'
          }
        ]
      },
      agreementPolicyTypeModel: {
        rebateAgreementTermModelList: [],
        rebateAgreementTemplateTypes: []
      },
      dialogSelectTemplateTypeVisible: false,
      editRebateAgreementPolicyVisible: false,
      isShowDefaultClauseRemark: true,
      title: '新增模板',
      agreementPolicyTypeList: [],
      provisionTypeList: [],
      rebateEnvironmentVariableList: [],
      calculationTemplateTypeList: [],
      isCover: false,
      policyTermsVariableList: [],
      isModifyTerm: false
    }
  },
  created() {
    this.initProvisionType()
    this.initCalculationTemplateType()
    this.initRebateEnvironmentVariable()
    this.initPolicyTermsVariable()
  },
  methods: {
    init(data) {
      if (data != null) {
        this.title = '编辑政策条款模板'
        const para = { id: data }
        this.GetAgreementPolicyTemplate(para)
        this.isCover = true
      } else {
        this.title = '新增政策条款模板'
      }
      this.editRebateAgreementPolicyVisible = true
    },
    initProvisionType() {
      var param = {
        enumType: 'ProvisionType'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.provisionTypeList = result.data.datas
          this.$forceupdate()
        })
        .catch(() => {
        })
    },
    initCalculationTemplateType() {
      var param = {
        enumType: 'CalculationTemplateType'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.calculationTemplateTypeList = result.data.datas
          this.$forceupdate()
        })
        .catch(() => {
        })
    },
    initPolicyTermsVariable() {
      AgreementService.QueryPolicyTermsVariableCascader().then((result) => {
        this.policyTermsVariableList = result
        this.$forceupdate()
      }).catch(() => {})
    },
    initRebateEnvironmentVariable() {
      AgreementService.QueryRebateEnvironmentVariableCascader().then((result) => {
        this.rebateEnvironmentVariableList = result
        this.$forceupdate()
      }).catch(() => {
      })
    },
    selectUpdate() {
      this.$forceUpdate()
    },
    handleDelete(row) {
      const index = this.agreementPolicyTypeModel.rebateAgreementTermModelList.indexOf(row)
      if (index !== -1) {
        this.agreementPolicyTypeModel.rebateAgreementTermModelList.splice(index, 1)
        this.isModifyTerm = true
      }
    },
    save() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.agreementPolicyTypeModel.id === null || this.agreementPolicyTypeModel.id === undefined) {
            AgreementService.AddAgreementPolicyTemplate(this.agreementPolicyTypeModel).then((result) => {
              if (result.succeed) {
                this.$notice.message('保存成功', 'success')
                this.close()
              } else {
                this.ShowTip(result)
              }
            })
          } else {
            if (this.agreementPolicyTypeModel.isAgreementUsed && this.isModifyTerm) {
              this.$confirm('该模版其他协议已经使用，修改后的公式会用到已使用该政策模版的协议中，是否确认 ?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              })
                .then(() => {
                  AgreementService.UpdateAgreementPolicyTemplate(this.agreementPolicyTypeModel).then((result) => {
                    if (result.succeed) {
                      this.$notice.message('保存成功', 'success')
                      this.close()
                    } else {
                      this.ShowTip(result)
                    }
                  })
                })
                .catch((error) => {
                  if (!error.succeed) {
                    this.$notice.message('取消删除', 'info')
                  }
                })
            } else {
              AgreementService.UpdateAgreementPolicyTemplate(this.agreementPolicyTypeModel).then((result) => {
                if (result.succeed) {
                  this.$notice.message('保存成功', 'success')
                  this.close()
                } else {
                  this.ShowTip(result)
                }
              })
            }
          }
        }
      })
    },
    close() {
      this.editRebateAgreementPolicyVisible = false
      this.agreementPolicyTypeModel = {}
      this.$refs['dataForm'].resetFields()

      this.isCover = false

      this.$emit('refresh')
    },
    cancle() {
      this.close()
    },
    handleAddFormula() {
      this.$refs.refConditionConfig.init('新增返利条件配置', null)
    },
    resultAddRebateAgreementTerm(data) {
      if (this.agreementPolicyTypeModel.rebateAgreementTermModelList === undefined || this.agreementPolicyTypeModel.rebateAgreementTermModelList === null) {
        this.agreementPolicyTypeModel.rebateAgreementTermModelList = []
      }
      // 判断是否存在
      if (this.agreementPolicyTypeModel.rebateAgreementTermModelList.findIndex(item => item.conditionalFormula === data.conditionalFormula) !== -1) {
        this.$notice.message('公式已存在', 'error')
      } else {
        this.isModifyTerm = true
        this.agreementPolicyTypeModel.rebateAgreementTermModelList.push(data)
      }
      this.$forceUpdate()
    },
    GetAgreementPolicyTemplate(id) {
      AgreementService.GetAgreementPolicyTemplate(id).then((result) => {
        this.agreementPolicyTypeModel = result.data
        this.$forceUpdate()
      })
        .catch((error) => {
          console.log(error)
        })
    },
    // 插入变量
    insertVariable(id) {
      if (this.agreementPolicyTypeModel.policyTermsCode === undefined || this.agreementPolicyTypeModel.policyTermsCode === '') {
        this.$notice.message('请选择变量', 'error')
        return
      }
      const insertTxt = '{{' + this.agreementPolicyTypeModel.policyTermsCode + '}}'

      var elInput = document.getElementById(id) // 获取dom
      var startPos = elInput.selectionStart
      var endPos = elInput.selectionEnd
      if (startPos === undefined || endPos === undefined) return
      var txt = elInput.value
      var result = txt.substring(0, startPos) + insertTxt + txt.substring(endPos)
      elInput.value = result
      // 这里比较重要 **给最终绑定的参数 进行赋值
      this.agreementPolicyTypeModel.content = result // 赋值
      elInput.focus()
      this.$nextTick(() => {
        elInput.selectionStart = startPos + insertTxt.length
        elInput.selectionEnd = startPos + insertTxt.length
      })

      this.agreementPolicyTypeModel.environmentVariableIds = null
    },
    handleDeleteTemplateScope(row) {
      this.agreementPolicyTypeModel.rebateAgreementTemplateTypes.map((item, index) => {
        if (item.id === row.id) {
          this.agreementPolicyTypeModel.rebateAgreementTemplateTypes.splice(index, 1)
        }
      })
    },
    handleCloseSelectTemplateTypeDialog() {
      this.dialogSelectTemplateTypeVisible = false
      this.$refs.refSelectTemplateType.clear()
    },
    chooseSelectTemplateType_callback(data) {
      this.agreementPolicyTypeModel.rebateAgreementTemplateTypes = []
      var templateTypes = []
      data.forEach(element => {
        element.productSpecId = element.id
        // element.id = null
        templateTypes.push(element)
      })
      this.agreementPolicyTypeModel.rebateAgreementTemplateTypes = templateTypes
      this.handleCloseSelectTemplateTypeDialog()
    },

    handleChooseSelectTemplateType() {
      this.$refs.refSelectTemplateType.handleCheck()
    },
    handleAddTemplateType() {
      this.dialogSelectTemplateTypeVisible = true
    }
  }

}
</script>
<style lang="css">
.uploadCss {
  float: right; color: #516e92 ;padding: 3px 0px;  font-size: 12px;font-weight: 400; background: transparent;  border-color: transparent
}
.myself-dialogform {
  margin-left: 10px;
  margin-right: 10px;
}
.el-card__header {
  padding: 8px 5px;
}

.el-card__body {
  padding: 0;
}
.col-botton {
   position: absolute;
   right: 5px;
   bottom: -20px;
}
</style>
