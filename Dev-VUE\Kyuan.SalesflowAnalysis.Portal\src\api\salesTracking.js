import HttpApi from './libs/api.request'

const controller = 'SalesTracking'

const api = new HttpApi(controller)

export default {
  QuerySalesTracking(params) {
    return api.get('QuerySalesTracking', params)
  },
  QueryImportSalesTrackingError(params) {
    return api.get('QueryImportSalesTrackingError', params)
  },
  QuerySalesTrackingExportHistory(params) {
    return api.get('QuerySalesTrackingExportHistory', params)
  },
  GetSalesTrackingExportColumn() {
    return api.get('GetSalesTrackingExportColumn')
  },
  ExportSalesTrackings(params) {
    return api.post('ExportSalesTrackings', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  GenerateSalesTrackingByCommercial(params) {
    return api.post('GenerateSalesTrackingByCommercial', params)
  }
}
