<template>
  <div>
    <el-dialog :title="title" width="70%" :close-on-click-modal="false" :visible="showAddDialog" @close="cancle()">
      <el-form ref="dataForm" :rules="rules" :model="tempData.tempFormModel" label-position="right" label-width="100px" class="el-dialogform">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="协议类型">
              <el-input
                v-model="rebateAgreementType"
                disabled
                placeholder="协议类型"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="厂商" prop="manufacturerId">
              <el-select
                v-model="tempData.tempFormModel.manufacturerId"
                style="width: 100%"
                :loading="manufacturerLoading"
                class="filter-item"
                placeholder="厂商"
                @change="manufacturerChange"
              >
                <el-option
                  v-for="item in manufacturerList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品/规格" prop="productAndSpecId">
              <el-cascader
                ref="refProductAndSpec"
                :key="productAndSpecKey"
                v-model="tempData.tempFormModel.productAndSpecId"
                style="width: 100%"
                :loading="productAndSpecLoading"
                :options="productAndSpecList"
                placeholder="产品/规格"
                clearable
                class="filter-item"
                :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
                @change="productChange"
              /></el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="通用名">
              <el-input
                v-model="tempData.tempFormModel.commonName"
                disabled
                placeholder="通用名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供货价" prop="supplyPrice">
              <el-input
                v-model="tempData.tempFormModel.supplyPrice"
                clearable
                placeholder="供货价"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="协议数量" prop="minimumPurchaseQuantity">
              <el-input
                v-model="tempData.tempFormModel.minimumPurchaseQuantity"
                clearable
                placeholder="协议数量"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="补偿单价" prop="rebateUnitPrice">
              <el-input
                v-model="tempData.tempFormModel.rebateUnitPrice"
                clearable
                placeholder="补偿单价"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="零售价格" prop="retailPrice">
              <el-input
                v-model="tempData.tempFormModel.retailPrice"
                clearable
                placeholder="零售价格"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="返点比率" prop="rebateRatio">
              <el-input
                v-model="tempData.tempFormModel.rebateRatio"
                clearable
                placeholder="返点比率"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="税率（%）" prop="taxRate">
              <el-input
                v-model="tempData.tempFormModel.taxRate"
                clearable
                placeholder="税率（%）"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="一季度指标" prop="q1Quota">
              <el-input
                v-model="tempData.tempFormModel.q1Quota"
                clearable
                placeholder="一季度指标"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="二季度指标" prop="q2Quota">
              <el-input
                v-model="tempData.tempFormModel.q2Quota"
                clearable
                placeholder="二季度指标"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="三季度指标" prop="q3Quota">
              <el-input
                v-model="tempData.tempFormModel.q3Quota"
                clearable
                placeholder="三季度指标"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="四季度指标" prop="q4Quota">
              <el-input
                v-model="tempData.tempFormModel.q4Quota"
                clearable
                placeholder="四季度指标"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标数量" prop="quotaQuantity">
              <el-input
                v-model="tempData.tempFormModel.quotaQuantity"
                clearable
                placeholder="指标数量"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="满赠产品/规格" prop="giftProductId">
              <el-cascader
                ref="refProductAndSpec"
                :key="productAndSpecKey"
                v-model="tempData.tempFormModel.giftProductId"
                style="width: 100%"
                :loading="productAndSpecLoading"
                :options="productAndSpecList"
                placeholder="满赠产品/规格"
                clearable
                class="filter-item"
                :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
                @change="productChange"
              /></el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="满赠标准" prop="taxRate">
              <el-input
                v-model="tempData.tempFormModel.taxRate"
                clearable
                placeholder="满赠标准"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="赠送数量" prop="giftQuantity">
              <el-input
                v-model="tempData.tempFormModel.giftQuantity"
                clearable
                placeholder="赠送数量"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="销售组数量" prop="salesGroupQuantity">
              <el-input
                v-model="tempData.tempFormModel.salesGroupQuantity"
                clearable
                placeholder="销售组数量"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="销售组返利金额" prop="salesGroupRebate">
              <el-input
                v-model="tempData.tempFormModel.salesGroupRebate"
                clearable
                placeholder="销售组返利金额"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          v-if="!viewModel"
          :loading="btnSaveLoading"
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>

import ManufacturerService from '@/api/manufacturer'
import ProductService from '@/api/product'
import AccountingPriceService from '@/api/accountingPrice'

export default {
  name: '',
  components: {
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    viewModel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    // const validateSpec = (rule, value, callback) => {
    //   if (!this.tempData.tempFormModel.productAndSpecId.length) {
    //     callback(new Error('请选择产品/规格'))
    //   } else {
    //     callback()
    //   }
    // }
    return {
      rules: {
        // manufacturerId: [
        //   { required: true, message: '请选择厂商', trigger: 'change' }
        // ],
        // productAndSpecId: [
        //   { type: 'array', required: true, validator: validateSpec, trigger: 'change' }
        // ],
        // price: [
        //   {
        //     required: true, trigger: 'blur', message: '请填写考核价'
        //   },
        //   { pattern: /((^[1-9]\d*))(\.\d{0,4}){0,1}$/, message: '考核价仅支持正数，最多4位小数' }
        // ],
        // timeRange: [
        //   { required: true, message: '请选择价格时间', trigger: 'blur' }
        // ],
        // minimumPurchaseQuantity: [
        //   { required: true, message: '请输入协议数量', trigger: 'change' }
        // ],
        // rebateUnitPrice: [
        //   { required: true, message: '请输入补偿单价', trigger: 'change' }
        // ]
      },
      tempData: { tempFormModel: {}},
      showAddDialog: false,
      btnSaveLoading: false,
      manufacturerLoading: false,
      productAndSpecLoading: false,
      manufacturerList: [],
      productAndSpecList: [],
      productAndSpecKey: 0,
      rebateAgreementType: '',
      title: ''
    }
  },
  watch: {
    id(val) {
      this.tempData.tempFormModel.id = val
    }
  },
  mounted() {
  },
  created() {
    this.initManufacturer()
    this.rebateAgreementType = '达量返利'
  },
  methods: {
    init() {
      this.title = '新增品规'
      this.showAddDialog = true
      if (!this.id) {
        this.initProductAndSpec()
        return
      }
      this.get(this.id)
    },
    initManufacturer() {
      this.manufacturerLoading = true
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturerLoading = false
          this.manufacturerList = result
        })
        .catch((error) => {
          this.manufacturerLoading = false
          console.log(error)
        })
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      const para = { manufacturerId: this.tempData.tempFormModel.manufacturerId }
      ProductService.QueryProductAndSpecCascader(para)
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    manufacturerChange() {
      this.tempData.tempFormModel.productAndSpecId = []
      ++this.productAndSpecKey
      this.initProductAndSpec()
    },
    productChange(value) {
      if (!value || value.length === 0) {
        this.tempData.tempFormModel.commonName = ''
        return
      } else {
        const para = { id: value[0] }
        ProductService.GetProduct(para)
          .then((result) => {
            this.tempData.tempFormModel.commonName = result.data.commonName
            this.$forceUpdate()
          })
          .catch((error) => {
            console.log(error)
          })
      }
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    get(id) {
      this.btnSaveLoading = true
      AccountingPriceService.GetAccountingPrice({ id: id }).then(result => {
        this.tempData.tempFormModel = result.data
        this.manufacturerChange()
        this.tempData.tempFormModel.productAndSpecId = [result.data.productId, result.data.productSpecId]
        this.tempData.tempFormModel.timeRange = [new Date(result.data.startDate), new Date(result.data.endDate)]
        this.btnSaveLoading = false
      }).catch(error => {
        this.btnSaveLoading = false
        console.log(error)
      })
    },
    save() {
      this.showAddDialog = false
    },

    close() {
      this.showAddDialog = false
    },
    cancle() {
      this.showAddDialog = false
    }
  }

}
</script>
