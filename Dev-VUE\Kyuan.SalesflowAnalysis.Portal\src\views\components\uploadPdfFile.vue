<template>
  <div>
    <el-upload
      ref="upload"
      class="upload-demo"
      action="customize"
      :multiple="false"
      :show-file-list="true"
      :auto-upload="false"
      :on-remove="handleRemove"
      :limit="2"
      :file-list="fileList"
      :on-change="(file,fileList)=>{return onChangeFile(file,fileList)}"
    >
      <el-button size="small" type="primary" icon="el-icon-upload">选择文件</el-button>
      <div slot="tip" style="margin-left:10px; display:inline;">
        <span class="info"><i class="el-alert__icon el-icon-info" /><span style="padding:0 8px">仅支持.pdf，且不超过20M</span></span>
      </div>
    </el-upload>
  </div>
</template>

<script>
export default {
  data() {
    return {
      fileList: [],
      accept: ['.pdf'],
      baseSize: 1024,
      limitSize: 20
    }
  },
  methods: {
    onChangeFile(file, fileList) {
      const uploadFilesArr = this.$refs.upload.uploadFiles // 上传文件列表
      if (uploadFilesArr.length > 1) {
        this.$refs.upload.uploadFiles.shift()
      }

      const ext = file.name.substring(file.name.lastIndexOf('.'))
      const isAccept = this.accept.findIndex(f => f === ext) > -1
      if (!isAccept) {
        this.$notice.message(`导入文件格式仅支持${this.accept.join(',')}`, 'error')
        this.fileList = []
        this.$emit('getUploadFile', null)
      } else {
        if (file.size === 0) {
          this.$notice.message('请不要上传空文件', 'error')
          this.fileList = []
          this.$emit('getUploadFile', null)
        } else {
          const isLessLimitSize = file.size / this.baseSize < this.baseSize * this.limitSize

          if (!isLessLimitSize) {
            this.$notice.message(`${'上传文件大小不能超过 '}${this.limitSize}${'M!'}`, 'error')
            this.fileList = []
            this.$emit('getUploadFile', null)
          } else {
            this.fileList = this.$refs.upload.uploadFiles
            this.$emit('getUploadFile', file)
          }
        }
      }
    },
    handleRemove(file, fileList) {
      this.fileList = []
      this.$emit('getUploadFile', null)
    }
  }
}
</script>
<style scoped>
.info {
    width: 100%;
    padding: 8px 16px;
    margin: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
    opacity: 1;
    -webkit-transition: opacity .2s;
    transition: opacity .2s;
    background-color: #f4f4f5;
    color: #909399;
}
</style>
