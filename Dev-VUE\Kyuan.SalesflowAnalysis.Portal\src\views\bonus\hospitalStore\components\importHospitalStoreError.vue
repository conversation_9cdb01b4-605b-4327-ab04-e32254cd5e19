<template>
  <div class="list-container">
    <el-row type="flex" justify="end" :gutter="10" style="margin-bottom:10px">
      <el-col :span="3.5">
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-download"
          @click="handleExport"
        >
          导出所有数据（含正确数据）
        </el-button>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-table
          :data="list"
          stripe
          border
          fit
          highlight-current-row
          style="width: 100%;"
          :default-sort="{prop: 'createTime', order: 'descending'}"
          :header-cell-class-name="'tableStyle'"
          :row-class-name="handleRowClass"
          @sort-change="sortChange"
        >
          <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
          <el-table-column sortable="custom" prop="HospitalCode" label="医院编码" align="center" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.hospitalCode }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="HospitalName" label="医院名称" align="left" header-align="center" min-width="200px">
            <template slot-scope="{ row }">
              <span>{{ row.hospitalName }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="ProvinceName" label="所属省份" align="center" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.provinceName }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="CityName" label="所属城市" align="center" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.cityName }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="CountyName" label="所属区县" align="center" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.countyName }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="DrugStoreName" label="药店名称" align="left" header-align="center" min-width="200px">
            <template slot-scope="{ row }">
              <span>{{ row.drugStoreName }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="DrugStoreCode" label="药店编码" align="center" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.drugStoreCode }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="ProductName" label="产品名称" align="center" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="SpecificationName" label="规格" align="center" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.specificationName }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="Distance" label="距离" align="center" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.distance }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="HospitalStoreType" label="关联类型" align="center" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.hospitalStoreType }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="ErrorMessage" label="错误信息" header-align="center" align="left" min-width="150px">
            <template slot-scope="{ row }">
              <span>{{ row.errorMessage }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col class="el-colRight">
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.pageIndex"
          :limit.sync="listQuery.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
  </div>
</template>
<script>
import HospitalStoreService from '@/api/hospitalStore'
import Pagination from '@/components/Pagination'

export default {
  components: {
    Pagination
  },
  props: {
    importMasterTempId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
    }
  },
  watch: {
    importMasterTempId: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        this.listQuery.importMasterTempId = val
        this.getList()
      }
    }
  },
  methods: {
    getList() {
      HospitalStoreService.QueryImportHospitalStoreError(this.listQuery).then(res => {
        this.list = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(res => {})
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    // 确认导出
    handleExport() {
      HospitalStoreService.ExportImportHospitalStoreError(this.listQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '关联药店错误数据.xlsx'

          fileDownload(result.data, filename)
        })
        .catch(() => {

        })
    }

  }
}
</script>
<style scoped>
  .el-dialog-s {
  z-index: 12;
}
</style>
