<template>
  <div>
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      :close-on-click-modal="false"
      title="新增产品规格"
      :visible="dialogSpecEditFormVisible"
      width="90%"
      @close="btnClose"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="110px"
        class="el-dialogform"
      >
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item v-if="handleInfo.tempProductId !== undefined && handleInfo.tempProductId !== null" label="产品名称">
              {{ temp.productNameCn }}
            </el-form-item>
            <el-form-item v-else label="产品名称" prop="productNameCn">
              <el-row type="flex" :gutter="10" justify="start">
                <el-col :span="17">
                  <el-input
                    v-model="temp.productNameCn"
                    clearable
                    placeholder="产品名称"
                    maxlength="50"
                    :disabled="temp.productId !== ''"
                  />
                </el-col>
                <el-col :span="3.5">
                  <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleShowProduct">
                    选择
                  </el-button>
                </el-col>
                <el-col :span="3.5">
                  <el-button class="filter-item" type="primary" icon="el-icon-delete" @click="handleClearProduct">
                    清除
                  </el-button>
                </el-col>
              </el-row>

            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item v-if="handleInfo.tempProductId !== undefined && handleInfo.tempProductId !== null" label="英文名称">
              {{ temp.productNameEn }}
            </el-form-item>
            <el-form-item v-else label="英文名称" prop="productNameEn">
              <el-input
                v-model="temp.productNameEn"
                clearable
                placeholder="英文名称"
                maxlength="50"
                :disabled="temp.productId !== ''"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item v-if="handleInfo.tempProductId !== undefined && handleInfo.tempProductId !== null" label="通用名">
              {{ temp.productCommonName }}
            </el-form-item>
            <el-form-item v-else label="通用名" prop="productCommonName">
              <el-input
                v-model="temp.productCommonName"
                clearable
                placeholder="通用名"
                maxlength="100"
                :disabled="temp.productId !== ''"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="厂商名称">
              <el-input
                v-model="temp.manufacturerName"
                disabled
                placeholder="厂商名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="规格编码" prop="code">
              <el-input
                v-model="temp.code"
                clearable
                placeholder="规格编码"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="包装规格" prop="spec">
              <el-input
                v-model="temp.spec"
                clearable
                placeholder="包装规格"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="包装单位" prop="unit">
              <el-input
                v-model="temp.unit"
                clearable
                placeholder="包装单位"
                maxlength="10"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="剂型" prop="dosageFormCode">
              <el-select
                v-model="temp.dosageFormCode"
                style="width: 100%"
                class="filter-item"
                placeholder="剂型"
              >
                <el-option
                  v-for="item in dosageFormList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="生产厂家" prop="pharmaceuticalFactory">
              <el-input
                v-model="temp.pharmaceuticalFactory"
                clearable
                placeholder="生产厂家"
                maxlength="300"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnClose()"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="createData"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="el-dialog-ls"
      append-to-body
      title="选择产品"
      :close-on-click-modal="false"
      :visible="dialogProductVisible"
      width="90%"
      @close="closeProductDialog"
    >
      <SelectProduct ref="refSelectProduct" :show-dialog="dialogProductVisible" :manufacturer-id="temp.manufacturerId" @success="selectProductSuccess" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeProductDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import ProductService from '@/api/product'
import MasterDataService from '@/api/masterData'
import SelectProduct from '@/views/components/selectProduct'

export default {
  components: {
    SelectProduct
  },
  props: {
    productId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      span: 12,
      dosageFormList: [],
      dialogSpecEditFormVisible: false,
      dialogProductVisible: false,
      handleInfo: {},
      temp: {
        productId: '',
        productNameCn: '',
        productNameEn: '',
        productCommonName: '',
        manufacturerName: '',
        manufacturerId: '',
        code: '',
        spec: '',
        unit: '',
        dosageFormCode: '',
        pharmaceuticalFactory: ''
      },
      rules: {
        productNameCn: [{
          required: true,
          type: 'string',
          message: '请输入产品名称',
          trigger: ['blur', 'change']
        }
        ],
        productCommonName: [{
          required: true,
          type: 'string',
          message: '请输入通用名',
          trigger: ['blur', 'change']
        }
        ],
        spec: [
          {
            required: true,
            type: 'string',
            message: '请输入包装规格',
            trigger: 'blur'
          }
        ],
        code: [
          {
            required: true,
            type: 'string',
            message: '请输入规格编码',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    initPage(model) {
      // 为了解决页面渲染慢的问题，将 model先赋值给handleInfo
      this.handleInfo = model

      this.handleCreate()
      this.queryDosageForm()

      this.temp.manufacturerId = model.tempManufacturerId
      this.temp.manufacturerName = model.manufacturerName

      if (model.tempProductId !== undefined && model.tempProductId !== null) {
        this.getProduct(model.tempProductId)
      } else {
        if (model.productName && model.productName !== '') {
          this.temp.productNameCn = model.productName
        }
        if (model.commonName && model.commonName !== '') {
          this.temp.productCommonName = model.commonName
        }
      }
      if (model.specificationName && model.specificationName !== '') {
        this.temp.spec = model.specificationName
      }
    },
    getProduct(productId) {
      ProductService.GetProduct({
        id: productId
      }).then(res => {
        this.temp.productId = res.data.id
        this.temp.productNameCn = res.data.nameCn
        this.temp.productNameEn = res.data.nameEn
        this.temp.productCommonName = res.data.commonName
        this.temp.manufacturerId = res.data.manufacturerId
        this.temp.manufacturerName = res.data.manufacturerName
      }).catch(res => {})
    },
    resetTemp() {
      this.temp.productId = ''
      this.temp.productNameCn = ''
      this.temp.productNameEn = ''
      this.temp.productCommonName = ''
      this.temp.manufacturerName = ''
      this.temp.manufacturerId = ''
      this.temp.code = ''
      this.temp.spec = ''
      this.temp.unit = ''
      this.temp.dosageFormCode = ''
      this.temp.pharmaceuticalFactory = ''
    },
    btnClose() {
      this.dialogSpecEditFormVisible = false
    },
    queryDosageForm() {
      MasterDataService.GetDicts({ parentCode: 'DosageForm' }).then(res => {
        this.dosageFormList = res.data
      })
    },
    handleCreate() {
      this.resetTemp()
      this.dialogSpecEditFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          ProductService.AddProductAndSpec(this.temp)
            .then((result) => {
              if (result.succeed) {
                this.dialogSpecEditFormVisible = false
                this.$emit('success', result.data)
                this.showMessage('创建成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
              this.loading = false
            })
        }
      })
    },
    handleShowProduct() {
      this.dialogProductVisible = true
    },
    handleClearProduct() {
      this.temp.productId = ''
      this.temp.productNameCn = ''
      if (this.handleInfo.productName && this.handleInfo.productName !== '') {
        this.temp.productNameCn = this.handleInfo.productName
      }
      this.temp.productCommonName = ''
      if (this.handleInfo.commonName && this.handleInfo.commonName !== '') {
        this.temp.productCommonName = this.handleInfo.commonName
      }

      this.temp.productNameEn = ''
    },
    selectProductSuccess(val) {
      this.dialogProductVisible = false
      this.temp.productId = val.id
      this.temp.productNameCn = val.nameCn
      this.temp.productNameEn = val.nameEn
      this.temp.productCommonName = val.commonName
    },
    closeProductDialog() {
      this.dialogProductVisible = false
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    }
  }
}
</script>
<style scoped>
  .el-dialog-s {
  z-index: 12;
}
  .el-dialog-ls {
  z-index: 13;
}
</style>
