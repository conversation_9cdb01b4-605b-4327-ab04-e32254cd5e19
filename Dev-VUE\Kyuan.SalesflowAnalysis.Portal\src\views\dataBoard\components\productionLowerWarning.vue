<template>
  <div class="panel-group">
    <el-card style="height: 250px;">
      <div slot="header" class="clearfix cardHeader">
        <div>
          <span>{{ title }}</span>
          <span class="waringCount">{{ showTotalCount }}</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="handleMore">详情</el-button>
        </div>
      </div>
      <el-table
        :data="topList"
        stripe
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column label="岗位名称" show-overflow-tooltip header-align="center" align="left">
          <template slot-scope="{ row }">
            <span>{{ row.stationName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="销售额" width="100" prop="productionAmount" align="center">
          <template slot-scope="{ row }">
            <span>{{ row.productionAmount | toMoney }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!--弹出dialog-->
    <el-dialog append-to-body :title="title" width="60%" :close-on-click-modal="false" :visible="showDialog" @close="cancleDialog()">
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="4">
            <el-select
              v-model="listQuery.departmentId"
              value-key="value"
              class="filter-item"
              placeholder="部门"
              style="width:100%"
              clearable
            >
              <el-option
                v-for="item in deptList"
                :key="item.key"
                :label="item.name"
                :value="item.key"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-input
              v-model="listQuery.stationName"
              clearable
              placeholder="岗位名称"
              class="filter-item"
            />
          </el-col>
          <el-col :span="4">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-search"
              @click="handleFilter"
            >
              查询
            </el-button>
          </el-col>
        </el-row>
        <el-row type="flex" :gutter="10" justify="end">
          <el-col :span="3.5">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-download"
              @click="handleExport"
            >
              导出
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table
              :data="dataList"
              stripe
              fit
              border
              highlight-current-row
              style="width: 100%"
              :header-cell-class-name="'tableStyle'"
            >
              <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
              <el-table-column label="部门" min-width="100" header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.departmentName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="岗位名称" min-width="150" header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.stationName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="销售额" width="120" prop="productionAmount" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.productionAmount | toMoney }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col v-if="totalCount > 0" class="el-colRight">
            <pagination v-show="totalCount > 0" :total="totalCount" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="search" />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleDialog()">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import dashboardService from '@/api/dashboard'

export default {
  components: {
    Pagination
  },
  data() {
    return {
      title: '岗位销售额过低预警',
      showDialog: false,
      listQuery: {
        departmentId: null,
        pageIndex: 1,
        pageSize: 10
      },
      filter: {},
      dataList: [],
      topList: [],
      totalCount: 0, // 数据总数
      topCount: 1,
      showTotalCount: 0,
      deptList: []
    }
  },
  methods: {
    initPage(filter, provinceList, deptList, count) {
      this.filter = filter

      this.filter.provinceIds = filter.selectProvinceIds.length === 0 ? filter.provinceIds : filter.selectProvinceIds
      this.deptList = deptList
      this.topCount = count
      this.getTopList()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getTopList() {
      this.filter.pageIndex = 1
      this.filter.pageSize = this.topCount
      dashboardService.QueryProductionLowerWarning(this.filter)
        .then((res) => {
          this.topList = res.data.datas
          this.filter.pageSize = 10
          this.showTotalCount = res.data.recordCount
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.search()
    },
    search() {
      this.listQuery.timeRange = this.filter.timeRange

      dashboardService.QueryProductionLowerWarning(this.listQuery)
        .then((res) => {
          this.dataList = res.data.datas
          this.totalCount = res.data.recordCount
        })
    },
    handleExport() {
      this.listQuery.timeRange = this.filter.timeRange

      dashboardService.ExportProductionLowerWarning(this.listQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '产值过低预警.xlsx'

          fileDownload(result.data, filename)
        })
        .catch(() => {

        })
    },
    handleMore() {
      this.showDialog = true
      this.listQuery.departmentId = this.filter.departmentId
      this.listQuery.areaIds = this.filter.areaIds
      this.listQuery.provinceIds = this.filter.provinceIds
      this.listQuery.isSelectedAllProvince = this.filter.isSelectedAllProvince
      this.listQuery.pageIndex = 1
      this.search()
    },
    cancleDialog() {
      this.showDialog = false
      this.listQuery =
      {
        pageIndex: 1,
        pageSize: 10,
        stationName: null
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.cardHeader {
      height: 10px;
    }

.el-card ::v-deep .el-card__header {
  padding: 18px 20px 20px 20px;
  border-bottom: 1px solid #e6ebf5;
}

.el-card ::v-deep .el-card__body {
  padding: 0px 10px 0px 10px;
}
</style>
