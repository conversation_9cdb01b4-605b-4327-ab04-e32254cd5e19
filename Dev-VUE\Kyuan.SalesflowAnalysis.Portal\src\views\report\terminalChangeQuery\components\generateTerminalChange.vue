<template>
  <div>
    <el-dialog :title="title" :close-on-click-modal="false" :visible="true" @close="cancle()">
      <el-form ref="dataForm" :rules="rules" :model="tempData.tempFormModel" label-position="right" label-width="150px">
        <el-row :gutter="10">
          <el-form-item label="生成月份" prop="yearMonthSplicing">
            <el-col :span="24">
              <el-date-picker
                v-model="tempData.tempFormModel.yearMonthSplicing"
                clearable
                style="width:100%"
                type="month"
                placeholder="生成月份"
                format="yyyy-MM"
                value-format="yyyy-MM"
                :picker-options="pickerOptions"
              />
            </el-col>
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          v-loading="btnSaveLoading"
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>

import ReportService from '@/api/report'

export default {
  name: '',
  components: {
  },
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          // 只允许选择上个月及以前的月份
          const year = new Date(Date.now()).getFullYear()
          let month = new Date(Date.now()).getMonth() + 1
          if (month < 10) month = '0' + month
          const ym = year + '-' + month
          const date = new Date(ym + '-01').setDate(0)// 如果 setDate 参数指定0，那么日期就会被设置为上个月的最后一天。
          return time.getTime() > new Date(date).getTime()
        }
      },
      rules: {
        yearMonthSplicing: [
          { required: true, message: '请选择生成月份', trigger: 'blur' }
        ]
      },
      tempData: { tempFormModel: {}},
      btnSaveLoading: false
    }
  },
  mounted() {
  },
  created() {
    this.init()
  },
  methods: {
    init() {
    },
    save() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.generate()
        }
      })
    },
    generate() {
      this.btnSaveLoading = true
      ReportService.GenerateTerminalReportByMonth(this.tempData.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempData.tempFormModel = result.data
          this.$notice.message('报表生成成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('报表生成失败', 'error')
          }
        })
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    }
  }

}
</script>
