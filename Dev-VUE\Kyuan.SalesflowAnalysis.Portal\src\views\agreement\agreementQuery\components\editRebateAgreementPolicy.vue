<template>
  <div>
    <el-dialog custom-class="el-dialog-s" :title="title" width="70%" append-to-body :close-on-click-modal="false" :visible="editRebateAgreementPolicyVisible" @close="cancle()">
      <el-form ref="dataForm" :rules="rules" :model="tempData.tempFormModel" label-position="right" label-width="100px" class="el-dialogform">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="协议类型" prop="rebateAgreementId">
              <el-select
                v-model="tempData.tempFormModel.rebateAgreementId"
                style="width: 100%"
                class="filter-item"
                placeholder="协议类型"
              >
                <el-option
                  v-for="item in rebateAgreementList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="支付方式" prop="enumPaymentType">
              <el-select
                v-model="tempData.tempFormModel.enumPaymentType"
                style="width: 100%"
                class="filter-item"
                placeholder="支付方式"
              >
                <el-option
                  v-for="item in paymentTypeList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="返利基数" prop="enumRebateBaseType">
              <el-select
                v-model="tempData.tempFormModel.enumRebateBaseType"
                style="width: 100%"
                class="filter-item"
                placeholder="返利基数"
              >
                <el-option
                  v-for="item in rebateBaseTypeList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="品种全部达标">
              <el-switch v-model="tempData.tempFormModel.isAllProductStandards" class="drawer-switch" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="存在扣款条款">
              <el-switch v-model="tempData.tempFormModel.hasDefaultClause" class="drawer-switch" />
            </el-form-item>
          </el-col>
          <el-col v-if="isShowDefaultClauseRemark" :span="24">
            <el-form-item label="扣款条款">
              <el-input
                v-model="tempData.tempFormModel.defaultClauseRemark"
                clearable
                placeholder="扣款条款"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                v-model="tempData.tempFormModel.Remark"
                type="textarea"
                clearable
                placeholder="备注"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="24">
            <el-table
              :data="productList"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%;"
              :header-cell-class-name="'tableStyle'"
            >
              <el-table-column
                fixed
                label="序号"
                type="index"
                align="center"
              />
              <el-table-column label="产品名称" align="center" sortable="custom" min-width="100px" prop="SAPCode">
                <template slot-scope="{ row }">
                  <span>{{ row.sapCode }}</span>
                </template>
              </el-table-column>
              <el-table-column label="规格" align="center" sortable="custom" min-width="110px" prop="CRMCode">
                <template slot-scope="{ row }">
                  <span>{{ row.crmCode }}</span>
                </template>
              </el-table-column>
              <el-table-column label="厂商" align="center" sortable="custom" min-width="100px" prop="Employee.DisplayName">
                <template slot-scope="{ row }">
                  <span>{{ row.employeeDisplayName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="协议数量" align="center" sortable="custom" min-width="100px" prop="Province.NameCn">
                <template slot-scope="{ row }">
                  <span>{{ row.provinceNameCn }}</span>
                </template>
              </el-table-column>
              <el-table-column label="补偿单价" align="center" sortable="custom" min-width="100px" prop="City.NameCn">
                <template slot-scope="{ row }">
                  <span>{{ row.cityNameCn }}</span>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" align="center" header-align="center" width="90" class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <i class="el-icon-circle-check eltablei" title="删除" @click="handleEnable(row)" />
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row> -->
      </el-form>
      <el-card class="" shadow="never">
        <div slot="header" class="clearfix">
          <span>品规列表</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="handleAddProduct">新增品规</el-button>
        </div>

        <el-table
          :data="productList"
          stripe
          border
          fit
          highlight-current-row
          style="width: 100%;"
          :header-cell-class-name="'tableStyle'"
        >
          <el-table-column
            fixed
            label="序号"
            type="index"
            align="center"
          />
          <el-table-column label="产品名称" align="center" sortable="custom" min-width="100px" prop="productName">
            <template slot-scope="{ row }">
              <span>{{ row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="规格" align="center" sortable="custom" min-width="110px" prop="spec">
            <template slot-scope="{ row }">
              <span>{{ row.spec }}</span>
            </template>
          </el-table-column>
          <el-table-column label="厂商" align="center" sortable="custom" min-width="100px" prop="Employee.manufacturerName">
            <template slot-scope="{ row }">
              <span>{{ row.manufacturerName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="协议数量" align="center" sortable="custom" min-width="100px" prop="Province.minimumPurchaseQuantity">
            <template slot-scope="{ row }">
              <span>{{ row.minimumPurchaseQuantity }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补偿单价" align="center" sortable="custom" min-width="100px" prop="City.rebateUnitPrice">
            <template slot-scope="{ row }">
              <span>{{ row.rebateUnitPrice }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" header-align="center" width="90" class-name="small-padding fixed-width">
            <template slot-scope="{ row }">
              <i class="el-icon-edit-outline eltablei" title="更新" @click="handleUpdate(row)" />
              <i class="el-icon-delete eltablei" title="删除" @click="handleDelete(row)" />
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <el-card class="box-card" shadow="never" style="margin-top: 10px;">
        <div slot="header" class="clearfix">
          <span>目标终端列表</span>
          <el-button style="float: right; padding: 3px 10px" type="text">添加目标终端</el-button>
          <el-button style="float: right; padding: 3px 0" type="text">导入目标终端</el-button>
        </div>

        <el-table
          :data="targetReceiverList"
          stripe
          border
          fit
          highlight-current-row
          style="width: 100%;"
          :header-cell-class-name="'tableStyle'"
        >
          <el-table-column
            fixed
            label="序号"
            type="index"
            align="center"
          />
          <el-table-column label="终端编码" align="center" sortable="custom" min-width="100px" prop="code">
            <template slot-scope="{ row }">
              <span>{{ row.code }}</span>
            </template>
          </el-table-column>
          <el-table-column label="终端名称" align="center" sortable="custom" min-width="110px" prop="name">
            <template slot-scope="{ row }">
              <span>{{ row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="省份" align="center" sortable="custom" min-width="100px" prop="Employee.provinceName">
            <template slot-scope="{ row }">
              <span>{{ row.provinceName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="城市" align="center" sortable="custom" min-width="100px" prop="Province.cityName">
            <template slot-scope="{ row }">
              <span>{{ row.cityName }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" header-align="center" width="90" class-name="small-padding fixed-width">
            <template slot-scope="{ row }">
              <i class="el-icon-delete eltablei" title="删除" @click="handleDelete(row)" />
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <el-card class="box-card" shadow="never" style="margin-top: 10px;">
        <div slot="header" class="clearfix">
          <span>计算公式</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="handleAddFormula">新增计算公式</el-button>
        </div>

        <el-table
          :data="formulaList"
          stripe
          border
          fit
          highlight-current-row
          style="width: 100%;"
          :header-cell-class-name="'tableStyle'"
        >
          <el-table-column
            fixed
            label="序号"
            type="index"
            align="center"
          />
          <el-table-column label="条件" align="center" sortable="custom" min-width="100px" prop="condition">
            <template slot-scope="{ row }">
              <span>{{ row.condition }}</span>
            </template>
          </el-table-column>
          <el-table-column label="结果" align="center" sortable="custom" min-width="110px" prop="result">
            <template slot-scope="{ row }">
              <span>{{ row.result }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" header-align="center" width="90" class-name="small-padding fixed-width">
            <template slot-scope="{ row }">
              <i class="el-icon-edit-outline eltablei" title="更新" @click="handleEditFormula(row)" />
              <i class="el-icon-delete eltablei" title="删除" @click="handleDelete(row)" />
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <el-card class="box-card" shadow="never" style="margin-top: 10px;">
        <div slot="header" class="clearfix">
          <span>额外补偿配置列表</span>
          <el-button style="float: right; padding: 3px 0" type="text">新增</el-button>
        </div>

        <el-table
          :data="extraList"
          stripe
          border
          fit
          highlight-current-row
          style="width: 100%;"
          :header-cell-class-name="'tableStyle'"
        >
          <el-table-column
            fixed
            label="序号"
            type="index"
            align="center"
          />
          <el-table-column label="额外补偿配置内容" align="center" sortable="custom" min-width="100px" prop="SAPCode">
            <template slot-scope="{ row }">
              <span>{{ row.sapCode }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" header-align="center" width="90" class-name="small-padding fixed-width">
            <template slot-scope="{ row }">
              <i class="el-icon-delete eltablei" title="删除" @click="handleDelete(row)" />
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <ChooseProduct ref="refChooseProduct" />
    <ConditionConfig ref="refConditionConfig" />
  </div>
</template>
<script>
import ChooseProduct from './chooseProduct'
import ConditionConfig from './conditionConfig'
export default {
  name: '',
  components: {
    ChooseProduct,
    ConditionConfig
  },
  props: {
  },
  data() {
    return {
      rules: {
        rebateAgreementId: [
          { required: true, message: '请输入协议类型', trigger: 'change' }
        ],
        enumPaymentType: [
          { required: true, message: '请输入支付方式', trigger: 'change' }
        ]
      },
      tempData: { tempFormModel: { hasDefaultClause: true }},
      editRebateAgreementPolicyVisible: false,
      isShowDefaultClauseRemark: true,
      title: '新增政策',
      rebateAgreementList: [{ key: '1', value: '达量返利' }, { key: '2', value: '阶梯式达量返利' }],
      paymentTypeList: [{ key: '1', value: '票折' }, { key: '2', value: '服务费' }],
      rebateBaseTypeList: [{ key: '1', value: '进货' }],
      productList: [{ productName: '来士普', spec: '10mg*7t', manufacturerName: '灵北', minimumPurchaseQuantity: '60', rebateUnitPrice: '3.08' }],
      targetReceiverList: [{ code: 'DPHY_07682', name: '广州圣泉康复医院有限公司', provinceName: '广东省', cityName: '广州' }],
      formulaList: [{ condition: '销售达成率>=85%  and  销售达成率<=100%  ', result: '销售数量*3' }],
      extraList: []
    }
  },
  watch: {
  },
  mounted() {
  },
  methods: {
    init() {
      this.editRebateAgreementPolicyVisible = true
    },
    handleDelete(row) {
      console.log(row)
    },
    handleAddProduct() {
      this.$refs.refChooseProduct.init('新增品规')
    },
    save() {
      this.editRebateAgreementPolicyVisible = false
    },
    close() {
      this.editRebateAgreementPolicyVisible = false
    },
    cancle() {
      this.editRebateAgreementPolicyVisible = false
    },
    handleAddFormula() {
      this.$refs.refConditionConfig.init('新增返利条件配置', null)
    },
    handleEditFormula(row) {
      this.$refs.refConditionConfig.init('编辑返利条件配置', row)
    }
  }

}
</script>
