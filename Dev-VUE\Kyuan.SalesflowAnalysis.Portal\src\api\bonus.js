import HttpApi from './libs/api.request'

const controller = 'Bonus'

const api = new HttpApi(controller)

export default {
  QueryImportQuotaMasterTemp(params) {
    return api.get('QueryImportQuotaMasterTemp', params)
  },
  QueryImportReceiverQuotaError(params) {
    return api.get('QueryImportReceiverQuotaError', params)
  },
  ExportImportReceiverQuotaError(params) {
    return api.post('ExportImportReceiverQuotaError', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryImportRegionQuotaError(params) {
    return api.get('QueryImportRegionQuotaError', params)
  },
  QueryReceiverQuota(params) {
    return api.get('QueryReceiverQuota', params)
  },
  QueryStationQuota(params) {
    return api.get('QueryStationQuota', params)
  },
  AddReceiverQuota(params) {
    return api.post('AddReceiverQuota', params)
  },
  AddStationQuota(params) {
    return api.post('AddStationQuota', params)
  },
  UpdateReceiverQuota(params) {
    return api.post('UpdateReceiverQuota', params)
  },
  UpdateStationQuota(params) {
    return api.post('UpdateStationQuota', params)
  },
  DeleteReceiverQuota(params) {
    return api.post('DeleteReceiverQuota', params)
  },
  GetReceiverQuota(params) {
    return api.get('GetReceiverQuota', params)
  },
  GetStationQuota(params) {
    return api.get('GetStationQuota', params)
  },
  SaveSalesIncrementalBonusSetting(params) {
    return api.post('SaveSalesIncrementalBonusSetting', params)
  },
  GetSalesIncrementalBonusSetting(params) {
    return api.get('GetSalesIncrementalBonusSetting', params)
  },
  QueryBonusResult(params) {
    return api.get('QueryBonusResult', params)
  },
  QueryBonusResultEmployee(params) {
    return api.get('QueryBonusResultEmployee', params)
  },
  QueryBonusResultEmployeeDetail(params) {
    return api.get('QueryBonusResultEmployeeDetail', params)
  },
  QueryBonusResultEmployeeIncrement(params) {
    return api.get('QueryBonusResultEmployeeIncrement', params)
  },
  QueryPersonalBonusResultEmployee(params) {
    return api.get('QueryPersonalBonusResultEmployee', params)
  },
  QueryBonusResultEmployeeHistory(params) {
    return api.get('QueryBonusResultEmployeeHistory', params)
  },
  GetBonusResultEmployee(params) {
    return api.get('GetBonusResultEmployee', params)
  },
  GetBonusResultExportColumn() {
    return api.get('GetBonusResultExportColumn')
  },
  ExportBonusResult(params) {
    return api.post('ExportBonusResult', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  GetBonusResultEmployeeDetailExportColumn(params) {
    return api.get('GetBonusResultEmployeeDetailExportColumn', params)
  },
  ExportBonusResultEmployeeDetail(params) {
    return api.post('ExportBonusResultEmployeeDetail', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  ComputeBonus(params) {
    return api.post('ComputeBonus', params)
  },
  AddBonusResultEmployeeHistory(params) {
    return api.post('AddBonusResultEmployeeHistory', params)
  },
  DelBonusResult(params) {
    return api.post('DelBonusResult', params)
  },
  ReleaseField(params) {
    return api.post('ReleaseField', params)
  },
  CancelReleaseField(params) {
    return api.post('CancelReleaseField', params)
  },
  GrantBonusEmployee(params) {
    return api.post('GrantBonusEmployee', params)
  },
  CancelGrantBonusEmployee(params) {
    return api.post('CancelGrantBonusEmployee', params)
  },
  QueryPersonalBonusResultEmployeeDetail(params) {
    return api.get('QueryPersonalBonusResultEmployeeDetail', params)
  },
  FinishEditBonus(params) {
    return api.post('FinishEditBonus', params)
  },
  ApproveBonus(params) {
    return api.post('ApproveBonus', params)
  },
  ApproveEmployeeBonus(params) {
    return api.post('ApproveEmployeeBonus', params)
  },
  RejectEmployeeBonus(params) {
    return api.post('RejectEmployeeBonus', params)
  },
  QueryTargetTerminal(params) {
    return api.get('QueryTargetTerminal', params)
  },
  // 目标终端导出列字典
  GetTargetTerminalExportColumnDictionary() {
    return api.get('GetTargetTerminalExportColumnDictionary')
  },
  GetTargetTerminalExportColumn() {
    return api.get('GetTargetTerminalExportColumn')
  },
  //  厂商下拉框
  QueryManufacturerSelect() {
    return api.get('QueryManufacturerSelect')
  },
  ExportTargetTerminal(params) {
    return api.post('ExportTargetTerminal', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  GetReceiverQuotaColumn() {
    return api.get('GetReceiverQuotaColumn')
  },
  ExportReceiverQuota(params) {
    return api.post('ExportReceiverQuota', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  GetStationQuotaColumn() {
    return api.get('GetStationQuotaColumn')
  },
  ExportStationQuota(params) {
    return api.post('ExportStationQuota', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  GetMyBonusExportColumn() {
    return api.get('GetMyBonusExportColumn')
  },
  ExportMyBonus(params) {
    return api.post('ExportMyBonus', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryDepartBonusResult(params) {
    return api.get('QueryDepartmentBonusResult', params)
  },
  // 部门总监审批通过
  ApproveDepartmentEmployeeBonus(params) {
    return api.post('ApproveDepartmentEmployeeBonus', params)
  },
  // 部门总监审批拒绝
  RejectDepartmentEmployeeBonus(params) {
    return api.post('RejectDepartmentEmployeeBonus', params)
  },
  // 部门总监审批主数据状态
  ApproveDepartmentBonus(params) {
    return api.post('ApproveDepartmentBonus', params)
  },
  QueryBonusCategorySelect() {
    return api.get('QueryBonusCategorySelect')
  },
  QueryBonusTemplateSelect(params) {
    return api.get('QueryBonusTemplateSelect', params)
  },
  // 获取奖励类型模板列表
  QueryBonusTemplate(params) {
    return api.get('QueryBonusTemplate', params)
  },
  // 新增奖励类型
  AddBonusTemplate(params) {
    return api.post('AddBonusTemplate', params)
  },
  // 删除奖励类型
  DeleteBonusTemplate(params) {
    return api.post('DeleteBonusTemplate', params)
  },
  GetBonusTemplate(params) {
    return api.get('GetBonusTemplate', params)
  },

  GetBonusVariableMapping(params) {
    return api.get('GetBonusVariableMapping', params)
  },

  QueryBonusVariable(params) {
    return api.get('QueryBonusVariable', params)
  },
  // 获取基础数据模板Mapping信息
  QueryBonusVariableDetailMapping(params) {
    return api.get('QueryBonusVariableDetailMapping', params)
  },
  // 获取基础数据配置信息
  QueryBonusVariableDetailSelect(params) {
    return api.get('QueryBonusVariableDetailSelect', params)
  },
  // 获取基础数据模板Mapping信息
  QueryBonusResultEntityDetailMapping(params) {
    return api.get('QueryBonusResultEntityDetailMapping', params)
  },
  // 获取基础数据配置信息
  QueryBonusResultEntityDetailSelect(params) {
    return api.get('QueryBonusResultEntityDetailSelect', params)
  },
  GetBonusResultEntityMapping(params) {
    return api.get('GetBonusResultEntityMapping', params)
  },
  QueryBonusResultVariableSelect() {
    return api.get('QueryBonusResultVariableSelect')
  },
  QueryBonusResultEntitySelect(params) {
    return api.get('QueryBonusResultEntitySelect', params)
  },
  ResetBonusVariableMapping(params) {
    return api.post('ResetBonusVariableMapping', params)
  },
  ResetBonusResultEntityMapping(params) {
    return api.post('ResetBonusResultEntityMapping', params)
  },
  SaveBonusVariableAndResultMapping(params) {
    return api.post('SaveBonusVariableAndResultMapping', params)
  },
  UpdateBonusVariableDetailMapping(params) {
    return api.post('UpdateBonusVariableDetailMapping', params)
  },
  UpdateBonusResultEntityDetailMapping(params) {
    return api.post('UpdateBonusResultEntityDetailMapping', params)
  },
  ResetBonusAllMapping(params) {
    return api.post('ResetBonusAllMapping', params)
  },
  // 停用奖励类型模板
  DisableBonusTemplate(params) {
    return api.post('DisableBonusTemplate', params)
  },
  // 启用奖励类型模板
  EnableBonusTemplate(params) {
    return api.post('EnableBonusTemplate', params)
  },
  QueryOtherQuota(params) {
    return api.get('QueryOtherQuota', params)
  },
  AddOtherQuota(params) {
    return api.post('AddOtherQuota', params)
  },
  DeleteOtherQuota(params) {
    return api.post('DeleteOtherQuota', params)
  },
  GetOtherQuota(params) {
    return api.get('GetOtherQuota', params)
  },
  UpdateOtherQuota(params) {
    return api.post('UpdateOtherQuota', params)
  }
}
