<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex" style="margin-bottom:-20px;">
        <el-col :span="span">
          <el-select
            v-model="filter.deptId"
            :loading="deptLoading"
            class="filter-item"
            placeholder="部门"
            clearable
          >
            <el-option
              v-for="item in deptList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="filter.timeRange"
            clearable
            class="filter-item"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            format="yyyy-MM"
            value-format="yyyy-MM"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="filter.receiverName"
            clearable
            placeholder="客户名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProductAndSpec"
            v-model="productAndSpecId"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            placeholder="产品"
            class="filter-item"
            clearable
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            @change="productChange"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="filter.customerCategoryId"
            :loading="deptLoading"
            class="filter-item"
            placeholder="客户类型"
            clearable
          >
            <el-option
              v-for="item in customerCategoryList"
              :key="item.key"
              :label="item.name"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="filter.enumHeadStatus"
            :loading="deptLoading"
            class="filter-item"
            placeholder="是否存在负责人"
            clearable
          >
            <el-option
              v-for="item in enumHeadStatusList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="filter.isWarning"
            :loading="deptLoading"
            class="filter-item"
            placeholder="当前年缺少潜力"
            clearable
          >
            <el-option
              v-for="item in isWarningOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="filter.employeeDisplayName"
            clearable
            placeholder="负责人"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'TargetReceiver_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'TargetReceiver_Button_BatchImport')"
            type="primary"
            icon="el-icon-upload2"
            @click="handleImport"
          >
            批量导入
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'TargetReceiver_Button_Add')"
            type="primary"
            icon="el-icon-plus"
            @click="handleCreate"
          >
            新增
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'TargetReceiver_Button_Export')"
            :loading="btnExportLoading"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              :index="indexMethod"
              type="index"
              align="center"
            />
            <el-table-column
              sortable="custom"
              prop="Department.Name"
              label="部门名称"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.deptName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="ProductSpec.Product.Manufacturer.Name"
              label="厂商名称"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.manufacturerName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="ProductSpec.Product.NameCn"
              label="产品"
              min-width="90px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="ProductSpec.Spec"
              label="规格"
              min-width="120px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.spec }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Receiver.Name"
              label="客户"
              min-width="220px"
              header-align="center"
              align="left"
            >
              <template slot-scope="{ row }">
                <span>{{ row.receiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Receiver.Code"
              label="客户编码"
              min-width="160px"
              header-align="center"
              align="left"
            >
              <template slot-scope="{ row }">
                <span>{{ row.receiverCode }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="CustomerCategory.Name"
              label="客户类型"
              min-width="110px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.customerCategoryName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Receiver.ReceiverTypeLevelOne.Name"
              label="收货方一级类型"
              min-width="130px"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.receiverFirstTypeName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Receiver.ReceiverTypeLevelOne.Name"
              label="收货方二级类型"
              min-width="130px"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.receiverSecondTypeName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Receiver.ReceiverTypeLevelThree.Name"
              label="收货方三级类型"
              min-width="130px"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.receiverThirdTypeName }}</span>
              </template>
            </el-table-column>

            <el-table-column
              sortable="custom"
              prop="StartDate"
              label="开始日期"
              min-width="110px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.startDateString }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="EndDate"
              label="结束日期"
              min-width="110px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.endDateString === '2099-12'? '': row.endDateString }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="IsWarning"
              label="当前年缺少潜力"
              min-width="130px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.isWarningDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Employee.DisplayName"
              label="负责人"
              min-width="110px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.employeeDisplayName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Station.Name"
              label="岗位名称"
              min-width="110px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.stationName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="LastSynchronize"
              label="最后同步时间"
              min-width="120px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span v-if="row.lastSynchronize != null">{{ row.lastSynchronize |parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i v-if="$isPermitted($store.getters.user, 'TargetReceiver_Button_Edit')" class="el-icon-edit-outline eltablei" @click="handleUpdate(row)" />
                <i v-if="$isPermitted($store.getters.user, 'TargetReceiver_Button_Del')" class="el-icon-delete eltablei" @click="handleDelete(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="filter.pageIndex"
            :limit.sync="filter.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <ModifyTargetReceiver
      v-if="dialogEditFormVisible"
      :id="itemId"
      :title="modifyDialogTitle"
      :view-model="modifyDialogIsReadonly"
      @hidden="onHidden()"
      @refresh="onRefresh()"
    />
    <AddTargetReceiver
      v-if="dialogAddFormVisible"
      @hidden="onHidden()"
      @refresh="onRefresh()"
    />
    <el-dialog
      title="导入目标客户"
      :close-on-click-modal="false"
      :visible="dialogImportVisible"
      width="80%"
      class="popup-search"
      @close="closeImportDialog"
    >
      <ImportTargetReceiver ref="refImportTargetReceiver" :show-import="dialogImportVisible" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeImportDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出目标客户"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import MaintenanceService from '@/api/maintenance'
import ProductService from '@/api/product'
import MasterDataService from '@/api/masterData'
import TargetReceiverService from '@/api/targetReceiver'

import ModifyTargetReceiver from './components/modifyTargetReceiver'
import ImportTargetReceiver from './components/importTargetReceiver'
import CustomExport from '@/components/Export/CustomExport'
import AddTargetReceiver from './components/addTargetReceiver'

export default {
  name: 'TargetReceiver',
  components: {
    Pagination,
    ModifyTargetReceiver,
    ImportTargetReceiver,
    CustomExport,
    AddTargetReceiver
  },
  data() {
    return {
      span: 4,
      deptList: [],
      deptLoading: false,
      list: [],
      total: 0,
      listLoading: true,
      filter: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      isWarningOptions: [
        {
          value: true,
          label: '是'
        },
        {
          value: false,
          label: '否'
        }
      ],
      productAndSpecLoading: false,
      productAndSpecList: null,
      productAndSpecId: [],
      customerCategoryList: [],
      dialogEditFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑目标客户',
        create: '新增目标客户'
      },
      itemId: null,
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: {},
      dialogImportVisible: false,
      enumHeadStatusList: [],
      enumHeadStatusLoading: false,
      dialogAddFormVisible: false
    }
  },
  created() {
    this.initDept()
    this.initProductAndSpec()
    this.initCustomerCategory()
    this.initHeadStatus()
    this.getList()
  },
  methods: {
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDepartmentForTargetReceiver()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch(() => {
          this.deptLoading = false
        })
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      ProductService.QueryProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    initCustomerCategory() {
      TargetReceiverService.QueryCustomerCategorySelect().then(res => {
        this.customerCategoryList = res
      })
    },
    initHeadStatus() {
      this.enumHeadStatusLoading = true
      MasterDataService.GetEnumInfos({ enumType: 'HeadStatus' })
        .then((result) => {
          this.enumHeadStatusLoading = false
          this.enumHeadStatusList = result.data.datas
        })
        .catch((error) => {
          this.enumHeadStatusLoading = false
          console.log(error)
        })
    },
    indexMethod(index) {
      return (this.filter.pageIndex - 1) * this.filter.pageSize + index + 1
    },
    getList() {
      this.listLoading = true
      if (this.productAndSpecId) {
        const [productId, productSpecId] = this.productAndSpecId
        this.filter.productId = productId
        this.filter.productSpecId = productSpecId
      }
      TargetReceiverService.QueryTargetReceiver(this.filter)
        .then((result) => {
          this.listLoading = false

          this.list = result.data.datas
          this.total = result.data.recordCount
          this.filter.pageIndex = result.data.pageIndex
        })
        .catch((error) => {
          console.log(error)
          this.loading = false
        })
    },
    handleFilter() {
      this.filter.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.filter.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.filter.order = orderSymbol + column.prop
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    productChange() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    handleDelete(row) {
      this.$confirm('确定删除此目标客户吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (row.customerCategoryCode === this.$constDefinition.customerCategory.selfSupportCode) {
          this.$confirm('是否要同时删除目标客户关联潜力?', '提示', {
            confirmButtonText: '是',
            cancelButtonText: '否',
            type: 'warning'
          }).then(() => {
            row.deleteQuota = true
            this.deleteTargetReceiver(row)
          }).catch(() => {
            row.deleteQuota = false
            this.deleteTargetReceiver(row)
          })
        } else {
          row.deleteQuota = false
          this.deleteTargetReceiver(row)
        }
      }).catch(error => {
        this.listLoading = false
        if (!error.succeed) {
          this.$notice.message('取消删除', 'info')
        }
      })
    },
    deleteTargetReceiver(row) {
      this.listLoading = true
      TargetReceiverService.DeleteTargetReceiver(row).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.getList()
          this.$notice.message('删除成功', 'success')
        }
      }).catch(error => {
        this.listLoading = false
        console.log(error)
      })
    },
    handleCreate() {
      this.itemId = null
      this.dialogAddFormVisible = true
      this.dialogStatus = 'create'
    },
    handleUpdate(row) {
      this.itemId = row.id
      this.modifyDialogIsReadonly = false
      this.dialogEditFormVisible = true
      this.dialogStatus = 'update'
      this.modifyDialogTitle = this.textMap[this.dialogStatus]
    },
    onHidden() {
      this.dialogEditFormVisible = false
      this.dialogAddFormVisible = false
    },
    onRefresh() {
      this.getList()
      this.dialogEditFormVisible = false
      this.dialogAddFormVisible = false
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      TargetReceiverService.GetTargetReceiverExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      this.filter.checkedColumns = checkColumns
      TargetReceiverService.ExportTargetReceivers(this.filter)
        .then((result) => {
          this.filter.checkedColumns = null
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '目标客户.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '目标客户.csv'
          }
          fileDownload(result.data, fileName)
        })
        .catch((error) => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    handleImport() {
      this.dialogImportVisible = true
    },
    closeImportDialog() {
      this.$refs.refImportTargetReceiver.clear()
      this.dialogImportVisible = false
      this.handleFilter()
    }
  }
}
</script>

