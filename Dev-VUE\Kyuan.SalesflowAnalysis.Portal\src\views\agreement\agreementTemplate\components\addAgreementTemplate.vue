<template>
  <el-dialog
    :close-on-click-modal="false"
    title="协议模板明细"
    :visible="dialogTemplateViewVisible"
    width="60%"
    @close="cancle"
  >
    <el-form ref="dataForm" :rules="rules" :model="agreementTemplateModel" label-position="right" label-width="100px" class="myself-dialogform">
      <el-row :gutter="10" type="flex">
        <el-col :span="12">
          <el-form-item label="模板名称" prop="name">
            <el-input
              v-model="agreementTemplateModel.name"
              placeholder="模板名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模板编码" prop="code">
            <el-input
              v-model="agreementTemplateModel.code"
              clearable
              disabled
              placeholder="编码自动生成"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="签约方类型" prop="enumSigningPartiesType">
            <el-select
              v-model="agreementTemplateModel.enumSigningPartiesType"
              style="width: 100%"
              class="filter-item"
              placeholder="签约方类型"
              clearable
              @change="selectUpdate"
            >
              <el-option
                v-for="item in signingPartiesTypeList"
                :key="item.value"
                :label="item.desc"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模板类型" prop="rebateAgreementTemplateTypeId">
            <el-select
              v-model="agreementTemplateModel.rebateAgreementTemplateTypeId"
              style="width: 100%"
              class="filter-item"
              placeholder="模板类型"
              clearable
              @change="selectUpdate"
            >
              <el-option
                v-for="item in rebateAgreementTemplateTypeList"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end" style="margin-bottom: 6px;">
        <el-col :span="8.5" align="right">
          <el-cascader
            ref="refProductAndSpec"
            v-model="rebateAgreementVariableIds"
            :options="rebateVariableList"
            placeholder="变量组 / 协议变量"
            style="width:100%"
            clearable
            class="filter-item"
            :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
            @change="rebateAgreementVariableChange"
          />
        </el-col>
        <el-col :span="3.5">
          <el-button
            type="primary"
            @click="insertVariable()"
          >插入变量</el-button>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="协议明细:" prop="rebateAgreementTemplateContentHtml">
            <WangEditor ref="refWangEditor" :html-value="agreementTemplateModel.rebateAgreementTemplateContentHtml" @contentData="getPrivacyAgreement" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button icon="el-icon-close" @click="cancle()">
        关闭
      </el-button>
      <el-button
        :loading="btnSaveLoading"
        type="primary"
        icon="el-icon-check"
        @click="save()"
      >
        保存
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import MasterDataService from '@/api/masterData'
import AgreementService from '@/api/agreement'
import WangEditor from '../../../components/wangEditor.vue'

export default {
  components: {
    WangEditor
  },
  data() {
    return {
      dialogTemplateViewVisible: false,
      agreementTemplateModel: {
        isParagraph: false
      },
      rules: {
        name: [
          { required: true, message: '模版名称不可为空', trigger: 'blur' }
        ],
        enumSigningPartiesType: [
          { required: true, message: '请选择签约方类型', trigger: 'change' }
        ],
        rebateAgreementTemplateTypeId: [
          { required: true, message: '请选择模版类型', trigger: 'change' }
        ]
      },
      signingPartiesTypeList: [],
      rebateAgreementTemplateTypeList: [],
      textContent: '',
      isParagraphList: [
        {
          'value': true,
          'desc': '是'
        },
        {
          'value': false,
          'desc': '否'
        }
      ],
      isAdd: true,
      btnSaveLoading: false,
      rebateVariableList: [],
      rebateAgreementVariableIds: [],
      selectedVariable: '' // 选中的协议变量
    }
  },
  methods: {
    init(data) {
      this.initSigningPartiesType()
      this.initAgreementPolicyType()
      this.initRebateAgreementTemplateVariable()

      this.dialogTemplateViewVisible = true
      if (data) {
        this.isAdd = false
        const para = { id: data }
        this.GetAgreementTemplate(para)
      } else {
        this.isAdd = true
      }
    },
    initSigningPartiesType() {
      var param = {
        enumType: 'SigningPartiesType'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.signingPartiesTypeList = result.data.datas
          this.$forceupdate()
        })
        .catch(() => {
        })
    },
    initAgreementPolicyType() {
      AgreementService.QueryAgreementTemplateTypeCascader().then((result) => {
        this.rebateAgreementTemplateTypeList = result
        this.$forceUpdate()
      })
        .catch((error) => {
          console.log(error)
        })
    },
    initRebateAgreementTemplateVariable() {
      AgreementService.QueryRebateAgreementTemplateVariableCascader().then((result) => {
        this.rebateVariableList = result
        this.$forceupdate()
      }).catch(() => {
      })
    },
    selectUpdate() {
      this.$forceUpdate()
    },
    // 富文本内容输出
    getPrivacyAgreement(val) {
      this.agreementTemplateModel.rebateAgreementTemplateContentHtml = val
    },
    GetAgreementTemplate(id) {
      this.$nextTick(() => {
        AgreementService.GetAgreementTemplate(id).then((result) => {
          this.agreementTemplateModel = result.data
          this.$forceUpdate()
        })
          .catch((error) => {
            console.log(error)
          })
      })
    },
    cancle() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.dialogTemplateViewVisible = false
      this.agreementTemplateModel = {
        isParagraph: false
      }
      this.isAdd = false
      this.$refs.refWangEditor.clear()
      this.$emit('refresh')
    },
    save() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.agreementTemplateModel.isParagraph && !this.agreementTemplateModel.paragraphTypeCode) {
            this.$notice.message('段落Code不可为空。', 'error')
            return
          }
          if (this.agreementTemplateModel.rebateAgreementTemplateContentHtml === '<p><br></p>') {
            this.$notice.message('返利模版内容不可为空。', 'error')
            return
          }

          if (this.isAdd) {
            this.addRebateAgreementTemplate()
          } else {
            this.updateRebateAgreementTemplate()
          }
        }
      })
    },
    addRebateAgreementTemplate() {
      this.btnSaveLoading = true
      AgreementService.AddRebateAgreementTemplate(this.agreementTemplateModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.agreementTemplateModel = result.data
          this.$notice.message('新增成功', 'success')
          this.cancle()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('新增失败。', 'error')
          }
        })
    },
    updateRebateAgreementTemplate() {
      this.btnSaveLoading = true
      AgreementService.UpdateRebateAgreementTemplate(this.agreementTemplateModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.agreementTemplateModel = result.data
          this.$notice.message('修改成功', 'success')
          this.cancle()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('修改失败。', 'error')
          }
        })
    },
    insertVariable() {
      if (!this.selectedVariable) {
        this.$notice.message('请选择协议变量', 'error')
        return
      }
      // 插入内容到指定位置
      this.$refs.refWangEditor.insertContent(`{{${this.selectedVariable}}}`)
      this.selectedVariable = ''
      this.rebateAgreementVariableIds = []
    },
    rebateAgreementVariableChange(val) {
      if (!val || val.length === 0) {
        return
      } else {
        const rebateVariable = this.rebateVariableList.find(item => { return item.value === val[0] })
        const rebateVariableDetail = rebateVariable.children.find(detail => { return detail.value === val[1] })
        this.selectedVariable = rebateVariableDetail.remark
      }
    }
  }
}

</script>
