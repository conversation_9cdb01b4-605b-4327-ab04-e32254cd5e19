<template>
  <div>
    <el-dialog custom-class="el-dialog-s" :title="title" width="80%" append-to-body :close-on-click-modal="false" :visible="showAddDialog" @close="closeAddAgreementDialog">
      <el-form
        ref="dataForm"
        :model="requestFormRebateAgreement"
        :rules="rules"
        label-position="right"
        label-width="100px"
        class="el-dialogform"
      >
        <el-row type="flex" class="filter-container">
          <el-col :span="24">
            <el-form-item label="协议标题" prop="name">
              <el-input
                v-model="requestFormRebateAgreement.name"
                placeholder="协议标题"
                class="filter-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="代付方" prop="rebateAgentName">
              <el-row>
                <el-col :span="20" style="padding-right:5px">
                  <el-input
                    v-model="requestFormRebateAgreement.rebateAgentName"
                    readonly
                    placeholder="代付方"
                    maxlength="300"
                  />
                </el-col>
                <el-col :span="4">
                  <el-button
                    class="filter-item"
                    type="primary"
                    icon="el-icon-search"
                    title="选择代付方"
                    @click="handleSelectRebateAgent"
                  />
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="返利接收方" prop="rebateReceiverName">
              <el-row>
                <el-col :span="15" style="padding-right:5px">
                  <el-input
                    v-model="requestFormRebateAgreement.rebateReceiverName"
                    readonly
                    placeholder="返利接收方"
                    maxlength="300"
                  />
                </el-col>
                <el-col :span="3" style="padding-right:5px">
                  <el-button
                    class="filter-item"
                    type="primary"
                    icon="el-icon-search"
                    title="选择接收方"
                    @click="handleSelectReceiver"
                  />
                </el-col>
                <el-col :span="6">
                  <el-button
                    class="filter-item"
                    type="primary"
                    icon="el-icon-search"
                    :disabled="historyDisabled"
                  >
                    历史协议
                  </el-button>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="部门" prop="departmentId">
              <el-select
                v-model="requestFormRebateAgreement.departmentId"
                :loading="deptLoading"
                placeholder="部门"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in deptList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="支付周期" prop="checkedPaymentCycleFlags">
              <el-checkbox-group v-model="checkedPaymentCycleFlags" @change="handleCheckedPaymentCycleChange">
                <el-checkbox v-for="item in paymentTypeList" :key="item.value" :label="item.value">{{ item.desc }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="起始日期" prop="startDate">
              <el-date-picker
                v-model="requestFormRebateAgreement.startDate"
                type="date"
                style="width: 100%"
                placeholder="起始日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="截止日期" prop="endDate">
              <el-date-picker
                v-model="requestFormRebateAgreement.endDate"
                type="date"
                style="width: 100%"
                placeholder="截止日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="协议文本">
              <el-col :span="3.5">
                <file-upload
                  :controller="controller"
                  :method="method"
                  @uploadSuccess="uploadSuccess"
                />
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="">
              <el-table
                :data="requestFormRebateAgreement.attachmentList"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
              >
                <el-table-column label="文件名称" min-width="180px" align="center">
                  <template slot-scope="{ row }">
                    <a
                      style="text-decoration:underline"
                      @click="handleDownLoadFile(row.id,row.fileName)"
                    >{{ row.fileName }}</a>
                  </template>
                </el-table-column>
                <el-table-column label="上传时间" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.createTime | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <i class="el-icon-delete eltablei" title="删除" @click="handleDeleteFile(row)" />

                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-row type="flex" justify="end">
        <el-col :span="24" class="el-colRight">

          <el-button
            class="filter-item"
            type="primary"
            icon="el-icon-plus"
            @click="handleAddPolicy"
          >
            新增政策
          </el-button>
        </el-col>
      </el-row>
      <el-collapse v-model="activeName" accordion style="margin-top:10px">
        <el-collapse-item v-for="item in requestFormRebateAgreement.requestFormRebateAgreementPolicyList" :key="item.rebateAgreementPolicyTypeId" name="1" style="font-size: 12px;">
          <el-row type="flex" justify="end" style="margin-top:10px">
            <el-col :span="24" class="el-colRight">
              <el-button
                class="filter-item"
                type="primary"
                icon="el-icon-edit"
                @click="handleEditPolicy(item)"
              >
                编辑
              </el-button>
              <el-button
                class="filter-item"
                type="primary"
                icon="el-icon-delete"
              >
                删除
              </el-button>
            </el-col>
          </el-row>
          <el-row type="flex" justify="end" style="margin-top:10px">
            <el-col :span="8">
              <span>政策模板：{{ item.rebateAgreementPolicyTypeName }}</span>
            </el-col>
            <el-col :span="8">
              <span>支付方式：{{ item.enumPaymentTypeName }}</span>
            </el-col>
            <el-col :span="8">
              <span>返利基数：{{ item.enumRebateBaseTypeName }}</span>
            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col :span="8">
              <span>是否全部品种达标：{{ item.needAllStandardName }}</span>
            </el-col>
            <el-col :span="8">
              <span>存在扣款条款：{{ item.hasDefaultClauseName }}</span>
            </el-col>
            <el-col :span="8">
              <span>扣款条款：{{ item.defaultClauseRemark }}</span>
            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col>
              <span>备注：{{ item.remark }}</span>
            </el-col>
          </el-row>
          <el-divider />
          <el-row style="margin-top:10px">
            <el-col>
              <span>品规列表</span>
            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col :span="24">
              <el-table
                :data="item.requestFormRebateAgreementProductList"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
              >
                <el-table-column
                  fixed
                  label="序号"
                  type="index"
                  align="center"
                />
                <el-table-column label="产品名称" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.productNameCn }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="规格" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.spec }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="厂商" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.manufacturerName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="供货价(元)" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.supplyPrice }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="销售下限" min-width="80px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.minimumPurchaseQuantity }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="折扣金额(元)" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.rebateUnitPrice }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="总指标" min-width="80px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.quotaQuantity }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="一季度指标" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.q1Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="二季度指标" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.q2Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="三季度指标" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.q3Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="四季度指标" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.q4Quota }}</span>
                  </template>
                </el-table-column>
              </el-table>

            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col>
              <span>目标终端</span>
            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col :span="24">
              <el-table
                :data="item.requestFormRebateAgreementTargetReceiverList"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
              >
                <el-table-column
                  fixed
                  label="序号"
                  type="index"
                  align="center"
                />
                <el-table-column label="编码" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.code }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="终端名称" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.targetReceiverName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="省份" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.provinceNameCn }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="城市" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.cityNameCn }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col>
              <span>计算公式</span>
            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col :span="24">
              <el-table
                :data="item.requestFormRebateAgreementTermList"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
              >
                <el-table-column
                  fixed
                  label="序号"
                  type="index"
                  align="center"
                />
                <el-table-column label="条件" align="center" min-width="300px">
                  <template slot-scope="{ row }">
                    <span>{{ row.agreementTermFormula }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="结果" align="center" min-width="200px">
                  <template slot-scope="{ row }">
                    <span>{{ row.rebateCalculateFormula }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-collapse-item>
      </el-collapse>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeAddAgreementDialog">
          取消
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleSave"
        >
          保存
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleSubmit"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="el-dialog-ls"
      append-to-body
      title="选择代付方"
      :close-on-click-modal="false"
      :visible="dialogRebateAgentVisible"
      width="80%"
      class="popup-search"
      @close="closeRebateAgentDialog"
    >
      <SelectReceiver ref="refDistributor" :show-dialog="dialogRebateAgentVisible" :distributor-types="distributorTypes" @success="selectRebateAgentSuccess" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeRebateAgentDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="选择返利接收方"
      :close-on-click-modal="false"
      :visible="dialogRebateReceiverVisible"
      width="80%"
      class="popup-search"
      @close="closeReceiverDialog"
    >
      <SelectReceiver ref="refReceiver" :show-dialog="dialogRebateReceiverVisible" :distributor-types="distributorTypes" @success="selectReceiverSuccess" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeReceiverDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>

    <EditRebateAgreementPolicy ref="refEditRebateAgreementPolicy" @success="resultRebateAgreementPolicy" />
  </div>
</template>
<script>
import MaintenanceService from '@/api/maintenance'
import MasterDataService from '@/api/masterData'
import AgreementSevices from '@/api/agreement'
import FileService from '@/api/file'
import SelectReceiver from '@/views/components/selectReceiver'
import FileUpload from './autoUploadFile'

import EditRebateAgreementPolicy from './editRebateAgreementPolicy'

export default {
  components: {
    EditRebateAgreementPolicy,
    SelectReceiver,
    FileUpload
  },
  data() {
    const validatePaymentCycle = (rule, value, callback) => {
      if (!this.checkedPaymentCycleFlags.length) {
        callback(new Error('请至少选择一个支付方式'))
      } else {
        callback()
      }
    }
    return {
      span: 12,
      title: '',
      showAddDialog: false,
      requestFormRebateAgreement: {
        enumPaymentCycleFlags: 0,
        requestFormRebateId: null,
        attachmentList: [],
        requestFormRebateAgreementPolicyList: []
      },
      deptList: [],
      paymentTypeList: [],
      dialogRebateReceiverVisible: false,
      dialogRebateAgentVisible: false,
      distributorTypes: [],
      controller: 'File',
      method: 'UploadAttachment',
      rules: {
        name: [
          { required: true, message: '请输入协议标题', trigger: 'change' },
          { max: 128, message: '协议标题长度必须小于等于 128 个字符', trigger: 'blur' }
        ],
        departmentId: [
          { required: true, message: '请选择部门', trigger: 'change' }
        ],
        rebateReceiverName: [
          { required: true, message: '请选择返利接收方', trigger: 'change,blur' }
        ],
        startDate: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        endDate: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ],
        enumPaymentCycle: [
          { required: true, message: '请选择支付周期', trigger: 'change' }
        ],
        checkedPaymentCycleFlags: [
          { type: 'array', required: true, validator: validatePaymentCycle, trigger: 'change' }
        ]
      },
      activeName: '1',
      deptLoading: false,
      historyDisabled: true,
      checkedPaymentCycleFlags: []
    }
  },
  methods: {
    initPage(formId) {
      this.title = formId != null ? '编辑协议' : '新增协议'
      this.showAddDialog = true
      // 申请单ID
      this.requestFormRebateAgreement.requestFormRebateId = formId
      this.initDept()
      this.initPaymentType()
    },
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch(() => {
          this.deptLoading = false
        })
    },
    initPaymentType() {
      var param = {
        enumType: 'PaymentCycleFlag'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.paymentTypeList = result.data.datas
        })
        .catch((error) => {
          console.log(error)
        })
    },
    handleSelectRebateAgent() {
      this.distributorTypes = [10, 20, 30]
      this.dialogRebateAgentVisible = true
    },
    handleSelectReceiver() {
      this.distributorTypes = [10, 20, 30]
      this.dialogRebateReceiverVisible = true
    },
    closeRebateAgentDialog() {
      this.dialogRebateAgentVisible = false
      this.$refs.refDistributor.clear()
      this.distributorTypes = []
    },
    closeReceiverDialog() {
      this.dialogRebateReceiverVisible = false
      this.$refs.refReceiver.clear()
      this.distributorTypes = []
    },
    selectRebateAgentSuccess(val) {
      this.requestFormRebateAgreement.rebateAgentName = val.name
      this.requestFormRebateAgreement.rebateAgentId = val.id
      this.closeRebateAgentDialog()
    },
    selectReceiverSuccess(val) {
      this.requestFormRebateAgreement.rebateReceiverName = val.name
      this.requestFormRebateAgreement.rebateReceiverId = val.id
      this.historyDisabled = false
      this.closeReceiverDialog()
    },
    closeAddAgreementDialog() {
      this.clear()
      this.showAddDialog = false
    },
    uploadSuccess(val) {
      this.requestFormRebateAgreement.attachmentList.push(val)
    },
    handleSave() {
      // 保存
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.requestFormRebateAgreement.endDate < this.requestFormRebateAgreement.startDate) {
            this.showMessage('开始日期不能小于截止日期', 'error')
            return
          }
          if (this.requestFormRebateAgreement.attachmentList.length === 0) {
            this.showMessage('请上传协议文件', 'error')
            return
          }
          // TODO政策做好后打开
          // if (this.requestFormRebateAgreement.requestFormRebateAgreementPolicyList.length === 0) {
          //   this.showMessage('请维护政策', 'error')
          //   return
          // }
          AgreementSevices.SaveRequestFormRebate(this.requestFormRebateAgreement)
            .then((result) => {
              if (result.succeed) {
                this.closeAddAgreementDialog()
                this.$emit('success')
                this.showMessage('创建成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
            })
        }
      })
    },
    handleSubmit() {
      // 提交
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.requestFormRebateAgreement.endDate < this.requestFormRebateAgreement.startDate) {
            this.showMessage('开始日期不能小于截止日期', 'error')
            return
          }
          if (this.requestFormRebateAgreement.attachmentList.length === 0) {
            this.showMessage('请上传协议文件', 'error')
            return
          }
          // TODO政策做好后打开
          // if (this.requestFormRebateAgreement.requestFormRebateAgreementPolicyList.length === 0) {
          //   this.showMessage('请维护政策', 'error')
          //   return
          // }
          AgreementSevices.SubmitRequestFormRebate(this.requestFormRebateAgreement)
            .then((result) => {
              if (result.succeed) {
                this.closeAddAgreementDialog()
                this.$emit('success')
                this.showMessage('创建成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
            })
        }
      })
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleAddPolicy() {
      this.$refs.refEditRebateAgreementPolicy.init(null)
    },
    handleEditPolicy(row) {
      this.$refs.refEditRebateAgreementPolicy.init(row)
    },
    resultRebateAgreementPolicy(data) {
      this.requestFormRebateAgreement.requestFormRebateAgreementPolicyList.find((item, index) => {
        if (item.rebateAgreementPolicyTypeId === data.rebateAgreementPolicyTypeId) {
          this.requestFormRebateAgreement.requestFormRebateAgreementPolicyList.splice(index, 1)
        }
      })
      this.requestFormRebateAgreement.requestFormRebateAgreementPolicyList.push(data)
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.requestFormRebateAgreement = {
        attachmentList: []
      }
      this.historyDisabled = true
    },
    handleDeleteFile(row) {
      this.requestFormRebateAgreement.attachmentList.map((item, index) => {
        if (item.id === row.id) { this.requestFormRebateAgreement.attachmentList.splice(index, 1) }
      })
    },
    handleDownLoadFile(templateId, templateName) {
      FileService.downloadAttachment(templateId).then(res => {
        const fileDownload = require('js-file-download')
        var filename = templateName
        fileDownload(res.data, filename)
      })
        .catch(() => {
        })
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },
    handleCheckedPaymentCycleChange(value) {
      const paymentCycleFlags = []

      let cycleValue = 0
      value.forEach(e => {
        paymentCycleFlags.push(e)
        cycleValue += e
      })

      this.checkedPaymentCycleFlags = value
      this.requestFormRebateAgreement.enumPaymentCycleFlags = cycleValue
    }
  }
}
</script>
<style scoped>
.el-collapse-item ::v-deep .el-collapse-item__header {
  font-size: 14px !important;
  font-weight: 600 !important;
}
</style>
