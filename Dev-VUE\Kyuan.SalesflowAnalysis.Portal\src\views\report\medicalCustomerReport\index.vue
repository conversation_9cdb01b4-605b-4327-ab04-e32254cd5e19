<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="span">
          <el-cascader
            ref="refManufacturerAndProductAndSpec"
            v-model="manufacturerAndProductAndSpecId"
            :loading="manufacturerLoading"
            :options="manufacturerAndProductAndSpecList"
            placeholder="厂商 / 产品 / 规格"
            style="width:100%"
            clearable
            class="filter-item"
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            @change="handleProductChange"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.departmentId"
            :loading="deptLoading"
            class="filter-item"
            placeholder="部门"
            clearable
            @change="handelDepartmentChange"
          >
            <el-option
              v-for="item in deptList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.areaIds"
            class="filter-item"
            placeholder="大区"
            multiple
            clearable
            @change="handelAreaChange"
          >
            <el-option
              v-for="item in areaList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.provinceIds"
            class="filter-item"
            placeholder="省份"
            multiple
            clearable
          >
            <el-option
              v-for="item in province"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.distributorName"
            clearable
            placeholder="发货方名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.receiverName"
            clearable
            placeholder="终端名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.receiverTypeLevelOneIds"
            :loading="receiverTypeLevelThreeLoading"
            multiple
            class="filter-item"
            placeholder="终端类型"
            clearable
          >
            <el-option
              v-for="item in receiverTypeLevelThreeList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.customerCategoryIds"
            class="filter-item"
            placeholder="客户类型"
            multiple
            clearable
          >
            <el-option
              v-for="item in customerCategoryList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.employeeDisplayName"
            clearable
            placeholder="负责人"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.isHospitalStore"
            class="filter-item"
            placeholder="是否关联药店"
            clearable
          >
            <el-option
              v-for="item in hospitalStoreStatus"
              :key="item.value"
              :label="item.key"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.timeType"
            class="filter-item"
            placeholder="时间类型"
          >
            <el-option
              v-for="item in timeTypes"
              :key="item.key"
              :label="item.key"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-date-picker
            v-if="listQuery.timeType==='year'"
            v-model="listQuery.saleYear"
            type="year"
            format="yyyy"
            style="width: 100%"
            value-format="yyyy"
            placeholder="请选择年份"
          />
          <el-date-picker
            v-if="listQuery.timeType==='month'"
            v-model="listQuery.timeRange"
            clearable
            class="filter-item"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            format="yyyy-MM"
            value-format="yyyy-MM"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.enumAbnormalTypes"
            multiple
            class="filter-item"
            placeholder="异常预警类型"
            clearable
          >
            <el-option
              v-for="item in abnormalSalesTypeList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'MedicalCustomerReport_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'MedicalCustomerReport_Button_Export')"
            :loading="btnExportLoading"
            class="filter-item-button"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-row>
          <el-col :span="24">
            <label class="dataupdate">最新计算日期：{{ dataUpdateTime }}</label>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-table
              v-loading="listLoading"
              :data="list"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%;"
              :default-sort="{prop: 'createTime', order: 'descending'}"
              :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass"
              @sort-change="sortChange"
            >
              <el-table-column
                fixed
                label="序号"
                type="index"
                align="center"
                :index="indexMethod"
              />
              <el-table-column
                fixed
                label="厂家"
                sortable="custom"
                min-width="100px"
                header-align="center"
                align="center"
                prop="ManufacturerName"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.manufacturerName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                fixed
                sortable="custom"
                label="产品"
                min-width="90px"
                align="center"
                prop="ProductName"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.productName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                fixed
                label="规格"
                min-width="110px"
                align="center"
                sortable="custom"
                prop="Specification"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.specification }}</span>
                </template>
              </el-table-column>
              <el-table-column
                fixed
                label="部门名称"
                min-width="100px"
                align="center"
                sortable="custom"
                prop="DepartmentName"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.departmentName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                fixed
                label="大区名称"
                min-width="100px"
                align="center"
                sortable="custom"
                prop="ReceiverAreaName"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.receiverAreaName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="省份"
                min-width="100px"
                header-align="center"
                align="center"
                sortable="custom"
                prop="ReceiverProvinceName"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.receiverProvinceName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="发货方名称"
                min-width="260px"
                header-align="center"
                align="left"
                sortable="custom"
                prop="DistributorName"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.distributorName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="终端名称"
                min-width="260px"
                header-align="center"
                align="left"
                sortable="custom"
                prop="RelatedReceiverName"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.relatedReceiverName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="是否关联药店"
                min-width="120px"
                header-align="center"
                align="center"
                sortable="custom"
                prop="IsHospitalStore"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.isHospitalStoreDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="关联药店名称"
                min-width="100px"
                header-align="center"
                align="left"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.relatedPharmacy }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="月份"
                min-width="80px"
                header-align="center"
                align="center"
                sortable="custom"
                prop="SaleDate"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.saleYear }}-{{ row.saleMonth.toString().padStart(2,'0') }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="负责人"
                min-width="100px"
                header-align="center"
                align="center"
                sortable="custom"
                prop="PrincipalName"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.principalName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="终端类型"
                min-width="100px"
                header-align="center"
                align="center"
                sortable="custom"
                prop="RelatedReceiverThirdTypeName"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.relatedReceiverThirdTypeName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="客户分类属性"
                min-width="120px"
                header-align="center"
                align="center"
                sortable="custom"
                prop="CustomerTypeName"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.customerTypeName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="销售数量"
                min-width="100px"
                header-align="center"
                align="center"
                sortable="custom"
                prop="Quantity"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.quantity }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="上月销售数量"
                min-width="120px"
                header-align="center"
                align="center"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.compareMonthlyQuantity }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="销量变化"
                min-width="100px"
                header-align="center"
                align="center"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.salesQuantityChange }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="近X月平均销量"
                min-width="120px"
                header-align="center"
                align="center"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.historyAvgQuantityFormat }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="预警信息"
                min-width="100px"
                header-align="center"
                align="left"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.warningAllMessage }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col class="el-colRight">
            <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
          </el-col>
        </el-row>
      </el-row>
    </div>
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出报表"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import CustomExport from '@/components/Export/CustomExport'
import ProductService from '@/api/product'
import MaintenanceService from '@/api/maintenance'
import LocationService from '@/api/location'
import MasterDataService from '@/api/masterData'

import ReportService from '@/api/report'

export default {
  name: 'MedicalCustomer',
  components: {
    Pagination,
    CustomExport
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 0,
        pageSize: 10,
        timeType: 'year',
        timeRange: [],
        saleYear: null
      },
      listLoading: false,
      list: [],
      manufacturerLoading: false,
      manufacturerAndProductAndSpecId: [],
      manufacturerAndProductAndSpecList: [],
      deptLoading: false,
      deptList: [],
      manufacturerList: [],
      province: [],
      allAreaList: [],
      areaList: [],
      customerCategoryList: [],
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: {},
      hospitalStoreStatus: [{ 'key': '是', 'value': true }, { 'key': '否', 'value': false }],
      timeTypes: [{ 'key': '年', 'value': 'year' }, { 'key': '月份', 'value': 'month' }],
      receiverTypeLevelThreeLoading: false,
      receiverTypeLevelThreeList: [],
      abnormalSalesTypeList: [],
      dataUpdateTime: ''
    }
  },
  created() {
    this.initManufacturerAndProductAndSpec()
    this.initDept()
    this.initArea()
    this.queryProvince()
    this.initTargetReceiverType()
    this.initReceiverTypeLevelThreeList()
    this.initAbnormalSalesType()
    this.handleFilter()
    this.getSalesFlowUpdateDate()
  },
  methods: {
    getSalesFlowUpdateDate() {
      ReportService.GetSalesFlowUpdateDate()
        .then((result) => {
          this.dataUpdateTime = result.data
        })
        .catch((error) => {
          console.log(error)
        })
    },
    initManufacturerAndProductAndSpec() {
      this.productAndSpecLoading = true
      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then((result) => {
          this.manufacturerAndProductAndSpecList = result
          this.manufacturerLoading = false
        })
        .catch(() => {
          this.manufacturerLoading = false
        })
    },
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch(() => {
          this.deptLoading = false
        })
    },
    initArea() {
      MaintenanceService.GetAllAreas()
        .then((result) => {
          this.allAreaList = result.data
          ('this.allAreaList', this.allAreaList)
        })
        .catch((error) => {
          console.log(error)
        })
    },
    queryProvince() {
      LocationService.QueryProvince()
        .then((result) => {
          this.province = result
        })
        .catch((error) => {
          console.log(error)
        })
    },
    initTargetReceiverType() {
      MaintenanceService.QueryAllCustomerCategory()
        .then((result) => {
          this.customerCategoryList = result.data
        })
        .catch((error) => {
          console.log(error)
        })
    },
    initReceiverTypeLevelThreeList() {
      this.receiverTypeLevelThreeLoading = true
      MaintenanceService.GetReceiverTypeThreeByParent({ parentCode: 'Medical_Institution' }).then(res => {
        this.receiverTypeLevelThreeList = res
        this.receiverTypeLevelThreeLoading = false
      })
        .catch((error) => {
          this.receiverTypeLevelThreeLoading = false
          console.log(error)
        })
    },
    initDistributorType() {
      this.enumDistributorTypeLoading = true
      MasterDataService.GetEnumInfos({ enumType: 'DistributorType' })
        .then((result) => {
          this.enumDistributorTypeLoading = false
          this.enumDistributorTypeList = result.data.datas
        })
        .catch((error) => {
          this.enumDistributorTypeLoading = false
          console.log(error)
        })
    },
    initAbnormalSalesType() {
      const enumTypeList = ['AbnormalSalesType', 'StopPurchaseRisk', 'ChannelChangeType']
      MasterDataService.GetEnumInfoList({ enumTypes: enumTypeList })
        .then((result) => {
          this.abnormalSalesTypeList = result.data.datas
        })
        .catch((error) => {
          console.log(error)
        })
    },
    handleProductChange() {
      this.$refs.refManufacturerAndProductAndSpec.dropDownVisible = false
    },
    handelDepartmentChange() {
      this.areaList = this.allAreaList.filter(item => { return item.departmentId === this.listQuery.departmentId })
    },
    handelAreaChange() {
      if (this.listQuery.areaIds !== null && this.listQuery.areaIds.length > 0) {
        this.queryProvinceByAreaIds()
      } else {
        this.queryProvince()
      }
    },
    queryProvinceByAreaIds() {
      const para = { areaIds: this.listQuery.areaIds }
      MaintenanceService.QueryProvinceSelectByAreaIds(para)
        .then((result) => {
          this.provinceList = result
        })
        .catch((error) => {
          console.log(error)
          this.loading = false
        })
    },
    timeChange() {
      this.$forceUpdate()
    },
    handleFilter() {
      // 如果按月筛选，清空年的筛选条件
      if (this.listQuery.timeType === 'month') {
        this.listQuery.saleYear = null
      }
      // 如果按年筛选，清空月的筛选条件
      if (this.listQuery.timeType === 'year') {
        this.listQuery.timeRange = []
      }
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      if (this.manufacturerAndProductAndSpecId) {
        const [manufacturerId, productId, productSpecId] = this.manufacturerAndProductAndSpecId
        this.listQuery.productId = productId
        this.listQuery.productSpecId = productSpecId
        this.listQuery.manufacturerId = manufacturerId
      }
      this.listLoading = true
      ReportService.QueryMedicalCustomerReport(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      ReportService.GetMedicalCustomerExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      var exportParam = JSON.parse(JSON.stringify(this.listQuery))
      exportParam.checkedColumns = checkColumns
      ReportService.ExportMedicalCustomer(exportParam)
        .then(result => {
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '医疗客户报表.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '医疗客户报表.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    }
  }
}
</script>
<style>
  .dataupdate{
    font-size: 14px;
    line-height: 25px;
    color: #606266;
  }
</style>
