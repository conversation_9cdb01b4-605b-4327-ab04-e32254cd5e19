<template>
  <div>
    <el-dialog custom-class="el-dialog-s" :title="title" width="80%" append-to-body :close-on-click-modal="false" :visible="showAddDialog" @close="closeDialog">
      <el-form
        ref="dataForm"
        :model="dataModel"
        label-position="right"
        label-width="100px"
        class="el-dialogform"
        style="width: 90%;"
        :rules="rules"
      >
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="甲方" prop="name">
              <el-input
                v-model="dataModel.rebatePartyName"
                placeholder="甲方"
                class="filter-item"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="乙方" prop="rebateReceiverName">
              <el-row>
                <el-col :span="!isEdit?20:24" style="padding-right:5px">
                  <el-input
                    v-model="dataModel.rebateReceiverName"
                    readonly
                    placeholder="乙方"
                    maxlength="300"
                  />
                </el-col>
                <el-col v-if="!isEdit" :span="4">
                  <el-button

                    class="filter-item"
                    type="primary"
                    icon="el-icon-search"
                    title="选择乙方"
                    @click="handleSelectReceiver"
                  />
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="支付方式" prop="paymentTypeId">
              <el-select
                v-model="dataModel.paymentTypeId"
                class="filter-item"
                placeholder="支付方式"
                clearable
              >
                <el-option
                  v-for="item in rebatePaymentTypeList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="支付天数" prop="payWithinDaysId">
              <el-select
                v-model="dataModel.payWithinDaysId"
                class="filter-item"
                placeholder="支付天数"
                clearable
              >
                <el-option
                  v-for="item in rebatePayWithInDaysList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="起始日期" prop="startDate">
              <el-date-picker
                v-model="dataModel.startDate"
                type="date"
                style="width: 100%"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="起始日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="截止日期" prop="endDate">
              <el-date-picker
                v-model="dataModel.endDate"
                type="date"
                style="width: 100%"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="截止日期"
              />
            </el-form-item>
          </el-col>

          <el-col :span="span">
            <el-form-item label="签署日期" prop="signDate">
              <el-date-picker
                v-model="dataModel.signDate"
                type="date"
                style="width: 100%"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="签署日期"
              />
            </el-form-item>
          </el-col>

        </el-row>

      </el-form>
      <el-divider content-position="left">合作主体名单</el-divider>
      <el-row style="margin-bottom: 10px;">
        <el-col :offset="16" :span="4" class="btn-group">
          <el-link :href="'/template/RebateAgreementCertificateReceiverTemplate.xlsx'" type="primary" icon="el-icon-document">模板下载</el-link>
          <file-upload
            :controller="controller"
            :method="method"
            @uploadSuccess="uploadSuccess"
          >
            <!-- <el-button
              class="filter-item"
              type="primary"
              icon="el-icon-upload2"
            >
              导入
            </el-button> -->
          </file-upload>

        </el-col>
        <el-col :offset="1" :span="2">
          <el-button
            class="filter-item"
            type="primary"
            icon="el-icon-plus"
            @click="showChooseCertificateReceiver"
          >
            添加
          </el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-table
            max-height="250"
            :data="dataModel.rebateAgreementCertificateReceivers"
            stripe
            border
            fit
            highlight-current-row
            :header-cell-class-name="'tableStyle'"
            style="width: 100%;"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
            />
            <el-table-column label="主体名称" align="left" min-width="120px" prop="receiverName" />
            <el-table-column label="纳税人识别号" header-align="center" align="left">
              <template slot-scope="{ row }">
                <span>{{ row.taxRegistrationNo ==='NULL'?'': row.taxRegistrationNo }}</span>
              </template>
            </el-table-column>
            <el-table-column label="备注" header-align="center" align="left" prop="remark">
              <template slot-scope="{ row }">
                <el-input
                  v-model="row.remark"
                  placeholder="备注"
                  class="filter-item"
                />
              </template>

            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="120" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i class="el-icon-delete eltablei" title="删除" @click="handleDelDetail(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeDialog">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          :loading="btnSaveLoading"
          @click="submitForm"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <!--选择乙方-->
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="选择乙方"
      :close-on-click-modal="false"
      :visible="dialogDistributorVisible"
      width="80%"
      class="popup-search"
      @close="closeDistributorDialog"
    >
      <SelectReceiver
        ref="refSelectDistributor"
        :show-dialog="dialogDistributorVisible"
        :distributor-types="distributorTypes"
        :is-stopped="false"
        @success="selectDistributorSuccess"
      />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeDistributorDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <!--选择合作主体-->
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="选择合作主体"
      :close-on-click-modal="false"
      :visible="dialogReceiverVisible"
      width="80%"
      class="popup-search"
      @close="closeReceiverDialog"
    >
      <SelectReceiver
        ref="refSelectReceiver"
        :show-dialog="dialogReceiverVisible"
        :distributor-types="distributorTypes"
        :is-stopped="false"
        @success="selectCertificateReceiver"
      />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeReceiverDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import AgreementService from '@/api/agreement'
import SelectReceiver from '@/views/components/selectReceiver'
import FileUpload from '@/views/components/autoUploadFile'
export default {
  components: {
    SelectReceiver,
    FileUpload
  },
  data() {
    const validEndDateTime = (rule, value, callback) => {
      const startdate = Date.parse(this.dataModel.startDate)
      const endDate = Date.parse(value)
      if (!this.dataModel.startDate) {
        callback('请先选择开始时间')
        this.dataModel.endDate = ''
      }
      if (startdate > endDate) {
        callback('结束时间不能小于开始时间')
      }
      callback()
    }
    return {
      span: 12,
      showAddDialog: false,
      dataModel: {
        queryRebatePartyReceiver: '',
        rebateAgreementCertificateReceivers: []
      },
      enumStatusList: [],
      rebatePaymentTypeList: [],
      rebatePayWithInDaysList: [],
      controller: 'RebateAgreement',
      method: 'ImportCertificateReceiver',
      rules: {
        rebateReceiverName: [
          {
            required: true,
            message: '请选择乙方',
            trigger: 'change'
          }
        ],
        paymentTypeId: [
          {
            required: true,
            message: '请选择支付方式',
            trigger: 'change'
          }
        ],
        payWithinDaysId: [
          {
            required: true,
            message: '请选择支付天数',
            trigger: 'change'
          }
        ],
        startDate: [
          {
            required: true,
            message: '请选择开始日期',
            trigger: 'change'
          }
        ],
        endDate: [
          {
            required: true,
            message: '请选择截止日期',
            trigger: 'change'
          },
          {
            type: 'string',
            validator: validEndDateTime,
            trigger: 'change'
          }
        ],
        signDate: [
          {
            required: true,
            message: '请选择签署日期',
            trigger: 'change'
          }
        ]
      },
      btnSaveLoading: false,
      isEdit: false,
      dialogDistributorVisible: false,
      dialogVisible: false,
      btnLoading: false,
      distributorTypes: [],
      dialogReceiverVisible: false
    }
  },
  computed: {
    title: function() {
      return this.isEdit ? '编辑一级商名单备忘录' : '新增一级商名单备忘录'
    }
  },
  created() {
    // this.queryRebatePartyReceiver()
  },
  methods: {
    init(row) {
      this.queryRebatePaymentType()
      this.queryRebatePayWithInDays()
      if (row) {
        this.isEdit = true
        AgreementService.GetRebateAgreementCertificateByID({ id: row.id }).then(res => {
          this.dataModel = res.data
        }).catch(() => {
        })
      } else {
        this.isEdit = false
      }
      this.showAddDialog = true
      this.queryRebatePartyReceiver()
    },

    queryRebatePartyReceiver() {
      AgreementService.QueryRebatePartyReceiver().then(res => {
        this.dataModel.rebatePartyName = res.data.name
        this.dataModel.partyAId = res.data.id
        this.$forceUpdate()
      }).catch(() => {
      })
    },
    queryRebatePaymentType() {
      AgreementService.QueryRebatePaymentType().then(res => {
        this.rebatePaymentTypeList = res.data.datas
      }).catch(() => {
      })
    },
    queryRebatePayWithInDays() {
      AgreementService.QueryRebatePayWithInDays().then(res => {
        this.rebatePayWithInDaysList = res.data.datas
      }).catch(() => {
      })
    },
    handleSelectReceiver() {
      this.distributorTypes = [10, 20]
      this.dialogDistributorVisible = true
    },
    closeDistributorDialog() {
      this.dialogDistributorVisible = false
      this.$refs.refSelectDistributor.clear()
      this.distributorTypes = []
    },
    selectDistributorSuccess(val) {
      if (val.id === this.dataModel.partyAId) {
        this.$message.error('乙方不能和甲方相同')
      } else {
        this.$set(this.dataModel, 'rebateReceiverId', val.id)
        this.$set(this.dataModel, 'rebateReceiverName', val.name)
        this.closeDistributorDialog()
      }
    },
    showChooseCertificateReceiver() {
      if (!this.dataModel.rebateReceiverName) {
        this.$message.error('请先选择乙方')
        return false
      }
      this.distributorTypes = [10, 20]
      this.dialogReceiverVisible = true
    },
    closeReceiverDialog() {
      this.dialogReceiverVisible = false
      this.$refs.refSelectReceiver.clear()
      this.distributorTypes = []
    },
    selectCertificateReceiver(val) {
      if (val.name === this.dataModel.rebatePartyName) {
        this.$message.error('合作主体不能和甲方相同')
        return false
      } else if (val.name === this.dataModel.rebateReceiverName) {
        this.$message.error('合作主体不能和乙方相同')
        return false
      }
      if (!this.dataModel.rebateAgreementCertificateReceivers.some(s => { return s.receiverName === val.name })
      ) {
        this.dataModel.rebateAgreementCertificateReceivers.push({
          receiverName: val.name,
          receiverId: val.id,
          taxRegistrationNo: val.taxRegistrationNo
        })
        this.closeReceiverDialog()
      } else {
        this.$message.error('合作主体已存在')
      }
    },
    uploadSuccess(val) {
      if (!this.dataModel.rebateReceiverName) {
        this.$message.error('请先选择乙方')
        return false
      }
      val.datas.map(item => {
        if (!this.dataModel.rebateAgreementCertificateReceivers.some(s => { return s.receiverName === item.receiverName }) &&
      (this.dataModel.rebateReceiverName !== undefined && item.receiverName !== this.dataModel.rebateReceiverName) &&
      (this.dataModel.rebatePartyName !== undefined && item.receiverName !== this.dataModel.rebatePartyName)) {
          this.dataModel.rebateAgreementCertificateReceivers.push(item)
        }
      })
    },
    handleDelDetail(row) {
      this.$confirm('确定删除合作主体?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (row.id) {
          AgreementService.DeleteRebateAgreementCertificateReceiver(row).then(result => {
            if (result.succeed) {
              this.$notice.message('删除成功', 'success')
              var index = this.dataModel.rebateAgreementCertificateReceivers.findIndex(f => { return f.id === result.data.id })

              this.dataModel.rebateAgreementCertificateReceivers.splice(index, 1)
            }
          })
        } else {
          var index = this.dataModel.rebateAgreementCertificateReceivers.findIndex(f => { return f.receiverId === row.receiverId })
          this.dataModel.rebateAgreementCertificateReceivers.splice(index, 1)
        }
      })
    },
    submitForm() {
      if (!this.dataModel.rebateAgreementCertificateReceivers || this.dataModel.rebateAgreementCertificateReceivers.length === 0) {
        this.$message.error('需要至少一个合作主体')
        return false
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.btnSaveLoading = true
          if (!this.isEdit) {
            AgreementService.AddRebateAgreementCertificate(this.dataModel).then(result => {
              this.btnSaveLoading = false
              if (result.succeed) {
                this.$notice.message('新增成功', 'success')
                this.$emit('refreshData')
                this.closeDialog()
              } else {
                if (result.type !== -3) {
                  this.$notice.resultTip(result)
                }
              }
            }).catch(() => {
              this.btnSaveLoading = false
            })
          } else {
            AgreementService.UpdateRebateAgreementCertificate(this.dataModel).then(result => {
              this.btnSaveLoading = false
              if (result.succeed) {
                // this.agreementTemplateModel = result.data
                this.$notice.message('编辑成功', 'success')
                this.$emit('refreshData')
                this.closeDialog()
              } else {
                if (result.type !== -3) {
                  this.$notice.resultTip(result)
                }
              }
            }).catch(() => {
              this.btnSaveLoading = false
            })
          }
        }
      })
    },
    closeDialog() {
      this.showAddDialog = false
      this.$refs.dataForm.resetFields()
      this.dataModel = { rebateAgreementCertificateReceivers: [] }
    }
  }
}
</script>
<style scoped>
.el-collapse-item ::v-deep .el-collapse-item__header {
  font-size: 14px !important;
  font-weight: 600 !important;
}
.btn-group{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
</style>
