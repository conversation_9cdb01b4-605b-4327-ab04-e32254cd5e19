<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filter-container">
        <el-col :span="span">
          <el-input
            v-model="listQuery.code"
            clearable
            placeholder="协议编号"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.name"
            clearable
            placeholder="协议名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.rebateAgreementTemplateTypeId"
            style="width: 100%"
            class="filter-item"
            placeholder="协议类型"
            clearable
            @change="selectUpdate"
          >
            <el-option
              v-for="item in rebateAgreementTemplateTypeList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.departmentId"
            value-key="value"
            :loading="deptLoading"
            class="filter-item"
            placeholder="部门"
            clearable
            @change="selectUpdate"
          >
            <el-option
              v-for="item in deptList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProductAndSpec"
            v-model="manufacturerProductAndSpecId"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            placeholder="厂商 / 产品 / 规格"
            style="width:100%"
            clearable
            class="filter-item"
            :props="{ multiple: true, checkStrictly: false ,expandTrigger: 'hover', emitPath: true }"
            @change="handleProductChange"
          />
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProvinceCity"
            v-model="receiverProvinceCityId"
            :loading="provinceCityLoading"
            :options="provinceCityList"
            :props="{ checkStrictly: true ,expandTrigger: 'hover'}"
            placeholder="省份 / 城市"
            clearable
            style="width:100%"
            @change="provinceCitChange"
          />
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="listQuery.timeRange"
            clearable
            class="filter-item"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.rebateReceiverName"
            clearable
            placeholder="返利接收方"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.rebateProjectName"
            clearable
            placeholder="返利项目"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.enumStatus"
            class="filter-item"
            placeholder="状态"
            clearable
          >
            <el-option
              v-for="item in enumStatusList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.isExceedBasePrice"
            class="filter-item"
            placeholder="超出项目补偿单价"
            clearable
          >
            <el-option
              v-for="item in trueAndFalseList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.isUploadSealedContract"
            class="filter-item"
            placeholder="是否已盖章"
            clearable
          >
            <el-option
              v-for="item in trueAndFalseList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.isMergeCalculate"
            class="filter-item"
            placeholder="是否合并计算"
            clearable
          >
            <el-option
              v-for="item in trueAndFalseList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.isManuallyCalculate"
            class="filter-item"
            placeholder="是否手工计算"
            clearable
          >
            <el-option
              v-for="item in trueAndFalseList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.enumSigningPartiesType"
            class="filter-item"
            placeholder="协议签署方"
            clearable
          >
            <el-option
              v-for="item in signingPartiesTypeList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'Agreement_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Agreement_Button_Import')"
            type="primary"
            icon="el-icon-upload2"
            @click="handleImport"
          >
            协议导入
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Agreement_Button_BatchGenerateContract')"
            type="primary"
            icon="el-icon-check"
            @click="handelBatchGenerateContract"
          >
            批量生成导入协议PDF
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Agreement_Button_PackageDownloadContract')"
            type="primary"
            icon="el-icon-check"
            @click="handelPackageDownloadContract"
          >
            打包下载协议
          </el-button>
        </el-col>
      </el-row>
    </div>

    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="rebateAgreementList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column fixed label="协议编号" align="center" sortable="custom" min-width="120px" prop="Code">
              <template slot-scope="{ row }">
                <span>{{ row.code }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed
              label="协议名称"
              sortable="custom"
              min-width="210px"
              header-align="center"
              align="left"
              prop="Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="签署方类型" align="center" sortable="custom" min-width="110px" prop="RebateAgreementTemplate.EnumSigningPartiesType">
              <template slot-scope="{ row }">
                <span>{{ row.enumSigningPartiesTypeDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="部门" align="center" sortable="custom" min-width="90px" prop="Department.Name">
              <template slot-scope="{ row }">
                <span>{{ row.departmentName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="返利接收方省份"
              sortable="custom"
              min-width="130px"
              align="center"
              prop="RebateReceiver.Province.NameCn"
            >
              <template slot-scope="{ row }">
                <span>{{ row.rebateReceiverProvinceName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="返利接收方城市"
              sortable="custom"
              min-width="130px"
              align="center"
              prop="RebateReceiver.City.NameCn"
            >
              <template slot-scope="{ row }">
                <span>{{ row.rebateReceiverCityName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="返利接收方"
              sortable="custom"
              min-width="150px"
              header-align="center"
              align="left"
              prop="RebateReceiver.Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.rebateReceiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="返利项目"
              sortable="custom"
              min-width="100px"
              align="center"
              prop="RebateProject.Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.rebateProjectName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="品规"
              min-width="100px"
              align="center"
              prop="productSpecDesc"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productSpecDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="起始日期" align="center" sortable="custom" min-width="100px" prop="StartDate">
              <template slot-scope="{ row }">
                <span v-if="row.startDate">{{ row.startDate |parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="截止日期" align="center" sortable="custom" min-width="100px" prop="EndDate">
              <template slot-scope="{ row }">
                <span v-if="row.endDate">{{ row.endDate |parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="合同已盖章"
              min-width="90px"
              align="center"
              prop="IsUploadSealedContract"
            >
              <template slot-scope="{ row }">
                <span>{{ row.isUploadSealedContractDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="已签补偿类型"
              min-width="100px"
              align="center"
              prop="templateTypeName"
            >
              <template slot-scope="{ row }">
                <span>{{ row.templateTypeName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="是否合并计算"
              min-width="100px"
              align="center"
              prop="isMergeCalculateDesc"
            >
              <template slot-scope="{ row }">
                <span>{{ row.isMergeCalculateDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="是否手工计算"
              min-width="100px"
              align="center"
              prop="isManuallyCalculate"
            >
              <template slot-scope="{ row }">
                <span>{{ row.isManuallyCalculateDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="是否超出项目补偿单价"
              min-width="160px"
              align="center"
              prop="isExceedBasePrice"
            >
              <template slot-scope="{ row }">
                <span>{{ row.isExceedBasePrice?'是': '否' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="状态"
              min-width="60px"
              align="center"
              prop="enumStatus"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="120" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i class="el-icon-document eltablei" title="查看明细" @click="reviewDetail(row)" />
                <i v-if="!row.isMasteerAgreement && row.enumStatus !== $constDefinition.rebateAgreementStatus.ImportPart && row.enumStatus !== $constDefinition.rebateAgreementStatus.Import && row.rebateAgreementTemplateTypeCode !== $constDefinition.rebateAgreementTemplateType.changeChannels" class="el-icon-date eltablei" title="查看返利计算详情" @click="reviewTaskDetail(row)" />
                <i v-if="!row.isManuallyCalculate && !row.isMasteerAgreement && row.enumStatus !== $constDefinition.rebateAgreementStatus.ImportPart && row.enumStatus !== $constDefinition.rebateAgreementStatus.Import && row.enumStatus !== $constDefinition.rebateAgreementStatus.InApproval && row.enumStatus !== $constDefinition.rebateAgreementStatus.Obsolete && row.rebateAgreementTemplateTypeCode !== $constDefinition.rebateAgreementTemplateType.changeChannels" class="el-icon-edit eltablei" title="设置为手动填写返利金额" @click="setManuallyCalculate(row)" />
                <i v-if="!row.isUploadSealedContract && row.enumStatus !== $constDefinition.rebateAgreementStatus.ImportPart && row.enumStatus !== $constDefinition.rebateAgreementStatus.Import" class="el-icon-upload eltablei" title="回传盖章协议" @click="handleUploadShow(row)" />
                <i v-if="row.enumStatus !== $constDefinition.rebateAgreementStatus.ImportPart && row.enumStatus !== $constDefinition.rebateAgreementStatus.Import" class="el-icon-download eltablei" title="协议下载" @click="handleDownLoadTemplate(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :close-on-click-modal="false"
      title="上传文件"
      :visible="dialogUploadFormVisible"
      width="45%"
      @close="handleUploadHidden"
    >
      <el-form
        label-position="right"
        label-width="110px"
        class="el-dialogform"
      >
        <el-form-item label="选择文件" prop="nameCn">
          <file-upload
            ref="upload"
            @getUploadFile="getUploadFile"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleUploadHidden()"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handelUploadSealedContract"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="导入协议"
      :close-on-click-modal="false"
      :visible="dialogImportVisible"
      width="80%"
      class="popup-search"
      @close="closeImportDialog"
    >
      <ImportRebateAgreement ref="refImportAgreement" :show-import="dialogImportVisible" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeImportDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="打包下载协议"
      :close-on-click-modal="false"
      :visible="dialogPackageDownloadContractVisible"
      width="50%"
      class="popup-search"
      @close="closePackageDownloadContractDialog"
    >
      <div style="width:90%;margin:auto;margin-top:10px;">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="12">
            <el-date-picker
              v-model="downloadContractFilter.startYear"
              type="year"
              format="yyyy"
              style="width: 100%"
              value-format="yyyy"
              placeholder="协议开始年份"
              :clearable="false"
            />
          </el-col>
          <el-col :span="12">
            <el-select
              v-model="downloadContractFilter.contractStatus"
              style="width: 100%"
              class="filter-item"
              placeholder="是否盖章协议"
            >
              <el-option
                v-for="item in contractStatus"
                :key="item.value"
                :label="item.label"
                :value="item.label"
              />
            </el-select>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-download" @click="handleDownLoadZipContract">
          下载
        </el-button>
        <el-button icon="el-icon-close" @click="closePackageDownloadContractDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <AgreementView ref="refAgreementView" />
    <RebateResultTaskView ref="refRebateResultTaskView" />

  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

import AgreementService from '@/api/agreement'
import MaintenanceService from '@/api/maintenance'
import LocationService from '@/api/location'
import ProductService from '@/api/product'
import FileService from '@/api/file'
import MasterDataService from '@/api/masterData'

import FileUpload from '@/views/components/uploadPdfFile'
import AgreementView from './components/agreementView'
import RebateResultTaskView from './components/rebateResultTaskView'
import ImportRebateAgreement from './components/importRebateAgreementSummary'

export default {
  name: 'AgreementQuery',
  components: {
    Pagination,
    FileUpload,
    AgreementView,
    RebateResultTaskView,
    ImportRebateAgreement
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      rebateAgreementList: [],
      deptLoading: false,
      deptList: [],
      provinceCityLoading: false,
      provinceCityList: [],
      productAndSpecList: [],
      productAndSpecLoading: false,
      manufacturerProductAndSpecId: [],
      rebateAgreementId: null,
      dialogUploadFormVisible: false,
      method: 'UploadSealedContract',
      controller: 'RebateAgreement',
      enumStatusList: [],
      dialogImportVisible: false,
      btnBatchGenerateContractLoading: false,
      dialogPackageDownloadContractVisible: false,
      contractStatus: [
        {
          'value': 'sealed',
          'label': '已盖章'
        },
        {
          'value': 'notStamped',
          'label': '未盖章'
        }
      ],
      downloadContractFilter: {
        contractStatus: '已盖章',
        startYear: new Date().getFullYear().toString()
      },
      rebateAgreementTemplateTypeList: [],
      trueAndFalseList: [
        {
          value: true,
          label: '是'
        },
        {
          value: false,
          label: '否'
        }
      ],
      signingPartiesTypeList: [],
      receiverProvinceCityId: null
    }
  },
  created() {
    this.initDept()
    this.initProvinceCity()
    this.initProductAndSpec()
    this.initAgreementStatus()
    this.initAgreementType()
    this.initSigningPartiesType()
    this.handleFilter()
  },
  methods: {
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch(() => {
          this.deptLoading = false
        })
    },
    initProvinceCity() {
      this.provinceCityLoading = true
      LocationService.QueryProvinceCityCascader()
        .then((result) => {
          this.provinceCityList = result
          this.provinceCityLoading = false
        })
        .catch((error) => {
          this.provinceCityLoading = false
          console.log(error)
        })
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch(() => {
          this.productAndSpecLoading = false
        })
    },
    initAgreementStatus() {
      var param = {
        enumType: 'RebateAgreementStatus'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.enumStatusList = result.data.datas
          this.$forceupdate()
        })
        .catch(() => {
        })
    },
    initAgreementType() {
      AgreementService.QueryAgreementTemplateTypeCascader().then((result) => {
        this.rebateAgreementTemplateTypeList = result
        this.$forceUpdate()
      })
        .catch((error) => {
          console.log(error)
        })
    },
    initSigningPartiesType() {
      var param = {
        enumType: 'SigningPartiesType'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.signingPartiesTypeList = result.data.datas
          this.$forceupdate()
        })
        .catch(() => {
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      if (this.receiverProvinceCityId) {
        const [receiverProvinceId, receiverCityId] = this.receiverProvinceCityId
        this.listQuery.provinceId = receiverProvinceId
        this.listQuery.cityId = receiverCityId
      }
      this.getList()
    },
    getList() {
      if (this.manufacturerProductAndSpecId) {
        this.listQuery.ProductSpecIds = this.manufacturerProductAndSpecId.map(element => element[2])
      }

      this.listLoading = true
      this.listQuery.quotaType = 1
      AgreementService.QueryRebateAgreement(this.listQuery).then(res => {
        this.listLoading = false
        this.rebateAgreementList = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(() => {
        this.listLoading = true
      })
    },
    selectUpdate() {
      this.$forceUpdate()
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    provinceCitChange() {
      this.$refs.refProvinceCity.dropDownVisible = false
    },
    handleProductChange() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    handleUploadShow(row) {
      this.dialogUploadFormVisible = true
      this.rebateAgreementId = row.id
    },
    handleUploadHidden() {
      this.$refs.upload.handleRemove()
      this.dialogUploadFormVisible = false
    },
    getUploadFile(val) {
      this.file = val
    },
    handelUploadSealedContract() {
      if (this.file === null) {
        this.$notice.message('请选择文件。', 'error')
      } else {
        const formData = new FormData()
        formData.append('rebateAgreementId', this.rebateAgreementId)
        FileService.uploadTemplate(this.file.raw, formData, this.controller, this.method).then(res => {
          this.handleUploadHidden()
          this.handleFilter()
          this.$refs.upload.handleRemove()
          this.file = null
        })
      }
    },
    handleDownLoadTemplate(row) {
      var objectType = 'SealedRebateContract'
      if (!row.isUploadSealedContract) {
        objectType = 'RebateContract'
      }
      FileService.downloadAttachmentBusiness(row.id, objectType).then(res => {
        const fileDownload = require('js-file-download')
        fileDownload(res, `${row.name}.pdf`)
      })
        .catch(() => {
        })
    },
    reviewDetail(row) {
      this.$refs.refAgreementView.initPage(row.id)
    },

    reviewTaskDetail(row) {
      this.$refs.refRebateResultTaskView.initPage(row.id)
    },
    setManuallyCalculate(row) {
      this.$confirm('确定将该协议变更为手动填写返利金额吗，变更后将删除该协议的自动计算返利任务?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        AgreementService.UpdateRebateAgreementToManuallyCalculate(row).then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('已变更为手动填写返利结果', 'success')
          }
        })
          .catch(error => {
            this.listLoading = false
            console.log(error)
          })
      })
    },
    handleImport() {
      this.dialogImportVisible = true
    },
    closeImportDialog() {
      this.$refs.refImportAgreement.clear()
      this.dialogImportVisible = false
      this.handleFilter()
    },
    handelBatchGenerateContract() {
      AgreementService.GetWaitingGenerateContract().then(res => {
        if (res.succeed) {
          if (res.data > 0) {
            const msg = '还有' + res.data + '个合同文件正常生成中，请稍后再试。'
            this.$confirm(msg, '提示', {
              confirmButtonText: '确定',
              showCancelButton: false,
              cancelButtonText: '取消',
              type: 'warning'
            })
          } else {
            AgreementService.BatchGenerateContract().then(result => {
              if (result.succeed) {
                this.$notice.message('协议已提交异步生成，稍后刷新查看', 'success')
              }
            })
          }
        }
      })
    },
    handelPackageDownloadContract() {
      this.dialogPackageDownloadContractVisible = true
    },
    closePackageDownloadContractDialog() {
      this.downloadContractFilter.contractStatus = this.contractStatus[0].label
      this.downloadContractFilter.startYear = new Date().getFullYear().toString()
      this.dialogPackageDownloadContractVisible = false
    },
    handleDownLoadZipContract() {
      AgreementService.PackageDownloadContract(this.downloadContractFilter).then(res => {
        const fileDownload = require('js-file-download')
        const contractName = this.downloadContractFilter.startYear + this.downloadContractFilter.contractStatus + '协议.zip'
        fileDownload(res, contractName)
      })
        .catch(() => {
        })
    }
  }
}
</script>
<style scoped>
.flexwarp {
    display: flex;
    flex-wrap: wrap;
}

</style>

