<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filter-container">
        <el-col :span="span">
          <el-input
            v-model="filter.rebateAgreementCode"
            clearable
            placeholder="协议编号"
            class="filter-item"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="filter.rebateAgreementName"
            clearable
            placeholder="协议名称"
            class="filter-item"
          />
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="filter.timeRange"
            clearable
            class="filter-item"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @keyup.enter.native="search"
          />
        </el-col>

        <el-col :span="span">
          <el-cascader
            ref="refDistributorProvinceCity"
            v-model="rebateProvinceCityId"
            :loading="provinceCityLoading"
            :options="provinceCityList"
            class="filter-item"
            placeholder="返利接收方省份/城市"
            clearable
            :props="{ checkStrictly: true, expandTrigger: 'hover' }"
            @change="handleProvinceCityChange"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="filter.principalName"
            clearable
            placeholder="负责人"
            class="filter-item"
          />
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProductAndSpec"
            v-model="manufacturerProductAndSpecId"
            style="width: 100%"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            placeholder="厂商/产品/规格"
            clearable
            class="filter-item"
            :props="{ checkStrictly: true, expandTrigger: 'hover' }"
            @change="closeProductCascader"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="filter.enumRebateFeedbackStatus"
            value-key="value"
            class="filter-item"
            placeholder="状态"
            clearable
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'RebateResultTracking_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="search"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'RebateResultTracking_Button_Export')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="rebateResultList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              label="协议编号"
              sortable="custom"
              min-width="120px"
              header-align="center"
              align="left"
              prop="RebateAgreementCode"
            >
              <template slot-scope="{ row }">
                <span>{{ row.rebateAgreementCode }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="协议名称"
              sortable="custom"
              min-width="150px"
              header-align="center"
              align="left"
              prop="RebateAgreementName"
            >
              <template slot-scope="{ row }">
                <span>{{ row.rebateAgreementName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="计算开始时间"
              align="center"
              sortable="custom"
              min-width="120px"
              prop="StartDate"
            >
              <template slot-scope="{ row }">
                <span v-if="row.startDate">{{
                  row.startDate | parseTime("{y}-{m}-{d}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="计算截止时间"
              align="center"
              sortable="custom"
              min-width="120px"
              prop="EndDate"
            >
              <template slot-scope="{ row }">
                <span>{{
                  row.endDate | parseTime("{y}-{m}-{d}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="返利接收方省份"
              sortable="custom"
              min-width="130px"
              header-align="center"
              align="center"
              prop="RebateAgreement.RebateReceiver.Province.NameCn"
            >
              <template slot-scope="{ row }">
                <span>{{ row.rebateReceiverProvince }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="返利接收方"
              sortable="custom"
              min-width="150px"
              header-align="center"
              align="left"
              prop="RebateAgreement.RebateReceiver.Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.rebateReceiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="产品/规格"
              min-width="150px"
              header-align="center"
              align="left"
              prop="ProductAndSpec"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productAndSpec }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="返利金额"
              sortable="custom"
              min-width="100px"
              header-align="center"
              align="right"
              prop="RebateAmount"
            >
              <template slot-scope="{ row }">
                <span>{{ row.rebateAmount | toTwoNum | toThousandFilter }}元</span>
              </template>
            </el-table-column>
            <el-table-column
              label="负责人"
              sortable="custom"
              min-width="100px"
              header-align="center"
              align="left"
              prop="RebateAgreement.Principal.DisplayName"
            >
              <template slot-scope="{ row }">
                <span>{{ row.principalName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="反馈状态"
              sortable="custom"
              min-width="130px"
              align="center"
              prop="EnumRebateFeedbackStatus"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumRebateFeedbackStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="120"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i v-if="row.rebatePaymentTypeCode === 'RebatePaymentType_CreditNote' && row.enumRebateFeedbackStatus === $constDefinition.rebateFeedbackStatus.Unreviewed && $isPermitted($store.getters.user, 'RebateResultTracking_Button_Approval')" class="el-icon-edit-outline eltablei" title="审核" @click="handleApproval(row)" />
                <i v-if="row.rebatePaymentTypeCode === 'RebatePaymentType_Ticket' && row.enumRebateFeedbackStatus === $constDefinition.rebateFeedbackStatus.Untracked && $isPermitted($store.getters.user, 'RebateResultTracking_Button_Upload')" class="el-icon-upload2 eltablei" title="上传" @click="handleUpload(row)" />
                <i
                  v-if="row.enumRebateFeedbackStatus === $constDefinition.rebateFeedbackStatus.Tracked || row.enumRebateFeedbackStatus === $constDefinition.rebateFeedbackStatus.Pushed && $isPermitted($store.getters.user, 'RebateResultTracking_Button_Payment')"
                  class="el-icon-money eltablei"
                  title="支付"
                  @click="handlePayment(row)"
                />
                <i v-if="(row.enumRebateFeedbackStatus >= $constDefinition.rebateFeedbackStatus.Tracked) && $isPermitted($store.getters.user, 'RebateResultTracking_Button_Download')" class="el-icon-download eltablei" title="下载跟踪文件" @click="handleDownLoad(row)" />
                <i
                  class="el-icon-document eltablei"
                  title="查看明细"
                  @click="handleReview(row)"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="filter.pageIndex"
            :limit.sync="filter.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      :visible="showExportModal"
      :close-on-click-modal="false"
      title="导出返利跟踪明细"
      width="800"
      @close="handleExportCancel"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      title="上传文件"
      :visible="dialogUploadFormVisible"
      width="60%"
      @close="handleImportHidden"
    >
      <el-form
        v-model="uploadModel"
        label-position="right"
        label-width="90px"
        class="el-dialogform"
      >
        <el-form-item label="协议编号">
          {{ uploadModel.rebateAgreementCode }}
        </el-form-item>
        <el-form-item label="协议名称">
          {{ uploadModel.rebateAgreementName }}
        </el-form-item>
        <el-form-item label="返利接收方">
          {{ uploadModel.rebateReceiverName }}
        </el-form-item>
        <el-form-item label="返利金额">
          {{ uploadModel.rebateAmount }}(元)
        </el-form-item>
        <el-form-item label="选择文件">
          <ImageUpload
            ref="upload"
            @getUploadFile="getUploadFile"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleImportHidden()"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleSubmint"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
    <FeedbackApproval ref="refFeedbackApproval" @success="search" />
    <FeedbackView ref="refFeedbackView" />
  </div>
</template>
<script>
import ImageUpload from '@/views/components/uploadImage'
import RebateService from '@/api/rebate'
import MasterDataService from '@/api/masterData'
import Pagination from '@/components/Pagination'
import CustomExport from '@/components/Export/CustomExport'
import FeedbackApproval from './components/feedbackApproval'
import FeedbackView from './components/feedbackView'
import ProductService from '@/api/product'
import LocationService from '@/api/location'
import FileService from '@/api/file'

export default {
  name: 'RebateResult',
  components: {
    ImageUpload,
    Pagination,
    CustomExport,
    FeedbackApproval,
    FeedbackView
  },
  data() {
    return {
      span: 4,
      total: 0,
      filter: {
        enumRebateFeedbackStatus: 1,
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      rebateResultList: [],
      statusOptions: [],
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: {},
      productAndSpecList: [],
      provinceCityList: [],
      rebateProvinceCityId: null,
      manufacturerProductAndSpecId: [],
      productAndSpecLoading: false,
      provinceCityLoading: false,
      dialogUploadFormVisible: false,
      uploadModel: {},
      fileList: [],
      method: 'UploadTicketForDataManager',
      controller: 'Rebate'
    }
  },
  created() {
    this.initStatusOptions()
    this.search()
    this.initManufacturerAndProductAndSpec()
    this.initProvinceCity()
  },
  methods: {
    initStatusOptions() {
      var param = {
        enumType: 'RebateFeedbackStatus'
      }
      MasterDataService.GetEnumInfos(param)
        .then(result => {
          this.statusOptions = result.data.datas
        })
        .catch(() => {})
    },
    initManufacturerAndProductAndSpec() {
      this.productAndSpecLoading = true

      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then(result => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch(error => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    initProvinceCity() {
      this.provinceCityLoading = true
      LocationService.QueryProvinceCityCascader()
        .then(result => {
          this.provinceCityList = result
          this.provinceCityLoading = false
        })
        .catch(error => {
          this.provinceCityLoading = false
          console.log(error)
        })
    },
    closeProductCascader() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    handleProvinceCityChange() {
      this.$refs.refDistributorProvinceCity.dropDownVisible = false
    },
    getUploadFile(val) {
      this.fileList = val
    },
    search() {
      this.filter.pageIndex = 1
      this.getList()
    },
    getList() {
      this.listLoading = true

      if (this.rebateProvinceCityId) {
        const [provinceId, cityId] = this.rebateProvinceCityId
        this.filter.receiverProvinceId = provinceId
        this.filter.receiverCityId = cityId
      }
      if (this.manufacturerProductAndSpecId) {
        const [
          manufacturerId,
          productId,
          productSpecId
        ] = this.manufacturerProductAndSpecId
        this.filter.manufacturerId = manufacturerId
        this.filter.productId = productId
        this.filter.productSpecId = productSpecId
      }
      RebateService.QueryRebateResultForTracking(this.filter)
        .then(res => {
          this.listLoading = false
          this.rebateResultList = res.data.datas
          this.total = res.data.recordCount
          this.filter.pageIndex = res.data.pageIndex
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    handleApproval(row) {
      this.$refs.refFeedbackApproval.init(row.id)
    },
    handleReview(row) {
      this.$refs.refFeedbackView.init(row.id)
    },
    handleDownLoad(row) {
      this.listLoading = true
      RebateService.DownloadRebateTracking(row)
        .then(res => {
          this.listLoading = false
          console.log(res)
          const fileDownload = require('js-file-download')
          var filename = `${row.rebateAgreementName}.zip`
          fileDownload(res.data, filename)
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    indexMethod(index) {
      return (this.filter.pageIndex - 1) * this.filter.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.filter.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.filter.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.filter.pageSize = val
      this.search()
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      RebateService.GetRebateResultForTrackingColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      var exportParam = JSON.parse(JSON.stringify(this.filter))
      exportParam.checkedColumns = checkColumns
      RebateService.ExportRebateResultForTracking(exportParam)
        .then(result => {
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '返利跟踪列表.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '返利跟踪列表.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    handleImportHidden() {
      this.fileList = []
      this.$refs.upload.handleClear()
      this.dialogUploadFormVisible = false
    },
    handleSubmint() {
      if (this.fileList === null || this.fileList.length === 0) {
        this.$notice.message('请上传文件', 'error')
        return
      }
      const formData = new FormData()
      formData.append('resultId', this.uploadModel.id)
      FileService.uploadImages(this.fileList, formData, this.controller, this.method).then(res => {
        this.handleImportHidden()
        this.fileList = []
        this.$refs.upload.handleClear()
        this.dialogUploadFormVisible = false
        this.getList()
      })
    },
    handleUpload(val) {
      this.uploadModel = val
      this.dialogUploadFormVisible = true
    },
    handlePayment(row) {
      this.$confirm('确定支付?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        RebateService.PaymentConfirm({ id: row.id })
          .then(result => {
            this.listLoading = false
            if (result.succeed) {
              this.getList()
              this.$notice.message('已支付', 'success')
            }
          })
          .catch(() => {
            this.listLoading = false
          })
      })
    }
  }
}
</script>
<style scoped>
.flexwarp {
  display: flex;
  flex-wrap: wrap;
}
</style>
