<template>
  <div>
    <el-dialog
      :title="title"
      width="70%"
      :close-on-click-modal="false"
      :visible="true"
      @close="cancle()"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="tempFormModel"
        label-position="right"
        label-width="100px"
        class="el-dialogform"
      >
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item
              label="厂商/产品/规格"
              prop="manufacturerProductAndSpecId"
            >
              <el-cascader
                ref="refProductAndSpec"
                v-model="tempFormModel.manufacturerProductAndSpecId"
                :loading="productAndSpecLoading"
                :options="productAndSpecList"
                placeholder="厂商 / 产品 / 规格"
                style="width: 100%"
                clearable
                class="filter-item"
                :props="{
                  multiple: false,
                  checkStrictly: false,
                  expandTrigger: 'hover',
                }"
                @change="handleProductChange"
              /></el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="部门" prop="departmentId">
              <el-select
                v-model="tempFormModel.departmentId"
                placeholder="部门"
                style="width: 100%"
                clearable
                @change="handleDepartmentChange"
              >
                <el-option
                  v-for="item in showQuota ? deptNotEfficiencyList : deptList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="客户名称" prop="receiverName">
              <el-col :span="19" style="padding-left: 0px; padding-right: 0px">
                <el-input
                  v-model="tempFormModel.receiverName"
                  :readonly="true"
                  placeholder="客户名称"
                />
              </el-col>
              <el-col :span="5">
                <el-button
                  class="filter-item"
                  icon="el-icon-search"
                  style="width: 100%"
                  type="primary"
                  title="选择客户"
                  @click="handleSelectReceiver"
                />
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="客户类型" prop="customerCategoryId">
              <el-select
                v-model="tempFormModel.customerCategoryId"
                class="filter-item"
                placeholder="客户类型"
                clearable
                @change="handelCategoryChange()"
              >
                <el-option
                  v-for="item in customerCategoryList"
                  :key="item.key"
                  :label="item.name"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="起始月份" prop="startDate">
              <el-date-picker
                v-model="tempFormModel.startDate"
                type="month"
                format="yyyy-MM"
                value-format="yyyy-MM"
                style="width: 100%"
                placeholder="起始月份"
                @change="change"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="截止月份" prop="endDate">
              <el-date-picker
                v-model="tempFormModel.endDate"
                type="month"
                format="yyyy-MM"
                value-format="yyyy-MM"
                style="width: 100%"
                placeholder="截止月份"
                @change="handleEndDateChange"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="showQuota" :span="24">
            <el-form-item v-if="currentStation.name === undefined || currentStation.name === null || currentStation.name === ''" label="岗位" prop="stationName">
              <TreeSelect
                ref="treeSelectRef"
                v-model="selectStationModel"
                :options="stationArray"
                placeholder="请选择岗位"
                :multiple="false"
                :max-height="200"
                value-format="object"
                searchable
                @input="selectStation"
              />
            </el-form-item>
            <el-form-item v-if="currentStation.name !== undefined && currentStation.name !== null && currentStation.name !== ''" label="岗位" prop="stationName">
              {{ tempFormModel.stationName }}
            </el-form-item>
          </el-col>
          <el-col v-if="showQuota" :span="24">
            <el-form-item v-if="currentStation.name !== undefined && currentStation.name !== null && currentStation.name !== ''" label="负责人" prop="employeeDisplayName">
              {{ tempFormModel.employeeDisplayName }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-row v-if="showQuota" type="flex" justify="end">
        <el-col :span="24">
          <el-table
            :data="tempFormModel.quotaList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
          >
            <el-table-column fixed label="序号" type="index" align="center" />
            <el-table-column
              prop="Department.Name"
              label="月份"
              min-width="80px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.quotaMonth }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="Receiver.Name"
              label="客户"
              min-width="220px"
              header-align="center"
              align="left"
            >
              <template slot-scope="{ row }">
                <span>{{ row.receiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="ProductSpec.Product.Manufacturer.Name"
              label="品规"
              min-width="150px"
              align="center"
            >
              <template slot-scope="{ row }">
                <el-cascader
                  ref="refProductAndSpecRow"
                  v-model="tempFormModel.manufacturerProductAndSpecId"
                  :loading="productAndSpecLoading"
                  :options="productAndSpecList"
                  placeholder="厂商 / 产品 / 规格"
                  style="width: 100%"
                  disabled
                  class="filter-item"
                  :props="{
                    multiple: false,
                    checkStrictly: false,
                    expandTrigger: 'hover',
                  }"
                />
              </template>
            </el-table-column>
            <el-table-column label="潜力数量" min-width="120px" align="center">
              <template slot-scope="{ row }">
                <el-input
                  v-model="row.qty"
                  min="0"
                  placeholder="潜力数量"
                  :class="row.qty_error?'error-border':''"
                  size="mini"
                  @input="productItemChange(row)"
                />
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="70"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i class="el-icon-delete eltablei" @click="handleDelete(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()"> 关闭 </el-button>
        <el-button
          :loading="btnSaveLoading"
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="选择收货方"
      :close-on-click-modal="false"
      :visible="dialogReceiverVisible"
      width="90%"
      class="popup-search"
      @close="closeReceiverDialog"
    >
      <SelectReceiver
        ref="refSelectReceiver"
        :show-dialog="dialogReceiverVisible"
        :distributor-types="distributorTypes"
        :is-stopped="false"
        :department-id="tempFormModel.departmentId"
        @success="selectReceiverSuccess"
      />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeReceiverDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import moment from 'moment'

import SelectReceiver from '@/views/components/selectReceiver'
import MaintenanceService from '@/api/maintenance'
import ProductService from '@/api/product'
import TargetReceiverService from '@/api/targetReceiver'

export default {
  name: '',
  components: {
    SelectReceiver
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    viewModel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    var validCascaderProduct = (rule, value, callback) => {
      if (this.tempFormModel.manufacturerProductAndSpecId === undefined || this.tempFormModel.manufacturerProductAndSpecId.length === 0) {
        callback(new Error('请选择厂商产品规格'))
      } else {
        callback()
      }
    }

    return {
      span: 12,
      rules: {
        manufacturerProductAndSpecId: [
          { required: true, type: 'array', validator: validCascaderProduct, trigger: 'change' }
        ],
        receiverName: [
          { required: true, message: '请选择客户', trigger: 'change' }
        ],
        startDate: [
          { required: true, message: '请选择起始日期', trigger: 'change' }
        ],
        customerCategoryId: [
          { required: true, message: '请选择客户类型', trigger: 'change' }
        ]
      },
      tempFormModel: { manufacturerProductAndSpecId: [], distributorName: '', receiverName: '', departmentId: null },
      btnSaveLoading: false,
      productAndSpecLoading: false,
      productAndSpecList: [],
      productAndSpecKey: 0,
      deptLoading: false,
      deptList: [],
      distributorTypes: [],
      customerCategoryLoading: false,
      customerCategoryList: [],
      dialogDistributorVisible: false,
      dialogReceiverVisible: false,
      endDateString: '2099-12-31 00:00:00',
      showQuota: false,
      deptNotEfficiencyList: [],
      selectedReceiver: null,
      currentStation: {},
      datatree: [],
      stationArray: [],
      selectStationModel: null,
      currentStationFilter: {
        receiverId: null,
        endDate: null
      },
      oldStationId: null // 加载数据初始化，用于判断是否变更岗位
    }
  },
  watch: {
    id(val) {
      this.tempFormModel.id = val
    }
  },
  mounted() {
  },
  created() {
    this.initProductAndSpec()
    this.initDept()
    this.initCustomerCategory()

    this.initDeptNotEfficiency()
  },
  methods: {
    initProductAndSpec() {
      this.productAndSpecLoading = true
      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
          this.get(this.id)
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    handleProductChange(value) {
      if (value !== null && value !== undefined && value.length > 1) {
        this.tempFormModel.productSpecId = value[2]
        this.getQuotaList()
      } else {
        this.tempFormModel.quotaList = []
      }

      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch(() => {
          this.deptLoading = false
        })
    },
    initDeptNotEfficiency() {
      MaintenanceService.QueryDepartmentForTargetReceiver()
        .then((result) => {
          this.deptNotEfficiencyList = result
        })
        .catch(() => {
        })
    },
    initCustomerCategory() {
      this.customerCategoryLoading = true
      TargetReceiverService.QueryCustomerCategorySelect().then(res => {
        this.customerCategoryLoading = false
        this.customerCategoryList = res
      })
        .catch(
          this.customerCategoryLoading = false
        )
    },

    QueryOrganization() {
      MaintenanceService.QueryOrganizationExcludeAssistant().then(result => {
        if (result.succeed) {
          this.datatree = this.chearEmityChildren(result.data)
          this.stationArray = this.chearEmityChildren(result.data)

          if (this.tempFormModel.departmentId !== null && this.tempFormModel.departmentId !== undefined) {
            var deptStation = []
            deptStation.push(this.getDeptStation(this.datatree[0], this.tempFormModel.departmentId))
            this.stationArray = this.chearEmityChildren(deptStation)
          }
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    get(id) {
      this.btnSaveLoading = true
      TargetReceiverService.GetTargetReceiver({ id: id }).then(result => {
        this.tempFormModel = result.data
        this.tempFormModel.manufacturerProductAndSpecId = [result.data.manufacturerId, result.data.productId, result.data.productSpecId]
        this.tempFormModel.timeRange = [new Date(result.data.startDate), new Date(result.data.endDate)]
        this.oldStationId = this.tempFormModel.stationId
        this.btnSaveLoading = false

        if (this.tempFormModel.endDate === this.endDateString) {
          this.tempFormModel.endDate = ''
        }

        this.currentStation.name = this.tempFormModel.stationName
        this.currentStation.id = this.tempFormModel.stationId

        this.handelCategoryChange()
        this.QueryOrganization()

        // this.handleDepartmentChange()
      }).catch(error => {
        this.btnSaveLoading = false
        console.log(error)
      })
    },

    productItemChange(row, index) {
      var qty = Number(row.qty)
      const regex = /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/
      if (!regex.test(qty) || !qty) {
        this.$message.error('潜力数量只能是两位小数或整数')
        row.qty_error = true
      } else {
        row.qty_error = false
      }
    },
    save() {
      if (this.tempFormModel.quotaList.length > 0 && this.tempFormModel.quotaList.some(s1 => { return s1.qty_error })) {
        return false
      }
      if (this.tempFormModel.receiverIsStopped === true) {
        this.$notice.message('终端已停用，不能编辑目标客户数据', 'error')
        return
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          var checkedCustomerCategory = this.customerCategoryList.find(item => { return item.key === this.tempFormModel.customerCategoryId })
          if ((checkedCustomerCategory.value === this.$constDefinition.customerCategory.selfSupportDevelopCode || this.showQuota) && (this.tempFormModel.departmentId === undefined || this.tempFormModel.departmentId === null || this.tempFormModel.departmentId === '')) {
            this.$notice.message('部门不能为空', 'error')
            return
          }

          // 商务部只能选择经销商类型的客户 销售部只能选择医疗机构和零售类型的客户 零售部仅能选择零售类型的客户
          if (this.tempFormModel.departmentId) {
            TargetReceiverService.CheckDepartmentOfReceiverRange(this.tempFormModel).then(result => {
              if (result.data === this.$constDefinition.checkReceiverRange.notDistributor) {
                this.clearErrorData()
                this.$notice.message('商务部只能选择经销商类型的客户', 'error')
                return
              } else if (result.data === this.$constDefinition.checkReceiverRange.notRetailAndMedical) {
                this.clearErrorData()
                this.$notice.message('销售部只能选择医疗机构和零售类型的客户', 'error')
                return
              } else if (result.data === this.$constDefinition.checkReceiverRange.notRetail) {
                this.clearErrorData()
                this.$notice.message('零售部仅能选择零售类型的客户', 'error')
                return
              } else {
                var quotaYear = null
                if (this.tempFormModel.quotaList !== null && this.tempFormModel.quotaList !== undefined && this.tempFormModel.quotaList.length > 0) {
                  quotaYear = new Date(this.tempFormModel.quotaList[0].quotaMonth).getFullYear()
                }

                var startDate = new Date(moment(this.tempFormModel.startDate).format('YYYY-MM'))
                if (quotaYear !== null && quotaYear !== undefined && quotaYear !== '' && startDate.getFullYear() < quotaYear) {
                  startDate = new Date(quotaYear + '-01')
                }

                var endDateString = null
                if (this.tempFormModel.endDate !== undefined && this.tempFormModel.endDate !== null && this.tempFormModel.endDate !== '') {
                  endDateString = moment(this.tempFormModel.endDate).format('YYYY-MM')
                  if (startDate.getFullYear() < new Date(this.tempFormModel.endDate).getFullYear()) {
                    endDateString = startDate.getFullYear() + '-12'
                  }
                } else {
                  endDateString = startDate.getFullYear() + '-12'
                }

                var shortEndDate = new Date(endDateString)

                if (shortEndDate < startDate) {
                  this.$notice.message('截止月份不能小于起始月份', 'error')
                  return
                }
                if (this.showQuota) {
                  if (this.tempFormModel.quotaList !== undefined && this.tempFormModel.quotaList.length > 0) {
                    var minQuotaMonth = this.tempFormModel.quotaList[0].quotaMonth
                    var maxQuotaMonth = this.tempFormModel.quotaList[this.tempFormModel.quotaList.length - 1].quotaMonth

                    var minQuotaMonthDate = new Date(minQuotaMonth)
                    var maxQuotaMonthDate = new Date(maxQuotaMonth)
                    // 月份差
                    var differenceMonth = maxQuotaMonthDate.getMonth() - minQuotaMonthDate.getMonth() + 1
                    if (shortEndDate < maxQuotaMonthDate || startDate > minQuotaMonthDate) {
                      this.$confirm('潜力超出目标客户时间范围，是否继续保存?', '提示', {
                        confirmButtonText: '是',
                        cancelButtonText: '否',
                        type: 'warning'
                      }).then(() => {
                        this.update()
                      }).catch(() => {
                        return
                      })
                    } else if (shortEndDate > maxQuotaMonthDate || startDate < minQuotaMonthDate || this.tempFormModel.quotaList.length !== differenceMonth) {
                      this.$confirm('目标客户时间范围大于指标范围，是否要增加指标?', '提示', {
                        distinguishCancelAndClose: true,
                        confirmButtonText: '是',
                        cancelButtonText: '否',
                        type: 'warning'
                      }).then(() => {
                        var targetReceiverStartDate = startDate
                        if (startDate.getFullYear() < minQuotaMonthDate.getFullYear()) {
                          targetReceiverStartDate = new Date(minQuotaMonthDate.getFullYear() + '-01')
                        }

                        this.replenishQuotas(targetReceiverStartDate, shortEndDate)
                      }).catch(action => {
                        if (action === 'cancel') {
                          this.update()
                        } else {
                          return
                        }
                      })
                    } else {
                      if (this.tempFormModel.quotaList.findIndex(item => item.qty === 0) !== -1) {
                        this.$notice.message('请填写潜力数量', 'error')
                        return
                      }
                      this.update()
                    }
                  } else {
                    this.$confirm('目标客户时间范围内没有潜力，是否要增加潜力?', '提示', {
                      distinguishCancelAndClose: true,
                      confirmButtonText: '是',
                      cancelButtonText: '否',
                      type: 'warning'
                    }).then(() => {
                      const targetReceiverEndDate = new Date(shortEndDate)
                      const quotaMaxMonthDate = new Date(this.tempFormModel.startDate)
                      this.addAfterQuotas(quotaMaxMonthDate, targetReceiverEndDate, 0)
                    }).catch(action => {
                      if (action === 'cancel') {
                        this.update()
                      } else {
                        return
                      }
                    })
                  }
                } else {
                  this.update()
                }
              }
            })
          } else {
            this.update()
          }
        }
      })
    },
    clearErrorData() {
      this.tempFormModel.receiverId = null
      this.tempFormModel.receiverName = ''
      this.tempFormModel.stationName = ''
      this.tempFormModel.stationId = null
      this.tempFormModel.employeeId = null
      this.tempFormModel.employeeDisplayName = ''
      this.tempFormModel.quotaList = []
    },
    addAfterQuotas(startDate, endDate, startIndex) {
      // 计算目标终端结束日期与指标截止日期相差月份数
      const totalMonthsDifference = endDate.getMonth() - startDate.getMonth()
      for (var i = startIndex; i <= totalMonthsDifference; i++) {
        const addQuotaMonth = startDate.getMonth() + 1 + i

        const month = addQuotaMonth < 10 ? '0' + addQuotaMonth : addQuotaMonth
        const addQuotaDate = startDate.getFullYear() + '-' + month
        var quota = this.createNewQuota(month, addQuotaDate)
        this.tempFormModel.quotaList.push(quota)
      }
    },
    addStartQuotas(startDate, endDate, startIndex) {
      // 计算目标终端结束日期与指标截止日期相差月份数
      const totalMonthsDifference = endDate.getMonth() - startDate.getMonth() - 1
      for (var i = startIndex; i <= totalMonthsDifference; i++) {
        const addQuotaMonth = startDate.getMonth() + (totalMonthsDifference - i) + 1

        const month = addQuotaMonth < 10 ? '0' + addQuotaMonth : addQuotaMonth
        const addQuotaDate = startDate.getFullYear() + '-' + month

        var quota = this.createNewQuota(month, addQuotaDate)
        this.tempFormModel.quotaList.unshift(quota)
      }
    },
    replenishQuotas(startDate, endDate) {
      // 计算目标终端结束日期与指标截止日期相差月份数
      var totalMonthsDifference = (moment(endDate).month() + 1) - (moment(startDate).month() + 1)
      for (var i = 0; i <= totalMonthsDifference; i++) {
        var yearMonth = moment(startDate).add(i, 'month').format('YYYY-MM')
        var month = moment(startDate).add(i, 'month').format('MM')
        if (this.tempFormModel.quotaList && !this.tempFormModel.quotaList.some(s => {
          return s.quotaMonth === yearMonth
        })) {
          var quota = this.createNewQuota(month, yearMonth)
          this.tempFormModel.quotaList.splice(i, 0, quota)
        }
      }
    },
    createNewQuota(addQuotaMonth, addQuotaDate) {
      var quota = {
        productSpecId: this.tempFormModel.productSpecId,
        receiverId: this.tempFormModel.receiverId,
        receiverName: this.tempFormModel.receiverName,
        qty: 0,
        month: addQuotaMonth,
        yearMonthSplicing: addQuotaDate,
        quotaMonth: addQuotaDate,
        departmentId: this.tempFormModel.departmentId,
        customerCategoryId: this.tempFormModel.customerCategoryId
      }
      return quota
    },
    update() {
      this.btnSaveLoading = true
      this.tempFormModel.stationId = this.currentStation.id
      this.$nextTick(() => {
        TargetReceiverService.UpdateTargetReceiver(this.tempFormModel).then(result => {
          this.btnSaveLoading = false
          if (result.succeed) {
            if (this.showQuota && this.oldStationId !== this.tempFormModel.stationId) {
              this.$notice.message('修改成功,岗位已经变更如果需要处理原挂岗记录请在组织架构管理处手工变更；', 'success')
            } else {
              this.$notice.message('修改成功', 'success')
            }
            this.close()
          } else {
            if (result.type !== -3) {
              this.$notice.resultTip(result)
              this.tempFormModel.receiverName = ''
              this.$refs['dataForm'].resetFields()
            }
          }
        })
          .catch(error => {
            this.btnSaveLoading = false
            if (!error.processed) {
              this.$notice.message('修改失败。', 'error')
            }
          })
      })
    },
    handleSelectReceiver() {
      this.distributorTypes = [10, 20, 30]
      this.dialogReceiverVisible = true
    },
    closeReceiverDialog() {
      this.dialogReceiverVisible = false
      this.$refs.refSelectReceiver.clear()
      this.distributorTypes = []
    },
    selectReceiverSuccess(val) {
      this.selectedReceiver = val
      this.getQuotaList()
      this.tempFormModel.stationName = val.stationName
      if (val.id === this.tempFormModel.distributorId) {
        this.$notice.message('收货方不能和发货方相同', 'error')
      } else {
        // 解决弹出框打开后，不输入任何值，只选择收货方名称时，验证失效的问题
        this.$set(this.tempFormModel, 'receiverName', val.name)
        // this.tempFormModel.receiverName = val.name
        this.tempFormModel.receiverId = val.id
        this.closeReceiverDialog()
        // 获取当前岗位
        this.GetCurrentStationByReceiverId()
      }
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.tempFormModel = {}
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    },
    change() {
      this.$forceUpdate()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleDelete(row) {
      this.$confirm('确定删除此数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.tempFormModel.quotaList.indexOf(row)
        if (index !== -1) {
          this.tempFormModel.quotaList.splice(index, 1)
        }
      }).catch(error => {
        if (!error.succeed) {
          this.showMessage('取消删除', 'info')
        }
      })
    },
    handelCategoryChange() {
      var checkedCustomerCategory = this.customerCategoryList.find(item => { return item.key === this.tempFormModel.customerCategoryId })
      if (checkedCustomerCategory.value === this.$constDefinition.customerCategory.selfSupportCode) {
        this.showQuota = true
      } else {
        this.showQuota = false
        this.tempFormModel.quotaList = []
      }
    },
    getQuotaList() {
      TargetReceiverService.GetTargetReceiverQuota(this.tempFormModel).then(result => {
        this.tempFormModel.quotaList = result.data
      }).catch(error => {
        console.log(error)
      })
    },
    handleEndDateChange() {
      this.getQuotaList()
      this.GetCurrentStationByReceiverId()
    },
    GetCurrentStationByReceiverId() {
      if (this.tempFormModel.receiverId !== undefined && this.tempFormModel.receiverId !== '' && this.tempFormModel.receiverId !== null && this.tempFormModel.departmentId !== undefined && this.tempFormModel.departmentId !== '' && this.tempFormModel.departmentId !== null) {
        var endDate = this.tempFormModel.endDate ? this.tempFormModel.endDate : '9999-12'
        this.currentStationFilter.receiverId = this.tempFormModel.receiverId // this.selectedReceiver.id
        this.currentStationFilter.endDate = endDate
        this.currentStationFilter.deptId = this.tempFormModel.departmentId
        MaintenanceService.GetCurrentStationByReceiverId(this.currentStationFilter)
          .then((result) => {
            if (result.data) {
              this.currentStation = result.data
              this.tempFormModel.stationName = this.currentStation.name
              this.tempFormModel.employeeDisplayName = this.currentStation.employeeDisplayName
            } else {
              this.currentStation = {
                name: '',
                id: null
              }
            }
          })
          .catch(() => {
          })
      }
    },
    handleDepartmentChange() {
      if (this.tempFormModel.departmentId) {
        this.getQuotaList()
        var deptStation = []
        deptStation.push(this.getDeptStation(this.datatree[0], this.tempFormModel.departmentId))
        this.stationArray = this.chearEmityChildren(deptStation)
        if (this.stationArray[0] === undefined) {
          this.stationArray = this.datatree
        }
        this.GetCurrentStationByReceiverId()
      }

      this.$forceUpdate()
    },
    selectStation(sel) {
      if (sel) {
        if (sel.enableAddReceiver === false) {
          this.$notice.message('只能选择大区、地区以及代表的岗位', 'error')
          this.$refs.treeSelectRef.clear()
          return
        }

        this.currentStation.id = this.selectStationModel.pkid
        this.tempFormModel.stationId = this.selectStationModel.pkid
      } else {
        this.$set(this.tempFormModel, 'stationId', '')
      }
      this.$refs['dataForm'].validateField(['stationId'])
    },
    getDeptStation(station, departmentId) {
      if (station instanceof Array) {
        station.map(item => {
          this.getDeptStation(item, departmentId)
        })
      } else {
        if (station.deptId === departmentId) {
          return station
        }
        if (station.children !== null && station.children !== undefined) {
          if (station.children.length > 0) {
            for (var item of station.children) {
              var resultStation = this.getDeptStation(item, departmentId)
              if (resultStation) {
                return resultStation
              }
            }
          } else {
            delete station.children
          }
        }
      }
      return null
    },
    chearEmityChildren(stations) {
      return stations.map(item => {
        if (item !== null && item !== undefined && item.children !== null && item.children !== undefined) {
          if (item.children && item.children.length > 0) {
            this.chearEmityChildren(item.children)
          } else {
            delete item.children
          }
          item.isDefaultExpanded = true
          return item
        } else {
          return item
        }
      })
    }
  }

}
</script>
<style scoped>

.error-border{
}
.error-border .el-input__inner{
  border: 1px solid red;
  border-radius: 5px;
}
</style>
