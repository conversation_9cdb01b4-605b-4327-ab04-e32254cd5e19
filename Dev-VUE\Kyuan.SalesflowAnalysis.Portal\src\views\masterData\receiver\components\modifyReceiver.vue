<template>
  <div>
    <el-dialog :title="title" width="70%" :close-on-click-modal="false" :visible="true" @close="cancle()">
      <el-form ref="dataForm" :rules="rules" :model="tempFormModel" label-position="right" label-width="130px" class="el-dialogform">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="收货方名称" prop="name">
              <el-input
                v-model="tempFormModel.name"
                placeholder="收货方名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="Code" prop="code">
              <el-input
                v-model="tempFormModel.code"
                placeholder="Code"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="省份/城市/区县" prop="provinceCityCountyId">
              <el-cascader
                ref="refProvinceCityCounty"
                v-model="tempFormModel.provinceCityCountyId"
                :loading="provinceCityCountyLoading"
                :options="provinceCityCountyList"
                :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
                placeholder="省份 / 城市 / 区县"
                clearable
                style="width:100%"
                @change="closeProvinceCityCountyCascader"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="经销商类型" prop="enumDistributorType">
              <el-select
                v-model="tempFormModel.enumDistributorType"
                :loading="enumDistributorTypeLoading"
                class="filter-item"
                placeholder="经销商类型"
                clearable
                style="width:100%"
              >
                <el-option
                  v-for="item in enumDistributorTypeList"
                  :key="item.value"
                  :label="item.desc"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="上级单位" prop="parentReceiverId">
              <el-col :span="18" style="padding-left: 0px; padding-right: 0px;">
                <el-input
                  v-model="tempFormModel.parentName"
                  style="width: 100%; "
                  clearable
                  :readonly="true"
                  placeholder="上级单位"
                />
              </el-col>
              <el-col :span="3">
                <el-button icon="el-icon-search" type="primary" title="选择" @click="handleSelectReceiver" />
              </el-col>
              <el-col :span="3">
                <el-button icon="el-icon-delete" type="primary" title="清除" @click="handleDeleteParent" />
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="收货方类型" prop="receiverTypeId">
              <el-cascader
                ref="refReceiverType"
                v-model="tempFormModel.receiverTypeId"
                :loading="receiverTypeLoading"
                :options="receiverTypeList"
                :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
                placeholder="收货方类型"
                clearable
                style="width:100%"
                @change="closeReceiverTypeCascader"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="生效日期" prop="effectiveDate">
              <el-date-picker
                v-model="tempFormModel.effectiveDate"
                type="date"
                style="width: 100%"
                :disabled="id!=''&&id!=undefined&&id!=null"
                :picker-options="datePickerOptions"
                placeholder="生效日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="机构等级" prop="gradeLevel">
              <el-input
                v-model="tempFormModel.gradeLevel"
                placeholder="机构等级"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="机构经济类型" prop="economicType">
              <el-input
                v-model="tempFormModel.economicType"
                placeholder="机构经济类型"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="统一信用代码" prop="taxRegistrationNo">
              <el-input
                v-model="tempFormModel.taxRegistrationNo"
                placeholder="统一信用代码"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="工商注册号" prop="registrationNo">
              <el-input
                v-model="tempFormModel.registrationNo"
                placeholder="工商注册号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="是否互联网医院" prop="enumFlags">
              <el-select
                v-model="tempFormModel.enumFlags"
                :loading="enumReceiverFlagLoading"
                class="filter-item"
                placeholder="是否互联网医院"
                style="width:100%"
                clearable
              >
                <el-option
                  v-for="item in enumReceiverFlagList"
                  :key="item.value"
                  :label="item.desc"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="地址" prop="address">
              <el-input
                v-model="tempFormModel.address"
                placeholder="地址"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="id != null" :span="24">
            <el-form-item label="曾用名">
              <el-table
                v-loading="listLoading"
                :data="tempFormModel.receiverFormer"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
              >
                <el-table-column
                  fixed
                  label="序号"
                  type="index"
                  align="center"
                />
                <el-table-column label="曾用名" sortable min-width="150px" prop="Name">
                  <template slot-scope="scope">
                    <span>{{ scope.row.name }}</span>
                  </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" align="center" header-align="center" width="90" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <i class="el-icon-delete eltablei" title="删除" @click="handleDelete(scope.$index,scope.row)" />
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          v-if="!viewModel"
          :loading="btnSaveLoading"
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      custom-class="el-dialog-ls"
      append-to-body
      title="选择发货方"
      :close-on-click-modal="false"
      :visible="dialogReceiverVisible"
      width="80%"
      class="popup-search"
      @close="closeReceiverDialog"
    >
      <SelectReceiver ref="refDistributor" :show-dialog="dialogReceiverVisible" :distributor-types="distributorTypes" @success="selectReceiverSuccess" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeReceiverDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import LocationService from '@/api/location'
import MasterDataService from '@/api/masterData'
import ReceiverService from '@/api/receiver'
import SelectReceiver from '@/views/components/selectReceiver'
import MaintenanceService from '@/api/maintenance'

export default {
  name: '',
  components: {
    SelectReceiver
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    viewModel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const ruleProvinceValidator = (rule, value, callback) => {
      if (this.tempFormModel.provinceCityCountyId == null || this.tempFormModel.provinceCityCountyId.length === 0) {
        callback(new Error('请选择省份'))
      }
      callback()
    }
    return {
      span: 12,
      cascaderKey: 0,
      rules: {
        name: [
          { required: true, message: '请输入收货方名称', trigger: 'blur' },
          { max: 300, message: '收货方名称不允许超过300个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入Code', trigger: 'blur' },
          { max: 30, message: 'Code不允许超过30个字符', trigger: 'blur' }
        ],
        provinceCityCountyId: [
          { required: true, type: 'array', validator: ruleProvinceValidator, trigger: 'change' }
        ],
        enumDistributorType: [
          { required: true, message: '请选择经销商类型', trigger: 'change' }
        ],
        receiverTypeId: [
          { required: true, type: 'array', message: '请选择收货方类型', trigger: 'change' }
        ],
        effectiveDate: [
          { required: true, message: '请选择生效日期', trigger: 'blur' }
        ]
      },
      tempFormModel: { enumType: null, provinceId: null, provinceCityCountyId: null },
      btnSaveLoading: false,
      manufacturerLoading: false,
      productAndSpecLoading: false,
      manufacturerList: [],
      productAndSpecList: [],
      provinceCityCountyLoading: false,
      provinceCityCountyList: [],
      enumDistributorTypeLoading: false,
      enumDistributorTypeList: [],
      receiverTypeLoading: false,
      receiverTypeList: [],
      enumReceiverFlagLoading: false,
      enumReceiverFlagList: [],
      listLoading: false,
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6
        }
      },
      distributorTypes: [],
      dialogReceiverVisible: false
    }
  },
  watch: {
    id(val) {
      this.tempFormModel.id = val
    }
  },
  created() {
    this.initProvinceCityCounty()
    this.initDistributorType()
    this.initReceiverTypeList()
    this.initReceiverFlag()
    this.init()
  },
  methods: {
    initProvinceCityCounty() {
      this.provinceCityCountyLoading = true
      LocationService.QueryProvinceCityCountyCascader()
        .then((result) => {
          this.provinceCityCountyList = result
          this.provinceCityCountyLoading = false
        })
        .catch((error) => {
          this.provinceCityCountyLoading = false
          console.log(error)
        })
    },
    initReceiverTypeList() {
      this.receiverTypeLoading = true
      MaintenanceService.QueryReceiverTypeCascader().then(res => {
        this.receiverTypeList = res
        this.receiverTypeLoading = false
      })
        .catch((error) => {
          this.receiverTypeLoading = false
          console.log(error)
        })
    },
    initDistributorType() {
      this.enumDistributorTypeLoading = true
      MasterDataService.GetEnumInfos({ enumType: 'DistributorType' })
        .then((result) => {
          this.enumDistributorTypeLoading = false
          this.enumDistributorTypeList = result.data.datas
        })
        .catch((error) => {
          this.enumDistributorTypeLoading = false
          console.log(error)
        })
    },
    initReceiverFlag() {
      this.enumReceiverFlagLoading = true
      MasterDataService.GetEnumInfos({ enumType: 'ReceiverFlag' })
        .then((result) => {
          this.enumReceiverFlagLoading = false
          this.enumReceiverFlagList = result.data.datas
        })
        .catch((error) => {
          this.enumReceiverFlagLoading = false
          console.log(error)
        })
    },
    init() {
      if (!this.id) {
        this.clear()
        return
      }
      this.get(this.id)
    },
    get(id) {
      this.btnSaveLoading = true
      this.listLoading = true
      ReceiverService.GetReceiver({ id: id }).then(result => {
        this.tempFormModel = result.data
        if (this.tempFormModel.countyId) {
          this.tempFormModel.provinceCityCountyId = [this.tempFormModel.provinceId, this.tempFormModel.cityId, this.tempFormModel.countyId]
        } else if (this.tempFormModel.cityId) {
          this.tempFormModel.provinceCityCountyId = [this.tempFormModel.provinceId, this.tempFormModel.cityId]
        } else {
          this.tempFormModel.provinceCityCountyId = [this.tempFormModel.provinceId]
        }

        this.tempFormModel.receiverTypeId = [this.tempFormModel.receiverTypeLevelOneId, this.tempFormModel.receiverTypeLevelTwoId, this.tempFormModel.receiverTypeLevelThreeId]
        this.btnSaveLoading = false
        this.listLoading = false

        if (this.tempFormModel.enumFlags !== null && this.tempFormModel.enumFlags === 0) {
          delete this.tempFormModel.enumFlags
        }
      }).catch(error => {
        this.btnSaveLoading = false
        this.listLoading = false
        console.log(error)
      })
    },
    save() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // if (this.tempFormModel.enumReceiverFlagList) {
          //   this.tempFormModel.newEnumReceiverFlagList = this.tempFormModel.enumReceiverFlagList
          // }
          this.tempFormModel.provinceId = this.tempFormModel.provinceCityCountyId[0]

          if (this.tempFormModel.provinceCityCountyId.length >= 2) {
            this.tempFormModel.cityId = this.tempFormModel.provinceCityCountyId[1]
          } else {
            this.tempFormModel.cityId = null
          }

          if (this.tempFormModel.provinceCityCountyId.length === 3) {
            this.tempFormModel.countyId = this.tempFormModel.provinceCityCountyId[2]
          } else {
            this.tempFormModel.countyId = null
          }

          this.tempFormModel.receiverTypeLevelOneId = this.tempFormModel.receiverTypeId[0]
          this.tempFormModel.receiverTypeLevelTwoId = this.tempFormModel.receiverTypeId[1]
          this.tempFormModel.receiverTypeLevelThreeId = this.tempFormModel.receiverTypeId[2]

          if (!this.tempFormModel.id) {
            this.addNew()
          } else {
            this.update()
          }
        }
      })
    },
    addNew() {
      this.btnSaveLoading = true
      ReceiverService.AddReceiver(this.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempFormModel = result.data
          this.$notice.message('新增成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('新增失败。', 'error')
          }
        })
    },
    update() {
      this.btnSaveLoading = true
      ReceiverService.UpdateReceiver(this.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempFormModel = result.data
          this.$notice.message('修改成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('修改失败。', 'error')
          }
        })
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.tempFormModel = {}
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleDelete(index, row) {
      this.$confirm('确定删除此曾用名吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tempFormModel.receiverFormer.splice(index, 1)
      }).catch(error => {
        if (!error.succeed) {
          this.$notice.message('取消删除', 'info')
        }
      })
    },
    closeProvinceCityCountyCascader() {
      this.$refs.refProvinceCityCounty.dropDownVisible = false
    },
    handleSelectReceiver() {
      this.distributorTypes = [10, 20, 30]
      this.dialogReceiverVisible = true
    },
    closeReceiverDialog() {
      this.dialogReceiverVisible = false
      this.$refs.refDistributor.clear()
      this.distributorTypes = []
    },
    selectReceiverSuccess(val) {
      if (val.name === this.tempFormModel.name) {
        this.$notice.message('上级单位不能和当前收货方相同', 'error')
      } else {
        this.tempFormModel.parentName = val.name
        this.tempFormModel.parentReceiverId = val.id

        this.closeReceiverDialog()
      }
    },
    handleDeleteParent() {
      this.tempFormModel.parentName = ''
      this.tempFormModel.parentReceiverId = null
    },
    closeReceiverTypeCascader() {
      this.$refs.refReceiverType.dropDownVisible = false
    }
  }

}
</script>
