<template>
  <div>
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="跟踪文件查看"
      :close-on-click-modal="false"
      :visible="formVisable"
      width="50%"
      @close="close"
    >
      <el-form
        ref="dataForm"
        :model="model"
        label-position="left"
        label-width="100px"
        class="el-dialogform"
        style="width:95%"
      >
        <el-row type="flex" class="filter-container">
          <el-col :span="24">
            <el-form-item label="协议编号">
              {{ model.rebateAgreementCode }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="协议名称">
              {{ model.rebateAgreementName }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="接收方名称">
              {{ model.rebateReceiverName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始时间">
              {{ model.startDateDesc }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="截止时间">
              {{ model.endDateDesc }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品/规格">
              {{ model.productAndSpec }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="返利金额">
              {{ model.rebateAmount | toTwoNum | toThousandFilter }}元
            </el-form-item>
          </el-col>
          <el-col v-if=" model.enumRebateFeedbackStatus!==$constDefinition.rebateFeedbackStatus.Untracked" :span="24">
            <el-form-item :label="attachmentLabel">
              <div class="imgbox">
                <div v-for="(url, index) in imageList" :key="index" class="block">
                  <el-image
                    style="width: 100%; height: 100%;object-fit: cover;"
                    :src="url"
                    :preview-src-list="getSrcList(index)"
                  />
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col v-if="model.paymentTypeCode === 'RebatePaymentType_CreditNote' && model.enumRebateFeedbackStatus!==$constDefinition.rebateFeedbackStatus.Untracked" :span="24">
            <el-form-item label="外勤备注">
              {{ model.rebateFeedbackRemark }}
            </el-form-item>
          </el-col>
          <el-col v-if="model.paymentTypeCode === 'RebatePaymentType_CreditNote' && model.enumRebateFeedbackStatus===$constDefinition.rebateFeedbackStatus.Tracked" :span="24">
            <el-form-item label="审核备注">
              {{ model.rebateFeedbackReviewRemark }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="close()">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import RebateService from '@/api/rebate'
export default {
  data() {
    return {
      formVisable: false,
      imageList: [], // 图片列表
      model: {},
      attachmentLabel: ''
    }
  },
  created() {
  },
  methods: {
    init(id) {
      this.getRebateResult(id)
      this.formVisable = true
    },
    getRebateResult(id) {
      RebateService.GetRebateResult({ id: id }).then((result) => {
        this.model = result.data
        if (this.model.paymentTypeCode === 'RebatePaymentType_Ticket') {
          this.attachmentLabel = '票折文件'
        } else {
          this.attachmentLabel = '跟踪票据'
        }
        for (var i = 0; i < this.model.trackingImageList.length; i++) {
          this.imageList.push('data:image/png;base64,' + this.model.trackingImageList[i])
        }
      })
        .catch((error) => {
          console.log(error)
        })
    },
    getSrcList(index) {
      return this.imageList.slice(index).concat(this.imageList.slice(0, index))
    },
    close() {
      this.formVisable = false
      this.model = {}
      this.imageList = []
      this.$emit('success')
    }
  }

}
</script>
<style scoped>
.imgbox {
  display: flex;
}
.block {
  width: 80px;
  height: 80px;
  margin-right: 20px;
}
</style>
