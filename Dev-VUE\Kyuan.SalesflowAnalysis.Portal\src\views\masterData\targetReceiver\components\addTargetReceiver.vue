<template>
  <div>
    <el-dialog title="新增目标客户" width="70%" :close-on-click-modal="false" :visible="true" @close="cancle()">
      <el-form ref="dataForm" :rules="rules" :model="tempFormModel" label-position="right" label-width="100px" class="el-dialogform">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="厂商/产品/规格" prop="manufacturerProductAndSpecId">
              <el-cascader
                ref="refProductAndSpec"
                v-model="tempFormModel.manufacturerProductAndSpecId"
                :loading="productAndSpecLoading"
                :options="productAndSpecList"
                placeholder="厂商 / 产品 / 规格"
                style="width:100%"
                clearable
                :disabled="isReadOnly"
                class="filter-item"
                :props="{ multiple: false, checkStrictly: false ,expandTrigger: 'hover' }"
                @change="handleProductChange"
              /></el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="部门" prop="departmentId">
              <el-select
                v-model="tempFormModel.departmentId"
                placeholder="部门"
                style="width: 100%"
                clearable
                @change="changeDepartmentChange"
              >
                <el-option
                  v-for="item in deptList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="客户名称" prop="receiverName">
              <el-col :span="19" style="padding-left: 0px; padding-right: 0px;">
                <el-input
                  v-model="tempFormModel.receiverName"
                  :disabled="isReadOnly"
                  :readonly="true"
                  placeholder="客户名称"
                />
              </el-col>
              <el-col :span="5">
                <el-button
                  class="filter-item"
                  icon="el-icon-search"
                  style="width: 100%; "
                  type="primary"
                  :disabled="isReadOnly"
                  title="选择客户"
                  @click="handleSelectReceiver"
                />
              </el-col>
            </el-form-item>
          </el-col>

          <el-col :span="span">
            <el-form-item label="客户类型" prop="customerCategoryId">
              <el-select
                v-model="tempFormModel.customerCategoryId"
                class="filter-item"
                placeholder="客户类型"
                clearable
                @change="handelCategoryChange()"
              >
                <el-option
                  v-for="item in customerCategoryList"
                  :key="item.key"
                  :label="item.name"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="起始月份" prop="startDate">
              <el-date-picker
                v-model="tempFormModel.startDate"
                type="month"
                format="yyyy-MM"
                value-format="yyyy-MM"
                :disabled="isReadOnly"
                style="width: 100%"
                placeholder="起始月份"
                @change="change"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="截止月份" prop="endDate">
              <el-date-picker
                v-model="tempFormModel.endDate"
                type="month"
                format="yyyy-MM"
                :disabled="isReadOnly"
                value-format="yyyy-MM"
                style="width: 100%"
                placeholder="截止月份"
                @change="handleDateChange"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="showQuota" :span="24">
            <el-form-item v-if="currentStation.name === undefined || currentStation.name === null || currentStation.name === ''" label="岗位" prop="stationName">
              <TreeSelect
                ref="treeSelectRef"
                v-model="selectStationModel"
                :options="stationArray"
                placeholder="请选择岗位"
                :multiple="false"
                :max-height="200"
                value-format="object"
                searchable
                @input="selectStation"
              />
            </el-form-item>
            <el-form-item v-if="currentStation.name !== undefined && currentStation.name !== null && currentStation.name !== ''" label="岗位" prop="stationName">
              {{ currentStation.name }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-divider v-if="showQuota" content-position="right">
        <el-button
          v-if="!isReadOnly"
          class="filter-item"
          type="primary"
          @click="handleGenerateQuota"
        >
          生成终端潜力
        </el-button>
        <el-button
          v-if="isReadOnly"
          class="filter-item"
          type="primary"
          @click="handleCleanQuota"
        >
          清空重新选择
        </el-button></el-divider>
      <el-row v-if="showQuota" type="flex" justify="end">
        <el-col :span="24">
          <el-table
            :data="tempFormModel.quotaList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
            />
            <el-table-column
              prop="Department.Name"
              label="月份"
              min-width="80px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.yearMonthSplicing }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="Receiver.Name"
              label="客户"
              min-width="220px"
              header-align="center"
              align="left"
            >
              <template slot-scope="{ row }">
                <span>{{ row.receiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="ProductSpec.Product.Manufacturer.Name"
              label="品规"
              min-width="150px"
              align="center"
            >
              <template slot-scope="{ row }">
                <el-cascader
                  ref="refProductAndSpecRow"
                  v-model="tempFormModel.manufacturerProductAndSpecId"
                  :loading="productAndSpecLoading"
                  :options="productAndSpecList"
                  placeholder="厂商 / 产品 / 规格"
                  style="width:100%"
                  disabled
                  class="filter-item"
                  :props="{ multiple: false, checkStrictly: false ,expandTrigger: 'hover' }"
                />
                <!-- <span>{{ row.manufacturerName }}</span> -->
              </template>
            </el-table-column>
            <el-table-column
              label="潜力数量"
              min-width="120px"
              align="center"
            >
              <template slot-scope="{ row }">
                <el-input
                  v-model="row.qty"
                  min="0"
                  placeholder="潜力数量"
                  :class="row.qty_error?'error-border':''"
                  size="mini"
                  @input="changeQty(row)"
                />
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
              <template slot-scope="{ row,$index }">
                <i class="el-icon-delete eltablei" @click="handleDelete(row,$index)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          :loading="btnSaveLoading"
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="选择收货方"
      :close-on-click-modal="false"
      :visible="dialogReceiverVisible"
      width="90%"
      class="popup-search"
      @close="closeReceiverDialog"
    >
      <SelectReceiver
        ref="refSelectReceiver"
        :show-dialog="dialogReceiverVisible"
        :distributor-types="distributorTypes"
        :is-stopped="false"
        :department-id="tempFormModel.departmentId"
        @success="selectReceiverSuccess"
      />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeReceiverDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import moment from 'moment'
import SelectReceiver from '@/views/components/selectReceiver'
import MaintenanceService from '@/api/maintenance'
import ProductService from '@/api/product'
import TargetReceiverService from '@/api/targetReceiver'

export default {
  name: '',
  components: {
    SelectReceiver
  },
  props: {
    viewModel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validEndDateTime = (rule, value, callback) => {
      const startdate = Date.parse(this.tempFormModel.startDate)
      const endDate = Date.parse(value)
      if (!this.tempFormModel.startDate) {
        callback('请先选择起始月份')
        this.tempFormModel.endDate = ''
      }
      if (startdate > endDate) {
        callback('截止月份不能小于起始月份')
      }
      callback()
    }
    return {
      span: 12,
      rules: {
        manufacturerProductAndSpecId: [
          { required: true, message: '请选择厂商产品规格', trigger: 'change' }
        ],
        receiverName: [
          { required: true, message: '请选择客户', trigger: 'change' }
        ],
        startDate: [
          { required: true, message: '请选择起始日期', trigger: 'change' }
        ],
        endDate: [
          {
            required: false,
            type: 'string',
            validator: validEndDateTime,
            trigger: 'change'
          }
        ],
        customerCategoryId: [
          { required: true, message: '请选择客户类型', trigger: 'change' }
        ]
      },
      tempFormModel: { distributorName: '', receiverName: '', quotaList: [], isCoverQuota: false },
      btnSaveLoading: false,
      productAndSpecLoading: false,
      productAndSpecList: [],
      manufacturerProductAndSpecId: [],
      productAndSpecKey: 0,
      deptLoading: false,
      deptList: [],
      distributorTypes: [],
      customerCategoryLoading: false,
      customerCategoryList: [],
      dialogDistributorVisible: false,
      dialogReceiverVisible: false,
      endDateString: '2099-12-31 00:00:00',
      isReadOnly: false,
      showQuota: false,
      selectedReceiver: {
        stationName: ''
      },
      currentStationFilter: {
        receiverId: null,
        endDate: null
      },
      currentStation: {
        name: ''
      },
      datatree: [],
      stationArray: [],
      selectStationModel: null
    }
  },
  mounted() {
  },
  created() {
    this.init()
  },
  methods: {
    initProductAndSpec() {
      this.productAndSpecLoading = true
      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    handleProductChange() {
      if (this.tempFormModel.manufacturerProductAndSpecId !== null && this.tempFormModel.manufacturerProductAndSpecId !== undefined && this.tempFormModel.manufacturerProductAndSpecId.length > 1) {
        this.tempFormModel.productSpecId = this.tempFormModel.manufacturerProductAndSpecId[2]
      }
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDepartmentForTargetReceiver()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch(() => {
          this.deptLoading = false
        })
    },
    initCustomerCategory() {
      this.customerCategoryLoading = true
      TargetReceiverService.QueryCustomerCategorySelect().then(res => {
        this.customerCategoryLoading = false
        this.customerCategoryList = res
      })
        .catch(
          this.customerCategoryLoading = false
        )
    },
    init() {
      this.initProductAndSpec()
      this.initDept()
      this.initCustomerCategory()
      this.QueryOrganization()
      this.clear()
    },
    changeQty(row, index) {
      var qty = Number(row.qty)
      const regex = /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/
      if (!regex.test(qty) || row.qty === '') {
        this.$message.error('潜力只能是两位小数或整数')
        row.qty_error = true
      } else {
        row.qty_error = false
      }
    },
    save() {
      if (this.tempFormModel.quotaList.some(item => { return item.qty_error })) {
        return false
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          var checkedCustomerCategory = this.customerCategoryList.find(item => { return item.key === this.tempFormModel.customerCategoryId })
          if ((checkedCustomerCategory.value === this.$constDefinition.customerCategory.selfSupportDevelopCode || checkedCustomerCategory.value === this.$constDefinition.customerCategory.selfSupportCode) && (this.tempFormModel.departmentId === undefined || this.tempFormModel.departmentId === null || this.tempFormModel.departmentId === '')) {
            this.$notice.message('部门不能为空', 'error')
            return
          }
          if (this.tempFormModel.departmentId !== null && this.tempFormModel.departmentId !== undefined) {
            TargetReceiverService.CheckDepartmentOfReceiverRange(this.tempFormModel).then(result => {
              if (result.data === this.$constDefinition.checkReceiverRange.notDistributor) {
                this.clearErrorData()
                this.$notice.message('商务部只能选择经销商类型的客户', 'error')
                return
              } else if (result.data === this.$constDefinition.checkReceiverRange.notRetailAndMedical) {
                this.clearErrorData()
                this.$notice.message('销售部只能选择医疗机构和零售类型的客户', 'error')
                return
              } else if (result.data === this.$constDefinition.checkReceiverRange.notRetail) {
                this.clearErrorData()
                this.$notice.message('零售部仅能选择零售类型的客户', 'error')
                return
              } else {
                var endDateString = null
                if (this.tempFormModel.endDate !== undefined && this.tempFormModel.endDate !== null) {
                  endDateString = this.tempFormModel.endDate
                } else {
                  endDateString = new Date(this.tempFormModel.startDate).getFullYear() + '-12'
                }
                var startDate = moment(this.tempFormModel.startDate).format('YYYY-MM')
                var endDate = moment(endDateString).format('YYYY-MM')

                if (endDate < startDate) {
                  this.$notice.message('截止月份不能小于起始月份', 'error')
                  return
                }

                TargetReceiverService.GetTargetReceiverQuota(this.tempFormModel).then(result => {
                  if (result.data !== null && result.data !== undefined && result.data.length > 0) {
                    this.$confirm('目标客户在该时间段内已有指标，是否需要更新?', '提示', {
                      confirmButtonText: '是',
                      cancelButtonText: '否',
                      type: 'warning'
                    }).then(() => {
                      this.tempFormModel.isCoverQuota = true
                      this.addNew()
                    }).catch(() => {
                      this.tempFormModel.isCoverQuota = false
                      this.addNew()
                    })
                  } else {
                    this.addNew()
                  }
                }).catch(error => {
                  console.log(error)
                })
              }
            })
          } else {
            this.addNew()
          }
        }
      })
    },
    addNew() {
      this.btnSaveLoading = true
      this.tempFormModel.stationId = this.currentStation.id
      // 为防止后选部门 重新更新一下潜力部门id
      if (this.tempFormModel.quotaList && this.tempFormModel.quotaList.length > 0) {
        this.tempFormModel.quotaList.map(item => {
          item.departmentId = this.tempFormModel.departmentId
          return item
        })
      }
      TargetReceiverService.AddTargetReceiver(this.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempFormModel = result.data
          this.$notice.message('新增成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('新增失败。', 'error')
          }
        })
    },
    handleSelectReceiver() {
      this.distributorTypes = [10, 20, 30]
      this.dialogReceiverVisible = true
    },
    closeReceiverDialog() {
      this.dialogReceiverVisible = false
      this.$refs.refSelectReceiver.clear()
      this.distributorTypes = []
    },
    selectReceiverSuccess(val) {
      this.selectedReceiver = val
      // 解决弹出框打开后，不输入任何值，只选择收货方名称时，验证失效的问题
      this.$set(this.tempFormModel, 'receiverName', val.name)
      // this.tempFormModel.receiverName = val.name
      this.tempFormModel.receiverId = val.id
      this.closeReceiverDialog()
      // 获取当前岗位
      this.GetCurrentStationByReceiverId()
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.tempFormModel = { quotaList: [] }
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    },
    changeDepartmentChange() {
      if (this.tempFormModel.departmentId) {
        var deptStation = []
        deptStation.push(this.getDeptStation(this.datatree[0], this.tempFormModel.departmentId))
        this.stationArray = this.chearEmityChildren(deptStation)
        this.GetCurrentStationByReceiverId()
      }

      this.$forceUpdate()
    },
    change() {
      this.$forceUpdate()
    },
    handleGenerateQuota() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.isReadOnly = true
          this.tempFormModel.quotaList = []

          const startDate = this.tempFormModel.startDate
          const startYear = new Date(startDate).getFullYear()
          const startMonth = new Date(startDate).getMonth() + 1

          let endMonth = 12
          if (this.tempFormModel.endDate != null && this.tempFormModel.endDate !== undefined) {
            const endDate = this.tempFormModel.endDate
            const endYear = new Date(endDate).getFullYear()
            // 结束日期同一年，以结束日期月份为准
            if (endYear === startYear) {
              endMonth = new Date(endDate).getMonth() + 1
            }
          }

          for (let index = startMonth; index <= endMonth; index++) {
            const month = index < 10 ? '0' + index : index
            const date = startYear + '-' + month

            const quota = { productSpecId: this.tempFormModel.productSpecId,
              receiverId: this.tempFormModel.receiverId,
              receiverName: this.tempFormModel.receiverName,
              qty: 0,
              month: index,
              yearMonthSplicing: date,
              departmentId: this.tempFormModel.departmentId,
              customerCategoryId: this.tempFormModel.customerCategoryId
            }
            this.tempFormModel.quotaList.push(quota)
          }
        }
      })
    },
    handleCleanQuota() {
      this.isReadOnly = false
      this.tempFormModel.quotaList = []
    },
    handleDelete(row, index) {
      console.log(index)
      this.$confirm('确定删除此数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tempFormModel.quotaList.splice(index, 1)
      }).catch(error => {
        if (!error.succeed) {
          this.showMessage('取消删除', 'info')
        }
      })
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handelCategoryChange() {
      var checkedCustomerCategory = this.customerCategoryList.find(item => { return item.key === this.tempFormModel.customerCategoryId })
      if (checkedCustomerCategory.value === this.$constDefinition.customerCategory.selfSupportCode) {
        this.showQuota = true
        this.GetCurrentStationByReceiverId()
      } else {
        this.showQuota = false
        this.tempFormModel.quotaList = []
        this.tempFormModel.stationName = ''
        this.tempFormModel.stationId = null
        this.tempFormModel.employeeId = null
        this.tempFormModel.employeeDisplayName = ''
        this.currentStation.name = ''
        this.currentStation.id = null
      }
    },
    GetCurrentStationByReceiverId() {
      if (this.showQuota && this.tempFormModel.receiverId !== undefined && this.tempFormModel.receiverId !== '' && this.tempFormModel.receiverId !== null &&
        this.tempFormModel.departmentId !== undefined && this.tempFormModel.departmentId !== '' && this.tempFormModel.departmentId !== null
      ) {
        var endDate = this.tempFormModel.endDate ? this.tempFormModel.endDate : '9999-12'

        this.currentStationFilter.receiverId = this.selectedReceiver.id
        this.currentStationFilter.endDate = endDate
        this.currentStationFilter.deptId = this.tempFormModel.departmentId
        MaintenanceService.GetCurrentStationByReceiverId(this.currentStationFilter)
          .then((result) => {
            if (result.data) {
              this.currentStation = result.data
            } else {
              this.currentStation = {
                name: ''
              }
            }
          })
          .catch(() => {
          })
      }
    },
    handleDateChange() {
      this.GetCurrentStationByReceiverId()
    },
    selectStation(sel) {
      if (sel) {
        if (sel.enableAddReceiver === false) {
          this.$notice.message('只能选择大区、地区以及代表的岗位', 'error')
          this.$refs.treeSelectRef.clear()
          return
        }
        this.currentStation.id = this.selectStationModel.pkid
      } else {
        this.$set(this.tempFormModel, 'stationId', '')
      }
      this.$refs['dataForm'].validateField(['stationId'])
    },
    QueryOrganization() {
      MaintenanceService.QueryOrganizationExcludeAssistant().then(result => {
        if (result.succeed) {
          this.datatree = this.chearEmityChildren(result.data)
          this.stationArray = this.chearEmityChildren(result.data)
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    getDeptStation(station, departmentId) {
      if (station instanceof Array) {
        station.map(item => {
          this.getDeptStation(item, departmentId)
        })
      } else {
        if (station.deptId === departmentId) {
          return station
        }
        if (station.children !== null && station.children !== undefined) {
          if (station.children.length > 0) {
            for (var item of station.children) {
              var resultStation = this.getDeptStation(item, departmentId)
              if (resultStation) {
                return resultStation
              }
            }
          } else {
            delete station.children
          }
        }
      }
      return null
    },
    chearEmityChildren(stations) {
      return stations.map(item => {
        if (item !== null && item !== undefined && item.children !== null && item.children !== undefined) {
          if (item.children.length > 0) {
            this.chearEmityChildren(item.children)
          } else {
            delete item.children
          }
          item.isDefaultExpanded = true
          return item
        } else {
          return item
        }
      })
    },
    getQuotaList() {
      TargetReceiverService.GetTargetReceiverQuota(this.tempFormModel).then(result => {
        this.tempFormModel.quotaList = result.data
      }).catch(error => {
        console.log(error)
      })
    },
    clearErrorData() {
      this.tempFormModel.receiverId = null
      this.tempFormModel.receiverName = ''
      this.tempFormModel.stationName = ''
      this.tempFormModel.stationId = null
      this.tempFormModel.employeeId = null
      this.tempFormModel.employeeDisplayName = ''
      this.tempFormModel.quotaList = []
      this.currentStation.name = ''
      this.currentStation.id = null
    }
  }
}
</script>
<style>

.error-border{
}
.error-border .el-input__inner{
  border: 1px solid red;
  border-radius: 5px;
}
</style>
