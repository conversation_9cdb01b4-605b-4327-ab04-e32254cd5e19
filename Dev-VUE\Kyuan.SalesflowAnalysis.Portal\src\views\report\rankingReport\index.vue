<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="span">
          <el-date-picker
            v-model="listQuery.checkedMonth"
            class="filter-item"
            placeholder="流向月份"
            type="month"
            :clearable="false"
            format="yyyy-MM"
            value-format="yyyy-MM"
            @change="timeChange"
            @blur="timeChange"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.manufacturerId"
            :loading="manufacturerLoading"
            class="filter-item"
            placeholder="厂商名称"
            clearable
            @change="manufacturerChange"
          >
            <el-option
              v-for="item in manufacturerList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProductAndSpec"
            :key="productAndSpecKey"
            v-model="productAndSpecId"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            placeholder="产品/规格"
            class="filter-item"
            clearable
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            @change="productChange"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.receiverName"
            clearable
            placeholder="终端名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'SalesRankingReport_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'SalesRankingReport_Button_Export')"
            :loading="btnExportLoading"
            class="filter-item-button"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              label="月份"
              sortable="custom"
              min-width="100px"
              header-align="center"
              align="center"
              prop="YearMonthSplicing"
            >
              <template slot-scope="{ row }">
                <span>{{ row.yearMonthSplicing }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="省份"
              sortable="custom"
              min-width="100px"
              header-align="center"
              align="center"
              prop="ProvinceNameCn"
            >
              <template slot-scope="{ row }">
                <span>{{ row.provinceNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="城市"
              sortable="custom"
              min-width="100px"
              header-align="center"
              align="center"
              prop="CityNameCn"
            >
              <template slot-scope="{ row }">
                <span>{{ row.cityNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="终端名称"
              sortable="custom"
              min-width="200px"
              header-align="center"
              align="left"
              prop="ReceiverName"
            >
              <template slot-scope="{ row }">
                <span>{{ row.receiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="产品名称"
              sortable="custom"
              min-width="120px"
              header-align="center"
              align="center"
              prop="ProductNameCn"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column label="包装规格" sortable="custom" min-width="120px" prop="Spec" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.spec }}</span>
              </template>
            </el-table-column>
            <el-table-column label="销量" sortable="custom" min-width="100px" prop="SaleQty" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.saleQty }}</span>
              </template>
            </el-table-column>
            <el-table-column label="潜力" sortable="custom" min-width="100px" prop="QuotaQty" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span v-if="!row.quotaQty">-</span>
                <span v-if="row.quotaQty">{{ row.quotaQty }}</span>
              </template>
            </el-table-column>
            <el-table-column label="进度" sortable="custom" min-width="100px" prop="AchievedRate" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.achievedRate }}</span>
              </template>
            </el-table-column>
            <el-table-column label="排名" sortable="custom" min-width="100px" prop="Ranking" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.ranking }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出报表"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import CustomExport from '@/components/Export/CustomExport'
import ManufacturerService from '@/api/manufacturer'
import ProductService from '@/api/product'
import ReportService from '@/api/report'
import CycleService from '@/api/cycle'

export default {
  components: {
    Pagination,
    CustomExport
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-SaleQty'
      },
      listLoading: false,
      list: [],
      manufacturerLoading: false,
      productAndSpecLoading: false,
      manufacturerList: [],
      productAndSpecList: null,
      productAndSpecId: [],
      productAndSpecKey: 0,
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: {}
    }
  },
  created() {
    this.initManufacturer()
    this.initProductAndSpec()
  },
  methods: {
    initManufacturer() {
      this.manufacturerLoading = true
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturerLoading = false
          this.manufacturerList = result
        })
        .catch((error) => {
          this.manufacturerLoading = false
          console.log(error)
        })
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      const para = { manufacturerId: this.listQuery.manufacturerId }
      ProductService.QueryProductAndSpecCascader(para)
        .then((result) => {
          this.productAndSpecList = result
          if (this.$route.query.fromPage) {
            this.productAndSpecId = this.$route.query.productSpecId
          }
          this.productAndSpecLoading = false
          this.getMaxCycle()
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    manufacturerChange() {
      this.productAndSpecId = []
      ++this.productAndSpecKey
      this.initProductAndSpec()
    },
    getMaxCycle() {
      CycleService.GetTheLatestProcessedCycle()
        .then((result) => {
          if (result.succeed) {
            const model = result.data
            if (model) {
              this.listQuery.checkedMonth = model.yearMonthSplicing
            }
            this.handleFilter()
          } else {
            this.$notice.resultTip(result)
          }
        })
        .catch(error => {
          console.log(error)
        })
    },
    timeChange() {
      this.$forceUpdate()
    },
    handleFilter() {
      if (this.listQuery.checkedMonth) {
        this.listQuery.pageIndex = 1
        this.getList()
      } else {
        this.$notice.message('请选择月份', 'error')
      }
    },
    getList() {
      if (this.productAndSpecId) {
        const [productId, productSpecId] = this.productAndSpecId
        this.listQuery.productId = productId
        this.listQuery.productSpecId = productSpecId
      }
      this.listLoading = true
      ReportService.QuerySalesRankingDetail(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    productChange() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    // 显示导出model
    onShowExportModal() {
      if (!this.listQuery.checkedMonth) {
        this.$notice.message('请选择月份', 'error')
        return
      }

      this.btnExportLoading = true
      ReportService.GetSalesRankingDetailExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      var exportParam = JSON.parse(JSON.stringify(this.listQuery))
      exportParam.checkedColumns = checkColumns
      ReportService.ExportSalesRankingDetail(exportParam)
        .then(result => {
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '销售排名明细.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '销售排名明细.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    }

  }
}
</script>
