<template>
  <div>
    <el-card>
      <div slot="header" class="clearfix">
        <span>销售部奖励待办</span>
      </div>
      <div>
        <el-row>
          <el-col :span="24">
            <el-table :data="list" style="width: 100%;height:215;">
              <el-table-column prop="Title" label="部门" min-width="100px">
                <template slot-scope="{ row }">
                  <span> {{ row.departmentName }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="Title" label="待审核奖励" min-width="100px">
                <template slot-scope="{ row }">
                  <span class="link-type" @click="showBonus"> {{ row.bonusResultName }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import HomeService from '@/api/home'

export default {
  components: {
  },
  data() {
    return {
      list: [],
      announcementId: '',
      listQuery: {
        pageIndex: 1,
        pageSize: 5,
        order: '-CreateTime'
      }
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      HomeService.QueryWaitApprovalBonus(this.listQuery).then(res => {
        this.list = res.data.datas
      }).catch(res => {})
    },
    showBonus() {
      this.$router.push({ name: 'Compute', query: { fromPage: 'Home' }})
    }
  }
}
</script>
