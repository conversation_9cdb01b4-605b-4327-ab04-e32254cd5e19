import HttpApi from './libs/api.request'

const controller = 'ReceiverAlias'

const api = new HttpApi(controller)

export default {
  QueryReceiverAlias(params) {
    return api.get('QueryReceiverAlias', params)
  },
  GetReceiverAlias(params) {
    return api.get('GetReceiverAlias', params)
  },
  AddReceiverAlias(params) {
    return api.post('AddReceiverAlias', params)
  },
  UpdateReceiverAlias(params) {
    return api.post('UpdateReceiverAlias', params)
  },
  DeleteReceiverAlias(params) {
    return api.post('DeleteReceiverAlias', params)
  },
  GetReceiverAliasExportColumn() {
    return api.get('GetReceiverAliasExportColumn')
  },
  ExportReceiverAlias(params) {
    return api.post('ExportReceiverAlias',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  }
}
