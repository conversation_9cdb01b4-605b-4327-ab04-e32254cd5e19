<template>
  <div>
    <el-dialog custom-class="el-dialog-s" title="导入实际成本" width="80%" append-to-body :close-on-click-modal="false" :visible="showDialog" @close="handleCancle()">

      <div class="top-btn">
        <el-link name="inp" :href="'/template/ActualCostTemplate.xlsx'" download="实际成本" type="primary" icon="el-icon-document">模板下载</el-link>
        <file-upload
          name="inp"
          :controller="controller"
          :method="method"
          :form-data="formData"
          @uploadSuccess="uploadSuccess"
        />
        <el-button
          name="inp"
          type="primary"
          icon="el-icon-download"
          @click="handleExport()"
        >
          导出
        </el-button>
      </div>
      <el-row>
        <el-col :span="24">
          <el-table
            ref="errorDataTable"
            :data="rebateProjectPhaseCostList"
            max-height="400"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :header-cell-class-name="'tableStyle'"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
            />
            <el-table-column label="部门" align="center" width="150px">
              <template slot-scope="{ row }">
                {{ row.departmentName }}
              </template>
            </el-table-column>
            <el-table-column label="省份" align="center" width="150px">
              <template slot-scope="{ row }">
                {{ row.provinceName }}
              </template>
            </el-table-column>
            <el-table-column label="工号" align="center" width="120px">
              <template slot-scope="{ row }">
                {{ row.jobNo }}
              </template>
            </el-table-column>
            <el-table-column
              label="员工姓名"
              align="center"
              width="80px"
            >
              <template slot-scope="{ row }">
                {{ row.employeeDisplayName }}
              </template>
            </el-table-column>
            <el-table-column label="客户code" align="center" width="80px">
              <template slot-scope="{ row }">
                {{ row.receiverCode }}
              </template>
            </el-table-column>
            <el-table-column label="客户名称" align="center" min-width="150px">
              <template slot-scope="{ row }">
                {{ row.receiverName }}
              </template>
            </el-table-column>
            <el-table-column label="费用日期" align="center" min-width="100px">
              <template slot-scope="{ row }">
                {{ row.costDateStr }}
              </template>
            </el-table-column>
            <el-table-column label="成本科目" align="center" min-width="80px">
              <template slot-scope="{ row }">
                {{ row.costType }}
              </template>
            </el-table-column>
            <el-table-column label="成本金额(元)" align="center" min-width="100px">
              <template slot-scope="{ row }">
                {{ row.amountStr }}
              </template>
            </el-table-column>
            <el-table-column label="备注" align="center" min-width="150px">
              <template slot-scope="{ row }">
                {{ row.remark }}
              </template>
            </el-table-column>
            <el-table-column label="错误信息" align="center" min-width="250px">
              <template slot-scope="{ row }">
                {{ row.errorMsg }}
              </template>
            </el-table-column>
            <!-- <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i class="el-icon-delete eltablei" title="删除" @click="handleDeleteCost(row)" />
              </template>
            </el-table-column> -->
          </el-table>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleCancle()">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import FileUpload from '@/views/components/autoUploadFile'
import * as XLSX from 'xlsx'

import { saveAs } from 'file-saver'
export default {
  components: {
    FileUpload
  },
  data() {
    return {
      showDialog: false,
      rebateProjectPhaseCostList: [],
      controller: 'Project',
      method: 'ImportProjectPhaseCost',
      initData: {},
      formData: {}
    }
  },

  methods: {
    initPage(data) {
      this.showDialog = true
      this.initData = data
      this.formData = {
        departmentList: JSON.stringify(data.departmentList),
        startTime: data.startTime,
        endTime: data.endTime
      }
    },
    handleCancle() {
      this.showDialog = false
      this.rebateProjectPhaseCostList = []
    },
    handleExport() {
      const records = []
      // 表头
      var header = this.$refs.errorDataTable.$el.querySelector('.el-table__header').querySelectorAll('tr')
      header.forEach(row => {
        const record = []
        row.querySelectorAll('th').forEach((cell, index) => {
          if (index > 0) { record.push(cell.innerText) }
        })
        records.push(record)
      })
      // 数据
      var rows = this.$refs.errorDataTable.$el.querySelector('.el-table__body').querySelectorAll('tr')
      rows.forEach(row => {
        const record = []
        row.querySelectorAll('td').forEach((cell, index) => {
          if (index > 0) { record.push(cell.innerText) }
        })
        records.push(record)
      })
      // 创建工作表
      const worksheet = XLSX.utils.aoa_to_sheet(records)
      // 创建工作簿并添加工作表
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
      // 生成 Excel 文件
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
      // 使用 saveAs 保存文件
      const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' })
      saveAs(data, '实际成本错误数据.xlsx')
    },
    uploadSuccess(val) {
      this.$message.success('导入成功')
      if (val.errorData && val.errorData.length > 0) {
        this.rebateProjectPhaseCostList = val.errorData
      } else {
        this.showDialog = false
      }
      if (val.successData) {
        this.$emit('successData', val.successData)
      }
    }
  }
}
</script>
<style >
.top-btn{
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  margin-bottom: 15px;
}
[name="inp"]{
  margin-right: 10px;
}

[name="inp"]:last-child{
  margin-right: 0px;
}
</style>
