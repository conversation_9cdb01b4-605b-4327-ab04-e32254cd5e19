<template>
  <div>
    <el-row>
      <el-col>
        <el-input v-if="showType === 'input' && chooseType === 'single'" ref="refInput" :value="inputValue" :placeholder="placeholder" prefix-icon="el-icon-search" @focus="handleShowSelect" @clear="clearSelect" />
        <i
          v-else
          class="el-icon-user-solid"
          style="
            font-size: 1.3rem;
            cursor: pointer;
            line-height: 170%;
            color: #304156;
          "
          @click="handleShowSelect"
        />
      </el-col>
    </el-row>

    <!--选择人-->
    <el-dialog append-to-body :title="title" width="60%" :close-on-click-modal="false" :visible="showDialog" @close="cancleDialog()">
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filiter-container">
          <el-col :span="4">
            <el-input v-model="listQuery.jobNo" clearable placeholder="工号" class="filter-item" />
          </el-col>
          <el-col :span="4">
            <el-input v-model="listQuery.displayName" clearable placeholder="请输入员工姓名" class="filter-item" />
          </el-col>
          <el-col :span="span">
            <el-button class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
              查询
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="dataList-container">
        <el-row :class="showSelectAll && chooseType === 'multiple' ? '' : 'components-dialog'">
          <el-col :span="24">
            <el-table
              ref="refTable"
              :data="dataList"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%"
              :default-sort="{ prop: 'JobNo', order: 'ascending' }"
              :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass"
              @select-all="handleSelectionAll"
              @select="handleSelectionRow"
            >
              <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
              <el-table-column
                label="工号"
                min-width="100px"
                align="center"
                header-align="center"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.jobNo }}</span>
                </template>
              </el-table-column>
              <el-table-column label="姓名" min-width="120px" align="left" header-align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.displayName }}{{ row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="职务" min-width="150px" align="left" header-align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.title }}</span>
                </template>
              </el-table-column>
              <el-table-column label="邮箱" min-width="150px" align="left" header-align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.email }}</span>
                </template>
              </el-table-column>

              <el-table-column v-if="chooseType === 'single'" fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <i style="color: #13ce66" class="el-icon-circle-check eltablei" title="选择" @click="handleSingleCheck(row)" />
                </template>
              </el-table-column>
              <el-table-column v-else type="selection" width="55" />
            </el-table>
          </el-col>
          <el-col v-if="showPagination" class="el-colRight">
            <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleDialog()">
          关闭
        </el-button>
        <el-button v-if="chooseType === 'multiple'" type="primary" icon="el-icon-check" @click="submit">
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import props from './props'
import Pagination from '@/components/Pagination'
import maintenanceApi from '@/api/maintenance'

export default {
  components: {
    Pagination
  },
  props: props,
  data() {
    return {
      span: 4,
      showDialog: false,
      showPagination: false,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '+DisplayName'
      },
      dataList: [],
      displayName: this.inputValue,
      selectedData: [],
      resultData: []
    }
  },
  methods: {
    handleShowSelect() {
      this.showDialog = true
      this.getList()
      this.$refs.refInput.blur()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList() {
      // this.$set(this.resultData, this.initData)
      // this.resultData = this.initData
      maintenanceApi.QueryEmployee(this.listQuery).then((res) => {
        if (res) {
          this.dataList = res.data.datas
          this.total = res.data.recordCount
          this.listQuery.pageIndex = res.data.pageIndex
          this.showPagination = this.total > this.listQuery.pageSize
          // this.initSelect()
        }
      })
    },
    initSelect() {
      // this.$nextTick(() => {
      //   if (this.resultData.length > 0) {
      //     this.resultData.map(item => {
      //       if (this.dataList.some(s => { return (item.id && item.id === s.id) || (item.memberId && item.memberId === s.memberId) })) {
      //         var scitem = this.dataList.find(f => { return (item.id && item.id === f.id) || (item.memberId && item.memberId === f.memberId) })

      //         this.$refs.refTable.toggleRowSelection(scitem, true)
      //       } else {
      //         this.$refs.refTable.toggleRowSelection(item, false)
      //       }
      //     })
      //   }
      // })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    // 单选
    handleSingleCheck(row) {
      this.displayName = row[this.showColumn]
      // this.$emit('update', this.displayName)
      this.$emit('change', row)
      this.cancleDialog()
    },
    // 多选(全选)
    handleSelectionAll(selections) {
      if (selections.length > 0) {
        selections.map(sel => {
          if (!this.resultData.some(res => { return res.id === sel.id })) {
            this.resultData.push(sel)
          }
        })
      } else {
        this.dataList.map(sel => {
          const index = this.resultData.findIndex((item) => {
            return item.id === sel.id
          })
          this.resultData.splice(index, 1)
        })
      }
    },
    // 多选(单选)
    handleSelectionRow(selection, row) {
      if (selection.some(sel => { return sel.id === row.id })) {
        if (!this.resultData.some(res => { return res.id === row.id })) {
          this.resultData.push(row)
        }
      } else {
        const index = this.resultData.findIndex((item) => {
          return item.id === row.id
        })
        this.resultData.splice(index, 1)
      }
    },
    submit() {
      if (this.resultData.length > 0) {
        this.$emit('change', this.resultData)
        this.cancleDialog()
      } else {
        this.$message.error('至少选择一个')
      }
    },
    clearSelect() {
    },
    cancleDialog() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
        order: '+DisplayName'
      }
      this.showDialog = false
      this.resultData = []
      this.dataList = []
    }
  }

}
</script>

<style scoped>
::v-deep .el-input__validateIcon {
  display: none;
}
.components-dialog ::v-deep th .el-checkbox {
  display: none !important;
}
</style>
