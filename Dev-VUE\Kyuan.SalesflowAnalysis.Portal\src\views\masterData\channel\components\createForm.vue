<template>
  <div class="channelBox">
    <el-dialog :close-on-click-modal="false" :title="title" :visible="isVisible" width="70%" @close="btnClose">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="90px">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="12">
            <el-form-item label="经销商名称" prop="receiverName">
              <el-col :span="20" style="padding-left: 0px; padding-right: 0px;">
                <el-input v-model="temp.receiverName" :readonly="true" placeholder="经销商名称" />
              </el-col>
              <el-col :span="4">
                <el-button
                  icon="el-icon-search"
                  style="width: 100%; "
                  type="primary"
                  title="选择经销商"
                  @click="handleSelectReceiver"
                />
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效日期" prop="startMonth">
              <el-date-picker
                v-model="temp.startMonth"
                type="month"
                clearable
                value-format="yyyy-MM"
                style="width: 100%"
                :picker-options="datePickerOptions"
                placeholder="生效日期"
              />
            </el-form-item></el-col>
        </el-row>
      </el-form>
      <!-- {{ temp }} -->
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button type="primary" icon="el-icon-plus" @click="handleCreateChannel">
            新增渠道
          </el-button>
        </el-col>
      </el-row>
      <div class="list-container">
        <el-table :data="tableData" stripe style="width: 100%" size="small">
          <el-table-column fixed label="序号" type="index" align="center" />
          <el-table-column label="厂商" width="250px" align="center">
            <template slot-scope="{ row }">
              <el-select
                v-model="row.manufacturerId"
                style="width: 100%"
                class="filter-item"
                placeholder="厂商"
                @change="manufacturerChange(row)"
              >
                <el-option v-for="item in manufacturers" :key="item.key" :label="item.value" :value="item.key" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="产品/规格" width="250px" align="center">
            <template slot-scope="{ row }">
              <el-cascader
                ref="refProductAndSpec"
                v-model="row.productAndSpecId"
                style="width:100%"
                :options="row.productAndSpecList"
                placeholder="产品/规格"
                clearable
                @change="handleProductChange(row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="上游商业">
            <template slot-scope="{ row,$index}">
              <el-input v-model="row.distributorName" placeholder="请输入上游商业" @focus="handleSelectDistributor($index)" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="60" align="center">
            <template slot-scope="row">
              <i class="el-icon-delete eltablei" @click="handleDeleteChannel(row.$index)" />
            </template>
          </el-table-column>
        </el-table>
        <!-- {{ tableData }} -->
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnClose()"> 关闭 </el-button>
        <el-button :loading="btnLoading" type="primary" icon="el-icon-check" @click="submitForm">
          保存
        </el-button>
      </div>
    </el-dialog>
    <!--选择客户-->
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="选择经销商"
      :close-on-click-modal="false"
      :visible="dialogReceiverVisible"
      width="80%"
      class="popup-search"
      @close="closeReceiverDialog"
    >
      <SelectReceiver
        ref="refSelectReceiver"
        :show-dialog="dialogReceiverVisible"
        :distributor-types="distributorTypes"
        :is-stopped="false"
        @success="selectReceiverSuccess"
      />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeReceiverDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <!--选择经销商-->
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="选择上游商业"
      :close-on-click-modal="false"
      :visible="dialogDistributorVisible"
      width="80%"
      class="popup-search"
      @close="closeDistributorDialog"
    >
      <SelectReceiver
        ref="refSelectDistributor"
        :show-dialog="dialogDistributorVisible"
        :distributor-types="distributorTypes"
        :is-stopped="false"
        @success="selectDistributorSuccess"
      />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeDistributorDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import SelectReceiver from '@/views/components/selectReceiver'
import LocationService from '@/api/location'
import channelService from '@/api/channel'
import ProductService from '@/api/product'
import ManufacturerService from '@/api/manufacturer'

export default {
  components: {
    SelectReceiver
  },
  data() {
    return {
      isVisible: false,
      dialogDistributorVisible: false,
      title: '新增',
      btnLoading: false,
      temp: {
        distributorId: undefined,
        provinceAndCityId: undefined,
        receiverName: undefined,
        effectiveDate: undefined
      },
      rules: {
        receiverName: [
          {
            required: true,
            type: 'string',
            message: '请输入经销商名称',
            trigger: 'change'
          }
        ],
        startMonth: [
          {
            required: true,
            type: 'string',
            message: '请输入生效日期',
            trigger: 'change'
          }
        ]
      },
      tableData: [],
      distributorTypes: [],
      dialogReceiverVisible: false,
      provinceAndCityList: [],
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6
        }
      },
      editDistributorIndex: null
    }
  },
  created() {
    this.initProvinceAndCity()
    this.initPage()
  },
  methods: {
    initPage() {
      this.initManufacturer()
      this.initProductAndSpec()
    },
    initManufacturer() {
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturers = result
        })
        .catch(() => {
        })
    },
    initProductAndSpec() {
      ProductService.QueryProductAndSpecCascader()
        .then((result) => {
          this.allProductAndSpecList = result
        })
        .catch(() => {
        })
    },
    initProvinceAndCity() {
      LocationService.QueryProvinceCityCascader()
        .then((result) => {
          this.provinceAndCityList = result
        })
        .catch((error) => {
          console.log(error)
        })
    },
    closeProvinceCityCascader() {
      this.$refs.refProvinceCity.dropDownVisible = false
    },
    handleCreate() {
      this.title = '新增渠道'
      this.isVisible = true
    },
    btnClose() {
      this.tableData = []
      this.temp = {}
      this.isVisible = false
      this.$refs.dataForm.resetFields()
    },
    // 选择收货方
    handleSelectReceiver() {
      this.distributorTypes = [10, 20, 30]
      this.dialogReceiverVisible = true
    },
    closeReceiverDialog() {
      this.dialogReceiverVisible = false
      this.$refs.refSelectReceiver.clear()
      this.distributorTypes = []
    },
    selectReceiverSuccess(val) {
      if (val.id === this.temp.distributorId) {
        this.$notice.message('经销商不能和上游商业相同', 'error')
      } else {
        // 解决弹出框打开后，不输入任何值，只选择收货方名称时，验证失效的问题
        this.$set(this.temp, 'receiverName', val.name)
        // this.temp.receiverName = val.name
        this.temp.receiverId = val.id
        this.closeReceiverDialog()
      }
    },
    // 选择经销商
    handleSelectDistributor(index) {
      this.editDistributorIndex = index
      this.distributorTypes = [10, 20]
      this.dialogDistributorVisible = true
      this.editDistributorRow = {}
    },
    closeDistributorDialog() {
      this.dialogDistributorVisible = false
      this.$refs.refSelectDistributor.clear()
      this.editDistributorIndex = null
      this.distributorTypes = []
    },
    selectDistributorSuccess(val) {
      if (val.id === this.temp.receiverId) {
        this.$notice.message('经销商不能和上游商业相同', 'error')
      } else {
        this.$set(this.tableData[this.editDistributorIndex], 'distributorId', val.id)
        this.$set(this.tableData[this.editDistributorIndex], 'distributorName', val.name)
        this.closeDistributorDialog()
      }
    },
    // 保存
    submitForm() {
      if (this.tableData === undefined || this.tableData.length === 0) {
        this.$notice.message('产品渠道信息不可为空', 'error')
        return
      }
      const hasDuplicate = this.tableData.filter((item, index, array) => {
        return array.some((innerItem, innerIndex) => { return innerIndex !== index && innerItem.productSpecId === item.productSpecId && innerItem.distributorId === item.distributorId })
      })
      if (hasDuplicate.length > 0) {
        this.$notice.message('产品渠道存在重复数据', 'error')
        return
      }

      const emptyIndex = this.tableData.findIndex(element => element.productSpecId === undefined || element.distributorId === undefined)
      if (emptyIndex !== -1) {
        this.$notice.message('产品渠道存在不完整数据', 'error')
        return
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          if (this.temp.provinceAndCityId) {
            this.temp.provinceId = this.temp.provinceAndCityId[0]
            if (this.temp.provinceAndCityId.length === 2) {
              this.temp.cityId = this.temp.provinceAndCityId[1]
            }
          }
          this.temp.ChannelDistributorRequests = this.tableData
          channelService.AddChannel(this.temp)
            .then((result) => {
              if (result.succeed) {
                this.$message({
                  message: '创建成功',
                  type: 'success'
                })
                this.btnClose()
                this.$emit('success')
              } else {
                this.ShowTip(result)
              }
              this.btnLoading = false
            })
            .catch(() => {
              this.btnLoading = false
            })
        }
      })
    },
    // 新增渠道
    handleCreateChannel() {
      this.tableData.push({
        // id: new Date().getTime()
      })
    },
    // 删除渠道
    handleDeleteChannel(index) {
      this.tableData.splice(index, 1)
    },
    // 选择产商
    manufacturerChange(row) {
      row.productAndSpecId = []
      row.productId = null
      row.productSpecId = null
      row.productAndSpecList = this.allProductAndSpecList.filter(item => { return item.parentId === row.manufacturerId })
    },
    // 选择产品/规格
    handleProductChange(row) {
      if (row.productAndSpecId !== null && row.productAndSpecId.length > 1) {
        row.productId = row.productAndSpecId[0]
        row.productSpecId = row.productAndSpecId[1]
      }
    }
  }
}
</script>
