<template>
  <div>
    <el-dialog custom-class="el-dialog-s" :title="title" width="60%" append-to-body :close-on-click-modal="false" :visible="showAddDialog" @close="handleCloseProjectDialog">
      <el-form
        ref="dataForm"
        :model="rebateProject"
        :rules="rules"
        label-position="right"
        label-width="100px"
        class="el-dialogform"
      >
        <el-row type="flex" class="filter-container">
          <el-col :span="projectNameSpan">
            <el-form-item label="项目名称" prop="name">
              <el-input
                v-model="rebateProject.name"
                placeholder="项目名称"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="isUpdate" :span="span">
            <el-form-item label="项目编码" prop="code">
              <el-input
                v-model="rebateProject.code"
                disabled
                placeholder="项目编码"
              />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="span">
            <el-form-item label="负责人" prop="principalName">
              <el-row>
                <el-col :span="17" style="padding-right:5px">
                  <el-input
                    v-model="rebateProject.principalName"
                    readonly
                    placeholder="负责人"
                  />
                </el-col>
                <el-col :span="7">
                  <el-button

                    type="primary"
                    icon="el-icon-search"
                    title="选择"
                    @click="handleChoosePrincipal"
                  >
                    选择
                  </el-button>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col> -->
          <el-col :span="span">
            <el-form-item label="起始日期" prop="startTime">
              <el-date-picker
                v-model="rebateProject.startTime"
                type="date"
                style="width: 100%"
                placeholder="起始日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="截止日期" prop="endTime">
              <el-date-picker
                v-model="rebateProject.endTime"
                type="date"
                style="width: 100%"
                placeholder="截止日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="费用类型" prop="enumCostType">
              <el-select
                v-model="rebateProject.enumCostType"
                placeholder="费用类型"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in costTypeList"
                  :key="item.value"
                  :label="item.desc"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="选择部门" prop="checkedDepartments">
              <el-checkbox v-model="checkAllDepartment" :disabled="rebateProject.rebateProjectPhaseList.length>0" :indeterminate="isIndeterminateDepartment" @change="handleCheckAllDepartmentChange">药品事业部</el-checkbox>
              <el-checkbox-group v-model="checkedDepartments" :disabled="rebateProject.rebateProjectPhaseList.length>0" @change="handleCheckedDepartmentChange">
                <el-checkbox v-for="item in deptList" :key="item.key" :label="item.key">{{ item.value }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="产品">
              <el-col :span="3.5" class="el-colRight">

                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  @click="handleAddProduct"
                >
                  选择产品
                </el-button>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="已选产品" prop="productIds">
              <el-tag
                v-for="tag in rebateProject.rebateProjectProductList"
                :key="tag.productSpecId"
                :closable="rebateProject.rebateProjectPhaseList.length == 0"
                :disable-transitions="false"
                @close="handleDeleteProduct(tag)"
              >
                {{ tag.productNameCn+'('+tag.spec+')' }}
              </el-tag>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-row type="flex" justify="end">
        <el-col :span="1" />
        <el-col :span="22">
          <el-divider content-position="right">
            <el-button
              class="filter-item"
              type="primary"
              icon="el-icon-plus"
              @click="handleAddPhase"
            >
              添加实施阶段
            </el-button>
          </el-divider>
        </el-col>
        <el-col :span="1" />
      </el-row>
      <el-row type="flex" justify="end">
        <el-col :span="1" />
        <el-col :span="22">
          <el-tabs v-model="activeName" type="border-card" closable @tab-remove="handleDeletePhase">
            <el-tab-pane v-for="item in rebateProject.rebateProjectPhaseList" :key="item.tempId" :label="`${fomatDate(item.startTime)}至${fomatDate(item.endTime)}`" :name="item.title">
              <el-row :gutter="10">
                <el-col :span="8">
                  <span>预计销售金额：{{ item.totalIncome | toTwoNum }}元</span>
                </el-col>
                <el-col :span="8">
                  <span>预计成本：{{ item.estimateAmount | toTwoNum }}元</span>
                </el-col>
                <el-col :span="8">
                  <span>返点比例：{{ item.rebateRatio }}%</span>
                </el-col>
              </el-row>
              <el-row :gutter="10" style="padding-top: 10px;">
                <el-col :span="8">
                  <span>实际销售金额：{{ item.actualTotalIncome | toTwoNum }}元</span>
                </el-col>
                <el-col :span="8">
                  <span>实际成本：{{ item.actualAmount | toTwoNum }}元</span>
                </el-col>
                <el-col :span="8">
                  <span>返利金额：{{ item.actualTotalInvolvement| toTwoNum }}元</span>
                </el-col>
              </el-row>
              <el-divider />
              <el-row v-for="departmentItem in item.rebateProjectPhaseDepartmentList" :key="departmentItem.departmentId" style="margin-top: 15px;">
                <el-row>
                  <el-col :span="12">
                    <span>部门名称：{{ departmentItem.departmentName }}</span>
                  </el-col>
                  <el-col :span="12">
                    <span>部门投入比例：{{ departmentItem.rebateRatio }}%</span>
                  </el-col>
                </el-row>
                <el-row style="margin-top:10px">
                  <el-col :span="24">
                    <el-table
                      :data="departmentItem.rebateProjectPhaseProductSpecList"
                      stripe
                      border
                      fit
                      highlight-current-row
                      style="width: 100%;"
                      :default-sort="{prop: 'productNameCn', order: 'descending'}"
                      :header-cell-class-name="'tableStyle'"
                      :row-class-name="handleRowClass"
                    >
                      <el-table-column
                        fixed
                        label="序号"
                        type="index"
                        align="center"
                      />  <el-table-column label="产品名称" align="center" min-width="100px">
                        <template slot-scope="{ row }">
                          <span>{{ row.productNameCn }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="规格" align="center" min-width="100px">
                        <template slot-scope="{ row }">
                          <span>{{ row.spec }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="厂商" align="center" min-width="80px">
                        <template slot-scope="{ row }">
                          <span>{{ row.manufacturerName }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="核算单价(元)" align="center" min-width="90px">
                        <template slot-scope="{ row }">
                          <span>{{ row.accountingBase | toTwoNum }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="返点比例(%)" align="center" min-width="90px">
                        <template slot-scope="{ row }">
                          <span>{{ row.rebateRatio }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="返点单价(元)" align="center" min-width="90px">
                        <template slot-scope="{ row }">
                          <span>{{ row.rebatePrice | toTwoNum }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
                        <template slot-scope="{ row }">
                          <i class="el-icon-delete eltablei" title="删除" @click="handleDeleteProjectPhaseProduct(departmentItem,row)" />
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-col>
                </el-row>
              </el-row>
              <el-row type="flex">
                <el-col :span="21" />
                <el-col :span="3" style="text-align: right; padding-top: 10px;">
                  <el-button
                    class="filter-item"
                    type="primary"
                    icon="el-icon-edit-outline"
                    @click="handleEditPhase"
                  >
                    编辑
                  </el-button>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </el-col>
        <el-col :span="1" />
      </el-row>
      <el-row type="flex" justify="end" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleCloseProjectDialog">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleSave"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="el-dialog-ls"
      append-to-body
      title="选择产品"
      :close-on-click-modal="false"
      :visible="dialogSelectProductAndSpecVisible"
      width="60%"
      class="popup-search"
      @close="handleCloseProductDialog"
    >
      <SelectProductAndSpec ref="refSelectProductAndSpec" :show-dialog="dialogSelectProductAndSpecVisible" :selected="rebateProject.rebateProjectProductList" @success="chooseProduct_callback" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleCloseProductDialog">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleChooseProductAndSpec"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="el-dialog-ls"
      append-to-body
      title="选择负责人"
      :close-on-click-modal="false"
      :visible="dialogSelectPrincipalVisible"
      width="60%"
      class="popup-search"
      @close="handleClosePrincipalDialog"
    >
      <SelectPrincipal ref="refSelectPrincipal" :show-dialog="dialogSelectPrincipalVisible" @success="choosePrincipal_callback" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="dialogSelectPrincipalVisible">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <AddProjectPhase ref="refAddProjectPhase" @success="saveProjectPhase_callback" />
  </div>
</template>
<script>
import MasterDataService from '@/api/masterData'
import ProjectService from '@/api/project'
import MaintenanceService from '@/api/maintenance'
import SelectProductAndSpec from './selectProductAndSpec'
import SelectPrincipal from './selectPrincipal'
import AddProjectPhase from './addProjectPhase'
import moment from 'moment'

export default {
  components: {
    SelectProductAndSpec,
    SelectPrincipal,
    AddProjectPhase
  },
  data() {
    const validateDepartment = (rule, value, callback) => {
      if (!this.checkedDepartments.length) {
        callback(new Error('请至少选择一个部门'))
      } else {
        callback()
      }
    }
    return {
      span: 12,
      projectNameSpan: 12,
      title: '',
      showAddDialog: false,
      rebateProject: {
        rebateProjectProductList: [],
        rebateProjectDepartmentList: [],
        rebateProjectPhaseList: []
      },
      costTypeList: [],
      deptList: [],
      checkedDepartments: [],
      dialogSelectProductAndSpecVisible: false,
      dialogSelectPrincipalVisible: false,
      isUpdate: false,
      activeNames: [],
      rules: {
        name: [
          { required: true, message: '请输入项目标题', trigger: 'change' },
          { max: 128, message: '项目标题长度必须小于等于 128 个字符', trigger: 'blur' }
        ],
        checkedDepartments: [
          { type: 'array', required: true, validator: validateDepartment, trigger: 'change' }
        ],
        startTime: [
          { required: true, message: '请选择起始日期', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择截止日期', trigger: 'change' }
        ],
        enumCostType: [
          { required: true, message: '请选择费用类型', trigger: 'change' }
        ],
        rebateRatioPercent: [
          { required: true, message: '请输入返点比例', trigger: 'blur' },
          { pattern: /((^[1-9]\d*))(\.\d{0,2}){0,1}$/, message: '返点比例仅支持正数，最多2位小数' }
        ]
      },
      activeName: '',
      dialogProductPriceVisible: false,
      isIndeterminateDepartment: false,
      checkAllDepartment: []
    }
  },
  methods: {
    initPage(id) {
      this.initDept()
      if (this.$refs['dataForm']) {
        this.$refs.dataForm.clearValidate('checkedDepartments')
      }
      if (id != null) {
        this.title = '编辑项目'
        this.getRebateProject(id)
        this.projectNameSpan = 12
        this.isUpdate = true
      } else {
        this.title = '新增项目'
        this.projectNameSpan = 24
      }
      this.showAddDialog = true
      this.rebateProject.id = id
      this.initCostType()
    },
    // ---------------------------------页面工具初始化数据加载-------------------------------
    initDept() {
      MaintenanceService.QueryDepartmentForProject()
        .then((result) => {
          this.deptList = result.filter(item => item.parentKey !== undefined)
        })
        .catch(() => {

        })
    },
    initCostType() {
      var param = {
        enumType: 'CostType'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.costTypeList = result.data.datas
        })
        .catch((error) => {
          console.log(error)
        })
    },
    getRebateProject(id) {
      ProjectService.GetRebateProject({ id: id })
        .then((result) => {
          this.rebateProject = result.data
          this.checkedDepartments = result.data.rebateProjectDepartmentList
          this.rebateProjectPhaseListFomat()
          const checkedCount = this.checkedDepartments.length
          this.checkAllDepartment = checkedCount === this.deptList.length
          this.isIndeterminateDepartment = checkedCount > 0 && checkedCount < this.deptList.length
        })
        .catch(() => {
        })
    },
    // ---------------------------------产品、负责人、部门-------------------------------
    // 产品
    handleAddProduct() {
      this.dialogSelectProductAndSpecVisible = true
    },
    handleChooseProductAndSpec() {
      this.$refs.refSelectProductAndSpec.handleCheck()
    },
    handleCloseProductDialog() {
      this.dialogSelectProductAndSpecVisible = false
      this.$refs.refSelectProductAndSpec.clear()
    },
    chooseProduct_callback(data) {
      var manufacturerNames = []
      var products = []

      data.forEach(element => {
        if (manufacturerNames.indexOf(element.manufacturerName) === -1) {
          manufacturerNames.push(element.manufacturerName)
        }
        element.productSpecId = element.id
        element.id = null
        products.push(element)
      })
      if (manufacturerNames.length > 1) {
        this.showMessage('只能选择同一厂商的品规', 'error')
        return
      }
      // 已使用的规格
      const useProductSpecIds = []

      // 实施阶段中已使用的规格
      this.rebateProject.rebateProjectPhaseList.forEach(phase => {
        phase.rebateProjectPhaseDepartmentList.forEach(department => {
          department.rebateProjectPhaseProductSpecList.forEach(spec => {
            const findIndex = useProductSpecIds.findIndex(p => p.productSpecId === spec.productSpecId)
            if (findIndex === -1) {
              useProductSpecIds.push(spec.productSpecId)
            }
          })
        })
      })

      let showError = false
      useProductSpecIds.forEach(item => {
        var index = data.findIndex(p => p.productSpecId === item)
        if (index === -1) {
          showError = true
        }
      })
      if (showError) {
        this.showMessage('不能去除已使用的品规', 'error')
        return
      }

      this.rebateProject.rebateProjectProductList = []
      this.rebateProject.rebateProjectProductList = products
      this.handleCloseProductDialog()
    },
    handleDeleteProduct(row) {
      this.$confirm('确定删除此产品？确定后实施阶段中对应的产品将会一并删除。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.rebateProject.rebateProjectProductList.map((item, index) => {
          if (item.productSpecId === row.productSpecId) { this.rebateProject.rebateProjectProductList.splice(index, 1) }
        })
        // 删除实施阶段产品 rebateProjectPhaseProductSpecList
        let phaseEmptyCount = 0
        for (let i = 0; i < this.rebateProject.rebateProjectPhaseList.length; i++) {
          let deptEmptyCount = 0
          for (let j = 0; j < this.rebateProject.rebateProjectPhaseList[i].rebateProjectPhaseDepartmentList.length; j++) {
            const index = this.rebateProject.rebateProjectPhaseList[i].rebateProjectPhaseDepartmentList[j].rebateProjectPhaseProductSpecList.findIndex(f => { f.productSpecId === row.productSpecId })
            this.rebateProject.rebateProjectPhaseList[i].rebateProjectPhaseDepartmentList[j].rebateProjectPhaseProductSpecList.splice(index, 1)
            if (this.rebateProject.rebateProjectPhaseList[i].rebateProjectPhaseDepartmentList[j].rebateProjectPhaseProductSpecList.length === 0) {
              this.rebateProject.rebateProjectPhaseList[i].rebateProjectPhaseDepartmentList[j] = []
              deptEmptyCount++
            }
          }

          if (this.rebateProject.rebateProjectPhaseList[i].rebateProjectPhaseDepartmentList.length === deptEmptyCount) {
            this.rebateProject.rebateProjectPhaseList[i] = []
            phaseEmptyCount++
          }
        }
        if (this.rebateProject.rebateProjectPhaseList.length === phaseEmptyCount) {
          this.rebateProject.rebateProjectPhaseList = []
        }
      }).catch(error => {
        this.listLoading = false
        if (!error.succeed) {
          this.$notice.message('取消删除', 'info')
        }
      })
    },

    // 负责人
    handleChoosePrincipal() {
      this.dialogSelectPrincipalVisible = true
    },
    handleClosePrincipalDialog() {
      this.dialogSelectPrincipalVisible = false
      this.$refs.refSelectPrincipal.clear()
    },
    choosePrincipal_callback(val) {
      this.rebateProject.principalName = val.displayName + '(' + val.jobNo + ')'
      this.rebateProject.principalId = val.id
      this.dialogSelectPrincipalVisible = false
    },
    // 部门
    handleDepartmentTopChecked(e, item) {
      if (e) {
        this.departmentDisabled = true
      } else {
        if (this.rebateProject.rebateProjectPhaseList.length > 0) {
          this.$confirm('确定删除此部门？确定后实施阶段会全部删除。', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            // 删除实施阶段
            this.rebateProject.rebateProjectPhaseList = []
          }).catch(error => {
            this.listLoading = false
            if (!error.succeed) {
              this.$notice.message('取消删除', 'info')
              this.$nextTick(() => {
                this.checkedDepartments.push(item.key)
              })
            }
          })
        }
      }

      if (this.checkedDepartments.length === 0) {
        this.departmentDisabled = false
      }
    },

    // ---------------------------------实施阶段-------------------------------
    handleAddPhase() {
      if (this.checkedDepartments.length === 0) {
        this.showMessage('请至少选择一个部门', 'error')
        return
      }
      if (this.rebateProject.rebateProjectProductList.length === 0) {
        this.showMessage('请选择产品', 'error')
        return
      }

      var rebateProjectPhaseDepartmentList = []
      var departmentList = []
      var productList = []
      var allDeptList = this.deptList
      // 通过选择的部门集合生成子控件数据
      this.checkedDepartments.forEach(element => {
        allDeptList.find((item, index) => {
          if (item.key === element) {
            departmentList.push(item)
          }
        })
        // 部门下产品集合
        productList = JSON.parse(JSON.stringify(this.rebateProject.rebateProjectProductList))
      })
      var rebateProjectPhaseModel = {
        isNew: true, // 子控件中区别新增还是编辑
        estimateAmount: 0,
        actualAmount: 0,
        actualTotalIncome: 0,
        actualTotalInvolvement: 0,
        tempId: null,
        rebateProjectPhaseCostList: []
      }
      rebateProjectPhaseModel.rebateProjectPhaseDepartmentList = rebateProjectPhaseDepartmentList
      rebateProjectPhaseModel.departmentList = departmentList
      rebateProjectPhaseModel.productList = productList
      this.$refs.refAddProjectPhase.init(rebateProjectPhaseModel)
    },
    handleEditPhase() {
      const data = this.rebateProject.rebateProjectPhaseList.find(v => v.title === this.activeName)

      var copyData = JSON.parse(JSON.stringify(data))

      var departmentList = []
      var productList = []

      var allDeptList = this.deptList
      // 通过选择的部门集合生成子控件数据
      this.checkedDepartments.forEach(element => {
        allDeptList.find((item, index) => {
          if (item.key === element) {
            departmentList.push(item)
          }
        })
      })

      // 部门下产品集合
      productList = JSON.parse(JSON.stringify(this.rebateProject.rebateProjectProductList))

      copyData.departmentList = departmentList
      copyData.productList = productList

      this.$refs.refAddProjectPhase.init(copyData)
    },
    handleDeletePhase(itemTitle) {
      this.$confirm('确定删除此实施阶段吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.rebateProject.rebateProjectPhaseList.map((item, index) => {
          if (item.title === itemTitle) { this.rebateProject.rebateProjectPhaseList.splice(index, 1) }
        })
        this.rebateProjectPhaseListFomat()
      }).catch(error => {
        if (!error.succeed) {
          this.$notice.message('取消删除', 'info')
        }
      })
    },
    saveProjectPhase_callback(data) {
      // 新增和编辑时，进行集合唯一标识
      if (data.tempId === null) { data.tempId = Math.floor(Math.random() * 36) }
      this.rebateProject.rebateProjectPhaseList.map((item, index) => {
        if (item.tempId === data.tempId) { this.rebateProject.rebateProjectPhaseList.splice(index, 1) }
      })
      this.rebateProject.rebateProjectPhaseList.push(data)
      // 集合排序
      this.arraySort(this.rebateProject.rebateProjectPhaseList, 'startTime')
      this.rebateProjectPhaseListFomat()
    },
    // 实施阶段数据处理
    rebateProjectPhaseListFomat() {
      // 实施阶段只有最后一个显示删除属性处理
      for (let index = 0; index < this.rebateProject.rebateProjectPhaseList.length; index++) {
        this.activeNames.push(this.rebateProject.rebateProjectPhaseList[index].tempId)
        this.rebateProject.rebateProjectPhaseList[index].title = '实施阶段' + (index + 1)

        if (index === 0) {
          this.activeName = this.rebateProject.rebateProjectPhaseList[index].title
        }

        if (index === this.rebateProject.rebateProjectPhaseList.length - 1) {
          this.rebateProject.rebateProjectPhaseList[index].isShowDelete = true
        } else {
          this.rebateProject.rebateProjectPhaseList[index].isShowDelete = false
        }
      }
    },
    // 删除实施阶段产品
    handleDeleteProjectPhaseProduct(item, row) {
      const index = item.rebateProjectPhaseProductSpecList.indexOf(row)
      if (index !== -1) {
        item.rebateProjectPhaseProductSpecList.splice(index, 1)
      }
    },
    // ---------------------------------页面数据提交、关闭-------------------------------
    handleSave() {
      // 保存
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.rebateProject.endTime < this.rebateProject.startTime) {
            this.showMessage('起始日期不能小于截止日期', 'error')
            return
          }
          if (this.rebateProject.rebateProjectProductList.length === 0) {
            this.showMessage('请选择产品', 'error')
            return
          }
          if (this.rebateProject.rebateProjectPhaseList.length === 0) {
            this.showMessage('请添加实施阶段', 'error')
            return
          }
          // 判断实施阶段周期是否在项目周期范围，实施周期经过排序，取最小起始日期和最大截止日期比较
          var minStartTime = this.rebateProject.rebateProjectPhaseList[0].startTime
          var maxEndTime = this.rebateProject.rebateProjectPhaseList[this.rebateProject.rebateProjectPhaseList.length - 1].endTime
          if (minStartTime < this.rebateProject.startTime || maxEndTime > this.rebateProject.endTime) {
            this.showMessage('实施阶段日期必须在项目周期范围内', 'error')
            return
          }
          // 多个实施阶段，验证是否日期连贯
          if (this.rebateProject.rebateProjectPhaseList.length > 1) {
            for (let index = 0; index < this.rebateProject.rebateProjectPhaseList.length - 1; index++) {
              const current = this.rebateProject.rebateProjectPhaseList[index]
              const next = this.rebateProject.rebateProjectPhaseList[index + 1]
              if (this.datedifference(current.endTime, next.startTime) !== 1) {
                this.showMessage('下一个实施阶段日期必须与上一阶段日期相接', 'error')
                return
              }
            }
          }
          this.rebateProject.rebateProjectDepartmentList = this.checkedDepartments
          if (this.isUpdate) {
            ProjectService.UpdateRebateProject(this.rebateProject)
              .then((result) => {
                if (result.succeed) {
                  this.handleCloseProjectDialog()
                  this.$emit('success')
                  this.showMessage('保存成功', 'success')
                } else {
                  this.ShowTip(result)
                }
              })
              .catch(() => {
              })
          } else {
            ProjectService.AddRebateProject(this.rebateProject)
              .then((result) => {
                if (result.succeed) {
                  this.handleCloseProjectDialog()
                  this.$emit('success')
                  this.showMessage('新增成功', 'success')
                } else {
                  this.ShowTip(result)
                }
              })
              .catch(() => {
              })
          }
        }
      })
    },
    handleCloseProjectDialog() {
      this.clear()
      this.showAddDialog = false
    },
    clear() {
      this.isUpdate = false
      this.rebateProject = {
        rebateProjectProductList: [],
        rebateProjectDepartmentList: [],
        rebateProjectPhaseList: []
      }
      this.checkedDepartments = []
      this.checkAllDepartment = []
      this.$refs.dataForm.clearValidate()
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
    },
    // ---------------------------------页面工具类方法-------------------------------
    // 集合排序
    arraySort(array, key) {
      return array.sort((a, b) => {
        var x = a[key]
        var y = b[key]
        return ((x < y) ? -1 : (x > y) ? 1 : 0)
      })
    },
    // 格式化日期
    fomatDate(date) {
      return moment(date).format('YYYY-MM-DD')
    },
    // 日期比较，相差天数
    datedifference(beginTime, endTime) {
      return moment(endTime).diff(moment(beginTime), 'days')
    },
    // 消息提示
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleCheckAllDepartmentChange(val) {
      const departmentList = []
      this.deptList.forEach(e => {
        departmentList.push(e.key)
      })

      this.checkedDepartments = val ? departmentList : []
      this.isIndeterminateDepartment = false
    },
    handleCheckedDepartmentChange(value) {
      var departmentList = []
      value.forEach(e => {
        const pro = {
          id: e
        }
        departmentList.push(pro)
      })
      const checkedCount = value.length
      this.checkAllDepartment = checkedCount === this.deptList.length
      this.isIndeterminateDepartment = checkedCount > 0 && checkedCount < this.deptList.length
    }
  }
}
</script>
<style scoped>
.el-collapse-item ::v-deep .el-collapse-item__header {
  font-size: 14px !important;
  font-weight: 600 !important;
}

::v-deep  .el-tabs__new-tab {
  width: 28px !important;
}
</style>
