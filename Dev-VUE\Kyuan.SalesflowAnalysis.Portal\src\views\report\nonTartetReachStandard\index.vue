<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex" style="margin-bottom:-20px;">
        <el-col :span="span">
          <quarter-select ref="qs" :get-value="getQuarter" :default-value="defaultQuarter" />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="filter.receiverName"
            clearable
            placeholder="终端名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="filter.manufacturerId"
            :loading="manufacturerLoading"
            class="filter-item"
            placeholder="厂商名称"
            clearable
            @change="manufacturerChange"
          >
            <el-option
              v-for="item in manufacturerList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProductAndSpec"
            :key="productAndSpecKey"
            v-model="productAndSpecId"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            placeholder="产品/规格"
            class="filter-item"
            clearable
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            @change="productChange"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'NonTartetReachStandard_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'NonTartetReachStandard_Button_Query')"
            :loading="btnExportLoading"
            class="filter-item-button"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              :index="indexMethod"
              type="index"
              align="center"
            />
            <el-table-column
              sortable="custom"
              prop="quarter"
              label="季度"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.quarter | toQuarter }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Receiver.Name"
              label="终端名称"
              min-width="250px"
              header-align="center"
              align="left"
            >
              <template slot-scope="{ row }">
                <span>{{ row.receiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="ProductSpec.Product.Manufacturer.Name"
              label="厂商名称"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.manufacturerName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="ProductSpec.Product.NameEn"
              label="产品名称"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="ProductSpec.Spec"
              label="包装规格"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productSpec }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="filter.pageIndex"
            :limit.sync="filter.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出报表"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import CustomExport from '@/components/Export/CustomExport'
import ReportService from '@/api/report'
import ManufacturerService from '@/api/manufacturer'
import ProductService from '@/api/product'
import CycleService from '@/api/cycle'
import QuarterSelect from './components/quarterSelect'

export default {
  name: 'Location',
  components: {
    Pagination,
    QuarterSelect,
    CustomExport
  },
  data() {
    return {
      span: 4,
      manufacturerLoading: false,
      productAndSpecLoading: false,
      manufacturerList: [],
      productAndSpecList: null,
      productAndSpecId: [],
      productAndSpecKey: 0,
      list: [],
      total: 0,
      listLoading: false,
      defaultQuarter: '',
      filter: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: {}
    }
  },
  created() {
    this.initManufacturer()
    this.initProductAndSpec()
    this.GetTheLatestProcessedCycle()
  },
  mounted() {

  },
  methods: {
    getQuarter(quarter) {
      this.filter.quarter = quarter
    },

    GetTheLatestProcessedCycle() {
      this.manufacturerLoading = true
      CycleService.GetTheLatestProcessedCycle()
        .then((result) => {
          this.manufacturerLoading = false
          this.filter.quarter = this.defaultQuarter = result.data.quarter.toString()
          this.getList()
        })
        .catch((error) => {
          this.manufacturerLoading = false
          console.log(error)
        })
    },

    initManufacturer() {
      this.manufacturerLoading = true
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturerLoading = false
          this.manufacturerList = result
        })
        .catch((error) => {
          this.manufacturerLoading = false
          console.log(error)
        })
    },

    initProductAndSpec() {
      this.productAndSpecLoading = true
      const para = { manufacturerId: this.filter.manufacturerId }
      ProductService.QueryProductAndSpecCascader(para)
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },

    manufacturerChange() {
      this.productAndSpecId = []
      ++this.productAndSpecKey
      this.initProductAndSpec()
    },

    productChange() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    },

    indexMethod(index) {
      return (this.filter.pageIndex - 1) * this.filter.pageSize + index + 1
    },

    getList() {
      if (this.productAndSpecId) {
        const [productId, productSpecId] = this.productAndSpecId
        this.filter.productId = productId
        this.filter.productSpecId = productSpecId
      }

      this.listLoading = true

      ReportService.QueryNonTargetReachStandard(this.filter)
        .then((result) => {
          this.listLoading = false

          this.list = result.data.datas
          this.total = result.data.recordCount
          this.filter.pageIndex = result.data.pageIndex
        })
        .catch((error) => {
          console.log(error)
          this.listLoading = false
        })
    },

    handleFilter() {
      this.filter.pageIndex = 1
      this.getList()
    },

    sortChange(column, prop, order) {
      this.filter.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.filter.order = orderSymbol + column.prop
      this.getList()
    },

    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    // 显示导出model
    onShowExportModal() {
      if (!this.filter.quarter) {
        this.$notice.message('请选择季度', 'error')
        return
      }

      this.btnExportLoading = true
      ReportService.GetNonTargetReachStandardColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      var exportParam = JSON.parse(JSON.stringify(this.filter))
      exportParam.checkedColumns = checkColumns
      ReportService.ExportNonTartetReachStandard(exportParam)
        .then(result => {
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '非目标终端达成报表.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '非目标终端达成报表.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    }
  }
}
</script>
