<template>
  <el-dialog
    title="配置导入导出的代码实现"
    width="80%"
    :close-on-click-modal="false"
    :visible="showDialog"
    @close="closeDialog()"
  >
    <el-form
      ref="dataForm"
      label-position="right"
      label-width="90px"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="基础数据导出">
            <el-table
              :data="variableMappings"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%;"
              :default-sort="{prop: 'createTime', order: 'descending'}"
              :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass"
            >
              <el-table-column
                prop="Name"
                label="Sheet名称"
                header-align="center"
                align="center"
                min-width="120px"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="代码实现" min-width="120px" align="center">
                <template slot-scope="{ row }">
                  <el-select
                    v-model="row.bonusVariableId"
                    style="width: 100%"
                    class="filter-item"
                    placeholder="代码实现"
                    clearable
                    filterable
                    :disabled="row.enumBonusMappingStatus !== 1"
                    @change="variableChange(row.bonusVariableId,row)"
                  >
                    <el-option
                      v-for="item in variables"
                      :key="item.id"
                      :label="item.title"
                      :value="item.id"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="代码说明" min-width="200px" align="left" header-align="center">
                <template slot-scope="{ row }">
                  {{ row.bonusVariableRemark }}
                </template>
              </el-table-column>
              <el-table-column label="状态" min-width="90px" align="center">
                <template slot-scope="{ row }">
                  <label>{{ row.enumBonusMappingStatusDesc }}</label>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" header-align="center" width="70" class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <i v-if="row.enumBonusMappingStatus !== 1" class="el-icon-delete eltablei" title="重新配置" @click="handleResetVariable(row)" />
                  <i v-if="row.enumBonusMappingStatus !== 1" class="el-icon-tickets eltablei" title="配置明细" @click="handleConfigure(row)" />
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="计算结果导入">
            <el-select
              v-model="bonusResultVariableId"
              style="width: 100%"
              class="filter-item"
              placeholder="代码实现"
              :disabled="bonusTemplateStatus !== 1 || (bonusTemplateStatus === 1 && bonusResultVariableDisabled)"
              clearable
              filterable
              @change="bonusResultVariableChange"
            >
              <el-option
                v-for="item in resultVariables"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-table
              :data="resultEntityMappings"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%;"
              :default-sort="{prop: 'createTime', order: 'descending'}"
              :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass"
            >
              <el-table-column
                prop="SheetName"
                label="Sheet名称"
                header-align="center"
                align="center"
                min-width="150px"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.sheetName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="代码实现" min-width="150px" align="center">
                <template slot-scope="{ row }">
                  <el-select
                    v-model="row.bonusResultEntityId"
                    style="width: 100%"
                    class="filter-item"
                    clearable
                    :disabled="row.enumBonusMappingStatus !== 1"
                    placeholder="代码实现"
                  >
                    <el-option
                      v-for="item in resultEntity"
                      :key="item.key"
                      :label="item.value"
                      :value="item.key"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="状态" min-width="100px" align="center">
                <template slot-scope="{ row }">
                  <label>{{ row.enumBonusMappingStatusDesc }}</label>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <i v-if="row.enumBonusMappingStatus !== 1" title="重新配置" class="el-icon-delete eltablei" @click="handleResetResultEntity(row)" />
                  <i v-if="row.enumBonusMappingStatus !== 1" class="el-icon-tickets eltablei" title="配置" @click="handleConfigureResult(row)" />
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="isShowSave" type="primary" icon="el-icon-check" @click="handleSaveAll">
        全部保存
      </el-button>
      <el-button v-if="bonusTemplateStatus !== 1" icon="el-icon-refresh" @click="handleResetAll">
        全部重置
      </el-button>
      <el-button icon="el-icon-close" @click="closeDialog">
        关闭
      </el-button>
      <ConfigureVariableDetail ref="configureVariableDetail" @success="configureSuccess" />
      <ConfigureResultVariableDetail ref="configureResultVariableDetail" @success="configureResultSuccess" />
    </div>
  </el-dialog>
</template>
<script>
import bonusService from '@/api/bonus'
import ConfigureVariableDetail from './configureVariableDetail'
import ConfigureResultVariableDetail from './configureResultVariableDetail'

export default {
  components: {
    ConfigureVariableDetail,
    ConfigureResultVariableDetail
  },
  data() {
    return {
      span: 12,
      showDialog: false,
      variableMappings: [],
      variables: [],
      resultEntityMappings: [],
      resultVariables: [],
      resultEntity: [],
      bonusResultVariableId: undefined,
      bonusTemplateStatus: 0,
      bonusResultVariableDisabled: false,
      bonusTemplateId: undefined,
      isShowSave: false
    }
  },
  methods: {
    initPage(row) {
      this.isShowSave = false
      this.bonusTemplateId = row.id
      this.showDialog = true
      if (row.bonusResultVariableId != null || row.bonusResultVariableId !== undefined) {
        this.bonusResultVariableId = row.bonusResultVariableId
        this.bonusResultVariableDisabled = true
        this.initResultEntity(this.bonusResultVariableId)
      }

      this.bonusTemplateStatus = row.enumStatus
      this.initVariableMappings()
      this.initVariables()
      this.initResultEntityMappings()
      this.initResultVariables()
    },
    initVariableMappings() {
      bonusService.GetBonusVariableMapping({ bonusTemplateId: this.bonusTemplateId })
        .then((result) => {
          this.variableMappings = result
          var index = this.variableMappings.findIndex(item => item.enumBonusMappingStatus === 1)
          if (index >= 0) {
            this.isShowSave = true
          }
        })
        .catch(() => {
        })
    },
    initVariables() {
      bonusService.QueryBonusVariable()
        .then((result) => {
          this.variables = result
        })
        .catch(() => {
        })
    },
    initResultEntityMappings() {
      bonusService.GetBonusResultEntityMapping({ bonusTemplateId: this.bonusTemplateId })
        .then((result) => {
          this.resultEntityMappings = result
          var index = this.resultEntityMappings.findIndex(item => item.enumBonusMappingStatus === 1)
          if (index >= 0) {
            this.isShowSave = true
          }
        })
        .catch(() => {
        })
    },
    initResultVariables() {
      bonusService.QueryBonusResultVariableSelect()
        .then((result) => {
          this.resultVariables = result
        })
        .catch(() => {
        })
    },
    initResultEntity(bonusResultVariableId) {
      bonusService.QueryBonusResultEntitySelect({ bonusResultVariableId: bonusResultVariableId })
        .then((result) => {
          this.resultEntity = result
        })
        .catch(() => {
        })
    },
    variableChange(selectId, row) {
      let variable = {}
      row.bonusVariableRemark = undefined
      if (selectId !== '') {
        variable = this.variables.find((item) => {
          return selectId === item.id
        })

        row.bonusVariableRemark = variable.remark
      }
    },
    handleResetVariable(row) {
      this.$confirm('确定要重置此实现吗?点击确定后明细会一同重置！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const tempRow = Object.assign({}, row)
          bonusService.ResetBonusVariableMapping(tempRow)
            .then((result) => {
              if (result.succeed) {
                row.enumBonusMappingStatus = result.data.enumBonusMappingStatus
                row.enumBonusMappingStatusDesc = result.data.enumBonusMappingStatusDesc
                row.bonusVariableId = undefined
                row.bonusVariableRemark = undefined
                this.isShowSave = true
                this.$notice.message('重置成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
              this.loading = false
            })
        })
        .catch((error) => {
          if (!error.succeed) {
            this.$notice.message('取消重置', 'info')
          }
        })
    },

    handleResetResultEntity(row) {
      this.$confirm('确定要重置此实现吗?点击确定后明细会一同重置！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const tempRow = Object.assign({}, row)
          bonusService.ResetBonusResultEntityMapping(tempRow)
            .then((result) => {
              if (result.succeed) {
                row.enumBonusMappingStatus = result.data.enumBonusMappingStatus
                row.enumBonusMappingStatusDesc = result.data.enumBonusMappingStatusDesc
                row.bonusResultEntityId = undefined
                this.isShowSave = true
                this.$notice.message('重置成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
              this.loading = false
            })
        })
        .catch((error) => {
          if (!error.succeed) {
            this.$notice.message('取消重置', 'info')
          }
        })
    },
    handleResetAll() {
      this.$confirm('确定要全部重置吗?点击确定后明细会一同重置！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          bonusService.ResetBonusAllMapping({ id: this.bonusTemplateId })
            .then((result) => {
              if (result.succeed) {
                this.bonusTemplateStatus = 1
                this.bonusResultVariableDisabled = false
                this.bonusResultVariableId = undefined
                this.variableMappings = []
                this.resultEntityMappings = []
                this.resultEntity = []
                this.initVariableMappings()
                this.initResultEntityMappings()
                this.isShowSave = true
                this.$notice.message('全部重置成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
              this.loading = false
            })
        })
        .catch((error) => {
          if (!error.succeed) {
            this.$notice.message('取消全部重置', 'info')
          }
        })
    },
    handleSaveAll() {
      if (this.variableMappings === null || this.variableMappings.length === 0) {
        this.$notice.message('基础数据模板无数据，请检查上传模板是否正确')
        return false
      }
      if (this.resultEntityMappings === null || this.resultEntityMappings.length === 0) {
        this.$notice.message('计算结果模板无数据，请检查上传模板是否正确')
        return false
      }
      const variableMapping = this.variableMappings.find((item) => { return item.bonusVariableId === undefined || item.bonusVariableId === '' })
      if (variableMapping !== undefined && variableMapping !== null && variableMapping !== '') {
        this.$notice.message(`请选择基础数据导出【${variableMapping.name}】的代码实现`, 'error')
        return false
      }
      if (this.bonusResultVariableId === null || this.bonusResultVariableId === undefined || this.bonusResultVariableId === '') {
        this.$notice.message('请选择计算结果导入代码实现', 'error')
        return false
      }

      const resultEntityMapping = this.resultEntityMappings.find((item) => { return item.bonusResultEntityId === undefined || item.bonusResultEntityId === '' })
      if (resultEntityMapping !== undefined && resultEntityMapping !== null) {
        this.$notice.message(`请选择计算结果导入【${resultEntityMapping.sheetName}】的代码实现`, 'error')
        return false
      }

      const model = {
        bonusResultVariableId: this.bonusResultVariableId,
        variableMappingList: this.variableMappings,
        resultEntityMappingList: this.resultEntityMappings
      }
      bonusService.SaveBonusVariableAndResultMapping(model)
        .then(result => {
          this.showDialog = false
          if (result.succeed) {
            this.variableMappings = []
            this.resultEntityMappings = []
            this.bonusResultVariableId = undefined
            this.resultEntity = []
            this.$notice.message('全部保存成功', 'success')
            this.$emit('success')
          }
        })
        .catch(error => {
          this.showDialog = false
          console.log(error)
        })
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    closeDialog() {
      this.bonusResultVariableDisabled = false
      this.bonusResultVariableId = undefined
      this.variableMappings = []
      this.resultEntityMappings = []
      this.resultEntity = []
      this.showDialog = false
      this.$emit('success')
    },
    handleConfigure(row) {
      this.$refs.configureVariableDetail.initPage(row)
    },
    configureSuccess() {
      this.initVariableMappings()
    },
    handleConfigureResult(row) {
      this.$refs.configureResultVariableDetail.initPage(row)
    },
    configureResultSuccess() {
      this.initResultEntityMappings()
    },
    bonusResultVariableChange() {
      this.initResultEntity(this.bonusResultVariableId)
    }
  }
}
</script>
<style scoped>
  .el-dialog-s {
  z-index: 12;
  }
  .el-dialog-ls {
  z-index: 13;
  }
</style>
