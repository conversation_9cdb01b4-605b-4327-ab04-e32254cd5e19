/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    permissions: ['admin','editor']    control the page permissions (you can set multiple permissions)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

import Layout from '@/layout'

const projectRoutes = [
  {
    path: '/project',
    component: Layout,
    redirect: '/project/index',
    hidden: false,
    meta: {
      title: '项目管理',
      sort: 30,
      icon: 'money'
    },
    children: [
      {
        name: 'ProjectQuery',
        path: 'projectQuery',
        component: () => import('@/views/project/projectQuery/index'),
        meta: {
          title: '项目管理',
          icon: 'predict',
          sort: 1,
          noCache: false,
          permissions: ['Agreement_ProjectQuery']
        }
      }
    ]
  }
]

export default projectRoutes
