<template>
  <div>
    <el-dialog custom-class="el-dialog-s" :title="title" width="60%" append-to-body :close-on-click-modal="false" :visible="editRebateProjectPhaseVisible" @close="handleCancle()">
      <el-form ref="dataForm" style="width:88%" :rules="rules" :model="rebateProjectPhaseModel" label-position="right" label-width="100px" class="el-dialogform">
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="起始日期" prop="startTime">
              <el-date-picker
                v-model="rebateProjectPhaseModel.startTime"
                type="date"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                placeholder="起始日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="截止日期" prop="endTime">
              <el-date-picker
                v-model="rebateProjectPhaseModel.endTime"
                type="date"
                style="width: 100%"
                value-format="yyyy-MM-dd"
                placeholder="截止日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="预计销售金额" prop="totalIncome">
              <el-input
                v-model="rebateProjectPhaseModel.totalIncome"
                clearable
                placeholder="预计销售金额"
              >
                <i slot="suffix">元</i>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="预计成本" prop="estimateAmount">
              <el-row>
                <el-col :span="17">
                  <el-input
                    v-model="rebateProjectPhaseModel.estimateAmount"
                    readonly
                    placeholder="预计成本"
                    maxlength="300"
                  >
                    <i slot="suffix">元</i>
                  </el-input>
                </el-col>
                <el-col :span="7" style="text-align: right;">
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    @click="handleSelectCost(1)"
                  >
                    编辑
                  </el-button>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="返点比例" prop="rebateRatio">
              <el-input
                v-model="rebateProjectPhaseModel.rebateRatio"
                placeholder="返点比例"
              >    <i slot="suffix" class="el-input__icon ">%</i>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="实际成本" prop="actualAmount">
              <el-row>
                <el-col :span="17">
                  <el-input
                    v-model="rebateProjectPhaseModel.actualAmount"
                    placeholder="实际成本"
                    maxlength="300"
                  >
                    <i slot="suffix">元</i>
                  </el-input>
                </el-col>
                <el-col :span="7" style="text-align: right;">
                  <el-button

                    type="primary"
                    icon="el-icon-plus"
                    @click="handleSelectCost(2)"
                  >
                    编辑
                  </el-button>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="实际销售金额" prop="actualTotalIncome">
              <el-input
                v-model="rebateProjectPhaseModel.actualTotalIncome"
                readonly
                disabled
                placeholder="实际销售金额"
              >
                <i slot="suffix">元</i>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="返利金额" prop="actualTotalInvolvement">
              <el-input
                v-model="rebateProjectPhaseModel.actualTotalInvolvement"
                readonly
                disabled
                placeholder="返利金额"
              >
                <i slot="suffix">元</i>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider />
        <el-row type="flex" justify="end">
          <el-col>
            <el-button
              class="filter-item"
              type="primary"
              icon="el-icon-plus"
              @click="handleAddDepartmentProducts"
            >
              添加部门
            </el-button>
          </el-col>
        </el-row>
        <el-row v-for="item in rebateProjectPhaseModel.rebateProjectPhaseDepartmentList" :key="item.key">
          <el-col :span="24" class="el-colRight" style="margin: 20px 0 5px 0;">
            <el-button
              class="filter-item"
              icon="el-icon-delete"
              @click="handleDeleteDepartment(item)"
            >删除部门
            </el-button>
          </el-col>
          <el-col :span="span">
            <el-form-item label="部门名称：" prop="departmentName">
              <el-select
                v-model="item.departmentName"
                placeholder="部门名称"
                style="width: 100%"
                clearable
                @change="((val)=>{selectUpdate(val, item)})"
              >
                <el-option
                  v-for="dept in rebateProjectPhaseModel.departmentList"
                  :key="dept.key"
                  :label="dept.value"
                  :value="dept"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="部门投入比例：">
              <el-input
                v-model="item.rebateRatio"
                placeholder="部门投入比例(%)"
              >    <i slot="suffix" class="el-input__icon ">%</i>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="el-colRight">
            <el-button
              class="filter-item"
              icon="el-icon-plus"
              @click="handleAddProduct(item)"
            >
              添加产品
            </el-button>
          </el-col>
          <el-col :span="24">
            <el-table
              :data="item.rebateProjectPhaseProductSpecList"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%;"
              :header-cell-class-name="'tableStyle'"
            >
              <el-table-column
                fixed
                label="序号"
                type="index"
                align="center"
              />
              <el-table-column label="产品名称" align="center" min-width="100px" prop="productNameCn">
                <template slot-scope="{ row }">
                  <span>{{ row.productNameCn }}</span>
                </template>
              </el-table-column>
              <el-table-column label="规格" align="center" min-width="70px">
                <template slot-scope="{ row }">
                  <span>{{ row.spec }}</span>
                </template>
              </el-table-column>
              <el-table-column label="厂商" align="center" min-width="100px">
                <template slot-scope="{ row }">
                  <span>{{ row.manufacturerName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="核算单价(元)" align="center" min-width="90px">
                <template slot-scope="{ row,$index }">
                  <el-input
                    v-model="row.accountingBase"
                    min="0"
                    :class="row.accountingBase_error?'error-border':''"
                    placeholder="核算单价(元)"
                    size="mini"
                    @input="productItemChange(row,$index)"
                  />
                </template>
              </el-table-column>
              <el-table-column label="返点比例(%)" align="center" min-width="110px">
                <template slot-scope="{ row }">
                  <el-input
                    v-model="row.rebateRatio"
                    min="0"
                    :class="row.rebateRatio_error?'error-border':''"
                    placeholder="返点比例"
                    size="mini"
                    @input="productItemChange(row)"
                  />
                </template>
              </el-table-column>
              <el-table-column label="返点单价(元)" align="center" min-width="110px">
                <template slot-scope="{ row }">
                  <span>{{ row.rebatePrice }}</span>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <i class="el-icon-delete eltablei" title="删除" @click="handleDeleteProduct(item,row)" />
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleCancle()">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleSave()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="el-dialog-ls"
      append-to-body
      title="选择产品"
      :close-on-click-modal="false"
      :visible="dialogSelectProductAndSpecPartVisible"
      width="60%"
      class="popup-search"
      @close="handleCloseProductDialog"
    >
      <SelectProductAndSpecPart ref="refSelectProductAndSpecPart" :show-dialog="dialogSelectProductAndSpecPartVisible" :list-data="rebateProjectPhaseModel.productList" :selected="selectRebateProjectPhaseDepartment.chooseProductSpecList" @success="chooseProduct_callback" />
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleChooseProductAndSpec"
        >
          确认
        </el-button>
        <el-button icon="el-icon-close" @click="handleCloseProductDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <AddProjectPhaseCost ref="refAddProjectPhaseCost" @success="chooseProjectPhaseCost_callback" />
  </div>
</template>
<script>
import SelectProductAndSpecPart from './selectProductAndSpecPart'
import AddProjectPhaseCost from './addProjectPhaseCost'
export default {
  name: '',
  components: {
    SelectProductAndSpecPart,
    AddProjectPhaseCost
  },
  props: {
  },
  data() {
    return {
      editRebateProjectPhaseVisible: false,
      span: 12,
      rules: {
        startTime: [
          { required: true, message: '请选择起始日期', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择截止日期', trigger: 'change' }
        ],
        totalIncome: [
          { required: true, message: '请输入预计销售金额', trigger: 'blur' },
          { pattern: /((^[1-9]\d*))(\.\d{0,2}){0,1}$/, message: '预计销售金额仅支持正数，最多2位小数' }
        ],
        rebateRatio: [
          { required: true, message: '请输入返点比例', trigger: 'blur' },
          { pattern: /((^[1-9]\d*))(\.\d{0,2}){0,1}$/, message: '返点比例仅支持正数，最多2位小数' }
        ]
      },
      rebateProjectPhaseModel: {
        tempId: null,
        estimateAmount: 0,
        actualAmount: 0,
        actualTotalIncome: 0,
        actualTotalInvolvement: 0,
        rebateProjectPhaseCostList: [],
        rebateProjectPhaseDepartmentList: []
      },
      title: '新增实施阶段',
      departmentProducts: [],
      dialogSelectProductAndSpecPartVisible: false,
      selectRebateProjectPhaseDepartment: {
        rebateProjectPhaseProductSpecList: [],
        chooseProductSpecList: []
      },
      departmentName: ''
    }
  },
  created() {
  },
  methods: {
    init(data) {
      if (data.isNew) {
        this.title = '新增实施阶段'
      } else {
        this.title = '编辑实施阶段'
      }
      this.rebateProjectPhaseModel = data
      this.editRebateProjectPhaseVisible = true
    },
    handleSave() {
      if (this.rebateProjectPhaseModel.rebateProjectPhaseDepartmentList && this.rebateProjectPhaseModel.rebateProjectPhaseDepartmentList.some(s1 => { return s1.rebateProjectPhaseProductSpecList && s1.rebateProjectPhaseProductSpecList.some(s2 => { return s2.accountingBase_error || s2.rebateRatio_error }) })) {
        return false
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.rebateProjectPhaseModel.endTime < this.rebateProjectPhaseModel.startTime) {
            this.showMessage('起始日期不能小于截止日期', 'error')
            return
          }
          for (let index = 0; index < this.rebateProjectPhaseModel.rebateProjectPhaseDepartmentList.length; index++) {
            const element = this.rebateProjectPhaseModel.rebateProjectPhaseDepartmentList[index]
            if (element.departmentId === null || element.departmentId === '') {
              this.showMessage('请选择部门', 'error')
              return
            }
            if (element.rebateRatio === null || element.rebateRatio === '') {
              this.showMessage('请填写部门投入比例', 'error')
              return
            }
            const reg = /((^[1-9]\d*))(\.\d{0,2}){0,1}$/
            if ((!reg.test(element.rebateRatio))) {
              this.showMessage('部门投入比例仅支持正数，最多2位小数', 'error')
              return
            }
            if (parseFloat(element.rebateRatio) > parseFloat(this.rebateProjectPhaseModel.rebateRatio)) {
              this.showMessage('部门投入比例不能高于实施阶段返点比例', 'error')
              return
            }
            if (element.rebateProjectPhaseProductSpecList === null || element.rebateProjectPhaseProductSpecList === undefined || element.rebateProjectPhaseProductSpecList.length === 0) {
              this.showMessage('请添加部门下产品信息', 'error')
              return
            }
            for (let specIndex = 0; specIndex < element.rebateProjectPhaseProductSpecList.length; specIndex++) {
              const product = element.rebateProjectPhaseProductSpecList[specIndex]
              if (product.accountingBase === null || product.accountingBase === '' || product.accountingBase === undefined) {
                this.showMessage('请完整填写核算单价', 'error')
                return
              }
              if (product.rebateRatio === null || product.rebateRatio === '' || product.rebateRatio === undefined) {
                this.showMessage('请完整填写产品返点比例', 'error')
                return
              }
              if (parseFloat(product.rebateRatio) > parseFloat(element.rebateRatio)) {
                this.showMessage('产品返点比例不能高于部门投入比例', 'error')
                return
              }
            }
          }

          this.$emit('success', JSON.parse(JSON.stringify(this.rebateProjectPhaseModel)))
          this.close()
        }
      })
    },
    close() {
      this.editRebateProjectPhaseVisible = false
      this.rebateProjectPhaseModel = { tempId: null,
        estimateAmount: 0,
        actualAmount: 0,
        actualTotalIncome: 0,
        actualTotalInvolvement: 0,
        rebateProjectPhaseCostList: [],
        rebateProjectPhaseDepartmentList: [] }
      this.$refs['dataForm'].resetFields()
    },
    handleCancle() {
      this.close()
    },
    productItemChange(row, index) {
      var accountingBase = Number(row.accountingBase)
      var rebateRatio = Number(row.rebateRatio)
      const regex = /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/
      if (row.accountingBase && !regex.test(accountingBase)) {
        this.$message.error('核算单价只能是两位小数或整数')
        row.accountingBase_error = true
      } else {
        row.accountingBase_error = false
      }
      if (row.rebateRatio && !regex.test(rebateRatio)) {
        this.$message.error('返点比例只能是两位小数或整数')
        row.rebateRatio_error = true
      } else {
        row.rebateRatio_error = false
      }

      if (Number(row.accountingBase) && Number(row.rebateRatio)) {
        row.rebatePrice = (parseFloat(row.accountingBase) * parseFloat(row.rebateRatio) / 100).toFixed(2)
      }
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },
    handleAddDepartmentProducts() {
      var rebateProjectPhaseDepartment = { departmentId: null, departmentName: null }
      this.rebateProjectPhaseModel.rebateProjectPhaseDepartmentList.push(rebateProjectPhaseDepartment)
    },
    handleCloseProductDialog() {
      this.dialogSelectProductAndSpecPartVisible = false
      this.selectRebateProjectPhaseDepartment = {}
      this.$refs.refSelectProductAndSpecPart.list = []
    },
    chooseProduct_callback(data) {
      // 原来的品规数据集合
      const oldProjectPhaseProductSpecList = this.selectRebateProjectPhaseDepartment.rebateProjectPhaseProductSpecList
      this.selectRebateProjectPhaseDepartment.rebateProjectPhaseProductSpecList = []
      // 遍历当前选择的
      data.forEach(element => {
        var findIndex = oldProjectPhaseProductSpecList.findIndex(item => item.productSpecId === element.productSpecId)
        // 原来集合中不包含的添加
        if (findIndex === -1) {
          const rebateProjectPhaseProductSpec = {
            id: null,
            manufacturerName: element.manufacturerName,
            productNameCn: element.productNameCn,
            productSpecId: element.productSpecId,
            spec: element.spec
          }
          this.selectRebateProjectPhaseDepartment.rebateProjectPhaseProductSpecList.push(rebateProjectPhaseProductSpec)
        } else {
          // 包含的取原来的集合数据
          const oldProjectPhaseProductSpec = oldProjectPhaseProductSpecList.find(item => item.productSpecId === element.productSpecId)
          this.selectRebateProjectPhaseDepartment.rebateProjectPhaseProductSpecList.push(oldProjectPhaseProductSpec)
        }
      })
    },
    handleChooseProductAndSpec() {
      this.$refs.refSelectProductAndSpecPart.handleCheck()
      this.handleCloseProductDialog()
    },
    handleAddProduct(row) {
      this.selectRebateProjectPhaseDepartment = row
      this.selectRebateProjectPhaseDepartment.chooseProductSpecList = []

      if (row.rebateProjectPhaseProductSpecList === undefined || row.rebateProjectPhaseProductSpecList === null) {
        row.rebateProjectPhaseProductSpecList = []
      } else {
        row.rebateProjectPhaseProductSpecList.forEach(element => {
          this.rebateProjectPhaseModel.productList.find((item, index) => {
            if (item.productSpecId === element.productSpecId) {
              this.selectRebateProjectPhaseDepartment.chooseProductSpecList.push(item)
            }
          })
        })
      }

      this.dialogSelectProductAndSpecPartVisible = true
    },
    selectUpdate(val, item) {
      var findIndex = this.rebateProjectPhaseModel.rebateProjectPhaseDepartmentList.findIndex(element => element.departmentId === val.key)
      if (findIndex > -1) {
        this.$notice.message('不能重复添加部门', 'error')
        item.departmentName = ''
        item.departmentId = ''
      } else {
        item.departmentName = val.value
        item.departmentId = val.key
      }
      this.$forceUpdate()
    },
    handleDeleteProduct(item, row) {
      const index = item.rebateProjectPhaseProductSpecList.indexOf(row)
      if (index !== -1) {
        item.rebateProjectPhaseProductSpecList.splice(index, 1)
      }
    },
    handleDeleteDepartment(row) {
      const index = this.rebateProjectPhaseModel.rebateProjectPhaseDepartmentList.indexOf(row)
      if (index !== -1) {
        this.rebateProjectPhaseModel.rebateProjectPhaseDepartmentList.splice(index, 1)
      }
    },
    handleSelectCost(type) {
      if (type === 2 && (!this.rebateProjectPhaseModel.startTime || !this.rebateProjectPhaseModel.endTime)) {
        this.$message.error('请先选择实施时间')
        return false
      }
      var costList = this.rebateProjectPhaseModel.rebateProjectPhaseCostList.filter(item => item.enumType === type)
      var data = {
        type: type,
        costList: costList,
        departmentList: this.rebateProjectPhaseModel.departmentList,
        startTime: this.rebateProjectPhaseModel.startTime,
        endTime: this.rebateProjectPhaseModel.endTime
      }
      this.$refs.refAddProjectPhaseCost.init(data)
    },
    chooseProjectPhaseCost_callback(data) {
      this.rebateProjectPhaseModel.rebateProjectPhaseCostList = this.rebateProjectPhaseModel.rebateProjectPhaseCostList.filter(item => item.enumType !== data.type)
      data.list.forEach(element => {
        this.rebateProjectPhaseModel.rebateProjectPhaseCostList.push(element)
      })

      const sum = data.list.reduce((pre, cur) => { return pre + parseFloat(cur.amount) }, 0).toFixed(2)

      // 预计收入
      if (data.type === 1) {
        this.rebateProjectPhaseModel.estimateAmount = sum
      }
      // 实际收入
      if (data.type === 2) {
        this.rebateProjectPhaseModel.actualAmount = sum
      }
    }
  }

}
</script>
<style lang="css">
.uploadCss {
  float: right; color: #516e92 ;padding: 3px 0px;  font-size: 12px;font-weight: 400; background: transparent;  border-color: transparent
}
.error-border .el-input__inner{
  border: 1px solid red;
  border-radius: 5px;
}
</style>
