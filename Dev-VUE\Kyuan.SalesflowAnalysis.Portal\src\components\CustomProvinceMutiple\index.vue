<template>
  <div style="width: 100%" class="ivuInput">
    <el-popover
      placement="bottom-start"
      trigger="hover"
      @hide="onPopperHide"
    >
      <div class="CheckboxGroupTemplate">
        <div class="CheckboxGroupHide" style="white-space: pre-wrap;">
          <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
          <div style="margin: 15px 0;" />
          <el-checkbox-group v-model="provinceNames" @change="handleCheckedCitiesChange">
            <el-col v-for="item in provinceList" :key="item.key" :span="6">
              <el-checkbox :label="item.value" />
            </el-col>
          </el-checkbox-group>
        </div>
      </div>
      <el-input
        slot="reference"
        v-model="provinceNameValues"
        readonly
        placeholder="省份"
        @on-focus="onInputFocus"
      />
    </el-popover>
  </div>
</template>

<script>
import { array } from 'jszip/lib/support'

export default {
  props: {
    provinceList: {
      type: array, // 类型
      default: [] // 默认值
    }
  },
  data() {
    return {
      provinceIDs: [],
      provinceNames: [],
      isIndeterminate: false,
      checkAll: true
    }
  },
  computed: {
    provinceNameValues: function() {
      return this.provinceNames.join(',')
    }
  },
  watch: {
    // 监听数组变化，取值
    provinceNames: {
      handler: function(newValue) {
        this.provinceIDs = []
        if (newValue.length > 0) {
          newValue.forEach(item => {
            this.provinceList.forEach(itemOne => {
              if (item === itemOne.value) {
                this.provinceIDs.push(itemOne.key)
              }
            })
          })
        }
      },
      deep: true
    },
    provinceList: {
      handler: function(newValue) {
        this.provinceIDs = []
        this.provinceNames = []
        newValue.forEach(item => {
          this.provinceIDs.push(item.key)
          this.provinceNames.push(item.value)
        })
      }
    }
  },
  methods: {
    onPopperHide() {
      this.$emit('input', this.provinceIDs) // 返回所有选中id
    },
    onInputFocus() {
      this.provinceNames = []
    },
    handleCheckAllChange(val) {
      this.isIndeterminate = false

      this.provinceIDs = []
      this.provinceNames = []
      if (val) {
        this.provinceList.forEach(item => {
          this.provinceIDs.push(item.key)
          this.provinceNames.push(item.value)
        })
      }
    },
    handleCheckedCitiesChange(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.provinceList.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.provinceList.length
    }
  }
}
</script>

  <style lang="scss" scoped>
 .CheckboxGroupTemplate {
  max-height: 230px;
  overflow: auto;
}
.CheckboxGroupTemplate::-webkit-scrollbar {
  width: 0 !important;
}

.CheckboxGroupHide {
  width: 490px;
  background: #fff;
}
.CheckboxGroupHide ul {
  margin-top: 5px;
  margin-bottom: 5px;
  clear: both;
}
.CheckboxGroupHide ul li {
  clear: both;
  list-style-type: none;
  border-bottom: 1px solid #dddee1;
  padding-bottom: 5px;
}
.CheckboxGroupHide ul li .left {
  width: 80px;
  display: inline-block;
  vertical-align: top;
}
.CheckboxGroupHide ul li .right {
  display: inline-block;
  width: 315px;
  word-wrap: break-word;
}
.CheckboxGroupHide ul:last-child li {
  border-bottom: none;
}
.addressStyle {
  float: right;
  margin-top: 5px;
  position: absolute;
  right: 20px;
  top: 3px;
  color: #666;
  cursor: pointer;
}
.leftAndRightOne {
  border-bottom: 1px solid #dddee1;
}
.leftAndRightOne:last-child {
  border-bottom: none;
}
.leftOne {
  display: inline-block;
  width: 55px;
  word-wrap: break-word;
  vertical-align: top;
}
.rightOne {
  display: inline-block;
  width: 255px;
  word-wrap: break-word;
}
.ivu-poptip-body {
  padding-right: 0 !important;
}
div.ivu-poptip {
  width: 100% !important;
}
    </style>
