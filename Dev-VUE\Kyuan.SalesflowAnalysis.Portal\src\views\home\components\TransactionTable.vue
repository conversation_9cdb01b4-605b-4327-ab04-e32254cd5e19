<template>
  <div>
    <el-card>
      <div slot="header" class="clearfix">
        <span>服务运行监测</span>
      </div>
      <div>
        <el-row>
          <el-col :span="24">
            <el-table :data="list" style="width:100%;overflow-y:auto;" max-height="215">
              <el-table-column label="服务名称" prop="name" />
              <el-table-column label="运行时间" width="140px" prop="lastExecuteTime" align="center" />
              <el-table-column label="状态" width="120px" align="center">
                <template slot-scope="{row}">
                  <el-tag :type="row.status | statusFilter">
                    {{ row.enumServiceStatusDesc }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import HomeService from '@/api/home'

export default {
  filters: {
    statusFilter(status) {
      const statusMap = {
        success: 'success',
        danger: 'danger',
        info: 'info'
      }
      return statusMap[status]
    },
    orderNoFilter(str) {
      return str.substring(0, 30)
    }
  },
  data() {
    return {
      list: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-LastExecuteTime'
      }
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      HomeService.QueryScehdulerTask(this.listQuery).then(res => {
        this.list = res.data.datas
      }).catch(res => {})
    }
  }
}
</script>

