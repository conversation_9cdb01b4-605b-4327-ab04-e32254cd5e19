import HttpApi from './libs/api.request'

const controller = 'Report'

const api = new HttpApi(controller)

export default {
  GetReportSetting() {
    return api.get('GetReportSetting')
  },
  SaveReportSetting(params) {
    return api.post('SaveReportSetting', params)
  },
  GenerateTerminalReportByMonth(params) {
    return api.post('GenerateTerminalReportByMonth', params)
  },
  GenerateNewTerminalReportByMonth(params) {
    return api.post('GenerateNewTerminalReportByMonth', params)
  },
  GenerateLoseTerminalReportByMonth(params) {
    return api.post('GenerateLoseTerminalReportByMonth', params)
  },
  QuerySalesAchievedRateDetail(params) {
    return api.get('QuerySalesAchievedRateDetail', params)
  },
  QuerySalesRankingDetail(params) {
    return api.get('QuerySalesRankingDetail', params)
  },
  QueryNonTargetReachStandard(params) {
    return api.get('QueryNonTargetReachStandard', params)
  },
  GetSalesAchievedRateDetailExportColumn() {
    return api.get('GetSalesAchievedRateDetailExportColumn')
  },
  ExportSalesAchievedRateDetail(params) {
    return api.post('ExportSalesAchievedRateDetail', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  GetSalesRankingDetailExportColumn() {
    return api.get('GetSalesRankingDetailExportColumn')
  },
  ExportSalesRankingDetail(params) {
    return api.post('ExportSalesRankingDetail', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  GetNonTargetReachStandardColumn() {
    return api.get('GetNonTargetReachStandardColumn')
  },
  ExportNonTartetReachStandard(params) {
    return api.post('ExportNonTartetReachStandard', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QuerySalesAchievement(params) {
    return api.get('QuerySalesAchievement', params)
  },
  GetSalesAchievementExportColumn() {
    return api.get('GetSalesAchievementExportColumn')
  },
  ExportSalesAchievement(params) {
    return api.post('ExportSalesAchievement', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryMedicalCustomerReport(params) {
    return api.get('QueryMedicalCustomerReport', params)
  },
  GetMedicalCustomerExportColumn() {
    return api.get('GetMedicalCustomerExportColumn')
  },
  ExportMedicalCustomer(params) {
    return api.post('ExportMedicalCustomer', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryWarningStopPurchase(params) {
    return api.get('QueryWarningStopPurchase', params)
  },
  SetWarningStopPurchaseHandled(params) {
    return api.post('SetWarningStopPurchaseHandled', params)
  },
  GetLastSalesFlowDate() {
    return api.get('GetLastSalesFlowDate')
  },
  GetSalesFlowUpdateDate() {
    return api.get('GetSalesFlowUpdateDate')
  },
  QueryRetailCustomerReport(params) {
    return api.get('QueryRetailCustomerReport', params)
  },
  GetRetailCustomerExportColumn() {
    return api.get('GetRetailCustomerExportColumn')
  },
  ExportRetailCustomer(params) {
    return api.post('ExportRetailCustomer', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryHeadquartersCoverage(params) {
    return api.get('QueryHeadquartersCoverage', params)
  },
  GetHeadquartersCoverageExportColumn() {
    return api.get('GetHeadquartersCoverageExportColumn')
  },
  ExportHeadquartersCoverage(params) {
    return api.post('ExportHeadquartersCoverage', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryDevelopSalesAchievement(params) {
    return api.get('QueryDevelopSalesAchievement', params)
  },
  QueryDevelopSalesAchievementDetail(params) {
    return api.get('QueryDevelopSalesAchievementDetail', params)
  },
  GetDevelopSalesAchievementExportColumn() {
    return api.get('GetDevelopSalesAchievementExportColumn')
  },
  ExportDevelopSalesAchievement(params) {
    return api.post('ExportDevelopSalesAchievement', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryRebateReport(params) {
    return api.get('QueryRebateReport', params)
  },
  GetRebateReportExportColumn() {
    return api.get('GetRebateReportExportColumn')
  },
  ExportRebateReport(params) {
    return api.post('ExportRebateReport', {
      data: params,
      responseType: 'arraybuffer'
    })
  }
}
