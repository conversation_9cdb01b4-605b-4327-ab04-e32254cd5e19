<template>
  <div class="card-panel" style="height:250px;">
    <el-row type="flex">
      <el-col :span="8">
        <svg-icon icon-class="user" class-name="card-panel-icon" />
      </el-col>
      <el-col :span="16" class="card-panel-user">
        <el-row>
          <el-col class="card-panel-name" :span="24">{{ employeeModel.displayName }}</el-col>
          <el-row />
          <el-row>
            <el-col class="card-panel-dept" :span="8">{{ employeeModel.departmentName }}</el-col>
            <el-col class="card-panel-title" :span="16">{{ employeeModel.title }}</el-col>
            <el-row />
          </el-row>
        </el-row></el-col>
    </el-row>
    <el-row type="flex" class="card-panel-detail">
      <el-col :span="8" class="card-panel-user">
        联系电话
      </el-col>
      <el-col :span="16" class="card-panel-user">
        {{ employeeModel.mobile }}
      </el-col>
      <el-col :span="8" class="card-panel-user">
        电子邮箱
      </el-col>
      <el-col :span="16" class="card-panel-user">
        {{ employeeModel.email }}
      </el-col>
      <el-col :span="8" class="card-panel-user">
        入职时间
      </el-col>
      <el-col :span="16" class="card-panel-user">
        <span v-if="employeeModel.hireDate"> {{ employeeModel.hireDate | parseTime('{y}-{m}-{d}') }}</span>
      </el-col>
      <el-col :span="8" class="card-panel-user">
        系统角色
      </el-col>
      <el-col :span="16" class="card-panel-user hiddenText">
        {{ employeeModel.roleNames }}
      </el-col>
    </el-row>
  </div>
</template>

<script>

import HomeService from '@/api/home'
export default {
  data() {
    return {
      employeeModel: {},
      userName: '员工姓名',
      dept: '销售部',
      title: '高级销售代表'
    }
  },
  created() {
    this.fetchData()
    this.getModel()
  },
  methods: {
    fetchData() {
      this.list = [{ name: '科园流向二期系统上线', releaseTime: '2021-5-20' },
        { name: '科园流向二期系统功能介绍', releaseTime: '2021-7-10' }]
    },
    getModel() {
      HomeService.GetCurrentEmployee().then(res => {
        this.employeeModel = res.data
      }).catch(error => {
        console.log(error)
      })
    }
  }
}
</script>
<style scoped>
  .card-panel{
    background-color: white;
    padding: 15px;
  }
  .card-panel .card-panel-icon {
    color:#3e5371;
    float: left;
    font-size: 48px;
  }
  .card-panel-user
  {
    padding-left: 20px;
    line-height: 28px;
  }
  .hiddenText{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
  .card-panel .card-panel-name
  {
    line-height: 28px;
    font-size: 14px;
    color: #909399;
    font-weight: bold;
  }
    .card-panel .card-panel-dept
  {
    font-size: 12px;
    color: #909399;
    font-weight: bold;
  }

  .card-panel .card-panel-title
  {
    font-size: 12px;
    color: #909399;
    font-weight: bold;
  }

  .card-panel-detail{
    margin-top: 20px;
    color: #909399;
    font-size: 12px;
    flex-wrap: wrap;
  }
</style>
