<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="span">
          <el-select
            v-model="listQuery.manufacturerId"
            class="filter-item"
            placeholder="厂商名称"
            clearable
          >
            <el-option
              v-for="item in manufacturerList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.nameCn"
            clearable
            placeholder="产品名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.commonName"
            clearable
            placeholder="通用名"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Product_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Product_Button_BatchImport')"
            type="primary"
            icon="el-icon-upload2"
            @click="handleImport"
          >
            批量导入
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Product_Button_Add')"
            type="primary"
            icon="el-icon-plus"
            @click="handleCreate"
          >
            新增
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Product_Button_Export')"
            :loading="btnExportLoading"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              :index="indexMethod"
              type="index"
              align="center"
            />
            <el-table-column
              sortable="custom"
              prop="Manufacturer.Name"
              label="厂商名称"
              min-width="100px"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.manufacturerName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="NameCn"
              label="产品名称"
              min-width="100px"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.nameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="CommonName"
              label="通用名"
              min-width="100px"
              header-align="center"
              align="left"
            >
              <template slot-scope="{ row }">
                <span>{{ row.commonName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="NameEn"
              label="英文名称"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.nameEn }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="140"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i v-if="$isPermitted($store.getters.user, 'Product_Button_EditSpec')" class="el-icon-folder-opened eltablei" title="编辑规格" @click="handleEditSpec(row)" />
                <i v-if="$isPermitted($store.getters.user, 'Product_Button_Edit')" class="el-icon-edit-outline eltablei" title="编辑" @click="handleUpdate(row)" />
                <i v-if="$isPermitted($store.getters.user, 'Product_Button_Del')" class="el-icon-delete eltablei" title="删除" @click="handleDelete(row)" />
                <i v-if="$isPermitted($store.getters.user, 'Product_Button_Query')" class="el-icon-document eltablei" title="详细信息" @click="handleInfo(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :close-on-click-modal="false"
      :title="textMap[dialogStatus]"
      :visible="dialogEditFormVisible"
      width="45%"
      @close="btnClose"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="90px"
        class="el-dialogform"
      >
        <el-form-item label="厂商名称" prop="manufacturerId">
          <el-select
            v-model="temp.manufacturerId"
            style="width: 100%"
            class="filter-item"
            placeholder="厂商名称"
          >
            <el-option
              v-for="item in manufacturerList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="产品名称" prop="nameCn">
          <el-input
            v-model="temp.nameCn"
            clearable
            placeholder="产品名称"
            maxlength="50"
          />
        </el-form-item>
        <el-form-item label="通用名" prop="commonName">
          <el-input
            v-model="temp.commonName"
            clearable
            placeholder="通用名"
            maxlength="100"
          />
        </el-form-item>
        <el-form-item label="英文名称" prop="nameEn">
          <el-input
            v-model="temp.nameEn"
            clearable
            placeholder="英文名称"
            maxlength="50"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnClose()"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="dialogStatus === 'create' ? createData() : updateData()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="产品详细信息"
      :close-on-click-modal="false"
      :visible="dialogProductInfoVisible"
      width="80%"
      @close="closeProductInfoDialog"
    >
      <ProductInfo :product-id="productId" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeProductInfoDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible="showExportModal"
      :close-on-click-modal="false"
      title="导出产品"
      width="800"
      @close="handleExportCancel"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
    <el-dialog
      title="导入产品"
      :close-on-click-modal="false"
      :visible="dialogImportVisible"
      width="80%"
      class="popup-search"
      @close="closeImportDialog"
    >
      <ImportProduct :show-import="dialogImportVisible" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeImportDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="包装规格"
      :close-on-click-modal="false"
      :visible="dialogProductSpecVisible"
      class="popup-search"
      width="80%"
      @close="closeProductSpecDialog"
    >
      <ProductSpecInfo :product-id.sync="productId" :show-dialog="dialogProductSpecVisible" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeProductSpecDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import ProductService from '@/api/product'
import ManufacturerService from '@/api/manufacturer'
import ProductInfo from './components/productInfo'
import CustomExport from '@/components/Export/CustomExport'
import ImportProduct from './components/importProduct'
import ProductSpecInfo from './components/specification'

export default {
  components: {
    Pagination,
    ProductInfo,
    CustomExport,
    ImportProduct,
    ProductSpecInfo
  },
  data() {
    return {
      span: 4,
      productId: '',
      list: [],
      manufacturerList: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      btnExportLoading: false,
      temp: {
        id: undefined,
        nameCn: '',
        nameEn: '',
        commonName: '',
        manufacturerId: ''
      },
      dialogEditFormVisible: false,
      dialogProductInfoVisible: false,
      dialogImportVisible: false,
      dialogProductSpecVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑',
        create: '新增'
      },
      rules: {
        manufacturerId: [
          {
            required: true,
            type: 'string',
            message: '请输入厂商名称',
            trigger: 'change'
          }
        ],
        nameCn: [
          {
            required: true,
            type: 'string',
            message: '请输入产品名称',
            trigger: 'blur'
          }
        ],
        commonName: [
          {
            required: true,
            type: 'string',
            message: '请输入通用名',
            trigger: 'blur'
          }
        ]
      },
      showExportModal: false,
      columnDictionary: {},
      permissions: {}
    }
  },
  created() {
    this.queryManufacturer()
    this.getList()
  },
  methods: {
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList() {
      this.listLoading = true

      ProductService.QueryProducts(this.listQuery)
        .then((result) => {
          this.listLoading = false

          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        })
        .catch((error) => {
          console.log(error)
          this.loading = false
        })
    },
    queryManufacturer() {
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturerList = result
        })
        .catch((error) => {
          console.log(error)
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        nameCn: '',
        nameEn: '',
        commonName: '',
        manufacturerId: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogEditFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          ProductService.AddProduct(this.temp)
            .then((result) => {
              if (result.succeed) {
                this.btnClose()
                this.getList()
                this.showMessage('创建成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
              this.loading = false
            })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.dialogStatus = 'update'
      this.temp.edit = false
      this.dialogEditFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          ProductService.UpdateProduct(this.temp)
            .then((result) => {
              if (result.succeed) {
                this.btnClose()
                this.getList()
                this.showMessage('更新成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
              this.loading = false
            })
        }
      })
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },

    handleDelete(row) {
      this.$confirm('确定删除此产品吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const tempRow = Object.assign({}, row)

          ProductService.DeleteProduct(tempRow)
            .then((result) => {
              if (result.succeed) {
                this.getList()
                this.showMessage('删除成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
              this.loading = false
            })
        })
        .catch((error) => {
          if (!error.succeed) {
            this.showMessage('取消删除', 'info')
          }
        })
    },

    handleInfo(row) {
      this.productId = row.id
      this.dialogProductInfoVisible = true
    },
    closeProductInfoDialog() {
      this.dialogProductInfoVisible = false
    },
    btnClose() {
      this.dialogEditFormVisible = false
      this.temp = {}
      this.$refs.dataForm.resetFields()
    },
    handleImport() {
      this.dialogImportVisible = true
    },
    closeImportDialog() {
      this.dialogImportVisible = false
      this.handleFilter()
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      ProductService.GetProductExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      this.listQuery.checkedColumns = checkColumns
      ProductService.ExportProducts(this.listQuery)
        .then((result) => {
          this.listQuery.checkedColumns = null
          const fileDownload = require('js-file-download')
          var filename = '产品.xlsx'

          fileDownload(result.data, filename)
        })
        .catch((error) => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    handleEditSpec(row) {
      this.productId = row.id
      this.dialogProductSpecVisible = true
    },
    closeProductSpecDialog() {
      this.productId = ''
      this.dialogProductSpecVisible = false
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    }

  }
}
</script>
