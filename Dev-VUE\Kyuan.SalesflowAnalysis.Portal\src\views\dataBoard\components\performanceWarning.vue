<template>
  <div class="panel-group">
    <el-card style="height: 270px">
      <div slot="header" class="clearfix ">
        <div>
          <el-row>
            <el-col :span="6" class="padding-top-title">
              <span>{{ title }}</span>
            </el-col>
            <el-col :span="16">
              <el-row style="color:#909399; font-size :12px;padding-left: 10px;">
                <el-col :span="12">协议数量合计:{{ performancData.totalAgreementQuantity }}</el-col>
                <el-col :span="12">销售数量合计:{{ performancData.totalSalesQuantity }}</el-col>
                <el-col :span="12">返利支出合计:{{ performancData.totalInvestmentAmount| toMoney }}</el-col>
                <el-col :span="12">达成率:{{ performancData.achievementRate | toTwoNum }}%</el-col>
              </el-row>
            </el-col>
            <el-col :span="2" class="padding-top-title">
              <el-button
                style="float: right; padding: 3px 0"
                type="text"
                @click="handleMore"
              >详情</el-button>
            </el-col>

          </el-row></div>
      </div>

      <el-table
        :data="topList"
        stripe
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          label="客户名称"
          header-align="center"
          show-overflow-tooltip
          min-width="120"
          align="left"
        >
          <template slot-scope="{ row }">
            <span>{{ row.receiverName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="品规" show-overflow-tooltip header-align="center" align="left">
          <template slot-scope="{ row }">
            <span>{{ row.productName }}({{ row.specification }})</span>
          </template>
        </el-table-column>
        <el-table-column label="返利支出" prop="quotaCount" width="90" align="center">
          <template slot-scope="{ row }">
            <span>{{ row.investmentAmount | toMoney }}</span>
          </template>
        </el-table-column>
        <el-table-column label="协议" prop="agreementCount" width="50" align="center">
          <template slot-scope="{ row }">
            <span>{{ row.agreementQuantity }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已完成" prop="completed" width="60" align="center">
          <template slot-scope="{ row }">
            <span>{{ row.completeQuantity }}</span>
          </template>
        </el-table-column>
        <el-table-column label="达成率" prop="completed" width="80" align="center">
          <template slot-scope="{ row }">
            <span>{{ row.achievementRate | toTwoNum }}%</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!--弹出dialog-->
    <el-dialog
      append-to-body
      :title="title"
      width="60%"
      :close-on-click-modal="false"
      :visible="showDialog"
      @close="cancleDialog()"
    >
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="8">
            <el-input
              v-model="listQuery.receiverName"
              clearable
              placeholder="客户名称"
              class="filter-item"
            />
          </el-col>
          <el-col :span="12">
            <el-cascader
              ref="refProductAndSpec"
              v-model="manufacturerProductAndSpecId"
              :options="productAndSpecList"
              placeholder="厂商 / 产品 / 规格"
              style="width: 100%"
              clearable
              class="filter-item"
              :props="{
                multiple: true,
                checkStrictly: false,
                expandTrigger: 'hover',
                emitPath: true,
              }"
            />
          </el-col>
          <el-col :span="4">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-search"
              @click="handleFilter"
            >
              查询
            </el-button>
          </el-col>
        </el-row>
        <el-row type="flex" :gutter="10" justify="end">
          <el-col :span="3.5">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-download"
              @click="handleExport"
            >
              导出
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table
              :data="dataList"
              stripe
              fit
              border
              highlight-current-row
              style="width: 100%"
              :header-cell-class-name="'tableStyle'"
            >
              <el-table-column
                fixed
                label="序号"
                :index="indexMethod"
                type="index"
                align="center"
              />
              <el-table-column
                label="客户名称"
                min-width="150"
                header-align="center"
                align="left"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.receiverName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="品规" header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.productName }}({{ row.specification }})</span>
                </template>
              </el-table-column>
              <el-table-column label="返利支出" width="90px" prop="quotaCount" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.investmentAmount | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="协议"
                width="80px"
                prop="agreementCount"
                header-align="center"
                align="right"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.agreementQuantity }}</span>
                </template>
              </el-table-column>
              <el-table-column label="已完成" width="80px" prop="completed" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.completeQuantity }}</span>
                </template>
              </el-table-column>
              <el-table-column label="达成率" width="80px" prop="completed" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.achievementRate | toTwoNum }}%</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col v-if="totalCount > 0" class="el-colRight">
            <pagination
              v-show="totalCount > 0"
              :total="totalCount"
              :page.sync="listQuery.pageIndex"
              :limit.sync="listQuery.pageSize"
              @pagination="search"
            />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleDialog()">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import dashboardService from '@/api/dashboard'

export default {
  components: {
    Pagination
  },
  data() {
    return {
      title: '零售客户履约情况',
      showDialog: false,
      productAndSpecList: [],
      manufacturerProductAndSpecId: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      filter: {},
      dataList: [],
      topList: [],
      totalCount: 0, // 数据总数
      topCount: 1,
      performancData: {
        totalAgreementQuantity: 0,
        totalSalesQuantity: 0,
        totalInvestmentAmount: 0,
        achievementRate: 0
      }
    }
  },
  methods: {
    initPage(filter, productAndSpecList, count) {
      this.filter = filter
      this.productAndSpecList = productAndSpecList
      this.manufacturerProductAndSpecId = this.filter.manufacturerProductAndSpecId
      this.topCount = count
      this.getTopList()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getTopList() {
      this.filter.pageIndex = 1
      this.filter.pageSize = this.topCount
      dashboardService
        .QueryTargetReceiverPerformance(this.filter)
        .then((res) => {
          this.performancData = res.data.datas[0]
          this.topList = res.data.datas[0].datas
          this.filter.pageSize = 10
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.search()
    },
    search() {
      if (this.manufacturerProductAndSpecId) {
        this.listQuery.productSpecIds = this.manufacturerProductAndSpecId.map(
          (element) => element[2]
        )
      }
      this.listQuery.timeRange = this.filter.timeRange

      dashboardService
        .QueryTargetReceiverPerformance(this.listQuery)
        .then((res) => {
          this.dataList = res.data.datas[0].datas
          this.totalCount = res.data.recordCount
        })
    },
    handleExport() {
      if (this.manufacturerProductAndSpecId) {
        this.listQuery.productSpecIds = this.manufacturerProductAndSpecId.map(
          (element) => element[2]
        )
      }
      this.listQuery.timeRange = this.filter.timeRange

      dashboardService.ExportTargetReceiverPerformance(this.listQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '零售客户履约情况.xlsx'

          fileDownload(result.data, filename)
        })
        .catch(() => {

        })
    },
    handleMore() {
      this.showDialog = true
      this.listQuery.productSpecIds = this.filter.productSpecIds
      this.listQuery.departmentId = this.filter.departmentId
      this.listQuery.areaIds = this.filter.areaIds
      this.listQuery.provinceIds = this.filter.provinceIds
      this.listQuery.isSelectedAllProvince = this.filter.isSelectedAllProvince
      this.listQuery.pageIndex = 1
      this.search()
    },
    cancleDialog() {
      this.showDialog = false
      this.manufacturerProductAndSpecId = this.filter.manufacturerProductAndSpecId
      this.listQuery =
      {
        pageIndex: 1,
        pageSize: 10,
        receiverName: null
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.cardHeader {
  height: 10px;
}

.el-card ::v-deep .el-card__header {
  padding: 8px 20px 20px 20px;
  border-bottom: 1px solid #e6ebf5;
}

.el-card ::v-deep .el-card__body {
  padding: 0px 10px 0px 10px;
}
.padding-top-title{
  padding-top:8px
}
</style>
