import variables from '@/styles/element-variables.scss'
import cfg from '@cfg'

const { showBanner, showSettings, tagsView, fixedHeader, sidebarLogo } = cfg

const state = {
  theme: variables.theme,
  showBanner: showBanner,
  showSettings: showSettings,
  tagsView: tagsView,
  fixedHeader: fixedHeader,
  sidebarLogo: sidebarLogo,
  downloadFileUrl: (process.env.NODE_ENV === 'development' ? cfg.apiUrl.dev : cfg.apiUrl.pro) + cfg.downloadFileApi
}

const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    if (Object.prototype.hasOwnProperty.call(state, key)) {
      state[key] = value
    }
  }
}

const actions = {
  changeSetting({ commit }, data) {
    commit('CHANGE_SETTING', data)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

