import HttpApi from './libs/api.request'

const controller = 'EmployeeStation'

const api = new HttpApi(controller)

export default {
  QueryEmployeeStation(params) {
    return api.get('QueryEmployeeStation', params)
  },
  GetEmployeeStationExportColumn() {
    return api.get('GetEmployeeStationExportColumn')
  },
  ExportEmployeeStation(params) {
    return api.post('ExportEmployeeStation',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  }
}
