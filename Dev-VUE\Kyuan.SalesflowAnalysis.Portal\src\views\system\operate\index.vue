<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container minusBottom" type="flex">
        <el-col :span="span">
          <el-input
            v-model="listQuery.operate"
            clearable
            placeholder="标题"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.controller"
            clearable
            placeholder="Controller"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.action"
            clearable
            placeholder="方法"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.input"
            clearable
            placeholder="输入"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.output"
            clearable
            placeholder="输出"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.userUniqueName"
            clearable
            placeholder="操作人"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="listQuery.logTime"
            clearable
            class="filter-item"
            type="daterange"
            range-separator="至"
            start-placeholder="操作时间起"
            end-placeholder="操作时间止"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'ActionLog_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              :index="indexMethod"
              type="index"
              align="center"
            />
            <el-table-column
              sortable="custom"
              prop="Operate"
              label="标题"
              align="center"
              min-width="150px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.operate }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Controller"
              label="Controller"
              align="center"
              min-width="120px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.controller }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Action"
              align="center"
              label="方法"
              min-width="160px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.action }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Input"
              label="输入"
              align="left"
              header-align="center"
              min-width="150px"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="{ row }">
                <span>{{ row.input }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Output"
              label="输出"
              align="left"
              header-align="center"
              min-width="150px"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="{ row }">
                <span>{{ row.output }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="UserUniqueName"
              align="center"
              label="操作人"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.userUniqueName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="LogTime"
              align="center"
              label="操作时间"
              min-width="150px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.logTime }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import LogService from '@/api/log'

export default {
  name: 'Operate',
  components: {
    Pagination
  },
  data() {
    return {
      span: 4,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-LogTime'
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList() {
      this.listLoading = true

      LogService.QueryLogApi(this.listQuery)
        .then((result) => {
          this.listLoading = false

          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    }
  }
}
</script>
<style lang="css">
  .el-tooltip__popper {
    font-size: 14px;
    max-width: 50%;
    background: #516e92 !important;
  }
</style>
