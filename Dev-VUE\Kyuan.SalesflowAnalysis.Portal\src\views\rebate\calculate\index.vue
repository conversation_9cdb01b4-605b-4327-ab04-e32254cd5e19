<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filter-container">
        <el-col :span="8">
          <el-date-picker
            v-model="filter.dateRange"
            clearable
            class="filter-item"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @keyup.enter.native="search"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="filter.status"
            :loading="statusLoading"
            class="filter-item"
            placeholder="状态"
            clearable
            @change="selectUpdate"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'RebateQuery_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="search"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="handleAddRebate"
          >
            新增返利计算
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="rebateResultList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              label="返利项目名称"
              sortable="custom"
              min-width="150px"
              header-align="center"
              align="left"
              prop="Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="起始日期"
              align="center"
              sortable="custom"
              min-width="100px"
              prop="StartTime"
            >
              <template slot-scope="{ row }">
                <span v-if="row.startDate">{{
                  row.startTime | parseTime("{y}-{m}-{d}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="截止日期"
              align="center"
              sortable="custom"
              min-width="100px"
              prop="EndTime"
            >
              <template slot-scope="{ row }">
                <span v-if="row.endDate">{{
                  row.endTime | parseTime("{y}-{m}-{d}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="状态"
              sortable="custom"
              min-width="130px"
              align="center"
              prop="EnumStatusDesc"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="协议数量"
              sortable="custom"
              min-width="150px"
              header-align="center"
              align="left"
              prop="TotalAgreementQuantity"
            >
              <template slot-scope="{ row }">
                <span>{{ row.totalAgreementQuantity }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="已计算协议数量"
              sortable="custom"
              min-width="150px"
              header-align="center"
              align="left"
              prop="CalculatedAgreementQuantity"
            >
              <template slot-scope="{ row }">
                <span>{{ row.calculatedAgreementQuantity }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="返利金额(元)"
              sortable="custom"
              min-width="130px"
              align="center"
              prop="TotalAmount"
            >
              <template slot-scope="{ row }">
                <span>{{ row.totalAmount }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="计算时间"
              min-width="150px"
              align="center"
              prop="CalculateTime"
            >
              <template slot-scope="{ row }">
                <span>{{ row.calculateTime | parseTime("{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>

            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="120"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i
                  class="el-icon-document eltablei"
                  title="查看明细"
                  @click="reviewDetail(row)"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="filter.pageIndex"
            :limit.sync="filter.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :close-on-click-modal="false"
      title="计算返利"
      :visible="dialogRebateCalculationVisible"
      width="35%"
      @close="handleRebateCalculationClose"
    >
      <el-form
        ref="rebateCalculationForm"
        :model="rebateCalculation"
        :rules="rebateRules"
        label-position="right"
        label-width="120px"
        class="el-dialogform"
      >
        <el-form-item label="返利计算开始时间" prop="startTime">
          <el-date-picker
            v-model="rebateCalculation.startTime"
            type="date"
            style="width: 100%"
            placeholder="返利计算开始时间"
          />
        </el-form-item>
        <el-form-item label="返利计算截止时间" prop="endTime">
          <el-date-picker
            v-model="rebateCalculation.endTime"
            type="date"
            style="width: 100%"
            placeholder="返利计算截止时间"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleRebateCalculationClose"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleRebateCalculationSave"
        >
          计算
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import RebateService from '@/api/rebate'
import MasterDataService from '@/api/masterData'
import Pagination from '@/components/Pagination'

export default {
  name: 'RebateResult',
  components: {
    Pagination
  },
  data() {
    return {
      span: 4,
      total: 0,
      filter: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      rebateResultList: [],
      statusOptions: [],
      rebateCalculation: {},
      dialogRebateCalculationVisible: false,
      rebateRules: {
        startTime: [
          { required: true, message: '请选择起始日期', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择截止日期', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.initStatusOptions()
    this.search()
  },
  methods: {
    initStatusOptions() {
      var param = {
        enumType: 'RebateResultStatus'
      }
      MasterDataService.GetEnumInfos(param)
        .then(result => {
          this.statusOptions = result.data.datas
          this.$forceupdate()
        })
        .catch(() => {})
    },
    selectUpdate() {
      this.$forceUpdate()
    },
    search() {
      this.filter.pageIndex = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      RebateService.QueryRebateResult(this.filter)
        .then(res => {
          this.listLoading = false
          this.rebateResultList = res.data.datas
          this.total = res.data.recordCount
          this.filter.pageIndex = res.data.pageIndex
        })
        .catch(() => {
          this.listLoading = true
        })
    },
    indexMethod(index) {
      return (this.filter.pageIndex - 1) * this.filter.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.filter.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.filter.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.filter.pageSize = val
      this.search()
    },
    handleAddRebate() {
      this.dialogRebateCalculationVisible = true
    },
    handleRebateCalculationSave() {
      // 保存
      this.$refs['rebateCalculationForm'].validate((valid) => {
        if (valid) {
          this.dialogRebateCalculationVisible = false
        }
      })
    },
    handleRebateCalculationClose() {
      this.dialogRebateCalculationVisible = false
    }
  }
}
</script>
<style scoped>
.flexwarp {
  display: flex;
  flex-wrap: wrap;
}
</style>
