export default {
  inputValue: {
    type: String,
    default: ''
  },
  showType: {
    type: String,
    default: 'icon'
  },
  title: {
    type: String,
    default: () => {
      return '选择经销商'
    }
  },
  showColumn: {
    type: String,
    default: 'displayName'
  },
  // 是否显示多选
  showSelectAll: {
    type: Boolean,
    default: false
  },
  chooseType: {
    type: String,
    default: () => {
      return 'multiple'
    }
  },
  placeholder: {
    type: String,
    default: () => {
      return '请选择'
    }
  },
  change: {
    type: [Function, null],
    default: null
  },
  initData: {
    type: Array,
    default: () => {
      return []
    }
  },
  distributorTypes: {
    type: Array,
    default: () => {
      return []
    }
  },
  isStopped: {
    type: Boolean,
    default: false
  },
  targetReceiverId: {
    type: String,
    default: null
  },
  departmentId: {
    type: String,
    default: null
  }
}
