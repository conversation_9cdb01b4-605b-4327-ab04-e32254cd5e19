<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex">
        <el-col :span="span">
          <el-select
            v-model="listQuery.deptId"
            style="width: 100%"
            class="filter-item"
            placeholder="部门"
            clearable
          >
            <el-option
              v-for="item in departments"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
            <el-table-column sortable="custom" prop="Department.Name" label="部门" min-width="80px" align="center" header-align="center">
              <template slot-scope="{ row }">
                <span>{{ row.departmentName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="Parent.Name" label="上级岗位名称" min-width="80px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.parentName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="Name" label="岗位名称" min-width="80px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="负责省份" min-width="320px" align="left" header-align="center">
              <template slot-scope="{ row }">
                <span>{{ row.provinceNames }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="80"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i class="el-icon-circle-check eltablei" title="选择" @click="handleCheck(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>

import MaintenanceService from '@/api/maintenance'
import Pagination from '@/components/Pagination'

export default {
  components: {
    Pagination
  },
  props: {
    showDialog: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      span: 4,
      departmentList: [],
      departmentId: '',
      listLoading: true,
      departments: [],
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      list: [],
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6
        }
      },
      stationVisable: false
    }
  },
  watch: {
    showDialog: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        this.stationVisable = val
        if (this.stationVisable === true) {
          this.initDepartment()
          this.getList()
        }
      }
    }
  },
  methods: {
    initDepartment() {
      MaintenanceService.QueryDept.then((res) => { this.departments = res })
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList() {
      this.listLoading = true
      MaintenanceService.QueryStation(this.listQuery).then(res => {
        this.listLoading = false
        this.list = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(res => {})
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleCheck(row) {
      this.$emit('success', row)
    },
    clear() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
    }
  }
}
</script>
<style scoped>
</style>
