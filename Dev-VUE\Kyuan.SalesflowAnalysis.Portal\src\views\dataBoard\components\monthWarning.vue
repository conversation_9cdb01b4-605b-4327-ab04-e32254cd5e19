<template>
  <div class="panel-group">
    <el-card style="height: 250px;">
      <div slot="header" class="clearfix cardHeader">
        <div>
          <span>{{ title }}</span>
          <span class="waringCount">{{ showTotalCount }}</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="handleMore">详情</el-button>
        </div>
      </div>
      <el-table
        :data="partialDataList"
        stripe
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column label="客户名称" prop="receiverName" show-overflow-tooltip header-align="center" align="left" />
        <el-table-column label="品规" prop="productSpecName" show-overflow-tooltip header-align="center" align="left" />
        <el-table-column label="潜力" width="60" prop="quota" align="right" />
        <el-table-column label="销量" width="60" prop="quantity" align="right" />
        <el-table-column label="达成率" width="80" align="right">
          <template slot-scope="{ row }">
            <span>{{ row.achievementRate | toTwoNum }}%</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!--弹出dialog-->
    <el-dialog append-to-body :title="title" width="60%" :close-on-click-modal="false" :visible="showDialog" @close="cancleDialog()">
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="8">
            <el-input
              v-model="listQuery.receiverName"
              clearable
              placeholder="客户名称"
              class="filter-item"
            />
          </el-col>
          <el-col :span="12">
            <el-cascader
              ref="refProductAndSpec"
              v-model="manufacturerProductAndSpecId"
              :options="productAndSpecList"
              placeholder="厂商 / 产品 / 规格"
              style="width: 100%"
              clearable
              class="filter-item"
              :props="{
                multiple: true,
                checkStrictly: false,
                expandTrigger: 'hover',
                emitPath: true,
              }"
            />
          </el-col>
          <el-col :span="4">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-search"
              @click="handleFilter"
            >
              查询
            </el-button>
          </el-col>
        </el-row>
        <el-row type="flex" :gutter="10" justify="end">
          <el-col :span="3.5">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-download"
              @click="handleExport"
            >
              导出
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table
              :data="dataList"
              stripe
              fit
              border
              highlight-current-row
              style="width: 100%"
              :header-cell-class-name="'tableStyle'"
            >
              <el-table-column label="客户名称" min-width="180px" prop="receiverName" header-align="center" align="left" />
              <el-table-column label="品规" width="180px" prop="productSpecName" header-align="center" align="left" />
              <el-table-column label="潜力" width="100px" prop="quota" header-align="center" align="right" />
              <el-table-column label="销量" width="100px" prop="quantity" header-align="center" align="right" />

              <el-table-column label="达成率" width="80" prop="achievementRate" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.achievementRate | toTwoNum }}%</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col v-if="totalCount > 0" class="el-colRight">
            <pagination
              v-show="totalCount > 0"
              :total="totalCount"
              :page.sync="listQuery.pageIndex"
              :limit.sync="listQuery.pageSize"
              @pagination="search"
            />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleDialog()">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import DashboardService from '@/api/dashboard'
import Pagination from '@/components/Pagination'

export default {
  components: {
    Pagination
  },
  data() {
    return {
      showDialog: false,
      title: '目标客户达成低于60%预警',
      partialDataList: [],
      dataList: [],
      totalCount: 0,
      tepmFilter: {},
      productAndSpecList: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      manufacturerProductAndSpecId: [],
      topCount: 1,
      showTotalCount: 0
    }
  },
  // 对接时应该为index页面调用
  created() {
  },
  methods: {
    initPage(filter, productAndSpecList, count) {
      this.tepmFilter = filter
      this.productAndSpecList = productAndSpecList
      this.manufacturerProductAndSpecId = this.tepmFilter.manufacturerProductAndSpecId
      this.topCount = count
      this.getTop5List()
    },
    getTop5List() {
      this.tepmFilter.pageIndex = 1
      this.tepmFilter.pageSize = this.topCount
      DashboardService.AchievementRateWarningByMonth(this.tepmFilter).then((result) => {
        if (result) {
          this.partialDataList = result.data.datas
          this.tepmFilter.pageSize = 10
          this.showTotalCount = result.data.recordCount
        }
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.search()
    },
    search() {
      if (this.manufacturerProductAndSpecId && this.manufacturerProductAndSpecId.length > 0) {
        this.listQuery.productSpecIds = this.manufacturerProductAndSpecId.map(
          (element) => element[2]
        )
      }
      this.listQuery.timeRange = this.tepmFilter.timeRange
      DashboardService.AchievementRateWarningByMonth(this.listQuery).then((result) => {
        if (result) {
          this.dataList = result.data.datas
          this.totalCount = result.data.recordCount
        }
      }).catch(() => {

      })
    },
    handleExport() {
      if (this.manufacturerProductAndSpecId && this.manufacturerProductAndSpecId.length > 0) {
        this.listQuery.productSpecIds = this.manufacturerProductAndSpecId.map(
          (element) => element[2]
        )
      }
      this.listQuery.timeRange = this.tepmFilter.timeRange
      DashboardService.ExportAchievementRateWarningByMonth(this.listQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '目标客户达成低于60%预警.xlsx'

          fileDownload(result.data, filename)
        })
        .catch(() => {

        })
    },
    handleMore() {
      this.listQuery.productSpecIds = this.tepmFilter.productSpecIds
      this.listQuery.departmentId = this.tepmFilter.departmentId
      this.listQuery.areaIds = this.tepmFilter.areaIds
      this.listQuery.provinceIds = this.tepmFilter.provinceIds
      this.listQuery.isSelectedAllProvince = this.tepmFilter.isSelectedAllProvince
      this.listQuery.pageIndex = 1
      this.showDialog = true
      this.search()
    },
    cancleDialog() {
      this.showDialog = false
      this.manufacturerProductAndSpecId = this.tepmFilter.manufacturerProductAndSpecId
      this.listQuery =
      {
        pageIndex: 1,
        pageSize: 10,
        receiverName: null
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.cardHeader {
      height: 10px;
    }

.el-card ::v-deep .el-card__header {
  padding: 18px 20px 20px 20px;
  border-bottom: 1px solid #e6ebf5;
}

.el-card ::v-deep .el-card__body {
  padding: 0px 10px 0px 10px;
}
</style>
