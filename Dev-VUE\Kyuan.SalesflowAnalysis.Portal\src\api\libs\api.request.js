import qs from 'qs'
import HttpRequest from '@/utils/axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import cfg from '@cfg'
import { getToken, setToken } from '@/utils/auth'

const apiUrl = process.env.NODE_ENV === 'development' ? cfg.apiUrl.dev : process.env.VUE_APP_TITLE === 'staging' ? cfg.apiUrl.stage : cfg.apiUrl.pro

function requestFunc(config) {
  var token = getToken()
  if (token) {
    config.headers.Authorization = token
  }
}

function responseFunc(response) {
  if (Object.prototype.hasOwnProperty.call(response.headers, 'jwt') && response.headers.jwt !== '') {
    setToken(response.headers.jwt)
  }
}

function errorFunc(error) {
  switch (error.response.status) {
    case 401:
    case 403:
      // to re-login
      MessageBox.confirm(error.message, '重新登录', {
        confirmButtonText: '重新登录',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
      })
      break
    default:
      Message({
        message: error.message || 'Error',
        type: 'error',
        duration: 5 * 1000
      })
      break
  }
}

class HttpApi extends HttpRequest {
  constructor(controller) {
    super(apiUrl, requestFunc, responseFunc, errorFunc) // 调用父类的 constructor(x, y)
    this.controller = controller
  }

  get(action, option) {
    if (!option || (
      !Object.prototype.hasOwnProperty.call(option, 'params') &&
      !Object.prototype.hasOwnProperty.call(option, 'headers') &&
      !Object.prototype.hasOwnProperty.call(option, 'processResult')
    )) {
      option = { params: option }
    }
    const { params, headers, processResult } = option

    return this.request({
      url: this.controller + '/' + action,
      params: JSON.encryptCycle(params),
      method: 'get',
      headers: headers,
      paramsSerializer: (params) => {
        return qs.stringify(params, { arrayFormat: 'repeat' })
      },
      processResult: processResult === undefined ? true : processResult
    })
  }

  post(action, option) {
    if (!option || (
      !Object.prototype.hasOwnProperty.call(option, 'data') &&
      !Object.prototype.hasOwnProperty.call(option, 'headers') &&
      !Object.prototype.hasOwnProperty.call(option, 'responseType') &&
      !Object.prototype.hasOwnProperty.call(option, 'processResult')
    )) {
      option = { data: option }
    }
    const { data, headers, responseType, processResult } = option
    return this.request({
      url: this.controller + '/' + action,
      data: JSON.encryptCycle(data),
      method: 'post',
      responseType: responseType,
      headers: headers,
      paramsSerializer: (data) => {
        return qs.stringify(data, { arrayFormat: 'indices', allowDots: true })
      },
      processResult: processResult === undefined ? true : processResult
    })
  }

  postForm(action, option) {
    if (!option || (
      !Object.prototype.hasOwnProperty.call(option, 'data') &&
      !Object.prototype.hasOwnProperty.call(option, 'headers') &&
      !Object.prototype.hasOwnProperty.call(option, 'processResult')
    )) {
      option = { data: option }
    }

    if (!option.headers) {
      option.headers = {}
    }

    option.headers['Content-Type'] = 'multipart/form-data'

    return this.post(action, option)
  }
}

export default HttpApi
