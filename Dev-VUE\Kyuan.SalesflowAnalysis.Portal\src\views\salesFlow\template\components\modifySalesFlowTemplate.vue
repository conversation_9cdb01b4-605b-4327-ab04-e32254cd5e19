<template>
  <div>
    <el-dialog :title="title" :close-on-click-modal="false" :visible="true" width="80%" @close="cancle()">
      <el-row type="flex" justify="end" :gutter="10" style="margin-bottom:10px;">
        <el-col :span="3.5">
          <el-button
            class="filter-item"
            type="primary"
            icon="el-icon-plus"
            @click="addRow"
          >
            添加列
          </el-button>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="tempData.tempFormModel.salesFlowTemplateMappingDetail"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
            />
            <el-table-column label="源列名" align="left" header-align="center" sortable min-width="200px" prop="fieldName">
              <template slot-scope="scope">
                <el-input v-model="scope.row.fieldName" clearable placeholder="源列名" />
              </template>
            </el-table-column>
            <el-table-column label="映射列名" align="left" header-align="center" sortable min-width="150px" prop="standardTemplate.fieldName">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.standardTemplate"
                  :loading="standardTemplateLoading"
                  value-key="id"
                  class="filter-item"
                  placeholder="映射列"
                  clearable
                  @change="((val)=>{standardTemplateChange(val, scope.$index)})"
                >
                  <el-option
                    v-for="item in standardTemplateList"
                    :key="item.id"
                    :label="item.fieldName"
                    :value="item"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="数据类型" align="center" sortable min-width="100px" prop="standardTemplate.enumFieldTypeDesc">
              <template slot-scope="scope">
                <span> {{ scope.row.standardTemplate? scope.row.standardTemplate.enumFieldTypeDesc :'' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="数据长度" align="center" sortable min-width="100px" prop="standardTemplate.length">
              <template slot-scope="scope">
                <span>{{ scope.row.standardTemplate? scope.row.standardTemplate.length :'' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="数据格式" align="center" sortable min-width="100px" prop="standardTemplate.enumFieldTypeDesc">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.enumDateFormatType"
                  :loading="enumDateFormatTypeLoading"
                  class="filter-item"
                  placeholder="数据格式"
                  clearable
                  :disabled="!scope.row.standardTemplate||scope.row.standardTemplate&&!scope.row.standardTemplate.isFormat"
                >
                  <el-option
                    v-for="item in enumDateFormatTypeList"
                    :key="item.value"
                    :label="item.desc"
                    :value="item.value"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="是否必填" align="center" header-align="center" sortable min-width="100px" prop="standardTemplate.isRequired">
              <template slot-scope="scope">
                <span>  {{ scope.row.standardTemplate&&scope.row.standardTemplate.isRequired===true?'是':'否' }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <i class="el-icon-delete eltablei" @click="handleDelete(scope.$index)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          v-if="!viewModel"
          :loading="btnSaveLoading"
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>

import SalesFlowTemplateMappingService from '@/api/salesFlowTemplateMapping'
import MasterDataService from '@/api/masterData'

export default {
  name: '',
  components: {
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    viewModel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tempData: { tempFormModel: {
      }},
      btnSaveLoading: false,
      listLoading: false,
      list: [],
      standardTemplateLoading: false,
      standardTemplateList: [],
      enumDateFormatTypeLoading: false,
      enumDateFormatTypeList: []
    }
  },
  watch: {
    id(val) {
      this.tempData.tempFormModel.id = val
    }
  },
  mounted() {
  },
  created() {
    this.initStandardTemplate()
    this.initEnumDateFormatType()
    this.init()
  },
  methods: {
    initStandardTemplate() {
      this.standardTemplateLoading = true
      SalesFlowTemplateMappingService.QueryStandardTemplateSelect()
        .then((result) => {
          this.standardTemplateLoading = false
          this.standardTemplateList = result
        })
        .catch((error) => {
          this.standardTemplateLoading = false
          console.log(error)
        })
    },
    initEnumDateFormatType() {
      this.enumDateFormatTypeLoading = true
      MasterDataService.GetEnumInfos({ enumType: 'DateFormatType' })
        .then((result) => {
          this.enumDateFormatTypeLoading = false
          this.enumDateFormatTypeList = result.data.datas
        })
        .catch((error) => {
          this.enumDateFormatTypeLoading = false
          console.log(error)
        })
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    init() {
      this.get(this.id)
    },
    get(id) {
      this.btnSaveLoading = true
      this.listLoading = true
      SalesFlowTemplateMappingService.GetSalesFlowTemplateMapping({ id: id }).then(result => {
        this.btnSaveLoading = false
        this.listLoading = false
        this.tempData.tempFormModel = result.data
        this.tempData.tempFormModel.salesFlowTemplateMappingDetail.forEach((item) => {
          if (item.enumDateFormatType === 0) {
            item.enumDateFormatType = ''
          }
        })
      }).catch(error => {
        this.btnSaveLoading = false
        this.listLoading = false
        console.log(error)
      })
    },
    standardTemplateChange(val, index) {
      const item = this.tempData.tempFormModel.salesFlowTemplateMappingDetail[index]
      if (val) {
        item.standardTemplateId = val.id
        if (val.isFormat) {
          item.enumDateFormatType = 1
        } else {
          item.enumDateFormatType = ''
        }
      } else {
        item.standardTemplateId = ''
        item.enumDateFormatType = ''
      }
    },
    validateTableList() {
      var message = ''
      if (this.tempData.tempFormModel.salesFlowTemplateMappingDetail) {
        if (this.tempData.tempFormModel.salesFlowTemplateMappingDetail.length === 0) {
          message += '请添加映射列'
        } else {
          this.tempData.tempFormModel.salesFlowTemplateMappingDetail.forEach((item, index) => {
            if (item.enumDateFormatType === '') {
              item.enumDateFormatType = 0
            }
            var orignalMessage = '第' + (index + 1) + '行：'
            var errorMessage = orignalMessage
            if (!item.fieldName) {
              errorMessage += '请输入源列名；'
            }
            if (item.standardTemplate) {
              if (item.standardTemplate.isFormat) {
                if (!item.enumDateFormatType > 0) {
                  errorMessage += '请选择数据类型；'
                }
              }
            } else {
              errorMessage += '请选择映射列名；'
            }
            if (errorMessage !== orignalMessage) {
              message += errorMessage + '<br />'
            }
          })
        }
      } else {
        message += '请添加映射列'
      }
      if (message) {
        this.tempData.tempFormModel.salesFlowTemplateMappingDetail.forEach((item) => {
          if (item.enumDateFormatType === 0) {
            item.enumDateFormatType = ''
          }
        })
        this.$notice.tip({ type: 'error', messages: message })
        return false
      } else {
        return true
      }
    },
    addRow() {
      this.tempData.tempFormModel.salesFlowTemplateMappingDetail.push({
        salesFlowTemplateMappingId: this.tempData.tempFormModel.id,
        enumDateFormatType: ''
      })
    },
    save() {
      if (this.validateTableList()) {
        if (!this.tempData.tempFormModel.id) {
          this.addNew()
        } else {
          this.update()
        }
      }
    },
    addNew() {
      this.btnSaveLoading = true
      SalesFlowTemplateMappingService.AddSalesFlowTemplateMapping(this.tempData.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempData.tempFormModel = result.data
          this.$notice.message('新增成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
          console.log('error')
        }
      })
        .catch(error => {
          if (!error.succeed && error.response.status === 200) {
            this.tempData.tempFormModel.salesFlowTemplateMappingDetail.forEach((item) => {
              if (item.enumDateFormatType === 0) {
                item.enumDateFormatType = ''
              }
            })
          }
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('新增失败。', 'error')
          }
        })
    },
    update() {
      this.btnSaveLoading = true
      SalesFlowTemplateMappingService.UpdateSalesFlowTemplateMapping(this.tempData.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempData.tempFormModel = result.data
          this.$notice.message('修改成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          if (!error.succeed && error.response.status === 200) {
            this.tempData.tempFormModel.salesFlowTemplateMappingDetail.forEach((item) => {
              if (item.enumDateFormatType === 0) {
                item.enumDateFormatType = ''
              }
            })
          }
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('修改失败。', 'error')
          }
        })
    },
    handleDelete(index) {
      this.$confirm('确定删除此映射列吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tempData.tempFormModel.salesFlowTemplateMappingDetail.splice(index, 1)
      }).catch(error => {
        if (!error.succeed) {
          this.$notice.message('取消删除', 'info')
        }
      })
    },
    close() {
      this.$emit('hidden')
    },
    cancle() {
      this.$emit('hidden')
    }
  }

}
</script>
