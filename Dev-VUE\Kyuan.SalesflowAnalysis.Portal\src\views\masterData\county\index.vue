<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex" style="margin-bottom:-20px;">
        <el-col :span="span">
          <el-cascader
            ref="refProvinceCity"
            v-model="filter.provinceCityId"
            :loading="provinceCityLoading"
            :options="provinceCityList"
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            placeholder="省份城市"
            clearable
            @change="provinceCitChange"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="filter.Name"
            clearable
            placeholder="区县名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'County_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              :index="indexMethod"
              type="index"
              align="center"
            />
            <el-table-column
              sortable="custom"
              prop="City.NameCn"
              label="城市"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.cityNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Code"
              label="Code编码"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.code }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Name"
              label="区县名称"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="ShortName"
              label="简称"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.shortName }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="filter.pageIndex"
            :limit.sync="filter.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import LocationService from '@/api/location'

export default {
  name: 'Location',
  components: {
    Pagination
  },
  data() {
    return {
      span: 4,
      list: [],
      provinceCityList: [],
      provinceCityLoading: false,
      city: [],
      total: 0,
      listLoading: true,
      filter: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
    }
  },
  created() {
    this.initProvinceCity()
    this.getList()
  },
  methods: {
    indexMethod(index) {
      return (this.filter.pageIndex - 1) * this.filter.pageSize + index + 1
    },
    initProvinceCity() {
      this.provinceCityLoading = true
      LocationService.QueryProvinceCityCascader()
        .then((result) => {
          this.provinceCityList = result
          this.provinceCityLoading = false
        })
        .catch((error) => {
          this.provinceCityLoading = false
          console.log(error)
        })
    },
    getList() {
      this.listLoading = true

      LocationService.QueryCounties(this.filter)
        .then((result) => {
          this.listLoading = false

          this.list = result.data.datas
          this.total = result.data.recordCount
          this.filter.pageIndex = result.data.pageIndex
        })
        .catch((error) => {
          console.log(error)
          this.loading = false
        })
    },
    handleFilter() {
      this.filter.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.filter.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.filter.order = orderSymbol + column.prop
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    provinceCitChange() {
      if (this.filter.provinceCityId !== undefined && this.filter.provinceCityId !== null && this.filter.provinceCityId.length > 0) {
        if (this.filter.provinceCityId.length > 1) {
          this.filter.cityId = this.filter.provinceCityId[1]
        } else {
          this.filter.cityId = null
        }
      } else {
        this.filter.cityId = null
      }
      this.$refs.refProvinceCity.dropDownVisible = false
    }
  }
}
</script>
