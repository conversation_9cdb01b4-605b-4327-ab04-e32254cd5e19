<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filiter-container">
        <el-col :span="6.5">
          <el-date-picker
            v-model="listQuery.timeRange"
            clearable
            class="filter-item"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.name"
            clearable
            placeholder="项目名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              label="项目名称"
              sortable="custom"
              min-width="150px"
              header-align="center"
              align="left"
              prop="Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>

            <el-table-column label="费用类型" align="center" sortable="custom" min-width="100px" prop="EnumCostType">
              <template slot-scope="{ row }">
                <span>{{ row.enumCostTypeDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="起始日期" align="center" sortable="custom" min-width="100px" prop="StartTime">
              <template slot-scope="{ row }">
                <span v-if="row.startTime">{{ row.startTime |parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="截止日期" align="center" sortable="custom" min-width="100px" prop="EndTime">
              <template slot-scope="{ row }">
                <span v-if="row.endTime">{{ row.endTime |parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="部门" align="center" sortable="custom" min-width="100px" prop="EnumCostType">
              <template slot-scope="{ row }">
                <span>{{ row.departmentNames }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="品规"
              min-width="150px"
              align="center"
              prop="ProductSpecs"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productSpecs }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="120" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i class="el-icon-circle-check eltablei" title="选择" @click="handleCheck(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import ProjectService from '@/api/project'
import Pagination from '@/components/Pagination'

export default {
  components: {
    Pagination
  },
  props: {
    showDialog: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      span: 4,
      listLoading: true,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      list: []
    }
  },
  watch: {
    showDialog: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        if (val === true) {
          this.getList()
        }
      }
    }
  },
  methods: {
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList() {
      this.listLoading = true
      ProjectService.QueryRebateProject(this.listQuery).then(res => {
        this.listLoading = false
        this.list = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(res => {})
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleCheck(row) {
      this.$emit('success', row)
    },
    clear() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
    }
  }
}
</script>
<style scoped>

</style>
