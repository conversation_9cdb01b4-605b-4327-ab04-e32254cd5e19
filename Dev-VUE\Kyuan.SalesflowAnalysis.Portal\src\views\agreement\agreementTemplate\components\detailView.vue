<template>
  <el-dialog
    :close-on-click-modal="false"
    title="协议模板明细"
    :visible="dialogTemplateViewVisible"
    width="60%"
    @close="cancle"
  >
    <el-form :model="agreementTemplateModel" label-position="right" label-width="100px" class="myself-dialogform">
      <el-row :gutter="10" type="flex">
        <el-col :span="12">
          <el-form-item label="模板名称:" prop="name">
            <label>{{ agreementTemplateModel.name }}</label>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模板编码:" prop="code">
            <label>{{ agreementTemplateModel.code }}</label>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="签约方类型:" prop="name">
            <label>{{ agreementTemplateModel.enumSigningPartiesTypeDesc }}</label>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模板类型:" prop="code">
            <label>{{ agreementTemplateModel.rebateAgreementTemplateTypeName }}</label>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态:" prop="EnumStatusDesc">
            <label>{{ agreementTemplateModel.enumStatusDesc }}</label>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="政策条款:" prop="name">
            <label v-html="agreementTemplateModel.rebateAgreementTemplateContentHtml" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button icon="el-icon-close" @click="cancle()">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import AgreementService from '@/api/agreement'

export default {
  components: {
  },
  data() {
    return {
      dialogTemplateViewVisible: false,
      agreementTemplateModel: {
      }
    }
  },
  methods: {
    init(data) {
      this.dialogTemplateViewVisible = true
      const para = { id: data }
      this.GetAgreementTemplate(para)
    },
    GetAgreementTemplate(id) {
      AgreementService.GetAgreementTemplate(id).then((result) => {
        this.agreementTemplateModel = result.data
        this.$forceUpdate()
      })
        .catch((error) => {
          console.log(error)
        })
    },
    cancle() {
      this.dialogTemplateViewVisible = false
      this.agreementTemplateModel = {}
    }
  }
}
</script>
