import HttpApi from './libs/api.request'

const controller = 'Channel'

const api = new HttpApi(controller)

export default {
  QueryChannels(params) {
    return api.get('QueryChannels', params)
  },
  AddChannel(params) {
    return api.post('AddChannel', params)
  },
  ChangeChannel(params) {
    return api.post('ChangeChannel', params)
  },
  StopChannel(params) {
    return api.post('StopProductChannel', params)
  },
  ExportReceiverProductChannel(params) {
    return api.post('ExportReceiverProductChannel',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  GetChannel(params) {
    return api.get('GetChannel', params)
  }
}
