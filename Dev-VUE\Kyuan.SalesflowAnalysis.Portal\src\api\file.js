import HttpApi from './libs/api.request'

const controller = 'File'

const api = new HttpApi(controller)

export default {
  upload(file, formData) {
    const fd = new FormData()

    if (formData) {
      for (const [key, valuefor] of formData) {
        fd.append(key, valuefor)
      }
    }
    fd.append('file', file)
    return api.postForm('upload', fd)
  },
  download(id) {
    return api.get(`DownloadFile/${id}`)
  },
  downloadAttachment(id) {
    return api.post('DownloadAttachment', {
      data: { id: id },
      responseType: 'arraybuffer'
    })
  },
  uploadTemplate(file, formData, controller, method) {
    const uploadController = controller
    const uploadApi = new HttpApi(uploadController)
    const uploadfd = new FormData()
    if (formData) {
      for (const [key, valuefor] of formData) {
        uploadfd.append(key, valuefor)
      }
    }
    uploadfd.append('file', file)
    return uploadApi.postForm(method, uploadfd)
  },
  uploadImages(fileList, formData, controller, method) {
    const uploadController = controller
    const uploadApi = new HttpApi(uploadController)
    const uploadfd = new FormData()
    if (formData) {
      for (const [key, valuefor] of formData) {
        uploadfd.append(key, valuefor)
      }
    }
    for (var i = 0; i < fileList.length; i++) {
      uploadfd.append(`file${i}`, fileList[i].raw)
    }
    return uploadApi.postForm(method, uploadfd)
  },
  downloadAttachmentBusiness(id, objectType) {
    return api.post('DownloadAttachmentWithBusiness', {
      data: { id: id, objectType: objectType },
      responseType: 'arraybuffer'
    })
  }
}
