<!--总销售增长率-->
<template>
  <div class="panel-group">
    <el-card style="height: 310px;">
      <div slot="header" class="clearfix cardHeader">
        <span>总销售额增长率</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleMore">详情</el-button>
      </div>
      <div ref="salesRevenueChart" style="width: 100%; height: 260px;" />
    </el-card>
    <!--弹出dialog:详情页-->
    <el-dialog append-to-body title="总销售额增长率查看" width="60%" :close-on-click-modal="false" :visible="showDialog" @close="cancleDialog()">
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="8">
            <el-date-picker
              v-model="filter.saleDate"
              class="filter-item"
              placeholder="销售月份"
              type="month"
              clearable
              format="yyyy-MM"
              value-format="yyyy-MM"
            />
          </el-col>
          <el-col :span="4">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-search"
              @click="search"
            >
              查询
            </el-button>
          </el-col>
        </el-row>
        <el-row type="flex" :gutter="10" justify="end">
          <el-col :span="3.5">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-download"
              @click="handleExport"
            >
              导出
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table
              :data="queryList"
              stripe
              fit
              border
              highlight-current-row
              style="width: 100%"
              :header-cell-class-name="'tableStyle'"
            >
              <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
              <el-table-column label="销售月份" min-width="150" header-align="center" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.saleDate | parseTime('{y}-{m}') }}</span>
                </template>
              </el-table-column>
              <el-table-column label="销售金额" width="120" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.salesAmount | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column label="同比销售金额" width="120" prop="storesCount" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.yoySalesAmount | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column label="同比增长率" width="120" prop="storesCount" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.yoySalesAmountRate | toTwoNum }}%</span>
                </template>
              </el-table-column>
              <el-table-column label="环比销售金额" width="120" prop="storesCount" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.ringCompareSalesAmount | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column label="环比增长率" width="120" prop="storesCount" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.ringCompareSalesAmountRate | toTwoNum }}%</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleDialog()">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import echarts from 'echarts'
import dashboardService from '@/api/dashboard'
export default {
  data() {
    return {
      filter: {
        timeRanges: [],
        manufacturerId: null,
        specId: null,
        departmentId: null,
        regionId: null,
        provinceId: null,
        projectId: null
      },
      salesGrowthRateLoading: false,
      salesGrowthRateDate: {},
      showDialog: false,
      dataList: [],
      queryList: []
    }
  },
  methods: {
    initPage(param) {
      this.filter = param
      this.getDataBoardSalesGrowth()
    },
    getDataBoardSalesGrowth() {
      this.salesGrowthRateLoading = true
      dashboardService.GetDataBoardSalesGrowth(this.filter)
        .then((res) => {
          this.salesGrowthRateDate = res
          this.salesGrowthRateLoading = false
          this.initChart()
        })
        .catch(() => {
          this.salesGrowthRateLoading = false
        })
    },
    initChart() {
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: function(params) {
            // params 是一个数组，数组中包含每个系列的数据信息
            let result = `${params[0].name}<br/>`
            params.forEach(function(item) {
              // item 是每一个系列的数据
              const seriesName = item.seriesName // 系列名称
              const marker = item.marker // 标志图形
              result += `${marker}${seriesName}: ${item.value.toFixed(2)}<br/>`
            })
            return result
          }
        },
        legend: {
          data: ['当前销售额', '同比销售额', '环比比率']
        },
        grid: {
          left: '15%'
        },
        xAxis: [
          {
            type: 'category',
            data: this.salesGrowthRateDate.salesMonths,
            axisPointer: {
              type: 'shadow'
            },
            axisLabel: {
              interval: 0,
              rotate: 25
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '',
            axisLabel: {
              formatter: '{value} ￥'
            }
          },
          {
            type: 'value',
            name: '',
            axisLabel: {
              formatter: '{value} %'
            }
          }
        ],
        series: [
          {
            name: '当前销售额',
            type: 'bar',
            data: this.salesGrowthRateDate.salesAmounts
          },
          {
            name: '同比销售额',
            type: 'bar',
            data: this.salesGrowthRateDate.yoySalesAmounts
          },
          {
            name: '环比比率',
            type: 'line',
            yAxisIndex: 1,
            data: this.salesGrowthRateDate.ringCompareSalesAmountRates
          }
        ]
      }
      const myChartSalesRevenue = echarts.init(this.$refs.salesRevenueChart)
      myChartSalesRevenue.setOption(option)
    },
    cancleDialog() {
      this.showDialog = false
      this.queryList = []
      this.filter.saleDate = ''
    },
    search() {
      if (this.filter.saleDate) {
        this.queryList = this.dataList.filter(item => {
          return item.saleDate.includes(this.filter.saleDate)
        })
      } else {
        this.queryList = this.dataList
      }
    },
    handleExport() {
      dashboardService.ExportDataBoardSalesGrowth(this.filter)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '总销售额增长率.xlsx'

          fileDownload(result.data, filename)
        })
        .catch(() => {

        })
    },
    indexMethod(index) {
      return (
        index + 1
      )
    },
    handleMore() {
      this.showDialog = true
      this.QuerySalesGrowthDetail()
    },
    QuerySalesGrowthDetail() {
      dashboardService.QuerySalesGrowthDetail(this.filter).then((res) => {
        this.dataList = res
        this.queryList = res
        this.salesGrowthRateLoading = false
        this.initChart()
      })
        .catch(() => {
          this.salesGrowthRateLoading = false
        })
    }
  }
}
</script>

  <style lang="scss" scoped>
  .cardHeader {
    height: 5px;
  }
  </style>

