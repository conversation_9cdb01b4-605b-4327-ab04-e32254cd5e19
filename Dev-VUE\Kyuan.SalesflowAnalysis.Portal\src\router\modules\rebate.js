import Layout from '@/layout'

const rebateRoutes = [
  {
    path: '/rebate',
    component: Layout,
    redirect: '/calculate/index',
    hidden: false,
    meta: {
      title: '返利管理',
      sort: 60,
      icon: 'job'
    },
    children: [
      {
        name: 'AgreementQuery',
        path: 'agreementQuery',
        component: () => import('@/views/agreement/agreementQuery/index'),
        meta: {
          title: '协议管理',
          icon: 'predict',
          sort: 1,
          noCache: false,
          permissions: ['Agreement_AgreementQuery']
        }
      },
      {
        name: 'AgreementRequestApproval',
        path: 'agreementRequestApproval',
        component: () => import('@/views/agreement/requestApproval/index'),
        meta: {
          title: '协议审批',
          icon: 'predict',
          sort: 1,
          noCache: false,
          permissions: ['Agreement_RequestApproval']
        }
      },
      {
        name: 'CertificateQuery',
        path: 'certificateQuery',
        component: () => import('@/views/agreement/certificateQuery/index'),
        meta: {
          title: '一级商名单备忘录',
          icon: 'predict',
          sort: 1,
          noCache: false,
          permissions: ['Agreement_CertificateQuery']
        }
      },
      {
        name: 'RebateResultQuery',
        path: 'rebateResultQuery',
        component: () => import('@/views/rebate/calculateDetail/index'),
        meta: {
          title: '返利计算',
          icon: 'predict',
          sort: 1,
          noCache: false,
          permissions: ['Rebate_RebateResultQuery']
        }
      },
      {
        name: 'RebateResultTracking',
        path: 'rebateResultTracking',
        component: () => import('@/views/rebate/feedback/index'),
        meta: {
          title: '返利跟踪',
          icon: 'link',
          sort: 1,
          noCache: false,
          permissions: ['Rebate_RebateResultTracking']
        }
      },
      {
        name: 'RebateAccruedExpense',
        path: 'rebateAccruedExpense',
        component: () => import('@/views/rebate/accruedExpense/index'),
        meta: {
          title: '返利预提',
          icon: 'nested',
          sort: 3,
          noCache: false,
          permissions: ['Rebate_RebateAccruedExpense']
        }
      },
      {
        name: 'AgreementTemplateQuery',
        path: 'AgreementTemplateQuery',
        component: () => import('@/views/agreement/agreementTemplate/index'),
        meta: {
          title: '协议模板',
          icon: 'predict',
          sort: 1,
          noCache: false,
          permissions: ['Agreement_AgreementQuery']
        }
      },
      {
        name: 'AgreementPolicyTemplate',
        path: 'agreementPolicyTemplate',
        component: () => import('@/views/agreement/policyTemplate/index'),
        meta: {
          title: '政策模板',
          icon: 'predict',
          sort: 1,
          noCache: false,
          permissions: ['Agreement_AgreementPolicyTemplate']
        }
      }
    ]
  }
]

export default rebateRoutes
