<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filter-container">
        <el-col :span="span">
          <el-input
            v-model="filter.rebateAgreementCode"
            clearable
            placeholder="协议编号"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filter.rebateAgreementName"
            clearable
            placeholder="协议名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refDistributorProvinceCity"
            v-model="rebateProvinceCityId"
            :loading="provinceCityLoading"
            :options="provinceCityList"
            class="filter-item"
            placeholder="返利接收方省份/城市"
            clearable
            :props="{ checkStrictly: true, expandTrigger: 'hover' }"
            @change="handleProvinceCityChange"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filter.rebateReceiverName"
            clearable
            placeholder="返利接收方名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProductAndSpec"
            v-model="manufacturerProductAndSpecId"
            style="width: 100%"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            placeholder="厂商/产品/规格"
            clearable
            class="filter-item"
            :props="{ checkStrictly: true, expandTrigger: 'hover' }"
            @change="closeProductCascader"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="filter.principalName"
            clearable
            placeholder="负责人"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select v-model="filter.isWarning" class="filter-item" placeholder="是否警告" clearable>
            <el-option v-for="item in isWarningList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select v-model="filter.isManuallyCalculate" class="filter-item" placeholder="是否手工录入" clearable>
            <el-option v-for="item in isManuallyCalculateList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select v-model="filter.enumStatus" class="filter-item" placeholder="状态" clearable @change="selectUpdate">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.desc" :value="item.value" />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'RebateResultQuery_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="search">
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button v-if="$isPermitted($store.getters.user, 'RebateResultQuery_Button_Add')" class="filter-item-button" type="primary" icon="el-icon-plus" @click="handleCreate">
            新增
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button v-if="$isPermitted($store.getters.user, 'RebateResultQuery_Button_Calculate')" class="filter-item-button" type="primary" icon="el-icon-check" @click="handleCalculate">
            计算
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button v-if="$isPermitted($store.getters.user, 'RebateResultQuery_Button_Push')" type="primary" icon="el-icon-document-checked" @click="handlePushFieldBatchConfirm">
            发送外勤确认
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button v-if="$isPermitted($store.getters.user, 'RebateResultQuery_Button_Export')" :loading="btnExportLoading" type="primary" icon="el-icon-download" @click="onShowExportModal">
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="rebateResultList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column fixed label="序号" type="index" align="center" :index="indexMethod" />
            <el-table-column fixed label="协议编号" align="center" sortable="custom" min-width="120px" prop="RebateAgreement.Code">
              <template slot-scope="{ row }">
                <span>{{ row.rebateAgreementCode }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="返利协议名称" prop="RebateAgreement.Name" sortable="custom" min-width="220px" header-align="center" align="left">
              <template slot-scope="{ row }">
                <span>{{ row.rebateAgreementName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="起始日期" prop="StartDate" align="center" sortable="custom" min-width="100px">
              <template slot-scope="{ row }">
                <span v-if="row.startDate">{{
                  row.startDate | parseTime("{y}-{m}-{d}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="截止日期" prop="EndDate" align="center" sortable="custom" min-width="100px">
              <template slot-scope="{ row }">
                <span v-if="row.endDate">{{
                  row.endDate | parseTime("{y}-{m}-{d}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="返利接收方省份" prop="RebateAgreement.RebateReceiver.Province.NameCn" align="center" sortable="custom" min-width="130px">
              <template slot-scope="{ row }">
                <span>{{ row.rebateReceiverProvince }}</span>
              </template>
            </el-table-column>
            <el-table-column label="返利接收方名称" prop="RebateAgreement.RebateReceiver.Name" align="left" header-align="center" sortable="custom" min-width="220px">
              <template slot-scope="{ row }">
                <span>{{ row.rebateReceiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="产品名称/规格" prop="RebateAgreement.ProductSpecDesc" align="center" sortable="custom" min-width="130px">
              <template slot-scope="{ row }">
                <span>{{ row.productAndSpec }}</span>
              </template>
            </el-table-column>
            <el-table-column label="支付周期" prop="RebateAgreement.PaymentCycle.Name" sortable="custom" min-width="110px" header-align="center" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.rebateAgreementPaymentCycle }}</span>
              </template>
            </el-table-column>
            <el-table-column label="计算周期" prop="EnumRebateComputeCycleFlag" sortable="custom" min-width="100px" header-align="center" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.enumRebateComputeCycleFlagDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="返利金额" prop="RebateAmount" align="center" sortable="custom" min-width="100px">
              <template slot-scope="{ row }">
                <span>{{ row.rebateAmount | toMoney }}元</span>
              </template>
            </el-table-column>
            <el-table-column label="负责人" prop="RebateAgreement.Principal.DisplayName" align="center" sortable="custom" min-width="100px">
              <template slot-scope="{ row }">
                <span>{{ row.principalName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否警告" prop="IsWarning" sortable="custom" min-width="95px" header-align="center" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.isWarningDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="警告原因" prop="WaringMessage" sortable="custom" min-width="100px" header-align="center" align="left">
              <template slot-scope="{ row }">
                <span>{{ row.waringMessage }}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否手工录入" prop="IsManuallyCalculate" sortable="custom" min-width="120px" header-align="center" align="left">
              <template slot-scope="{ row }">
                <span>{{ row.isManuallyCalculate?'是':'否' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" prop="EnumStatus" sortable="custom" min-width="90px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.enumStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="160"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i
                  v-if="row.enumStatus ==
                    $constDefinition.rebateResultStatus.Rejected
                  "
                  class="el-icon-chat-line-round eltablei"
                  title="拒绝原因"
                  @click="handleRejectReason(row)"
                />
                <i
                  v-if="row.isWarning"
                  class="el-icon-finished eltablei"
                  title="清除警告"
                  @click="handleClearWarning(row)"
                />
                <i
                  v-if="$isPermitted($store.getters.user, 'RebateResultQuery_Button_ReCalculate') && (row.enumStatus ===
                    $constDefinition.rebateResultStatus.Calculated ||
                    row.enumStatus ===
                    $constDefinition.rebateResultStatus.Error ||
                    row.enumStatus ===
                    $constDefinition.rebateResultStatus.Rejected) && !row.isManuallyCalculate
                  "
                  class="el-icon-refresh eltablei"
                  title="重新计算"
                  @click="handleReCalculate(row)"
                />
                <i
                  v-if="$isPermitted($store.getters.user, 'RebateResultQuery_Button_Add') && (row.enumStatus ===
                    $constDefinition.rebateResultStatus.Calculated ||
                    row.enumStatus ===
                    $constDefinition.rebateResultStatus.Rejected)
                  "
                  class="el-icon-edit-outline eltablei"
                  title="调整"
                  @click="handleResultAdjust(row)"
                />
                <i
                  v-if="row.enumStatus !=
                    $constDefinition.rebateResultStatus.Calculating &&
                    row.enumStatus !=
                    $constDefinition.rebateResultStatus.Error
                  "
                  class="el-icon-files eltablei"
                  title="涉及流向查看"
                  @click="handleSalesFlowDetail(row)"
                />
                <i
                  v-if="row.enumRebateComputeCycleFlag === $constDefinition.rebateComputeCycleFlag.Year &&
                    row.rebateAgreementPaymentCycleCode === 'QuarterYearPayment'"
                  class="el-icon-document-copy eltablei"
                  title="计算差额"
                  @click="reviewDifferenceView(row)"
                />
                <i
                  v-if="$isPermitted($store.getters.user, 'RebateResultQuery_Button_Push') && row.enumStatus ==
                    $constDefinition.rebateResultStatus.Calculated &&
                    !row.isWarning
                  "
                  class="el-icon-document-checked eltablei"
                  title="推送外勤确认"
                  @click="handlePushFieldConfirm(row)"
                />
                <i class="el-icon-document eltablei" title="查看明细" @click="handleViewDetail(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="filter.pageIndex"
            :limit.sync="filter.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <SalesFlowDetail ref="refSalesFlowDetail" />
    <ResultAdjust ref="refResultAdjust" @success="handleAdjust_callback" />
    <RejectReason ref="refRejectReason" />
    <ResultDetail ref="refResultDetail" />
    <DifferenceView ref="refDifferenceView" />
    <!--导出-->
    <el-dialog
      :visible="showExportModal"
      :close-on-click-modal="false"
      title="导出项目"
      width="800"
      @close="handleExportCancel"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
    <el-dialog
      title="计算任务"
      :close-on-click-modal="false"
      :visible="dialogCalculateResultVisible"
      width="80%"
      class="popup-search"
      @close="closeCalculateResultmportDialog"
    >
      <CalculateResult ref="refCalculateResult" :show-import="dialogCalculateResultVisible" @success="chooseCalculateResult_callback" />
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleChooseCalculateTask"
        >
          确认
        </el-button>
        <el-button icon="el-icon-close" @click="closeCalculateResultmportDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <AddRebateResult ref="dialogAddRebateResult" @refresh="onRefresh()" />
  </div>
</template>
<script>
import RebateService from '@/api/rebate'
import ProductService from '@/api/product'
import LocationService from '@/api/location'
import MasterDataService from '@/api/masterData'
import Pagination from '@/components/Pagination'

import SalesFlowDetail from './components/salesFlowDetail'
import ResultAdjust from './components/resultAdjust'
import RejectReason from './components/rejectReason'
import ResultDetail from './components/resultDetailView'
import DifferenceView from './components/quarterYearDifferenceView'
import CalculateResult from './components/calculateResult'

import CustomExport from '@/components/Export/CustomExport'
import AddRebateResult from './components/addRebateResult'

export default {
  name: 'RebateResult',
  components: {
    Pagination,
    SalesFlowDetail,
    ResultAdjust,
    RejectReason,
    DifferenceView,
    ResultDetail,
    CustomExport,
    AddRebateResult,
    CalculateResult
  },
  data() {
    return {
      span: 4,
      total: 0,
      filter: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      rebateResultList: [],
      statusOptions: [],
      rebateCalculation: {},
      productAndSpecLoading: false,
      productAndSpecList: [],
      manufacturerProductAndSpecId: [],
      provinceCityLoading: false,
      provinceCityList: [],
      rebateProvinceCityId: null,
      isWarningList: [
        {
          value: true,
          label: '是'
        },
        {
          value: false,
          label: '否'
        }
      ],
      isManuallyCalculateList: [
        {
          value: true,
          label: '是'
        },
        {
          value: false,
          label: '否'
        }
      ],
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: [],
      dialogCalculateResultVisible: false
    }
  },
  created() {
    this.initStatusOptions()
    this.initManufacturerAndProductAndSpec()
    this.initProvinceCity()
    this.search()
  },
  methods: {
    initStatusOptions() {
      var param = {
        enumType: 'RebateResultStatus'
      }
      MasterDataService.GetEnumInfos(param)
        .then(result => {
          this.statusOptions = result.data.datas
          this.$forceupdate()
        })
        .catch(() => { })
    },
    initProvinceCity() {
      this.provinceCityLoading = true
      LocationService.QueryProvinceCityCascader()
        .then(result => {
          this.provinceCityList = result
          this.provinceCityLoading = false
        })
        .catch(error => {
          this.provinceCityLoading = false
          console.log(error)
        })
    },
    initManufacturerAndProductAndSpec() {
      this.productAndSpecLoading = true

      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then(result => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch(error => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },

    search() {
      this.filter.pageIndex = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      if (this.rebateProvinceCityId) {
        const [provinceId, cityId] = this.rebateProvinceCityId
        this.filter.receiverProvinceId = provinceId
        this.filter.receiverCityId = cityId
      }
      if (this.manufacturerProductAndSpecId) {
        const [
          manufacturerId,
          productId,
          productSpecId
        ] = this.manufacturerProductAndSpecId
        this.filter.manufacturerId = manufacturerId
        this.filter.productId = productId
        this.filter.productSpecId = productSpecId
      }

      RebateService.QueryRebateResult(this.filter)
        .then(res => {
          this.listLoading = false
          this.rebateResultList = res.data.datas
          this.total = res.data.recordCount
          this.filter.pageIndex = res.data.pageIndex
        })
        .catch(() => {
          this.listLoading = true
        })
    },
    sortChange(column, prop, order) {
      this.filter.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.filter.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.filter.pageSize = val
      this.search()
    },
    selectUpdate() {
      this.$forceUpdate()
    },
    indexMethod(index) {
      return (this.filter.pageIndex - 1) * this.filter.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    closeProductCascader() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    handleProvinceCityChange() {
      this.$refs.refDistributorProvinceCity.dropDownVisible = false
    },
    handlePushFieldConfirm(row) {
      this.$confirm('确定推送外勤确认?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        RebateService.PushFieldConfirm({ id: row.id })
          .then(result => {
            this.listLoading = false
            if (result.succeed) {
              this.getList()
              this.$notice.message('已推送外勤确认', 'success')
            }
          })
          .catch(() => {
            this.listLoading = false
          })
      })
    },
    handlePushFieldBatchConfirm() {
      this.$confirm('是否全部推送外勤确认?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        RebateService.PushFieldBatchConfirm()
          .then(result => {
            this.listLoading = false
            if (result.succeed) {
              this.getList()
              this.$notice.message('已全部推送外勤确认', 'success')
            }
          })
          .catch(() => {
            this.listLoading = false
          })
      })
    },
    handleReCalculate(row) {
      this.$confirm('是否重新计算返利结果?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        RebateService.ReCalculateRebateResult(row)
          .then(result => {
            this.listLoading = false
            if (result.succeed) {
              this.getList()
              this.$notice.message('计算成功', 'success')
            }
          })
          .catch(() => {
            this.listLoading = false
          })
      })
    },
    reviewDifferenceView(row) {
      this.$refs.refDifferenceView.initPage(row.rebateAgreementId)
    },
    handleSalesFlowDetail(row) {
      var data = JSON.parse(JSON.stringify(row))
      this.$refs.refSalesFlowDetail.initPage(data)
    },
    handleResultAdjust(row) {
      var data = JSON.parse(JSON.stringify(row))
      this.$refs.refResultAdjust.init(data)
    },
    handleRejectReason(row) {
      var data = JSON.parse(JSON.stringify(row))
      this.$refs.refRejectReason.init(data)
    },
    handleClearWarning(row) {
      this.$confirm('是否清除该记录警告?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        RebateService.ClearRebateResultWarning(row)
          .then(result => {
            this.listLoading = false
            if (result.succeed) {
              this.getList()
              this.$notice.message('操作成功', 'success')
            }
          })
          .catch(() => {
            this.listLoading = false
          })
      })
    },
    handleAdjust_callback() {
      this.getList()
    },
    handleViewDetail(row) {
      this.$refs.refResultDetail.initPage(row.id, row.rebateAgentType)
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      RebateService.GetRebateResultExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      this.filter.checkedColumns = checkColumns
      RebateService.ExportRebateResultReceiverDetail(this.filter)
        .then(result => {
          this.filter.checkedColumns = null
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '返利结果.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '返利结果.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    handleCreate() {
      this.$refs.dialogAddRebateResult.initPage()
    },
    success() {
      this.search()
    },
    onRefresh() {
      this.search()
    },
    handleCalculate() {
      this.dialogCalculateResultVisible = true
    },
    closeCalculateResultmportDialog() {
      this.$refs.refCalculateResult.clear()
      this.dialogCalculateResultVisible = false
    },
    handleChooseCalculateTask() {
      this.$refs.refCalculateResult.handleCheck()
    },
    chooseCalculateResult_callback(data) {
      RebateService.CalculateRebateResultTask(data)
        .then((result) => {
          if (result.succeed) {
            this.$refs.refCalculateResult.clear()
            this.dialogCalculateResultVisible = false
            this.onRefresh()
          } else {
            this.ShowTip(result)
          }
        })
        .catch(() => {
        })
    }
  }
}
</script>
<style scoped>
.flexwarp {
  display: flex;
  flex-wrap: wrap;
}
</style>
