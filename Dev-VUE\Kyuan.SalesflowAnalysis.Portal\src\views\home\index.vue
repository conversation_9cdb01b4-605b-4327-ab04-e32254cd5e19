<template>
  <div class="dashboard-editor-container search-container-bg">
    <PanelGroup />
    <el-row type="flex" :gutter="10" class="home-flex-container">
      <el-col :span="8">
        <personalCenter />
      </el-col>
      <el-col :span="16">
        <noticeTable />
      </el-col>
      <el-col v-if="homePermission.isSales" :span="12">
        <el-card>
          <div slot="header" class="clearfix">
            <span>销售部流向进度</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="achievedRateMore">查看更多</el-button>
          </div>
          <div>
            <el-row type="flex" :gutter="10">
              <el-col :span="8">
                <el-cascader
                  v-model="productAndSpecId"
                  :options="productAndSpecList"
                  placeholder="产品/规格"
                  class="filter-item"
                  :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
                  @change="productChange"
                />
              </el-col></el-row>
            <div ref="chartView" :style="{width:'100%', height:'210px', padding:'5px,0,0,0'}" />
          </div></el-card>
      </el-col>
      <el-col v-if="homePermission.isSales || homePermission.isSysAdmin || homePermission.isDataAdmin" :span="12">
        <el-card>
          <div slot="header" class="clearfix">
            <span>销售部流向排名</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="rankingMore">查看更多</el-button>
          </div>
          <div>
            <salesRank @specChange="getSalesRankProductSpecId" />
          </div>
        </el-card>
      </el-col>
      <el-col v-if="homePermission.isSales || homePermission.isSysAdmin || homePermission.isDataAdmin" :span="12">
        <el-card>
          <div slot="header" class="clearfix">
            <span>销售部流向环比</span>
          </div>
          <div>
            <el-row type="flex" :gutter="10">
              <el-col :span="8">
                <el-cascader
                  v-model="compareProductAndSpecId"
                  :options="productAndSpecList"
                  placeholder="产品/规格"
                  class="filter-item"
                  :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
                  @change="compareProductChange"
                />
              </el-col>
              <el-col :span="4">
                <el-select
                  v-model="cycleType"
                  style="width: 100%"
                  class="filter-item"
                  @change="cycleTypeChange"
                >
                  <el-option
                    v-for="item in cycleTypeList"
                    :key="item.value"
                    :label="item.desc"
                    :value="item.value"
                  />
                </el-select>
              </el-col>
            </el-row>
            <div ref="compareChartView" :style="{width:'100%', height:'210px', padding:'5px,0,0,0'}" />
          </div></el-card>
      </el-col>
      <el-col v-if="homePermission.isSysAdmin || homePermission.isDataAdmin" :span="12">
        <transaction-table />
      </el-col>
      <el-col v-if="homePermission.isApproval" :span="12">
        <BonusWaitApproval />
      </el-col>
      <el-col :span="12">
        <ComplaintsWaitApproval v-show="homePermission.isDataAdmin || homePermission.isAssistant || homePermission.isSysAdmin" ref="refComplaints" />
      </el-col>
      <el-col v-if="false" :span="12">
        <picPanel />
      </el-col>
      <el-col v-if="homePermission.isDepartmentApproval" :span="12">
        <BonusExamineWaitApproval />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import PanelGroup from './components/PanelGroup'
import TransactionTable from './components/TransactionTable'
import noticeTable from './components/noticeTable'
import BonusWaitApproval from './components/bonusWaitApproval'
import personalCenter from './components/personalCenter'
import picPanel from './components/picPanel'
import salesRank from './components/salesRank'
import ComplaintsWaitApproval from './components/complaintsWaitApproval'
import HomeService from '@/api/home'
import ProductService from '@/api/product'
import BonusExamineWaitApproval from './components/bonusExamineWaitApproval'

export default {
  name: 'Home',
  components: {
    PanelGroup,
    TransactionTable,
    noticeTable,
    BonusWaitApproval,
    ComplaintsWaitApproval,
    personalCenter,
    picPanel,
    salesRank,
    BonusExamineWaitApproval
  },
  data() {
    return {
      achievedRateChart: {},
      homePermission: {},
      achievedRateData: null,
      productAndSpecId: [],
      productAndSpecList: [],
      compareChart: {},
      compareData: null,
      compareProductAndSpecId: [],
      cycleType: 10,
      cycleTypeList: [{
        'value': 10,
        'desc': '月份'
      },
      {
        'value': 20,
        'desc': '季度'
      }],
      salesRankProductSpecId: []
    }
  },
  computed: {
    ...mapState({
      downloadFileUrl: state => state.settings.downloadFileUrl
    })
  },
  created() {
    this.getHomePermission()
  },
  mounted() {
    const _this = this
    window.onresize = () => {
      if (_this.homePermission.isSales) {
        _this.achievedRateChart.resize()
      }
      if (_this.homePermission.isSales || _this.homePermission.isSysAdmin || _this.homePermission.isDataAdmin) {
        _this.compareChart.resize()
      }
    }
  },
  methods: {
    initProductAndSpec() {
      ProductService.QueryProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecList.some(p => {
            if (p.children) {
              if (p.children[0]) {
                this.$nextTick(() => {
                  this.productAndSpecId = [p.value, p.children[0].value]
                  this.compareProductAndSpecId = [p.value, p.children[0].value]
                  if (this.homePermission.isSales) {
                    this.initAchievedRateChart()
                  }
                  if (this.homePermission.isSales || this.homePermission.isSysAdmin || this.homePermission.isDataAdmin) {
                    this.initCompareChart()
                  }
                })
                return true
              }
            }
          })
        })
        .catch((error) => {
          console.log(error)
        })
    },
    achievedRateMore() {
      this.$router.push({ name: 'AchievedReport', query: { fromPage: 'Home', productSpecId: this.productAndSpecId }})
    },
    rankingMore() {
      this.$router.push({ name: 'RankingReport', query: { fromPage: 'Home', productSpecId: this.salesRankProductSpecId }})
    },
    productChange() {
      this.initAchievedRateChart()
    },
    compareProductChange() {
      this.initCompareChart()
    },
    cycleTypeChange() {
      this.initCompareChart()
    },
    initAchievedRateChart() {
      HomeService.QuerySalesAchievedRate({ productSpecId: this.productAndSpecId[1] }).then(res => {
        this.achievedRateData = res.data

        if (this.achievedRateData === undefined) {
          this.achievedRateData = { xData: [], sales: [], quotas: [], achievedRates: [] }
        }
        this.achievedRateChart = this.$echarts.init(this.$refs.chartView)
        var option = {
          backgroundColor: '#FFF',
          color: ['#00CACC', '#BAA1DD', '#09B3F0'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#909399'
              }
            },
            textStyle: {
              fontSize: 10
            }
          },
          toolbox: {
            feature: {
              magicType: { show: true, type: ['line'] },
              restore: { show: true },
              saveAsImage: { show: true }
            },
            showTitle: false,
            itemSize: 12
          },
          calculable: true,
          legend: {
            data: ['流向', '潜力', '进度']
          },
          xAxis: [
            {
              type: 'category',
              data: this.achievedRateData.xData,
              axisPointer: {
                type: 'shadow'
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#606266'
                },
                interval: 0,
                rotate: 15,
                fontSize: 10,
                showMaxLabel: true,
                formatter: function(value) {
                  var res = value
                  if (res.length > 15) {
                    res = res.substring(0, 14) + '..'
                  }
                  return res
                }
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '数量',
              splitLine: {
                show: false
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#606266'
                },
                fontSize: 10
              },
              scale: true
            },
            {
              type: 'value',
              name: '百分比',
              scale: true,
              splitLine: {
                show: false
              },
              axisLabel: {
                formatter: '{value}%',
                fontSize: 10
              }
            }
          ],
          series: [
            {
              name: '流向',
              type: 'bar',
              data: this.achievedRateData.sales,
              barWidth: 20,
              itemStyle: {
                normal: {
                  barBorderRadius: [5, 5, 0, 0] // 圆角
                }
              },
              barGap: '5%'// 间距
            },
            {
              name: '潜力',
              type: 'bar',
              data: this.achievedRateData.quotas,
              barWidth: 20,
              itemStyle: {
                normal: {
                  barBorderRadius: [5, 5, 0, 0] // 圆角
                }
              },
              barGap: '5%'// 间距
            },
            {
              name: '进度',
              type: 'line',
              yAxisIndex: 1,
              barWidth: 20,
              data: this.achievedRateData.achievedRates,
              itemStyle: {
                normal: {
                  barBorderRadius: [5, 5, 0, 0] // 圆角
                }
              },
              barGap: '5%'// 间距
            }
          ],
          textStyle: {
            fontSize: 10
          }
        }

        option && this.achievedRateChart.setOption(option)
      }).catch(error => {
        console.log(error)
      })
    },
    initCompareChart() {
      HomeService.QuerySalesCompare({ productSpecId: this.compareProductAndSpecId[1], salesCompareType: this.cycleType }).then(res => {
        this.compareData = res.data

        if (this.compareData === undefined) {
          this.compareData = { xData: [], currentSales: [], prevSales: [], legends: [] }
        }
        this.compareChart = this.$echarts.init(this.$refs.compareChartView)
        var option = {
          backgroundColor: '#FFF',
          color: ['#00CACC', '#BAA1DD'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#909399'
              }
            },
            textStyle: {
              fontSize: 10
            }
          },
          toolbox: {
            feature: {
              magicType: { show: true, type: ['line'] },
              restore: { show: true },
              saveAsImage: { show: true }
            },
            showTitle: false,
            itemSize: 12
          },
          calculable: true,
          legend: {
            data: this.compareData.legends
          },
          xAxis: [
            {
              type: 'category',
              data: this.compareData.xData,
              axisPointer: {
                type: 'shadow'
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#606266'
                },
                interval: 0,
                rotate: 15,
                fontSize: 10,
                showMaxLabel: true,
                formatter: function(value) {
                  var res = value
                  if (res.length > 15) {
                    res = res.substring(0, 14) + '..'
                  }
                  return res
                }
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '数量',
              splitLine: {
                show: false
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#606266'
                },
                fontSize: 10
              },
              scale: true
            }
          ],
          series: [
            {
              name: this.compareData.legends[0],
              type: 'bar',
              data: this.compareData.prevSales,
              barWidth: 20,
              itemStyle: {
                normal: {
                  barBorderRadius: [5, 5, 0, 0] // 圆角
                }
              },
              barGap: '5%'// 间距
            },
            {
              name: this.compareData.legends[1],
              type: 'bar',
              data: this.compareData.currentSales,
              barWidth: 20,
              itemStyle: {
                normal: {
                  barBorderRadius: [5, 5, 0, 0] // 圆角
                }
              },
              barGap: '5%'// 间距
            }
          ],
          textStyle: {
            fontSize: 10
          }
        }

        option && this.compareChart.setOption(option)
      }).catch(error => {
        console.log(error)
      })
    },
    getHomePermission() {
      HomeService.GetCurrentEmployeeRole().then(res => {
        this.homePermission = res.data

        if (this.homePermission.isAssistant || this.homePermission.isSysAdmin || this.homePermission.isDataAdmin) {
          this.$refs.refComplaints.initPage(this.homePermission)
        }
        this.initProductAndSpec()
      }).catch(error => {
        console.log(error)
      })
    },
    getSalesRankProductSpecId(val) {
      this.salesRankProductSpecId = val
    }
  }
}
</script>
<style scoped>
.box ul {
  display: grid;
  justify-content: space-evenly;
  padding: 10px;
  grid-template-columns: repeat(auto-fill, 160px);
  grid-gap: 10px;
  text-align: center;
}
.box li {
  width:160px;
  list-style: none;
  margin-bottom: 10px;
}

.appImgContainer{
  height: 120px;
  padding :5px 0px;
}

.appImgContainer img{
  width: 100px;
  height: 100px;
}

.appName{
  font-size: 18px;
  font-weight: bold;
}
.home-flex-container{
  flex-wrap: wrap;
}

 .home-flex-container .el-col{
    margin-bottom: 10px;
  }

</style>
