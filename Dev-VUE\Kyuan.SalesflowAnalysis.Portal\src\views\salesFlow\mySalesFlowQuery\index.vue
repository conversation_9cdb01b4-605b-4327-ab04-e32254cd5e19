<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="8">
          <el-date-picker
            v-model="listQuery.checkedMonths"
            clearable
            class="filter-item"
            type="monthrange"
            range-separator="至"
            start-placeholder="导入开始月份"
            end-placeholder="导入结束月份"
            format="yyyy-MM"
            value-format="yyyy-MM"
            unlink-panels
            @change="timeRangeChange"
            @blur="timeRangeChange"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="listQuery.salesDateRange"
            clearable
            class="filter-item"
            type="daterange"
            range-separator="至"
            start-placeholder="销售开始日期"
            end-placeholder="销售结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.manufacturerId"
            :loading="manufacturerLoading"
            class="filter-item"
            placeholder="厂商"
            clearable
            @change="manufacturerChange"
          >
            <el-option
              v-for="item in manufacturerList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProductAndSpec"
            v-model="productAndSpecId"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            placeholder="产品/规格"
            clearable
            class="filter-item"
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            @change="closeProductCascader"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refDistributorProvinceCity"
            v-model="distributorProvinceCityId"
            :loading="provinceCityLoading"
            :options="provinceCityList"
            class="filter-item"
            placeholder="发货方省份/城市"
            clearable
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            @change="closeDistributorCascader"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.distributorName"
            clearable
            placeholder="发货方名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refReceiverProvinceCity"
            v-model="receiverProvinceCityId"
            :loading="provinceCityLoading"
            :options="provinceCityList"
            class="filter-item"
            placeholder="收货方省份/城市"
            clearable
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            @change="closeReceiverCascader"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.receiverName"
            clearable
            placeholder="收货方名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.responsiblePerson"
            clearable
            placeholder="负责人"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.customerCategoryId"
            value-key="value"
            class="filter-item"
            placeholder="客户分类"
            clearable
          >
            <el-option
              v-for="item in customerCategoryList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.isTargetTerminal"
            class="filter-item"
            placeholder="是否目标终端"
            clearable
          >
            <el-option
              v-for="item in isTargetTerminalList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.salesFlowVersion"
            clearable
            placeholder="流向版本号"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'MySalesflow_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'MySalesflow_Button_Export')"
            :loading="btnExportLoading"
            class="filter-item-button"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column label="导入月份" align="center" sortable="custom" min-width="100px" prop="YearMonthSplicing">
              <template slot-scope="{ row }">
                <span>{{ row.yearMonthSplicing }}</span>
              </template>
            </el-table-column>
            <el-table-column label="发货方省份" align="center" sortable="custom" min-width="110px" prop="DistributorProvinceName">
              <template slot-scope="{ row }">
                <span>{{ row.distributorProvinceName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="发货方城市" align="center" sortable="custom" min-width="110px" prop="DistributorCityName">
              <template slot-scope="{ row }">
                <span>{{ row.distributorCityName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="发货方" align="left" sortable="custom" min-width="280px" header-align="center" prop="DistributorName">
              <template slot-scope="{ row }">
                <span>{{ row.distributorName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="收货方省份" align="center" sortable="custom" min-width="110px" prop="ReceiverProvinceName">
              <template slot-scope="{ row }">
                <span>{{ row.receiverProvinceName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="收货方城市" align="center" sortable="custom" min-width="110px" prop="ReceiverCityName">
              <template slot-scope="{ row }">
                <span>{{ row.receiverCityName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="收货方" align="left" sortable="custom" min-width="200px" header-align="center" prop="ReceiverName">
              <template slot-scope="{ row }">
                <span>{{ row.receiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="关联收货方" align="left" sortable="custom" min-width="200px" header-align="center" prop="RelatedReceiverName">
              <template slot-scope="{ row }">
                <span>{{ row.relatedReceiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="收货方负责人" align="center" header-align="center" sortable="custom" min-width="120px" prop="EmployeeDisplayName">
              <template slot-scope="{ row }">
                <span>{{ row.employeeDisplayName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="负责人工号" align="center" header-align="center" sortable="custom" min-width="110px" prop="EmployeeJobNo">
              <template slot-scope="{ row }">
                <span>{{ row.employeeJobNo }}</span>
              </template>
            </el-table-column>
            <el-table-column label="岗位" align="center" header-align="center" sortable="custom" min-width="310px" prop="StationName">
              <template slot-scope="{ row }">
                <span>{{ row.stationName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="厂商" align="center" sortable="custom" min-width="100px" prop="ManufacturerName" header-align="center">
              <template slot-scope="{ row }">
                <span>{{ row.manufacturerName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="产品名称" align="center" sortable="custom" min-width="100px" prop="ProductNameCn">
              <template slot-scope="{ row }">
                <span>{{ row.productNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column label="通用名" align="left" sortable="custom" min-width="150px" header-align="center" prop="ProductCommonName">
              <template slot-scope="{ row }">
                <span>{{ row.productCommonName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="包装规格" align="center" sortable="custom" min-width="100px" prop="Specification">
              <template slot-scope="{ row }">
                <span>{{ row.specification }}</span>
              </template>
            </el-table-column>
            <el-table-column label="批次" align="center" sortable="custom" min-width="100px" prop="BatchNumber">
              <template slot-scope="{ row }">
                <span>{{ row.batchNumber }}</span>
              </template>
            </el-table-column>
            <el-table-column label="产品效期" align="center" sortable="custom" min-width="100px" prop="ExpireDate">
              <template slot-scope="{ row }">
                <span v-if="row.expireDate">{{ row.expireDate | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="销售日期" align="center" sortable="custom" min-width="100px" prop="SaleDate">
              <template slot-scope="{ row }">
                <span>{{ row.saleDate | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="销售数量" align="right" sortable="custom" min-width="100px" header-align="center" prop="Quantity">
              <template slot-scope="{ row }">
                <span>{{ row.quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column label="客户分类" align="center" sortable="custom" min-width="100px" header-align="center" prop="CustomerCategory">
              <template slot-scope="{ row }">
                <span>{{ row.customerCategory }}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否发布" align="center" sortable="custom" min-width="100px" header-align="center" prop="isRelease">
              <template slot-scope="{ row }">
                <span>{{ row.isReleaseDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否目标终端" align="center" sortable="custom" min-width="140px" prop="IsTargetTerminal">
              <template slot-scope="{ row }">
                <span>{{ row.isTargetTerminalDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="版本号" align="center" sortable="custom" min-width="140px" prop="SalesFlowVersion">
              <template slot-scope="{ row }">
                <span>{{ row.salesFlowVersion }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出流向"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import CustomExport from '@/components/Export/CustomExport'
import ManufacturerService from '@/api/manufacturer'
import ProductService from '@/api/product'
import LocationService from '@/api/location'
import SalesFlowService from '@/api/salesFlow'
import MaintenanceService from '@/api/maintenance'

export default {
  name: 'MySalesFlowQuery',
  components: {
    Pagination,
    CustomExport
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      list: [],
      manufacturerLoading: false,
      productAndSpecLoading: false,
      manufacturerList: [],
      productAndSpecList: null,
      productAndSpecId: [],
      provinceCityLoading: false,
      provinceCityList: [],
      distributorProvinceCityId: null,
      receiverProvinceCityId: null,
      btnExportLoading: false,
      dialogEditFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑流向',
        create: '新增流向'
      },
      modifyDialogTitle: '',
      modifyDialogIsReadonly: false,
      itemId: null,
      showExportModal: false,
      columnDictionary: {},
      isTargetTerminalList: [
        {
          value: true,
          label: '是'
        },
        {
          value: false,
          label: '否'
        }
      ],
      isReleaseList: [
        {
          value: true,
          label: '是'
        },
        {
          value: false,
          label: '否'
        }
      ],
      customerCategoryList: []
    }
  },
  created() {
    this.initListQuery()
    this.initManufacturer()
    this.initProductAndSpec()
    this.initProvinceCity()
    this.handleFilter()
    this.initCustomerCategory()
  },
  methods: {
    initListQuery() {
      const year = new Date(Date.now()).getFullYear()
      let month = new Date(Date.now()).getMonth() + 1
      if (month < 10) month = '0' + month
      const ym = year + '-' + month
      this.listQuery.checkedMonths = [ym, ym]
    },
    initManufacturer() {
      this.manufacturerLoading = true
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturerLoading = false
          this.manufacturerList = result
        })
        .catch((error) => {
          this.manufacturerLoading = false
          console.log(error)
        })
    },
    initCustomerCategory() {
      MaintenanceService.QueryAllCustomerCategory()
        .then((result) => {
          this.customerCategoryList = result.data
        })
        .catch((error) => {
          console.log(error)
        })
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      const para = { manufacturerId: this.listQuery.manufacturerId }
      ProductService.QueryProductAndSpecCascader(para)
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    initProvinceCity() {
      this.provinceCityLoading = true
      LocationService.QueryProvinceCityCascader()
        .then((result) => {
          this.provinceCityList = result
          this.provinceCityLoading = false
        })
        .catch((error) => {
          this.provinceCityLoading = false
          console.log(error)
        })
    },
    manufacturerChange() {
      this.initProductAndSpec()
    },
    timeRangeChange() {
      this.$forceUpdate()
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      if (this.productAndSpecId) {
        const [productId, productSpecId] = this.productAndSpecId
        this.listQuery.productId = productId
        this.listQuery.productSpecId = productSpecId
      }
      if (this.distributorProvinceCityId) {
        const [distributorProvinceId, distributorCityId] = this.distributorProvinceCityId
        this.listQuery.distributorProvinceId = distributorProvinceId
        this.listQuery.distributorCityId = distributorCityId
      }
      if (this.receiverProvinceCityId) {
        const [receiverProvinceId, receiverCityId] = this.receiverProvinceCityId
        this.listQuery.receiverProvinceId = receiverProvinceId
        this.listQuery.receiverCityId = receiverCityId
      }
      this.listLoading = true
      SalesFlowService.QueryMySalesFlow(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      SalesFlowService.GetMySalesFlowExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      if (this.productAndSpecId) {
        const [productId, productSpecId] = this.productAndSpecId
        this.listQuery.productId = productId
        this.listQuery.productSpecId = productSpecId
      }
      if (this.distributorProvinceCityId) {
        const [distributorProvinceId, distributorCityId] = this.distributorProvinceCityId
        this.listQuery.distributorProvinceId = distributorProvinceId
        this.listQuery.distributorCityId = distributorCityId
      }
      if (this.receiverProvinceCityId) {
        const [receiverProvinceId, receiverCityId] = this.receiverProvinceCityId
        this.listQuery.receiverProvinceId = receiverProvinceId
        this.listQuery.receiverCityId = receiverCityId
      }
      this.listQuery.checkedColumns = checkColumns
      SalesFlowService.ExportSalesFlowByPermission(this.listQuery)
        .then(result => {
          this.listQuery.checkedColumns = null
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '流向.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '流向.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    closeDistributorCascader() {
      this.$refs.refDistributorProvinceCity.dropDownVisible = false
    },
    closeReceiverCascader() {
      this.$refs.refReceiverProvinceCity.dropDownVisible = false
    },
    closeProductCascader() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    }
  }
}
</script>
