<template>
  <div>
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="错误信息处理"
      :close-on-click-modal="false"
      :visible="errorInfoVisable"
      width="90%"
      @close="closeErrorInfoDialog"
    >
      <el-row>
        <el-col :span="10">
          <el-form
            ref="dataForm"
            :model="handleInfo"
            label-position="right"
            label-width="160px"
            class="el-dialogform"
          >
            <el-form-item label="原始发货方省份">
              <span>{{ orghandleInfo.distributorProvinceName }}</span>
            </el-form-item>
            <el-form-item label="原始发货方城市">
              <span>{{ orghandleInfo.distributorCityName }}</span>
            </el-form-item>
            <el-form-item label="原始发货方名称">
              <span>{{ orghandleInfo.distributorName }}</span>
            </el-form-item>
            <el-form-item label="原始收货方名称">
              <span>{{ orghandleInfo.receiverName }}</span>
            </el-form-item>
            <el-form-item label="原始厂商名称">
              <span>{{ orghandleInfo.manufacturerName }}</span>
            </el-form-item>
            <el-form-item label="原始规格">
              <span>{{ orghandleInfo.commonName }}/{{ orghandleInfo.specificationName }}</span>
            </el-form-item>
            <el-form-item label="原始批次">
              <span>{{ orghandleInfo.batchNumber }}</span>
            </el-form-item>
            <el-form-item label="原始产品效期">
              <span>{{ orghandleInfo.expireDate }}</span>
            </el-form-item>
            <el-form-item label="原始销售日期">
              <span>{{ orghandleInfo.saleDate }}</span>
            </el-form-item>
            <el-form-item label="原始销售数量">
              <span>{{ orghandleInfo.quantity }}</span>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="14">
          <el-form
            ref="dataForm"
            :model="handleInfo"
            label-position="right"
            label-width="120px"
            class="el-dialogform"
          >
            <el-form-item label="发货方省份" required>
              <el-row type="flex" :gutter="5" justify="start">
                <el-col :span="16">
                  <el-select
                    v-model="handleInfo.tempDistributorProvinceId"
                    style="width: 100%"
                    clearable
                    class="filter-item"
                    placeholder="发货方省份"
                    :disabled="!(handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.DistributorProvinceNotExist)"
                    @change="handleProvinceChange"
                  >
                    <el-option
                      v-for="item in provinceList"
                      :key="item.key"
                      :label="item.value"
                      :value="item.key"
                    />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="发货方城市" required>
              <el-row type="flex" :gutter="5" justify="start">
                <el-col :span="16">
                  <el-select
                    v-model="handleInfo.tempDistributorCityId"
                    style="width: 100%"
                    class="filter-item"
                    placeholder="发货方城市"
                    :disabled="!(handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.DistributorCityNotExist)"
                    @change="handleCityChange"
                  >
                    <el-option
                      v-for="item in cityList"
                      :key="item.key"
                      :label="item.value"
                      :value="item.key"
                    />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="发货方名称" required>
              <el-row type="flex" :gutter="5" justify="start">
                <el-col :span="16">
                  <el-input
                    v-model="handleInfo.tempDistributorName"
                    :disabled="!(handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.DistributorNotExist)"
                    readonly
                    placeholder="发货方名称"
                    maxlength="300"
                  />
                </el-col>
                <el-col :span="3.5">
                  <el-button
                    v-if="handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.DistributorNotExist"
                    class="filter-item"
                    type="primary"
                    icon="el-icon-search"
                    title="选择发货方"
                    @click="handleSelectDistributor"
                  >
                    选择
                  </el-button>
                </el-col>
                <el-col :span="3.5">
                  <el-button
                    v-if="handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.DistributorNotExist && handleInfo.distributorName !== undefined && handleInfo.distributorName !== null && handleInfo.distributorName !== ''"
                    class="filter-item"
                    type="primary"
                    icon="el-icon-plus"
                    title="新增发货方"
                    @click="handleAddDistributor"
                  >
                    新增
                  </el-button>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="标准收货方名称" required>
              <el-row type="flex" :gutter="5" justify="start">
                <el-col :span="16">
                  <el-input
                    v-model="handleInfo.tempReceiverName"
                    readonly
                    :disabled="!(handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.ReceiverrNotExist)"
                    placeholder="标准收货方名称"
                    maxlength="300"
                  />
                </el-col>
                <el-col :span="3.5">
                  <el-button
                    v-if="handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.ReceiverrNotExist"
                    class="filter-item"
                    type="primary"
                    title="选择收货方"
                    icon="el-icon-search"
                    @click="handleSelectReceiver"
                  >
                    选择
                  </el-button>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="厂商名称" required>
              <el-row type="flex" :gutter="5" justify="start">
                <el-col :span="16">
                  <el-select
                    v-model="handleInfo.tempManufacturerId"
                    style="width: 100%"
                    class="filter-item"
                    placeholder="厂商名称"
                    :disabled="!(handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.ManufacturerNotExist)"
                    @change="handleManufacturerChange"
                  >
                    <el-option
                      v-for="item in manufacturerList"
                      :key="item.key"
                      :label="item.value"
                      :value="item.key"
                    />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="标准产品规格" required>
              <el-row type="flex" :gutter="5" justify="start">
                <el-col :span="16">
                  <el-cascader
                    ref="refProductAndSpec"
                    :key="cascaderKey"
                    v-model="handleInfo.productAndSpecId"
                    style="width:100%"
                    :options="productAndSpecList"
                    :props="{checkStrictly: false ,expandTrigger: 'hover' }"
                    placeholder="标准产品"
                    :disabled="!(handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.ProductNotExist)"
                    @change="handleProductChange"
                  />
                </el-col>
                <el-col :span="3.5">
                  <el-button
                    v-if="handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.ProductNotExist"
                    class="filter-item"
                    type="primary"
                    icon="el-icon-plus"
                    title="新增产品规格"
                    @click="showAddSpec"
                  >
                    新增
                  </el-button>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="批次">
              <el-row type="flex" :gutter="5" justify="start">
                <el-col :span="16">
                  <el-input
                    v-model="handleInfo.tempBatchNumber"
                    clearable
                    placeholder="批次"
                    maxlength="50"
                  />
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="产品效期">
              <el-row type="flex" :gutter="5" justify="start">
                <el-col :span="16">
                  <el-date-picker
                    v-model="handleInfo.tempExpireDate"
                    style="width:100%"
                    type="date"
                    clearable
                    :disabled="!(handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.ExpireDateError)"
                    placeholder="产品效期"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    @input="dateChange"
                  />
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="销售日期" required>
              <el-row type="flex" :gutter="5" justify="start">
                <el-col :span="16">
                  <el-date-picker
                    v-model="handleInfo.tempSaleDate"
                    style="width:100%"
                    type="date"
                    clearable
                    :disabled="!(handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.SaleDateError) && !(handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.SaleDateNotExist) && !(handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.SaleDateRequired) "
                    placeholder="销售日期"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    @input="dateChange"
                  />
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="销售数量" required>
              <el-row type="flex" :gutter="5" justify="start">
                <el-col :span="16">
                  <el-input
                    v-model="handleInfo.tempQuantity"
                    :disabled="!(handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.QuantityError) && !(handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.QuantityRequired)"
                    clearable
                    placeholder="销售数量"
                  />
                </el-col>
              </el-row>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeErrorInfoDialog">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleSaveSalesFlow"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="el-dialog-ls"
      append-to-body
      title="选择发货方"
      :close-on-click-modal="false"
      :visible="dialogDistributorVisible"
      width="90%"
      @close="closeDistributorDialog"
    >
      <SelectDistributor ref="refDistributor" :show-dialog="dialogDistributorVisible" :distributor-types="distributorTypes" @success="selectDistributorSuccess" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeDistributorDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <AddReceiver ref="refAddDistributor" @success="addDistributorSuccess" />
    <el-dialog
      custom-class="el-dialog-ls"
      append-to-body
      title="选择收货方"
      :close-on-click-modal="false"
      :visible="dialogReceiverVisible"
      width="90%"
      class="popup-search"
      @close="closeReceiverDialog"
    >
      <SelectReceiver ref="refReceiver" :show-dialog="dialogReceiverVisible" :distributor-types="distributorTypes" @success="selectReceiverSuccess" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeReceiverDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>

    <AddSpec ref="refAddSpec" @success="addSpecSuccess" />
  </div>

</template>
<script>
import SalesFlowService from '@/api/salesFlow'
import LocationService from '@/api/location'
import ManufacturerService from '@/api/manufacturer'
import ProductService from '@/api/product'
import SelectReceiver from '@/views/components/selectReceiver'
import SelectDistributor from '@/views/components/selectDistributor'
import AddReceiver from './addReceiver'
import AddSpec from './addSpecification'
import moment from 'moment'

export default {
  components: {
    SelectDistributor,
    SelectReceiver,
    AddReceiver,
    AddSpec
  },
  props: {
    handleId: {
      type: String,
      default: ''
    },
    templateId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      errorInfoVisable: false,
      dialogDistributorVisible: false,
      productAndSpecList: [],
      distributorTypes: [],
      handleInfo: { tempDistributorCityId: null, tempReceiverName: '', tempSaleDate: '' },
      provinceList: [],
      cityList: [],
      allCityList: [],
      manufacturerList: [],
      dialogReceiverVisible: false,
      importHandleId: '',
      orghandleInfo: {},
      isSelectReceiver: false,
      cascaderKey: 0
    }
  },
  methods: {
    initPage(tempImportHandleId) {
      this.importHandleId = tempImportHandleId
      this.initProvince()
      this.initCity()
      this.initManufacturer()
      this.getSalesFlowHandle(tempImportHandleId)
      this.errorInfoVisable = true
    },
    initProvince() {
      LocationService.QueryProvince().then((result) => { this.provinceList = result })
    },
    initCity() {
      LocationService.QueryCity().then((result) => {
        this.allCityList = result
        this.cityList = result
        if (this.handleInfo.tempDistributorProvinceId) {
          this.cityList = this.allCityList.filter(item => { return item.parentKey === this.handleInfo.tempDistributorProvinceId })
        }
      })
    },
    initManufacturer() {
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturerList = result
        })
        .catch(() => {
        })
    },
    initProductAndSpec(val) {
      ++this.cascaderKey
      const para = { manufacturerId: val }
      ProductService.QueryProductCommonNameAndSpecCascader(para)
        .then((result) => {
          this.productAndSpecList = result
        })
        .catch(() => {
        })
    },
    handleProvinceChange() {
      this.cityList = this.allCityList.filter(item => { return item.parentKey === this.handleInfo.tempDistributorProvinceId })
      this.handleInfo.tempDistributorCityId = ''
      this.$forceUpdate()
    },
    handleCityChange() {
      this.$forceUpdate()
    },
    getSalesFlowHandle(val) {
      SalesFlowService.GetImportSalesFlowHandle({
        id: val
      }).then(res => {
        this.handleInfo = res.data
        this.orghandleInfo = this.$deepClone(res.data)
        if ((this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.DistributorNotExist) > 0) {
          this.handleInfo.tempDistributorName = ''
        }
        if (this.handleInfo.tempManufacturerId !== undefined) {
          this.initProductAndSpec(this.handleInfo.tempManufacturerId)
        }
        if (this.handleInfo.tempDistributorProvinceId !== null && this.handleInfo.tempDistributorProvinceId !== undefined) {
          this.cityList = this.allCityList.filter(item => { return item.parentKey === this.handleInfo.tempDistributorProvinceId })
        }
        if ((this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.SaleDateError) > 0 ||
        (this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.SaleDateRequired) > 0 ||
        (this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.SaleDateNotExist) > 0 ||
         this.handleInfo.tempSaleDate === '') {
          this.handleInfo.tempSaleDate = ''
        } else {
          this.handleInfo.tempSaleDate = moment(this.handleInfo.tempSaleDate).format('YYYY-MM-DD')
        }

        const isdate = moment.isDate(new Date(this.handleInfo.tempExpireDate))
        if ((this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.ExpireDateError) > 0 || !isdate || this.handleInfo.tempExpireDate === undefined || this.handleInfo.tempExpireDate === null || this.handleInfo.tempExpireDate === '') {
          this.handleInfo.tempExpireDate = ''
        } else {
          this.handleInfo.tempExpireDate = moment(this.handleInfo.tempExpireDate).format('YYYY-MM-DD')
        }
      }).catch(res => {})
    },
    handleSelectDistributor() {
      this.distributorTypes = [10, 20]
      this.dialogDistributorVisible = true
    },
    handleSelectReceiver() {
      this.distributorTypes = [20, 30]
      this.dialogReceiverVisible = true
    },
    closeDistributorDialog() {
      this.dialogDistributorVisible = false
      this.$refs.refDistributor.clear()
      this.distributorTypes = []
    },
    closeReceiverDialog() {
      this.dialogReceiverVisible = false
      this.$refs.refReceiver.clear()
      this.distributorTypes = []
    },
    selectDistributorSuccess(val) {
      this.handleInfo.tempDistributorName = val.name
      this.handleInfo.tempDistributorId = val.id
      this.closeDistributorDialog()
    },
    selectReceiverSuccess(val) {
      this.handleInfo.tempReceiverName = val.name
      this.handleInfo.tempReceiverId = val.id
      this.isSelectReceiver = true
      this.closeReceiverDialog()
    },
    handleAddDistributor() {
      var location = []
      if (this.handleInfo.tempDistributorProvinceId !== null) {
        location = [this.handleInfo.tempDistributorProvinceId]
      }
      if (this.handleInfo.tempDistributorCityId != null) {
        location = [this.handleInfo.tempDistributorProvinceId, this.handleInfo.tempDistributorCityId]
      }
      this.$refs.refAddDistributor.initPage('新增发货方', this.importHandleId, this.orghandleInfo.distributorName, location)
    },
    handleManufacturerChange() {
      this.initProductAndSpec(this.handleInfo.tempManufacturerId)
    },
    addDistributorSuccess(val) {
      this.handleInfo.tempDistributorName = val.name
      this.handleInfo.tempDistributorId = val.id
      this.$forceUpdate()
    },
    closeErrorInfoDialog() {
      this.errorInfoVisable = false
      this.isSelectReceiver = false
      this.handleInfo = { tempDistributorCityId: '' }
      this.$refs.dataForm.resetFields()
    },
    handleSaveSalesFlow() {
      if ((this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.DistributorProvinceNotExist) > 0 &&
       (this.handleInfo.tempDistributorProvinceId === undefined || this.handleInfo.tempDistributorProvinceId === null)) {
        this.showMessage('请选择发货方省份', 'error')
      } else if ((this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.DistributorCityNotExist) > 0 &&
      (this.handleInfo.tempDistributorCityId === undefined || this.handleInfo.tempDistributorCityId === null)) {
        this.showMessage('请选择发货方城市', 'error')
      } else if ((this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.DistributorNotExist) > 0 &&
      (this.handleInfo.tempDistributorId === undefined || this.handleInfo.tempDistributorId === null)) {
        this.showMessage('请选择发货方', 'error')
      } else if ((this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.ReceiverrNotExist) > 0 &&
      (this.handleInfo.tempReceiverId === undefined || this.handleInfo.tempReceiverId === null)) {
        this.showMessage('请选择标准收货方', 'error')
      } else if ((this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.ManufacturerNotExist) > 0 &&
      (this.handleInfo.tempManufacturerId === undefined || this.handleInfo.tempManufacturerId === null)) {
        this.showMessage('请选择厂商', 'error')
      } else if ((this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.ProductNotExist) > 0 &&
      (this.handleInfo.productAndSpecId === null || this.handleInfo.productAndSpecId.length === 0 || (this.handleInfo.productAndSpecId.length > 0 && (this.handleInfo.productAndSpecId[0] === null || this.handleInfo.productAndSpecId[1] === null)))) {
        this.showMessage('请选择标准产品规格', 'error')
      } else if (((this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.SaleDateError) > 0 ||
                 (this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.SaleDateNotExist) > 0 ||
                 (this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.SaleDateRequired) > 0) &&
                 (this.handleInfo.tempSaleDate === undefined || this.handleInfo.tempSaleDate === null || this.handleInfo.tempSaleDate === '')) {
        this.showMessage('请选择销售日期', 'error')
      } else if (((this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.QuantityError) > 0 ||
      (this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.QuantityRequired) > 0) &&
      (this.handleInfo.tempQuantity === undefined || this.handleInfo.tempQuantity === null)) {
        this.showMessage('请填写销售数量', 'error')
      } else if
      (((this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.QuantityError) > 0 ||
      (this.handleInfo.enumErrorSalesFlowType & this.$constDefinition.errorSalesFlowType.QuantityRequired) > 0) &&
          !/^-*\d+(\.\d{0,2}){0,1}$/.test(this.handleInfo.tempQuantity)) {
        this.showMessage('销售数量仅支持两位小数的数字', 'error')
      } else {
        if (this.isSelectReceiver && this.handleInfo.receiverName !== undefined && this.handleInfo.receiverName !== '') {
          // 如果选择收货方，验证收货方是否存在以当前原始收货方命名的别名，如果有不提示创建别名
          this.$confirm('是否使用原始收货方名称为所选标准收货方创建别名？', '提示', {
            distinguishCancelAndClose: true, // 区分【否】选项和【X】按钮
            confirmButtonText: '是',
            cancelButtonText: '否',
            type: 'warning'
          }).then(() => {
            this.checkCycle(true)
          }).catch((action) => {
            // 选择否时的操作
            if (action === 'cancel') {
              this.checkCycle(false)
            }
            // 点击X关闭，取消操作，停留在当前页面继续修改
          })
        } else {
          this.checkCycle(false)
        }
      }
    },
    checkCycle(val) {
      SalesFlowService.HandleSalesFlowIsOtherCycle(this.handleInfo).then((res) => {
        if (res.data) {
          this.$confirm('所选销售日期不在当前版本号所关联的销售月份范围内，当此版本号被覆盖时本流向将一同被覆盖，是否确认修改？', '提示', {
            distinguishCancelAndClose: true, // 区分【否】选项和【X】按钮
            confirmButtonText: '是',
            cancelButtonText: '否',
            type: 'warning'
          }).then(() => {
            this.saveData(val)
          }).catch((action) => {
          })
        } else {
          this.saveData(val)
        }
      })
    },
    saveData(val) {
      // 所选销售日期不在导入版本关联周期范围内，后期在对该版本覆盖时，该条流向会一同被覆盖
      this.handleInfo.isCreateAlias = val
      SalesFlowService.HandleSalesFlow(this.handleInfo).then((result) => {
        if (result.succeed) {
          this.closeErrorInfoDialog()
          if (result.data.enumErrorSalesFlowType !== 0) {
            this.showMessage('此流向还存在其他错误信息待处理，请稍后继续处理', 'info')
          } else {
            this.showMessage('处理成功', 'success')
          }
          this.$emit('success')
        } else {
          this.ShowTip(result)
        }
      })
    },
    showAddSpec() {
      this.$refs.refAddSpec.initPage(this.handleInfo)
    },
    addSpecSuccess(val) {
      this.initProductAndSpec(this.handleInfo.tempManufacturerId)
      if (this.handleInfo.tempManufacturerId === val.manufacturerId) {
        this.handleInfo.productAndSpecId = [val.productId, val.id]
        this.handleInfo.tempProductId = this.handleInfo.productAndSpecId[0]
        this.handleInfo.tempProductSpecId = this.handleInfo.productAndSpecId[1]
      } else {
        this.handleInfo.productAndSpecId = []
      }
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },
    handleProductChange() {
      if (this.handleInfo.productAndSpecId !== null && this.handleInfo.productAndSpecId !== undefined && this.handleInfo.productAndSpecId.length > 1) {
        this.handleInfo.tempProductId = this.handleInfo.productAndSpecId[0]
        this.handleInfo.tempProductSpecId = this.handleInfo.productAndSpecId[1]
      }
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    dateChange(val) {
      this.$forceUpdate()
    }
  }
}
</script>
<style scoped>
 .el-dialog-s {
  z-index: 11;
  }
  .el-dialog-ls {
  z-index: 12;
  }
  .el-dialogform {
    width: 100%!important;
    margin-left: 0px;
}
</style>
