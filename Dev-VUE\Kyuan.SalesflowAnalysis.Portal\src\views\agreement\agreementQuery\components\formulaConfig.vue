<template>
  <el-dialog
    :title="title"
    width="60%"
    :close-on-click-modal="false"
    :visible="true"
    append-to-body
    @close="cancle()"
  >
    <el-form>
      <el-form-item label="" :label-width="formLabelWidth">
        <div
          id="dialogTxtCon"
          ref="dialogTxtCon"
          class="inputsx"
          placeholder="请配置公式"
          contentEditable="true"
          @blur="textHtmlChanged"
          @keyup="keyUpVariable"
          v-html="htmlText"
        />
      </el-form-item>
    </el-form>
    <el-row :gutter="10" style="margin-top: 15px">
      <el-col :span="8">
        <el-card
          class="wait-task-user-box-card"
          shadow="never"
          :body-style="{ padding: '10px' }"
          style="height:330px;"
        >
          <template v-slot:header>
            <span class="font-small">基础运算符</span>
          </template>
          <el-row :gutter="5">
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addNumber('1', '1')"
            >1</el-button></el-col>
            <el-col
              align="center"
              :span="span"
            ><el-button
              class="numberButton"
              @click="addNumber('2', '2')"
            >2</el-button></el-col>
            <el-col
              align="center"
              :span="span"
            ><el-button
              class="numberButton"
              @click="addNumber('3', '3')"
            >3</el-button></el-col>
            <el-col
              align="center"
              :span="span"
            ><el-button
              class="numberButton"
              @click="addNumber('4', '4')"
            >4</el-button></el-col>
            <el-col
              align="center"
              :span="span"
            ><el-button
              class="numberButton"
              @click="addNumber('5', '5')"
            >5</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addNumber('6', '6')"
            >6</el-button></el-col>
            <el-col
              align="center"
              :span="span"
            ><el-button
              class="numberButton"
              @click="addNumber('7', '7')"
            >7</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addNumber('8', '8')"
            >8</el-button></el-col>
            <el-col
              align="center"
              :span="span"
            ><el-button
              class="numberButton"
              @click="addNumber('9', '9')"
            >9</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addNumber('0', '0')"
            >0</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addNumber('.', '.')"
            >.</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('+', '+')"
            >+</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('-', '-')"
            >-</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('*', '×')"
            >×</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('/', '÷')"
            >÷</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addBracket('(')"
            >(</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addBracket(')')"
            >)</el-button></el-col>
            <el-col
              v-if="displayCondition"
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('>', '>')"
            >></el-button></el-col>
            <el-col
              v-if="displayCondition"
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('<', '<')"
            ><</el-button></el-col>
            <el-col
              v-if="displayCondition"
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('≥', '≥')"
            >≥</el-button></el-col>
            <el-col
              v-if="displayCondition"
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('≤', '≤')"
            >≤</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('=', '=')"
            >=</el-button></el-col>
            <el-col
              v-if="displayCondition"
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('≠', '≠')"
            >≠</el-button></el-col>
          </el-row>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card
          class="wait-task-user-box-card"
          shadow="never"
          :body-style="{ padding: '10px' }"
          style="height:180px;"
        >
          <template v-slot:header>
            <span class="font-small">数学函数</span>
          </template>
          <el-row :gutter="5">
            <el-col
              :span="funSpan"
              align="center"
            >
              <el-button
                class="numberButton"
                @click="addFunction('Math.Count()')"
              >
                Count
              </el-button>
            </el-col>
            <el-col
              :span="funSpan"
              align="center"
            >
              <el-button
                class="numberButton"
                @click="addFunction('Math.Sum()')"
              >
                Sum
              </el-button>
            </el-col>
            <el-col
              :span="funSpan"
              align="center"
            >
              <el-button
                class="numberButton"
                @click="addFunction('Math.Avg()')"
              >
                Avg
              </el-button>
            </el-col>
            <el-col
              :span="funSpan"
              align="center"
            >
              <el-button
                class="numberButton"
                @click="addFunction('Math.Max()')"
              >
                Max
              </el-button>
            </el-col>
            <el-col
              :span="funSpan"
              align="center"
            >
              <el-button
                class="numberButton"
                @click="addFunction('Math.Min()')"
              >
                Min
              </el-button>
            </el-col>
            <el-col
              :span="funSpan"
              align="center"
            >
              <el-button
                class="numberButton"
                @click="addFunction('int.Parse()')"
              >
                Int
              </el-button>
            </el-col>
            <el-col
              v-if="displayCondition"
              :span="funSpan"
              align="center"
            >
              <el-button
                class="numberButton"
                @click="addFunction('and()')"
              >
                And
              </el-button>
            </el-col>
            <el-col
              v-if="displayCondition"
              :span="funSpan"
              align="center"
            >
              <el-button
                class="numberButton"
                @click="addFunction('or()')"
              >
                Or
              </el-button>
            </el-col>
          </el-row>
        </el-card>
        <el-card
          class="wait-task-user-box-card"
          shadow="never"
          :body-style="{ padding: '10px' }"
          style="margin-top:10px;height:140px;"
        >
          <template v-slot:header>
            <span class="font-small">变量说明</span>
          </template>
          <el-row :gutter="5">
            <div class="font-small" style="height:15px;color:grey">
              {{ variableRemark }}
            </div>
          </el-row>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card
          shadow="never"
          class="wait-task-user-box-card"
          :body-style="{ padding: '10px 2px' }"
          style="height:330px;"
        >
          <template v-slot:header>
            <span class="font-small">系统变量</span>
          </template>
          <el-collapse v-for="(item, index) in tempList" :key="index" accordion>
            <el-collapse-item
              :title="item.name"
              class="process-collapse"
              :name="item.id + index"
            >
              <el-table
                :data="baseFormVariableList"
                :show-header="false"
                border
                fit
                highlight-current-row
                style="width: 100%;"
                @cell-mouse-enter="handleMouseEnter"
                @cell-mouse-leave="handleMouselLeave"
              >
                <el-table-column
                  prop="name"
                  label="变量名"
                  header-align="center"
                  align="left"
                >
                  <template slot-scope="{ row }">
                    <el-button
                      size="small"
                      style="width:99%"
                      @click="addVariable(row.name)"
                    >{{ row.name }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
            <!-- <el-collapse-item title="基础变量表单">
              <el-table
                :data="baseFormVariableList"
                :show-header="false"
                border
                fit
                highlight-current-row
                style="width: 100%;"
                @cell-mouse-enter="handleMouseEnter"
                @cell-mouse-leave="handleMouselLeave"
              >
                <el-table-column
                  prop="name"
                  label="变量名"
                  header-align="center"
                  align="left"
                >
                  <template slot-scope="{ row }">
                    <el-button
                      size="small"
                      style="width:99%"
                      @click="addVariable(row.name)"
                    >{{ row.name }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
            <el-collapse-item title="检索汇总变量">
              <el-table
                :data="searchSummaryVariableList"
                :show-header="false"
                border
                fit
                highlight-current-row
                style="width: 100%;"
                @cell-mouse-enter="handleMouseEnter"
                @cell-mouse-leave="handleMouselLeave"
              >
                <el-table-column
                  prop="name"
                  label="变量名"
                  header-align="center"
                  align="left"
                >
                  <template slot-scope="{ row }">
                    <el-button
                      size="small"
                      style="width:99%"
                      @click="addVariable(row.name)"
                    >{{ row.name }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
            <el-collapse-item title="运算结果变量">
              <el-table
                :data="operationResultVariableList"
                :show-header="false"
                border
                fit
                highlight-current-row
                style="width: 100%;"
                @cell-mouse-enter="handleMouseEnter"
                @cell-mouse-leave="handleMouselLeave"
              >
                <el-table-column
                  prop="name"
                  label="变量名"
                  header-align="center"
                  align="left"
                >
                  <template slot-scope="{ row }">
                    <el-button
                      size="small"
                      style="width:99%"
                      @click="addVariable(row.name)"
                    >{{ row.name }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item> -->
          </el-collapse>
        </el-card>
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button icon="el-icon-close" @click="cancle()"> 关闭 </el-button>
      <el-button
        type="primary"
        icon="el-icon-check"
        @click="handleSaveFormula()"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import BonusService from '@/api/bonus'
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    displayCondition: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      userDefinedVariableTypeList: [],
      selectUserDefinedVariable: '',
      formulaSymbolList: [],
      formula: '',
      formulaDisplay: '',
      environmentVariableList: [],
      tempList: [{ 'name': 'groupName1', 'groupValue': [{ 'name': '销量指标', 'remark': '销量指标' }, { 'name': '供货价' }, { 'name': '返点比例' }, { 'name': '指标总数量' }, { 'name': '季度指标数量' }, { 'name': '补偿单价' }, { 'name': '指标金额' }] }, { 'name': 'groupName2', 'groupValue': [{ 'name': '销量指标', 'remark': '销量指标' }, { 'name': '供货价' }, { 'name': '返点比例' }, { 'name': '指标总数量' }, { 'name': '季度指标数量' }, { 'name': '补偿单价' }, { 'name': '指标金额' }] }],
      baseFormVariableList: [{ 'name': '销量指标', 'remark': '销量指标' }, { 'name': '供货价' }, { 'name': '返点比例' }, { 'name': '指标总数量' }, { 'name': '季度指标数量' }, { 'name': '补偿单价' }, { 'name': '指标金额' }],
      searchSummaryVariableList: [{ 'name': '进货数量' }, { 'name': '销售数量' }, { 'name': '目标终端销售数量' }, { 'name': '达标门店销售数量' }, { 'name': '门店达标标准(盒数)' }, { 'name': '满赠销售量' }],
      operationResultVariableList: [{ 'name': '达标门店数量(家)' }, { 'name': '销售数量达成率' }, { 'name': '销售金额达成率' }, { 'name': '销售额合计' }],
      baseOperator: ['+', '-', '*', '/', '>', '<', '≥', '≤'],
      numberList: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
      bracketList: ['(', ')'],
      variableRemark: '请把光标移动到右侧变量上',
      span: 6,
      funSpan: 8,
      searchValue: '',
      bonusUserDefinedVariable: {
        name: '',
        code: '',
        enumType: 10,
        displayText: '',
        expression: '',
        remark: ''
      },
      formLabelWidth: '240',
      htmlText: '',
      logicSymbols: [
        {
          itemName: '本金',
          itemCode: '1'
        },
        {
          itemName: '年利率',
          itemCode: '2'
        },
        {
          itemName: '利润',
          itemCode: '3'
        },
        {
          itemName: '计算规则1',
          itemCode: '4'
        }
      ],
      oldInputValue: ''
    }
  },
  created() {
    // this.initEnvironmentVariable()
    // this.initUserDefinedVariable()
  },
  methods: {
    initEnvironmentVariable() {
      BonusService.QueryBonusEnvironmentVariable()
        .then((result) => {
          this.environmentVariableList = JSON.parse(JSON.stringify(result))
        })
        .catch((error) => {
          console.log(error)
        })
    },
    initUserDefinedVariable() {
      BonusService.QueryBonusUserDefinedVariable()
        .then((result) => {
          this.baseFormVariableList = JSON.parse(JSON.stringify(result))
        })
        .catch((error) => {
          console.log(error)
        })
    },
    addNumber(key, value) {
      this.initFormula(key)
    },
    addSymbol(key, value) {
      this.initFormula(` ${key} `)
    },
    addBracket(key) {
      this.initFormula(` ${key} `)
    },
    addFunction(key) {
      this.initFormula(` ${key} `)
    },
    deleteSymbol() {
      if (this.formulaSymbolList.length !== 0) {
        const lastSymbol = this.formulaSymbolList.pop()

        this.formula = this.formula.slice(
          0,
          this.formula.lastIndexOf(lastSymbol.split(';')[0]) - 1
        )
        this.formulaDisplay = this.formulaDisplay.slice(
          0,
          this.formulaDisplay.lastIndexOf(lastSymbol.split(';')[1])
        )
      }
    },
    handleMouseEnter(row) {
      // this.variableRemark = row.remark
    },
    handleMouselLeave() {
      // this.variableRemark = ''
      /* this.$refs.dialogTxtCon.focus()
      document.execCommand('selectAll', false, null)
      document.getSelection().collapseToEnd() */
    },
    addVariable(name) {
      const html = `<span contentEditable="false">【${name}】</span>`
      this.initFormula(html)
    },
    initFormula(html) {
      this.$refs.dialogTxtCon.focus() // focus 输入框，否则会跟随光标追加 html
      const sel = window.getSelection()
      // Selection 选区对象，表示用户选择的文本范围或光标的当前位置
      if (sel.getRangeAt && sel.rangeCount) {
        let range = sel.getRangeAt(0) // 包含当前选区内容的区域对象
        range.deleteContents() // Range.deleteContents(): 移除来自 Document 的 Range 内容
        const el = document.createElement('div') // 生成一个 div
        el.innerHTML = html // div 里的内容为上面定义的要添加的元素
        const frag = document.createDocumentFragment()
        let node, lastNode
        while ((node = el.firstChild)) {
          // 把生成 div 里的内容，即 html，移到 frag 里
          lastNode = frag.appendChild(node)
        }
        range.insertNode(frag)

        if (lastNode) {
          range = range.cloneRange()
          range.setStartAfter(lastNode)
          range.collapse(true)
          sel.removeAllRanges()
          sel.addRange(range)
        }
      }

      this.oldInputValue = this.$refs.dialogTxtCon.innerHTML
    },
    handleSaveFormula() {
      const tempformulaStr = this.$refs.dialogTxtCon.innerText.replace(/\s+/g, '')

      if (tempformulaStr.length <= 0) {
        this.$notice.message('请输入公式后再保存', 'warning')
        return
      }

      if (!this.formulaVerify(tempformulaStr)) {
        this.$notice.message('运算公式配置不正确', 'error')
        return
      }
      // this.$emit('success', tempformulaStr)
      // this.cancle()
    },
    cancle() {
      this.$emit('hidden')
    },
    textHtmlChanged(e) {

    },
    // 限制键盘输入，除了删除键
    keyUpVariable(e) {
      if (e.code !== 'Backspace') {
        e.target.innerHTML = this.oldInputValue
        return false
      } else {
        this.oldInputValue = e.target.innerHTML
        return true
      }
    },
    formulaVerify(formulaStr) {
      // 错误情况，( 后面是运算符
      if (/\([\+\-\*\/\>\<\≥\≤]/.test(formulaStr)) {
        return false
      }
      // 错误情况，) 前面是运算符
      if (/[\+\-\*\/\>\<\≥\≤]\)/.test(formulaStr)) {
        return false
      }
      // 错误情况，( 前面不是运算符 或 空
      if (/[^\+\-\*\/\>\<\≥\≤\and\or\Count\Sum\Avg\Max\Min\Parse\(\s]\(/.test(formulaStr)) {
        return false
      }
      // 错误情况，) 后面不是运算符 或 空
      if (/\)[^\+\-\*\/\>\<\≥\≤\)\s]/.test(formulaStr)) {
        return false
      }
      // 错误情况，运算符号不能在首末位
      if (/^[\+\-\*\/\>\<\≥\≤.]|[\+\-\*\/\>\<\≥\≤.]$/.test(formulaStr)) {
        return false
      }
      // 错误情况，运算符连续
      if (/[\+\-\*\/\>\<\≥\≤]{2,}/.test(formulaStr)) {
        return false
      }
      // 错误情况，【】后面不是 运算符或 ) 或 ''
      if (/\【.+\】[^\+\-\*\/\>\<\≥\≤\)\s]/.test(formulaStr)) {
        return false
      }
      // 错误情况，【】前面不是 运算符或 ( 或 ''
      if (/[^\+\-\*\/\>\<\≥\≤\(\s]\【.+\】/.test(formulaStr)) {
        return false
      }
      return true
    }
  }
}
</script>
<style scoped>
.numberButton {
  width: 90%;
  height: 40px;
  margin-bottom:8px;
  font-size: large !important;
}
.el-card ::v-deep .el-card__header {
  background-color: #f5f7fa;
  padding: 3px !important;
}
.font-small {
  font-size: 12px;
}
.inputsx {
    background-color: #FFFFFF;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #DCDFE6;
    box-sizing: border-box;
    color: #606266;
    font-size: inherit;
    height: 40px;
    line-height: 40px;
    outline: none;
    padding: 0 15px;
    width: 100%;
}

.inputsx:empty::before {
            content: attr(placeholder);
        }

/deep/ .el-table__body-wrapper {
            height: 130px; /* 滚动条整体高 必须项 */
            border-right: none;
            overflow-y: scroll;/* overflow-y为了不出现水平滚动条*/
        }
        /deep/ .el-table__body-wrapper::-webkit-scrollbar {
            width: 5px;/* 滚动条的宽高 必须项 */
            height: 5px;
        }

       /deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
          border-radius: 10px; /*滚动条的圆角*/
   			  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    		  background-color: #409eff; /*滚动条的背景颜色*/
        }
</style>
