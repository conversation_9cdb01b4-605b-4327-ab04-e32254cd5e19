<template>
  <div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="终端潜力" name="receiver">
        <div class="search-container-bg" style="padding: 15px 10px 10px 10px">
          <el-row :gutter="10" class="filter-container" type="flex">
            <el-col :span="6">
              <el-select
                v-model="receiverQuotaQuery.departmentId"
                class="filter-item"
                placeholder="部门"
                clearable
              >
                <el-option
                  v-for="item in departments"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select
                v-model="receiverQuotaQuery.provinceId"
                class="filter-item"
                placeholder="省份"
                clearable
              >
                <el-option
                  v-for="item in provinceList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-input
                v-model="receiverQuotaQuery.receiverName"
                clearable
                placeholder="终端"
                class="filter-item"
                @keyup.enter.native="handleReceiverQuotaFilter"
              />
            </el-col>
            <el-col :span="6">

              <el-select
                v-model="receiverQuotaQuery.isHasStation"
                style="width: 100%"
                class="filter-item"
                placeholder="是否在岗"
                clearable
              >
                <el-option v-for="item in isHasStationList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>

            </el-col>
            <el-col :span="6">

              <el-select
                v-model="receiverQuotaQuery.isHasCustomerCategory"
                style="width: 100%"
                class="filter-item"
                placeholder="是否是目标客户"
                clearable
              >
                <el-option v-for="item in isHasCustomerCategoryList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>

            </el-col>
            <el-col :span="8">
              <el-date-picker
                v-model="receiverQuotaQuery.checkedMonths"
                clearable
                class="filter-item"
                style="width:100%"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始月份"
                end-placeholder="结束月份"
                format="yyyy-MM"
                value-format="yyyy-MM"
                @keyup.enter.native="handleReceiverQuotaFilter"
              />
            </el-col>
            <el-col :span="span">
              <el-select
                v-model="receiverQuotaQuery.manufacturerId"
                style="width: 100%"
                class="filter-item"
                placeholder="厂商"
                clearable
                @change="manufacturerChange"
              >
                <el-option
                  v-for="item in manufacturerList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-col>
            <el-col :span="span">
              <el-cascader
                ref="refReceiverProductAndSpec"
                :key="productAndSpecKey"
                v-model="productAndSpecId"
                :loading="productAndSpecLoading"
                :options="productAndSpecList"
                :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
                placeholder="产品/规格"
                clearable
                class="filter-item"
                @change="handleReceiverProductChange"
              />
            </el-col>
            <el-col :span="2">
              <el-button
                v-if="$isPermitted($store.getters.user, 'Quota_Button_Query')"
                class="filter-item-button"
                type="primary"
                icon="el-icon-search"
                @click="handleReceiverQuotaFilter"
              >
                查询
              </el-button>
            </el-col>
          </el-row>
          <el-row type="flex" justify="end" :gutter="10">
            <el-col :span="3.5">
              <el-button
                v-if="$isPermitted($store.getters.user, 'TerminalQuota_Button_BatchImport')"
                class="filter-item-button"
                type="primary"
                icon="el-icon-upload2"
                @click="handleReceiverQuotaImport"
              >
                批量导入
              </el-button>
            </el-col>
            <el-col :span="3.5">
              <el-button
                v-if="$isPermitted($store.getters.user, 'TerminalQuota_Button_Add')"
                class="filter-item-button"
                type="primary"
                icon="el-icon-plus"
                @click="handleCreate"
              >
                新增
              </el-button>
            </el-col>
            <el-col :span="3.5">
              <el-button
                v-if="$isPermitted($store.getters.user, 'Quota_Button_Export')"
                :loading="btnExportLoading"
                class="filter-item-button"
                type="primary"
                icon="el-icon-download"
                @click="onShowExportModal"
              >
                导出
              </el-button>
            </el-col>
          </el-row>
        </div>
        <div class="list-container">
          <el-row>
            <el-col :span="24">
              <el-table
                v-loading="listLoading"
                :data="receiverQuotaList"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
                @sort-change="receiverSortChange"
              >
                <el-table-column fixed label="序号" :index="indexReceiverMethod" type="index" align="center" />
                <el-table-column fixed sortable="custom" prop="Department.Name" label="部门" min-width="120px" align="left" header-align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.departmentName }}</span>
                  </template>
                </el-table-column>
                <el-table-column fixed sortable="custom" prop="Receiver.Name" label="终端" min-width="200px" align="left" header-align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.receiverName }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="ProductSpec.Product.Manufacturer.Name" label="厂商" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.manufacturerName }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="ProductSpec.Product.NameCn" label="产品名称" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.productName }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="ProductSpec.Spec" label="包装规格" min-width="100px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.spec }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="Cycle.YearMonthSplicing" label="月份" min-width="80px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.quotalMonth }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="Receiver.Employee.DisplayName" label="负责人" min-width="100px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.responsiblePerson }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="Receiver.Province.NameCn" label="省份" min-width="100px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.provinceName }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="Station.Name" label="所属岗位" min-width="200px" align="left" header-align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.stationName }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="CustomerCategory.Name" label="客户类型" min-width="120px" align="center" header-align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.customerCategoryName }}</span>
                  </template>
                </el-table-column>
                <el-table-column fixed="right" sortable="custom" prop="Qty" label="潜力数量" min-width="100px" align="right" header-align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.qty }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  fixed="right"
                  label="操作"
                  align="center"
                  header-align="center"
                  width="100"
                  class-name="small-padding fixed-width"
                >
                  <template slot-scope="{ row }">
                    <i v-if="$isPermitted($store.getters.user, 'TerminalQuota_Button_Edit')" class="el-icon-edit-outline eltablei" title="编辑" @click="handleReceiverUpdate(row)" />
                    <i v-if="$isPermitted($store.getters.user, 'TerminalQuota_Button_Del')" class="el-icon-delete eltablei" title="删除" @click="handleReceiverDelete(row)" />
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col class="el-colRight">
              <pagination
                v-show="receiverTotal > 0"
                :total="receiverTotal"
                :page.sync="receiverQuotaQuery.pageIndex"
                :limit.sync="receiverQuotaQuery.pageSize"
                @pagination="getReceiverQuotaList"
              />
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
      <el-tab-pane label="区域潜力" name="station">
        <div class="search-container-bg" style="padding: 15px 10px 10px 10px">
          <el-row :gutter="10" class="filter-container" type="flex">
            <el-col :span="span">
              <el-select
                v-model="stationQuotaQuery.departmentId"
                class="filter-item"
                placeholder="部门"
                clearable
              >
                <el-option
                  v-for="item in departments"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-input
                v-model="stationQuotaQuery.stationName"
                clearable
                placeholder="岗位"
                class="filter-item"
                @keyup.enter.native="handleStationQuotaFilter"
              />
            </el-col>
            <el-col :span="8">
              <el-date-picker
                v-model="stationQuotaQuery.checkedMonths"
                clearable
                class="filter-item"
                style="width:100%"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始月份"
                end-placeholder="结束月份"
                format="yyyy-MM"
                value-format="yyyy-MM"
                @keyup.enter.native="handleStationQuotaFilter"
              />
            </el-col>
            <el-col :span="span">
              <el-select
                v-model="stationQuotaQuery.manufacturerId"
                style="width: 100%"
                class="filter-item"
                placeholder="厂商"
                clearable
                @change="stationManufacturerChange"
              >
                <el-option
                  v-for="item in manufacturerList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-cascader
                ref="refRegionProductAndSpec"
                :key="productAndSpecKey"
                v-model="productAndSpecId"
                :loading="productAndSpecLoading"
                :options="stationProductAndSpecList"
                placeholder="产品/规格"
                clearable
                :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
                class="filter-item"
                @change="handleRegionProductChange"
              />
            </el-col>
            <el-col :span="span">
              <el-button
                v-if="$isPermitted($store.getters.user, 'Quota_Button_Query')"
                class="filter-item-button"
                type="primary"
                icon="el-icon-search"
                @click="handleStationQuotaFilter"
              >
                查询
              </el-button>
            </el-col>
          </el-row>
          <el-row type="flex" :gutter="10" justify="end">
            <el-col :span="3.5">
              <el-button
                v-if="$isPermitted($store.getters.user, 'Quota_Button_Export')"
                :loading="btnExportStationQuotaLoading"
                class="filter-item-button"
                type="primary"
                icon="el-icon-download"
                @click="onShowExportStationQuotaModal"
              >
                导出
              </el-button>
            </el-col>
          </el-row>
        </div>
        <div class="list-container">
          <el-row>
            <el-col :span="24">
              <el-table
                v-loading="listLoading"
                :data="stationQuotaList"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
                @sort-change="stationSortChange"
              >
                <el-table-column fixed label="序号" :index="indexStationMethod" type="index" align="center" />
                <el-table-column fixed sortable="custom" prop="Department.Name" label="部门" min-width="100px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.departmentName }}</span>
                  </template>
                </el-table-column>
                <el-table-column fixed sortable="custom" prop="Station.Name" label="岗位" min-width="180px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.stationName }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="ProductSpec.Product.Manufacturer.Name" align="center" label="厂商" min-width="90px">
                  <template slot-scope="{ row }">
                    <span>{{ row.manufacturerName }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="ProductSpec.Product.NameCn" label="产品名称" min-width="100px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.productName }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="ProductSpec.Spec" label="包装规格" min-width="100px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.spec }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="Cycle.YearMonthSplicing" label="月份" min-width="80px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.quotalMonth }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="省份" min-width="200px" align="left" :show-overflow-tooltip="true" header-align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.provinceNames }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="Qty" label="潜力数量" min-width="100px" header-align="center" align="right">
                  <template slot-scope="{ row }">
                    <span>{{ row.qty }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="OtherQuotaQty" label="额外潜力数量" min-width="120px" header-align="center" align="right">
                  <template slot-scope="{ row }">
                    <span>{{ row.otherQuotaQty }}</span>
                  </template>
                </el-table-column>
                <el-table-column fixed="right" prop="TotalQty" label="潜力总数量" min-width="110px" header-align="center" align="right">
                  <template slot-scope="{ row }">
                    <span>{{ row.totalQty }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  fixed="right"
                  label="操作"
                  align="center"
                  header-align="center"
                  width="80px"
                  class-name="small-padding fixed-width"
                >
                  <template slot-scope="{ row }">
                    <i v-if="$isPermitted($store.getters.user, 'StationQuota_Button_Edit')" class="el-icon-edit-outline eltablei" title="编辑" @click="handleStationUpdate(row)" />
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col class="el-colRight">
              <pagination
                v-show="stationTotal > 0"
                :total="stationTotal"
                :page.sync="stationQuotaQuery.pageIndex"
                :limit.sync="stationQuotaQuery.pageSize"
                @pagination="getStationQuotaList"
              />
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>
    <ImportReceiverQuotaList ref="dialogImportReceiverQuota" @success="success" />
    <AddReceiverQuota ref="dialogAddReceiverQuota" @success="success" />
    <EditReceiverQuota ref="dialogEditReceiverQuota" @success="success" />
    <EditStationQuota ref="dialogEditStationQuota" @success="success" />

    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出报表"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
    <el-dialog
      :visible="showExportStationQuotaModal"
      :before-close="handleExportStationQuotaCancel"
      :close-on-click-modal="false"
      title="导出报表"
      width="800"
    >
      <CustomExport
        :column="columnDictionaryOfStationQuota"
        @exportSubmitEvent="handleExportStationQuota"
        @exportCancelEvent="handleExportStationQuotaCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>
<script>
import ProductService from '@/api/product'
import BonusService from '@/api/bonus'
import MaintenanceService from '@/api/maintenance'
import ManufacturerService from '@/api/manufacturer'
import Pagination from '@/components/Pagination'
import CustomExport from '@/components/Export/CustomExport'
import AddReceiverQuota from './components/addReceiverQuota'
import EditReceiverQuota from './components/editReceiverQuota'
import ImportReceiverQuotaList from './components/importReceiverQuotaList'
import EditStationQuota from './components/editStationQuota'
import moment from 'moment'
import LocationService from '@/api/location'
import HomeService from '@/api/home'

export default {
  name: 'Quota',
  components: {
    Pagination,
    ImportReceiverQuotaList,
    AddReceiverQuota,
    EditReceiverQuota,
    EditStationQuota,
    CustomExport
  },
  data() {
    return {
      span: 4,
      activeName: 'receiver',
      productAndSpecId: [],
      manufacturerList: [],
      productAndSpecList: [],
      stationProductAndSpecList: [],
      departments: [],
      listLoading: true,
      receiverTotal: 0,
      stationTotal: 0,
      receiverQuotaQuery: {
        checkedMonths: [],
        pageIndex: 1,
        pageSize: 10,
        order: '-LastEditTime'
      },
      stationQuotaQuery: {
        checkedMonths: [],
        pageIndex: 1,
        pageSize: 10,
        order: '-LastEditTime'
      },
      currentMonth: '',
      receiverQuotaList: [],
      stationQuotaList: [],
      manufacturerLoading: false,
      productAndSpecLoading: false,
      currentTab: 'receiver',
      productAndSpecKey: 0,
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: {},
      btnExportStationQuotaLoading: false,
      showExportStationQuotaModal: false,
      columnDictionaryOfStationQuota: {},

      manufacturerProductAndSpecId: [],
      manufacturerProductAndSpecLoading: false,
      manufacturerProductAndSpecList: [],
      deptList: [],
      isHasStationList: [{
        value: true,
        label: '是'
      },
      {
        value: false,
        label: '否'
      }],
      isHasCustomerCategoryList: [{
        value: true,
        label: '是'
      },
      {
        value: false,
        label: '否'
      }],
      provinceList: [],
      isManager: false
    }
  },
  created() {
    this.currentMonth = moment(new Date()).format('YYYY-MM')
    // eslint-disable-next-line no-sequences
    this.receiverQuotaQuery.checkedMonths = [this.currentMonth, this.currentMonth]
    this.initDepartment()
    this.initProvinces()
    this.initManufacturer()
    this.initProductAndSpec()
    this.getReceiverQuotaList()
  },
  methods: {
    initManufacturerProductAndSpec() {
      this.manufacturerProductAndSpecLoading = true
      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then((result) => {
          this.manufacturerProductAndSpecList = result
          this.manufacturerProductAndSpecLoading = false
        })
        .catch((error) => {
          this.manufacturerProductAndSpecLoading = false
          console.log(error)
        })
    },
    initManufacturer() {
      this.manufacturerLoading = true
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturerLoading = false
          this.manufacturerList = result
        })
        .catch(() => {
          this.manufacturerLoading = false
        })
    },
    initDepartment() {
      MaintenanceService.QueryDepartmentForQuota().then((res) => { this.departments = res })
    },
    initProvinces() {
      LocationService.QueryProvinceSelectForDataReport(this.receiverQuotaQuery)
        .then((result) => {
          this.provinceList = result
        })
        .catch((error) => {
          console.log(error)
        })
    },
    manufacturerChange() {
      ++this.productAndSpecKey
      this.initProductAndSpec(this.receiverQuotaQuery.manufacturerId)
    },
    stationManufacturerChange() {
      ++this.productAndSpecKey
      this.initStationProductAndSpec(this.stationQuotaQuery.manufacturerId)
    },
    initStationProductAndSpec(id) {
      this.productAndSpecLoading = true
      const para = { manufacturerId: id }
      ProductService.QueryProductAndSpecCascader(para)
        .then((result) => {
          this.stationProductAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch(() => {
          this.productAndSpecLoading = false
        })
    },
    initProductAndSpec(id) {
      this.productAndSpecLoading = true
      const para = { manufacturerId: id }
      ProductService.QueryProductAndSpecCascader(para)
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch(() => {
          this.productAndSpecLoading = false
        })
    },
    indexReceiverMethod(index) {
      return (
        (this.receiverQuotaQuery.pageIndex - 1) * this.receiverQuotaQuery.pageSize + index + 1
      )
    },
    indexStationMethod(index) {
      return (
        (this.stationQuotaQuery.pageIndex - 1) * this.stationQuotaQuery.pageSize + index + 1
      )
    },
    getReceiverQuotaList() {
      if (this.productAndSpecId) {
        const [productId, productSpecId] = this.productAndSpecId
        this.receiverQuotaQuery.productId = productId
        this.receiverQuotaQuery.productSpecId = productSpecId
      }
      this.listLoading = true
      BonusService.QueryReceiverQuota(this.receiverQuotaQuery).then(res => {
        this.listLoading = false
        this.receiverQuotaList = res.data.datas
        this.receiverTotal = res.data.recordCount
        this.receiverQuotaQuery.pageIndex = res.data.pageIndex
      }).catch(res => { this.listLoading = false })
    },
    handleReceiverQuotaFilter() {
      this.receiverQuotaQuery.pageIndex = 1
      this.getReceiverQuotaList()
    },
    getStationQuotaList() {
      if (this.productAndSpecId) {
        const [productId, productSpecId] = this.productAndSpecId
        this.stationQuotaQuery.productId = productId
        this.stationQuotaQuery.productSpecId = productSpecId
      }
      this.listLoading = true
      BonusService.QueryStationQuota(this.stationQuotaQuery).then(res => {
        this.listLoading = false
        this.stationQuotaList = res.data.datas
        this.stationTotal = res.data.recordCount
        this.stationQuotaQuery.pageIndex = res.data.pageIndex
      }).catch(res => { this.listLoading = false })
    },
    handleStationQuotaFilter() {
      this.stationQuotaQuery.pageIndex = 1
      this.getStationQuotaList()
    },
    receiverSortChange(column, prop, order) {
      this.receiverQuotaQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.receiverQuotaQuery.order = orderSymbol + column.prop
      this.getReceiverQuotaList()
    },
    stationSortChange(column, prop, order) {
      this.stationQuotaQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.stationQuotaQuery.order = orderSymbol + column.prop
      this.getStationQuotaList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleClick(tab, event) {
      this.currentTab = tab.name

      if (tab.name === 'receiver') {
        this.receiverQuotaQuery = {
          checkedMonths: [this.currentMonth, this.currentMonth],
          manufacturerId: null,
          receiverName: '',
          pageIndex: 1,
          pageSize: 10,
          order: '-CreateTime'
        }
        this.productAndSpecId = []
        this.initProductAndSpec()
        this.getReceiverQuotaList()
      }
      if (tab.name === 'station') {
        this.stationQuotaQuery = {
          checkedMonths: [this.currentMonth, this.currentMonth],
          manufacturerId: null,
          departmentId: null,
          stationName: '',
          pageIndex: 1,
          pageSize: 10,
          order: '-CreateTime'
        }
        this.productAndSpecId = []

        this.initStationProductAndSpec()
        this.getStationQuotaList()
      }
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },
    handleReceiverQuotaImport() {
      this.$refs.dialogImportReceiverQuota.initPage()
    },
    handleCreate() {
      this.$refs.dialogAddReceiverQuota.initPage()
    },
    success() {
      if (this.currentTab === 'receiver') {
        this.handleReceiverQuotaFilter()
      } else if (this.currentTab === 'station') {
        this.handleStationQuotaFilter()
      } else {
        this.handleOtherQuotaFilter()
      }
    },
    handleReceiverUpdate(row) {
      this.$refs.dialogEditReceiverQuota.initPage(row.id)
    },
    handleReceiverDelete(row) {
      this.$confirm('确定删除此终端潜力吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          BonusService.DeleteReceiverQuota(row)
            .then((result) => {
              if (result.succeed) {
                this.getReceiverQuotaList()
                this.showMessage('删除成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
              this.loading = false
            })
        })
        .catch((error) => {
          if (!error.succeed) {
            this.showMessage('取消删除', 'info')
          }
        })
    },
    handleReceiverProductChange() {
      this.$refs.refReceiverProductAndSpec.dropDownVisible = false
    },
    handleRegionProductChange() {
      this.$refs.refRegionProductAndSpec.dropDownVisible = false
    },
    // 显示导出model
    onShowExportModal() {
      if (!this.receiverQuotaQuery.checkedMonths) {
        this.$notice.message('请选择月份', 'error')
        return
      }

      this.btnExportLoading = true
      BonusService.GetReceiverQuotaColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      var exportParam = JSON.parse(JSON.stringify(this.receiverQuotaQuery))
      exportParam.checkedColumns = checkColumns
      BonusService.ExportReceiverQuota(exportParam)
        .then(result => {
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '终端潜力.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '终端潜力.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    // 显示导出model
    onShowExportStationQuotaModal() {
      if (!this.stationQuotaQuery.checkedMonths) {
        this.$notice.message('请选择月份', 'error')
        return
      }

      this.btnExportStationQuotaLoading = true
      BonusService.GetStationQuotaColumn().then(result => {
        this.btnExportStationQuotaLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionaryOfStationQuota = result.data
        this.showExportStationQuotaModal = true
      }).catch(error => {
        this.btnExportStationQuotaLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExportStationQuota(checkColumns) {
      this.showExportStationQuotaModal = false
      var exportParam = JSON.parse(JSON.stringify(this.stationQuotaQuery))
      exportParam.checkedColumns = checkColumns
      BonusService.ExportStationQuota(exportParam)
        .then(result => {
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '区域潜力.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '区域潜力.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportStationQuotaCancel() {
      this.showExportStationQuotaModal = false
    },
    handleStationUpdate(row) {
      this.$refs.dialogEditStationQuota.initPage(row.id)
    }
  }
}
</script>
<style lang="css">
.el-tooltip__popper{
    font-size: 14px;
    max-width:50%;
    background: #516e92 !important;
}

</style>
