<template>
  <div>
    <el-dialog :title="title" width="50%" :close-on-click-modal="false" :visible="true" @close="cancle()">
      <el-form ref="dataForm" :rules="rules" :model="tempData.tempFormModel" label-position="right" label-width="100px" class="el-dialogform">
        <el-row :gutter="10">
          <el-col :span="span">
            <el-form-item label="停用收货方">
              省份/城市：  {{ tempData.tempFormModel.provinceNameCn }} <span v-show="tempData.tempFormModel.cityNameCn"> / {{ tempData.tempFormModel.cityNameCn }}</span>
              <br>
              经销商名称：{{ tempData.tempFormModel.name }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="停用月份" prop="stopDate">
              <el-date-picker
                v-model="tempData.tempFormModel.stopDate"
                style="width: 100%"
                clearable
                type="month"
                placeholder="停用月份"
                format="yyyy-MM"
                :picker-options="pickerOptions"
                @blur="timeRangeChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="目标收货方" prop="targetReceiverName">
              <el-col :span="20" style="padding-left: 0px; padding-right: 0px;">
                <el-input
                  v-model="tempData.tempFormModel.targetReceiverName"
                  clearable
                  :readonly="true"
                  style="width: 100%"
                  placeholder="目标收货方"
                />
              </el-col>
              <el-col :span="4">
                <el-button icon="el-icon-search" style="width: 100%; " type="primary" title="选择目标收货方" @click="handleSelectReceiver" />
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          v-if="!viewModel"
          :loading="btnSaveLoading"
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="选择目标收货方"
      :close-on-click-modal="false"
      :visible="dialogReceiverVisible"
      width="90%"
      class="popup-search"
      @close="closeReceiverDialog"
    >
      <SelectReceiver
        ref="refSelectReceiver"
        :show-dialog="dialogReceiverVisible"
        :distributor-types="distributorTypes"
        :target-receiver-id="targetReceiverId"
        :is-stopped="false"
        @success="selectReceiverSuccess"
      />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeReceiverDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>

import ReceiverService from '@/api/receiver'
import SelectReceiver from '@/views/components/selectReceiver'

export default {
  name: '',
  components: {
    SelectReceiver
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    viewModel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      span: 24,
      cascaderKey: 0,
      pickerOptions: {
        disabledDate(time) {
          // 只允许选择上个月及以前的月份
          const year = new Date(Date.now()).getFullYear()
          let month = new Date(Date.now()).getMonth() + 1
          if (month < 10) month = '0' + month
          const ym = year + '-' + month
          const date = new Date(ym + '-01').setDate(0)// 如果 setDate 参数指定0，那么日期就会被设置为上个月的最后一天。
          return time.getTime() > new Date(date).getTime()
        }
      },
      rules: {
        stopDate: [
          { required: true, message: '请选择停用月份', trigger: 'change' }
        ],
        targetReceiverName: [
          { required: true, message: '请选择目标收货方', trigger: 'change' }
        ]
      },
      tempData: { tempFormModel: {}},
      btnSaveLoading: false,
      manufacturerLoading: false,
      productAndSpecLoading: false,
      manufacturerList: [],
      productAndSpecList: [],
      distributorTypes: [],
      targetReceiverId: '',
      dialogReceiverVisible: false
    }
  },
  watch: {
    id(val) {
      this.tempData.tempFormModel.id = val
    }
  },
  mounted() {
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      if (!this.id) {
        this.clear()
        return
      }
      this.get(this.id)
    },
    timeRangeChange() {
      this.$forceUpdate()
    },
    get(id) {
      this.btnSaveLoading = true
      ReceiverService.GetReceiver({ id: id }).then(result => {
        this.tempData.tempFormModel = result.data
        if (this.tempData.tempFormModel.stopDate) {
          const year = new Date(this.tempData.tempFormModel.stopDate).getFullYear()
          let month = new Date(this.tempData.tempFormModel.stopDate).getMonth() + 1
          if (month < 10) month = '0' + month
          const ym = year + '-' + month
          const date = ym + '-01'
          this.tempData.tempFormModel.stopDate = date
        }
        this.btnSaveLoading = false
      }).catch(error => {
        this.btnSaveLoading = false
        console.log(error)
      })
    },
    checkReceiverQuota() {
      this.btnSaveLoading = true
      var promise = new Promise((resolve, reject) => {
        ReceiverService.CheckReceiverQuota(this.tempData.tempFormModel).then(result => {
          if (result.succeed) {
            resolve(result.data)
          }
        }).catch(() => {
          reject()
          this.btnSaveLoading = false
        })
      })
      return promise
    },
    save() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.checkReceiverQuota().then((res) => {
            if (res) {
              this.$confirm('停用终端和目标终端同时存在指标，是否合并？', '提示', {
                confirmButtonText: '合并',
                cancelButtonText: '不合并',
                type: 'warning'
              }).then(() => {
                this.tempData.tempFormModel.isMerge = true
                this.disableReceiver()
              }).catch(() => {
                this.tempData.tempFormModel.isMerge = false
                this.disableReceiver()
              })
            } else {
              this.tempData.tempFormModel.isMerge = true
              this.disableReceiver()
            }
          })
        }
      })
    },
    disableReceiver() {
      ReceiverService.DisableReceiver(this.tempData.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempData.tempFormModel = result.data
          this.$notice.message('停用成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('停用失败。', 'error')
          }
        })
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.tempData.tempFormModel = {}
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    },
    handleSelectReceiver() {
      this.distributorTypes = [20, 30]
      this.targetReceiverId = this.tempData.tempFormModel.id
      this.dialogReceiverVisible = true
    },
    closeReceiverDialog() {
      this.dialogReceiverVisible = false
      this.$refs.refSelectReceiver.clear()
      this.distributorTypes = []
    },
    selectReceiverSuccess(val) {
      if (val.id === this.tempData.tempFormModel.id) {
        this.$notice.message('目标收货方不能和收货方相同', 'error')
      } else {
        this.tempData.tempFormModel.targetReceiverName = val.name
        this.tempData.tempFormModel.targetReceiverId = val.id
        this.closeReceiverDialog()
      }
    }
  }

}
</script>
