import HttpApi from './libs/api.request'

const controller = 'RebateAgreement'

const api = new HttpApi(controller)

export default {
  QueryRebateAgreement(params) {
    return api.get('QueryRebateAgreement', params)
  },
  QuerySupplyRebateAgreement(params) {
    return api.get('QuerySupplyRebateAgreement', params)
  },
  SaveRequestFormRebate(params) {
    return api.post('SaveRequestFormRebate', params)
  },
  SubmitRequestFormRebate(params) {
    return api.post('SubmitRequestFormRebate', params)
  },
  UpdateRebateAgreementToManuallyCalculate(params) {
    return api.post('UpdateRebateAgreementToManuallyCalculate', params)
  },
  QueryAgreementPolicyTypeCascader() {
    return api.get('QueryAgreementPolicyTypeCascader')
  },
  GetRebateEnvironmentVariables() {
    return api.get('GetRebateEnvironmentVariables')
  },
  QueryAgreementPolicyTemplate(params) {
    return api.get('QueryAgreementPolicyTemplate', params)
  },
  AddAgreementPolicyTemplate(params) {
    return api.post('AddAgreementPolicyTemplate', params)
  },
  UpdateAgreementPolicyTemplate(params) {
    return api.post('UpdateAgreementPolicyTemplate', params)
  },
  GetAgreementPolicyTemplate(params) {
    return api.get('GetAgreementPolicyTemplate', params)
  },
  QueryRebateEnvironmentVariableCascader() {
    return api.get('QueryRebateEnvironmentVariableCascader')
  },
  QueryRebateProject(params) {
    return api.get('QueryRebateProject', params)
  },
  AddRebateProject(params) {
    return api.post('AddRebateProject', params)
  },
  UpdateRebateProject(params) {
    return api.post('UpdateRebateProject', params)
  },
  DeleteRebateProject(params) {
    return api.post('DeleteRebateProject', params)
  },
  GetRebateProject(params) {
    return api.get('GetRebateProject', params)
  },
  QueryRebateResult(params) {
    return api.get('QueryRebateResult', params)
  },
  ReleaseRebateAgreementPolicyTemplate(params) {
    return api.post('ReleaseRebateAgreementPolicyTemplate', params)
  },
  RevokeRebateAgreementPolicyTemplate(params) {
    return api.post('RevokeRebateAgreementPolicyTemplate', params)
  },
  QueryAgreementTemplate(params) {
    return api.get('QueryAgreementTemplate', params)
  },
  ReleaseRebateAgreementTemplate(params) {
    return api.post('ReleaseRebateAgreementTemplate', params)
  },
  RevokeRebateAgreementTemplate(params) {
    return api.post('RevokeRebateAgreementTemplate', params)
  },
  GetAgreementTemplate(params) {
    return api.get('GetAgreementTemplate', params)
  },
  QueryAgreementTemplateType(params) {
    return api.get('QueryAgreementTemplateType', params)
  },
  GetRebateAgreement(params) {
    return api.get('GetRebateAgreement', params)
  },
  QueryAgreementTemplateTypeCascader() {
    return api.get('QueryAgreementTemplateTypeCascader')
  },
  AddRebateAgreementTemplate(params) {
    return api.post('AddRebateAgreementTemplate', params)
  },
  UpdateRebateAgreementTemplate(params) {
    return api.post('UpdateRebateAgreementTemplate', params)
  },
  DeleteRebateAgreementTemplate(params) {
    return api.post('DeleteRebateAgreementTemplate', params)
  },
  DeleteRebateAgreementPolicyTemplate(params) {
    return api.post('DeleteRebateAgreementPolicyTemplate', params)
  },
  QueryImportAgreementMasterTemp(params) {
    return api.get('QueryImportAgreementMasterTemp', params)
  },
  QueryPolicyTermsVariableCascader() {
    return api.get('QueryPolicyTermsVariableCascader')
  },
  QueryImportAgreementError(params) {
    return api.get('QueryImportAgreementError', params)
  },
  ExportImportAgreementError(params) {
    return api.post('ExportImportAgreementError', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  BatchGenerateContract() {
    return api.get('BatchGenerateContract')
  },
  // 查询一级商名单备忘录列表
  QueryRebateAgreementCertificate(params) {
    return api.get('QueryRebateAgreementCertificate', params)
  },
  // 查询一级商名单备忘录
  GetRebateAgreementCertificateByID(params) {
    return api.get('GetRebateAgreementCertificateByID', params)
  },
  // 查询甲方
  QueryRebatePartyReceiver() {
    return api.get('QueryRebatePartyReceiver')
  },
  // 协议支付方式
  QueryRebatePaymentType() {
    return api.get('QueryRebatePaymentType')
  },
  // 返利支付天数
  QueryRebatePayWithInDays() {
    return api.get('QueryRebatePayWithInDays')
  },
  // 新增一级商名单备忘录
  AddRebateAgreementCertificate(params) {
    return api.post('AddRebateAgreementCertificate', params)
  },
  // 编辑一级商名单备忘录
  UpdateRebateAgreementCertificate(params) {
    return api.post('UpdateRebateAgreementCertificate', params)
  },
  // 删除一级商名单备忘录
  DeleteRebateAgreementCertificate(params) {
    return api.post('DeleteRebateAgreementCertificate', params)
  },
  // 删除一级商名单备忘录合作主体
  DeleteRebateAgreementCertificateReceiver(params) {
    return api.post('DeleteRebateAgreementCertificateReceiver', params)
  },
  PackageDownloadContract(params) {
    return api.post('PackageDownloadContract', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  GenerateCertificate(params) {
    return api.get('GenerateCertificate', params)
  },
  GetRebateAgreementTemplateExportColumn(params) {
    return api.get('GetRebateAgreementTemplateExportColumn', params)
  },
  ExportRebateAgreementTemplate(params) {
    return api.post('ExportRebateAgreementTemplate', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryRebateAgreementTemplateVariableCascader() {
    return api.get('QueryRebateAgreementTemplateVariableCascader')
  },
  GenerateMasterAgreement(params) {
    return api.post('GenerateMasterAgreement', params)
  },
  GenerateSubAgreement(params) {
    return api.post('GenerateSubAgreement', params)
  },
  GetWaitingGenerateContract() {
    return api.get('GetWaitingGenerateContract')
  }
}
