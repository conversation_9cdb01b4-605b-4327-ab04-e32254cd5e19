<template>
  <div>
    <el-table :data="list" style="width: 100%;height:250px;">
      <el-table-column prop="Title" label="公告通知" min-width="100px">
        <template slot-scope="{ row }">
          <span class="link-type" @click="handelContentInfo(row)"> {{ row.title }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="CreateTime" label="发布时间" min-width="100px">
        <template slot-scope="{ row }">
          <span>{{ row.createTime | parseTime('{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      title="公告详情"
      :close-on-click-modal="false"
      :visible="dialogContentInfoVisible"
      width="80%"
      @close="closeContentInfoDialog"
    >
      <NoticeInfo :announcement-id="announcementId" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeContentInfoDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import HomeService from '@/api/home'
import NoticeInfo from './noticeInfo'

export default {
  components: {
    NoticeInfo
  },
  data() {
    return {
      list: [],
      announcementId: '',
      listQuery: {
        pageIndex: 1,
        pageSize: 5,
        order: '-CreateTime'
      },
      dialogContentInfoVisible: false
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      HomeService.QueryAnnouncement(this.listQuery).then(res => {
        this.list = res.data.datas
      }).catch(res => {})
    },
    handelContentInfo(row) {
      this.announcementId = row.id
      this.dialogContentInfoVisible = true
    },
    closeContentInfoDialog() {
      this.dialogContentInfoVisible = false
    }
  }
}
</script>
