<template>
  <div>
    <el-dialog custom-class="el-dialog-s" :title="title" width="90%" append-to-body :close-on-click-modal="false" :visible="showAddDialog" @close="closeAddDistributorDialog">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="receiver"
        label-position="right"
        label-width="110px"
        class="el-dialogform"
      >
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="经销商名称" prop="name">
              <el-input
                v-model="receiver.name"
                disabled
                placeholder="经销商名称"
                class="filter-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="SAPCode" prop="sapCode">
              <el-input
                v-model="receiver.sapCode"
                placeholder="SAPCode"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="Code" prop="code">
              <el-input
                v-model="receiver.code"
                placeholder="Code"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="省份/城市/区县" prop="provinceCityCountyId">
              <el-cascader
                ref="refProvinceCityCountyCounty"
                v-model="receiver.provinceCityCountyId"
                :loading="provinceCityCountyLoading"
                :options="provinceCityCountyList"
                :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
                placeholder="省份 / 城市 / 区县"
                clearable
                style="width:100%"
                @change="closeProvinceCityCascader"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="经销商类型" prop="enumDistributorType">
              <el-select
                v-model="receiver.enumDistributorType"
                style="width: 100%"
                class="filter-item"
                placeholder="经销商类型"
                clearable
              >
                <el-option
                  v-for="item in distributorTypeList"
                  :key="item.value"
                  :label="item.desc"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="收货方类型" prop="receiverTypeId">
              <el-cascader
                ref="refReceiverType"
                v-model="receiver.receiverTypeId"
                :loading="receiverTypeLoading"
                :options="receiverTypeList"
                :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
                placeholder="收货方类型"
                clearable
                style="width:100%"
                @change="closeReceiverTypeCascader"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="生效日期" prop="effectiveDate">
              <el-date-picker
                v-model="receiver.effectiveDate"
                type="date"
                style="width: 100%"
                :picker-options="datePickerOptions"
                placeholder="生效日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="机构等级" prop="gradeLevel">
              <el-input
                v-model="receiver.gradeLevel"
                placeholder="机构等级"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="机构经济类型" prop="economicType">
              <el-input
                v-model="receiver.economicType"
                placeholder="机构经济类型"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="统一信用代码" prop="taxRegistrationNo">
              <el-input
                v-model="receiver.taxRegistrationNo"
                placeholder="统一信用代码"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="工商注册号" prop="registrationNo">
              <el-input
                v-model="receiver.registrationNo"
                placeholder="工商注册号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="是否互联网医院" prop="enumFlags">
              <el-select
                v-model="receiver.enumFlags"
                style="width: 100%"
                class="filter-item"
                placeholder="是否互联网医院"
                clearable
              >
                <el-option
                  v-for="item in receiverFlagList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="地址" prop="address">
              <el-input
                v-model="receiver.address"
                placeholder="地址"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="选择树节点">
              <el-row type="flex" :gutter="10" justify="start">
                <el-col :span="17">
                  <el-input
                    v-model="receiver.stationName"
                    placeholder="树节点"
                    class="filter-item"
                    readonly
                  />
                </el-col>
                <el-col :span="3.5">
                  <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleShowTree">
                    选择
                  </el-button>
                </el-col>
                <el-col :span="3.5">
                  <el-button class="filter-item" type="primary" icon="el-icon-delete" @click="handleClearTree">
                    清除
                  </el-button>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeAddDistributorDialog">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleSave"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="el-dialog-ls"
      append-to-body
      :close-on-click-modal="false"
      title="选择树节点"
      :visible="dialogTreeVisible"
      width="55%"
      @close="closeTree"
    >
      <el-form
        ref="treeForm"
        label-position="right"
        label-width="120px"
        class="el-dialogform"
      >

        <el-form-item label="组织架构树" required>
          <span>已选节点：{{ stationName }}</span>
          <el-tree :data="treeData" :props="defaultProps" @node-click="handleNodeClick" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeTree"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleSaveSelected"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import LocationService from '@/api/location'
import MasterDataService from '@/api/masterData'
import ReceiverService from '@/api/receiver'
import MaintenanceService from '@/api/maintenance'

export default {
  props: {
    provinceId: {
      type: String,
      default: ''
    },
    cityId: {
      type: String,
      default: ''
    }
  },
  data() {
    const ruleProvinceCityValidator = (rule, value, callback) => {
      if (value === null || value === undefined) {
        callback(new Error('请选择省份/城市'))
      } else {
        if (value.length === 0) {
          callback(new Error('请选择省份或城市'))
        }
      }
      callback()
    }

    return {
      span: 12,
      title: '新增发货方',
      showAddDialog: false,
      provinceCityCountyList: [],
      distributorTypeList: [],
      provinceCityCountyLoading: false,
      receiver: { },
      stationId: '',
      stationName: '',
      dialogTreeVisible: false,
      receiverTypeList: [],
      receiverTypeLoading: false,
      rules: {
        code: [
          {
            required: true,
            type: 'string',
            message: '请输入Code',
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            type: 'string',
            message: '请输入经销商名称',
            trigger: 'blur'
          }
        ],
        provinceCityCountyId: [
          {
            required: true,
            type: 'array',
            validator: ruleProvinceCityValidator,
            trigger: 'change'
          }
        ],
        enumDistributorType: [
          {
            required: true,
            type: 'number',
            message: '请选择经销商类型',
            trigger: 'change'
          }
        ],
        receiverTypeId: [
          { required: true, type: 'array', message: '请选择收货方类型', trigger: 'change' }
        ],
        effectiveDate: [
          {
            required: true,
            message: '请选择生效日期',
            trigger: 'blur'
          }
        ]
      },
      receiverFlagList: [{
        value: 1,
        label: '是'
      }, {
        value: 2,
        label: '否'
      }],
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6
        }
      }
    }
  },
  watch: {
    provinceId: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        return val
      }
    },
    cityId: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        return val
      }
    }
  },
  methods: {
    initPage(title, importSalesFlowHandleId, name, location, distributorId) {
      this.title = title
      this.receiver.name = name
      // 错误处理时创建收货方同时创建别名
      if (importSalesFlowHandleId !== undefined && importSalesFlowHandleId !== null) {
        this.receiver.importSalesFlowHandleId = importSalesFlowHandleId
      }
      if (distributorId !== undefined && distributorId !== null) {
        this.receiver.distributorId = distributorId
      }
      if (location !== null && location.length > 0) {
        this.receiver.provinceCityCountyId = location
      }

      this.receiver.receiverTypeId = [this.receiver.receiverTypeLevelOneId, this.receiver.receiverTypeLevelTwoId, this.receiver.receiverTypeLevelThreeId]
      this.initEnums()
      this.initProvinceCityCounty()
      this.getTreeList()
      this.showAddDialog = true
    },
    getTreeList() {
      MaintenanceService.QueryOrganization().then(result => {
        if (result.succeed) {
          this.treeData = result.data
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(() => {
      })
    },
    initEnums() {
      MasterDataService.GetEnumInfos({
        EnumType: 'DistributorType'
      }).then(res => {
        this.distributorTypeList = res.data.datas
      }).catch(res => {

      })
      this.initReceiverTypeList()
    },
    initReceiverTypeList() {
      this.receiverTypeLoading = true
      MaintenanceService.QueryReceiverTypeCascader().then(res => {
        this.receiverTypeList = res
        this.receiverTypeLoading = false
      })
        .catch((error) => {
          this.receiverTypeLoading = false
          console.log(error)
        })
    },
    closeReceiverTypeCascader() {
      this.$refs.refReceiverType.dropDownVisible = false
    },
    initProvinceCityCounty() {
      this.provinceCityCountyLoading = true
      LocationService.QueryProvinceCityCountyCascader()
        .then((result) => {
          this.provinceCityCountyList = result
          this.provinceCityCountyLoading = false
          if (this.provinceId) {
            if (this.cityId) {
              if (this.countyId) {
                this.receiver.provinceCityCountyId = [this.provinceId, this.cityId, this.countyId]
              }
              this.receiver.provinceCityCountyId = [this.provinceId, this.cityId]
            } else {
              this.receiver.provinceCityCountyId = [this.provinceId]
            }
          }
        })
        .catch(() => {
          this.provinceCityCountyLoading = false
        })
    },
    handleSave() {
      // 保存
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.receiver.provinceId = this.receiver.provinceCityCountyId[0]

          if (this.receiver.provinceCityCountyId.length >= 2) {
            this.receiver.cityId = this.receiver.provinceCityCountyId[1]
          } else {
            this.receiver.cityId = null
          }

          if (this.receiver.provinceCityCountyId.length === 3) {
            this.receiver.countyId = this.receiver.provinceCityCountyId[2]
          } else {
            this.receiver.countyId = null
          }

          if (this.receiver.receiverTypeId.length === 3) {
            this.receiver.receiverTypeLevelOneId = this.receiver.receiverTypeId[0]
            this.receiver.receiverTypeLevelTwoId = this.receiver.receiverTypeId[1]
            this.receiver.receiverTypeLevelThreeId = this.receiver.receiverTypeId[2]
          }
          ReceiverService.AddReceiverByHandleSalesFlow(this.receiver)
            .then((result) => {
              if (result.succeed) {
                this.showAddDialog = false
                let tempReceiver = {}
                tempReceiver = result.data
                this.receiver = {}
                this.$refs.dataForm.resetFields()
                this.$emit('success', tempReceiver)
                this.showMessage('创建成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
            })
        }
      })
    },
    closeAddDistributorDialog() {
      this.receiver = { }
      this.$refs.dataForm.resetFields()
      this.showAddDialog = false
    },
    handleSaveSelected() {
      if (this.stationId === null || this.stationId === '') {
        this.$notice.message('请选择节点', 'info')
      } else {
        this.receiver.stationId = this.stationId
        this.receiver.stationName = this.stationName
        this.dialogTreeVisible = false
      }
    },
    handleShowTree() {
      this.dialogTreeVisible = true
    },
    handleClearTree() {
      this.stationId = null
      this.stationName = ''
      this.receiver.startDate = ''
      this.receiver.stationId = null
      this.receiver.stationName = ''
    },
    closeTree() {
      this.dialogTreeVisible = false
    },
    handleNodeClick(data) {
      if (data.enableAddReceiver === true) {
        this.stationName = data.label
        this.stationId = data.pkid
      }
    },
    closeProvinceCityCascader() {
      this.$refs.refProvinceCityCountyCounty.dropDownVisible = false
    }
  }
}
</script>
<style scoped>
  .el-dialog-s {
  z-index: 12;
  }
  .el-dialog-ls {
  z-index: 13;
  }
</style>
