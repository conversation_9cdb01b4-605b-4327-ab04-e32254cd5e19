<template>
  <div>
    <el-dialog :title="title" width="70%" :close-on-click-modal="false" :visible="true" @close="cancle()">
      <el-form ref="dataForm" :rules="rules" :model="tempData.tempFormModel" label-position="right" label-width="100px" class="el-dialogform">
        <el-row :gutter="10">
          <el-col :span="span">
            <el-form-item label="厂商" prop="manufacturerId">
              <el-select
                v-model="tempData.tempFormModel.manufacturerId"
                style="width: 100%"
                :loading="manufacturerLoading"
                class="filter-item"
                placeholder="厂商"
                @change="manufacturerChange"
              >
                <el-option
                  v-for="item in manufacturerList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="产品/规格" prop="productAndSpecId">
              <el-cascader
                ref="refProductAndSpec"
                :key="productAndSpecKey"
                v-model="tempData.tempFormModel.productAndSpecId"
                style="width: 100%"
                :loading="productAndSpecLoading"
                :options="productAndSpecList"
                placeholder="产品/规格"
                clearable
                class="filter-item"
                :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
                @change="productChange"
              /></el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="通用名">
              <el-input
                v-model="tempData.tempFormModel.commonName"
                disabled
                placeholder="通用名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="是否目标终端" prop="isTargetTerminal">
              <el-checkbox v-model="tempData.tempFormModel.isTargetTerminal" />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="基线数量" prop="baseLineQuantity">
              <el-input
                v-model="tempData.tempFormModel.baseLineQuantity"
                clearable
                placeholder="基线数量"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          v-if="!viewModel"
          :loading="btnSaveLoading"
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>

import ManufacturerService from '@/api/manufacturer'
import ProductService from '@/api/product'
import SalesBaseLineService from '@/api/salesBaseLine'

export default {
  name: '',
  components: {
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    viewModel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validateSpec = (rule, value, callback) => {
      if (!this.tempData.tempFormModel.productAndSpecId.length) {
        callback(new Error('请选择产品/规格'))
      } else {
        callback()
      }
    }
    return {
      span: 24,
      rules: {
        manufacturerId: [
          { required: true, message: '请选择厂商', trigger: 'change' }
        ],
        productAndSpecId: [
          { type: 'array', required: true, validator: validateSpec, trigger: 'change' }
        ],
        baseLineQuantity: [
          {
            required: true, trigger: 'blur', message: '请填写基线'
          },
          { pattern: /((^[0-9]\d*))(\.\d{0,2}){0,1}$/, message: '基线仅支持正数，最多2位小数' }
        ]
      },
      tempData: { tempFormModel: {}},
      btnSaveLoading: false,
      manufacturerLoading: false,
      productAndSpecLoading: false,
      manufacturerList: [],
      productAndSpecList: [],
      productAndSpecKey: 0
    }
  },
  watch: {
    id(val) {
      this.tempData.tempFormModel.id = val
    }
  },
  mounted() {
  },
  created() {
    this.initManufacturer()
    this.init()
  },
  methods: {
    init() {
      if (!this.id) {
        this.initProductAndSpec()
        this.clear()
        return
      }
      this.get(this.id)
    },
    initManufacturer() {
      this.manufacturerLoading = true
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturerLoading = false
          this.manufacturerList = result
        })
        .catch((error) => {
          this.manufacturerLoading = false
          console.log(error)
        })
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      const para = { manufacturerId: this.tempData.tempFormModel.manufacturerId }
      ProductService.QueryProductAndSpecCascader(para)
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    manufacturerChange() {
      this.tempData.tempFormModel.productAndSpecId = []
      ++this.productAndSpecKey
      this.initProductAndSpec()
    },
    productChange(value) {
      if (!value || value.length === 0) {
        this.tempData.tempFormModel.commonName = ''
        return
      } else {
        const para = { id: value[0] }
        ProductService.GetProduct(para)
          .then((result) => {
            this.tempData.tempFormModel.commonName = result.data.commonName
            this.$forceUpdate()
          })
          .catch((error) => {
            console.log(error)
          })
      }
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    get(id) {
      this.btnSaveLoading = true
      SalesBaseLineService.GetSalesBaseLine({ id: id }).then(result => {
        this.tempData.tempFormModel = result.data
        this.manufacturerChange()
        this.tempData.tempFormModel.productAndSpecId = [result.data.productId, result.data.productSpecId]
        this.btnSaveLoading = false
      }).catch(error => {
        this.btnSaveLoading = false
        console.log(error)
      })
    },
    save() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.tempData.tempFormModel.productSpecId = this.tempData.tempFormModel.productAndSpecId[1]
          if (!this.tempData.tempFormModel.id) {
            this.addNew()
          } else {
            this.update()
          }
        }
      })
    },
    addNew() {
      this.btnSaveLoading = true
      SalesBaseLineService.AddSalesBaseLine(this.tempData.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempData.tempFormModel = result.data
          this.$notice.message('新增成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('新增失败。', 'error')
          }
        })
    },
    update() {
      this.btnSaveLoading = true
      SalesBaseLineService.UpdateSalesBaseLine(this.tempData.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempData.tempFormModel = result.data
          this.$notice.message('修改成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('修改失败。', 'error')
          }
        })
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.tempData.tempFormModel = {}
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    }
  }

}
</script>
