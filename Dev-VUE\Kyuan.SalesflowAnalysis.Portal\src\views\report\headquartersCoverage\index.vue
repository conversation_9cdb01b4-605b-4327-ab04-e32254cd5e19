<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="span">
          <el-select
            v-model="listQuery.headquartersCoverageType"
            class="filter-item"
            placeholder="统计维度"
          >
            <el-option
              v-for="item in headquartersCoverageTypeList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refManufacturerAndProductAndSpec"
            v-model="manufacturerAndProductAndSpecId"
            :loading="manufacturerLoading"
            :options="manufacturerAndProductAndSpecList"
            placeholder="厂商 / 产品 / 规格"
            style="width:100%"
            clearable
            class="filter-item"
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            @change="handleProductChange"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.provinceIds"
            class="filter-item"
            placeholder="省份"
            multiple
            clearable
          >
            <el-option
              v-for="item in province"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.receiverName"
            clearable
            placeholder="客户"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-date-picker
            v-model="listQuery.months"
            class="filter-item"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            format="yyyy-MM"
            value-format="yyyy-MM"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'HeadquartersCoverage_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'HeadquartersCoverage_Button_Export')"
            :loading="btnExportLoading"
            class="filter-item-button"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <!-- <el-row>
          <el-col :span="24">
            <label class="dataupdate">流向截止日期：{{ dataUpdateTime }}</label>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col :span="24">
            <el-table
              v-loading="listLoading"
              :data="list"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%;"
              :default-sort="{prop: 'createTime', order: 'descending'}"
              :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass"
              @sort-change="sortChange"
            >
              <el-table-column
                fixed
                label="序号"
                type="index"
                align="center"
                :index="indexMethod"
              />
              <el-table-column
                fixed
                label="厂家"
                min-width="100px"
                header-align="center"
                align="center"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.manufacturerName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                fixed
                label="产品"
                min-width="90px"
                align="center"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.productName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                fixed
                label="规格"
                min-width="80px"
                align="center"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.spec }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="省份"
                min-width="100px"
                header-align="center"
                align="center"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.provinceName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="连锁总部名称"
                min-width="260px"
                header-align="center"
                align="left"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.receiverName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="月份"
                min-width="130px"
                header-align="center"
                align="center"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.currentDate }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="门店总数"
                min-width="100px"
                header-align="center"
                align="center"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.totalQty }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="流向覆盖数"
                min-width="90px"
                header-align="center"
                align="center"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.coverageQty }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="门店覆盖率(%)"
                min-width="120px"
                header-align="center"
                align="center"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.salesCoverageRate }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col class="el-colRight">
            <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
          </el-col>
        </el-row>
      </el-row>
    </div>
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出报表"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import CustomExport from '@/components/Export/CustomExport'
import ProductService from '@/api/product'
import LocationService from '@/api/location'
import MasterDataService from '@/api/masterData'

import ReportService from '@/api/report'

export default {
  name: 'HeadquartersCoverageReport',
  components: {
    Pagination,
    CustomExport
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 0,
        pageSize: 10,
        months: [],
        headquartersCoverageType: 3
      },
      listLoading: false,
      list: [],
      manufacturerLoading: false,
      manufacturerAndProductAndSpecId: [],
      manufacturerAndProductAndSpecList: [],
      province: [],
      headquartersCoverageTypeList: [],
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: {}
    }
  },
  created() {
    const year = new Date(Date.now()).getFullYear()
    this.listQuery.months = [year + '-01', year + '-12']
    this.initManufacturerAndProductAndSpec()
    this.queryProvince()
    this.initHeadquartersCoverageType()
    this.handleFilter()
  },
  methods: {
    initHeadquartersCoverageType() {
      var param = {
        enumType: 'HeadquartersCoverageType'
      }
      MasterDataService.GetEnumInfos(param)
        .then(result => {
          this.headquartersCoverageTypeList = result.data.datas
          this.$forceupdate()
        })
        .catch(() => {})
    },
    initManufacturerAndProductAndSpec() {
      this.productAndSpecLoading = true
      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then((result) => {
          this.manufacturerAndProductAndSpecList = result
          this.manufacturerLoading = false
        })
        .catch(() => {
          this.manufacturerLoading = false
        })
    },
    queryProvince() {
      LocationService.QueryProvince()
        .then((result) => {
          this.province = result
        })
        .catch((error) => {
          console.log(error)
        })
    },
    handleProductChange() {
      this.$refs.refManufacturerAndProductAndSpec.dropDownVisible = false
    },
    timeChange() {
      this.$forceUpdate()
    },
    handleFilter() {
      if (this.listQuery.months != null && this.listQuery.months.length > 0) {
        this.listQuery.pageIndex = 1
        this.getList()
      } else {
        this.$notice.message('请选择日期', 'error')
      }
    },
    getList() {
      if (this.manufacturerAndProductAndSpecId) {
        const [manufacturerId, productId, productSpecId] = this.manufacturerAndProductAndSpecId
        this.listQuery.productId = productId
        this.listQuery.productSpecId = productSpecId
        this.listQuery.manufacturerId = manufacturerId
      }
      this.listLoading = true
      ReportService.QueryHeadquartersCoverage(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      ReportService.GetHeadquartersCoverageExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      var exportParam = JSON.parse(JSON.stringify(this.listQuery))
      exportParam.checkedColumns = checkColumns
      ReportService.ExportHeadquartersCoverage(exportParam)
        .then(result => {
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '医疗客户报表.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '医疗客户报表.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    }
  }
}
</script>
<style>
  .dataupdate{
    font-size: 14px;
    line-height: 25px;
    color: #606266;
  }
</style>
