<template>
  <el-dialog
    title="配置ExcelSheet字段匹配关系"
    width="60%"
    :close-on-click-modal="false"
    :visible="showDialog"
    append-to-body
    @close="cancle()"
  >
    <el-form
      ref="dataForm"
      :model="bonusVariableDetailMapping"
      label-position="right"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item>
            <el-table
              :data="bonusVariableDetailMappingList"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%;"
              :default-sort="{prop: 'createTime', order: 'descending'}"
              :header-cell-class-name="'tableStyle'"
            >
              <el-table-column
                prop="ColumnName"
                label="Sheet字段名称"
                header-align="center"
                align="center"
                min-width="60px"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.columnName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="对应数据库字段" min-width="60px" align="center">
                <template slot-scope="{ row }">
                  <el-select
                    v-model="row.bonusVariableDetailId"
                    style="width: 100%"
                    class="filter-item"
                    placeholder="对应数据库字段"
                    filterable
                    :disabled="row.tempEnable"
                    @change="variableDetailChange(row.bonusVariableDetailId,row)"
                  >
                    <el-option
                      v-for="item in bonusVariableDetails"
                      :key="item.id"
                      :label="item.title"
                      :value="item.id"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="数据库字段说明" min-width="140px" align="left" header-align="center">
                <template slot-scope="{ row }">
                  {{ row.remark }}
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <i v-if="row.tempEnable" class="el-icon-delete eltablei" @click="handleDelete(row)" />
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" icon="el-icon-check" @click="handleSave">
        保存
      </el-button>
      <el-button icon="el-icon-close" @click="closeDialog">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import BonusService from '@/api/bonus'
export default {
  components: {
  },
  data() {
    return {
      span: 12,
      dialogReceiverVisible: false,
      bonusVariableDetailMappingList: [],
      bonusVariableDetails: [],
      bonusVariableDetailMapping: { tempEnable: { type: Boolean, default: false }, remark: '' },
      showDialog: false,
      listLoading: false
    }
  },
  methods: {
    initPage(row) {
      this.showDialog = true
      this.initVariableDetailMappings(row.id)
      this.initVariableDetails(row.bonusVariableId)
    },
    initVariableDetailMappings(curBonusVariableMappingId) {
      const para = { bonusVariableMappingId: curBonusVariableMappingId }
      BonusService.QueryBonusVariableDetailMapping(para)
        .then((result) => {
          if (result.succeed) {
            this.bonusVariableDetailMappingList = result.data
            this.bonusVariableDetailMappingList.forEach((item) => {
              if (item.bonusVariableDetailId !== null && item.bonusVariableDetailId !== undefined) {
                item.tempEnable = true
                item.remark = '数据库字段：' + item.bonusVariableDetailDataName + '；'
                if (item.bonusVariableDetailRemark.length > 0) {
                  item.remark += '说明：' + item.bonusVariableDetailRemark + '；'
                }
              } else {
                item.tempEnable = false
              }
            })
          } else {
            this.$notice.resultTip(result)
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    initVariableDetails(curBonusVariableId) {
      const para = { bonusVariableId: curBonusVariableId }
      BonusService.QueryBonusVariableDetailSelect(para).then((res) => { this.bonusVariableDetails = res })
    },
    cancle() {
      this.showDialog = false
    },
    variableDetailChange(selectId, row) {
      if (selectId !== null && selectId !== '') {
        let variableDetail = {}
        variableDetail = this.bonusVariableDetails.find((item) => {
          return selectId === item.id
        })
        row.bonusVariableDetailRemark = variableDetail.remark
        row.bonusVariableDetailDataName = variableDetail.dataName
        row.remark = '数据库字段：' + row.bonusVariableDetailDataName + '；'
        if (row.bonusVariableDetailRemark.length > 0) {
          row.remark += '说明：' + row.bonusVariableDetailRemark + '；'
        }
      }
    },
    handleSave() {
      var mapping = this.bonusVariableDetailMappingList.find((item) => {
        return item.bonusVariableDetailId === undefined
      })
      if (mapping != null && mapping !== undefined) {
        this.$notice.message(mapping.columnName + '对应选项不可为空；', 'error')
        return false
      }
      this.listLoading = true
      var saveData = this.bonusVariableDetailMappingList.filter(item => item.tempEnable === false)
      if (saveData.length === 0) {
        this.$notice.message('没有要保存的选项', 'error')
        return
      }
      BonusService.UpdateBonusVariableDetailMapping(saveData)
        .then((result) => {
          this.listLoading = false
          if (result.succeed) {
            this.$notice.message('保存成功', 'success')
            this.closeDialog()
          } else {
            this.$notice.message('保存失败，请联系管理员', 'error')
          }
        })
        .catch(() => {
          this.listLoading = false
          this.loading = false
        })
    },
    closeDialog() {
      this.bonusVariableDetails = []
      this.showDialog = false
      this.$emit('success')
    },
    handleDelete(row) {
      row.bonusVariableDetailId = undefined
      row.bonusVariableDetailRemark = ''
      row.bonusVariableDetailDataName = ''
      row.tempEnable = false
      row.remark = ''
    }
  }
}
</script>
<style scoped>
  .el-dialog-s {
  z-index: 12;
  }
  .el-dialog-ls {
  z-index: 13;
  }
</style>
