<template>
  <div>
    <el-dialog :title="title" width="70%" :close-on-click-modal="false" :visible="showAddDialog" @close="cancle()">
      <el-form ref="dataForm" :model="tempFormModel" label-position="right" label-width="120px">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix" style="height: 4px; font-size: 12px;">
            <span>查看产品</span>
          </div>
          <div>
            <el-row :gutter="10" type="flex" style="flex-wrap:wrap">
              <el-col :span="12">
                <el-form-item label="政策模板">
                  {{ tempFormModel.agreementPolicyTypeName }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="厂商/产品/规格">
                  {{ `${tempFormModel.manufacturerName} / ${tempFormModel.productNameCn} / ${tempFormModel.spec}` }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="供货价">
                  {{ tempFormModel.supplyPrice }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="建议零售价">
                  {{ tempFormModel.retailPrice }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="协议数量">
                  {{ tempFormModel.minimumPurchaseQuantity }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="销售折扣">
                  {{ tempFormModel.rebateUnitPrice }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="返利比例">
                  {{ tempFormModel.rebateProportion }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="税率(%)">
                  {{ tempFormModel.taxRate }}
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-card class="box-card" shadow="never" style="margin-top: 10px;">
          <div slot="header" class="clearfix" style="height: 4px; font-size: 12px;">
            <span>指标信息</span>
          </div>
          <div>
            <el-row :gutter="10" type="flex" style="flex-wrap:wrap">
              <el-col :span="12">
                <el-form-item label="指标数量">
                  {{ tempFormModel.quotaQuantity }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="一季度指标数量">
                  {{ tempFormModel.q1Quota }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="二季度指标数量">
                  {{ tempFormModel.q2Quota }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="三季度指标数量">
                  {{ tempFormModel.q3Quota }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="四季度指标数量">
                  {{ tempFormModel.q4Quota }}
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-card class="box-card" shadow="never" style="margin-top: 10px;">
          <div slot="header" class="clearfix" style="height: 4px; font-size: 12px; ">
            <span>满赠信息</span>
          </div>
          <div>
            <el-row :gutter="10" type="flex" style="flex-wrap:wrap">
              <el-col :span="12">
                <el-form-item label="满赠标准">
                  {{ tempFormModel.giftStandard }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="赠送数量">
                  {{ tempFormModel.giftQuantity }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="满赠厂商/产品/规格">
                  {{ `${tempFormModel.giftManufacturerName} / ${tempFormModel.giftProductNameCn} / ${tempFormModel.giftSpec}` }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="满赠是否参与计算">
                  {{ tempFormModel.giftNeedComputeStr }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="销售组数量">
                  {{ tempFormModel.salesGroupQuantity }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="销售组返利金额">
                  {{ tempFormModel.salesGroupRebate }}
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>

export default {
  components: {
  },
  data() {
    return {
      tempFormModel: {},
      showAddDialog: false
    }
  },
  methods: {
    init(rebateAgreementProduct) {
      this.showAddDialog = true
      this.tempFormModel = rebateAgreementProduct
    },
    cancle() {
      this.showAddDialog = false
      this.tempFormModel = {}
    }
  }
}
</script>
