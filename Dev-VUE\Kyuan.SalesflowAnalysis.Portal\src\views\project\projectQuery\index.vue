<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filter-container">
        <el-col :span="4">
          <el-input
            v-model="listQuery.name"
            clearable
            placeholder="项目名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.enumCostType"
            value-key="value"
            class="filter-item"
            placeholder="费用类型"
            clearable
          >
            <el-option
              v-for="item in costTypeList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProductAndSpec"
            v-model="manufacturerProductAndSpecId"
            style="width: 100%"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            placeholder="厂商/产品/规格"
            clearable
            class="filter-item"
            :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.departmentId"
            value-key="value"
            class="filter-item"
            placeholder="部门"
            clearable
          >
            <el-option
              v-for="item in deptList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="listQuery.timeRange"
            clearable
            class="filter-item"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select v-model="listQuery.enumStatus" class="filter-item" placeholder="状态" clearable @change="selectUpdate">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.desc" :value="item.value" />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'RebateProject_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'RebateProject_Button_Add')"
            type="primary"
            icon="el-icon-plus"
            @click="handleAddProject"
          >
            新增
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'RebateProject_Button_Export')"
            :loading="btnExportLoading"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="rebateProjectList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              label="项目名称"
              sortable="custom"
              min-width="150px"
              header-align="center"
              align="left"
              prop="Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>

            <el-table-column label="费用类型" align="center" sortable="custom" min-width="100px" prop="EnumCostType">
              <template slot-scope="{ row }">
                <span>{{ row.enumCostTypeDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="起始日期" align="center" sortable="custom" min-width="100px" prop="StartTime">
              <template slot-scope="{ row }">
                <span v-if="row.startTime">{{ row.startTime |parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="截止日期" align="center" sortable="custom" min-width="100px" prop="EndTime">
              <template slot-scope="{ row }">
                <span v-if="row.endTime">{{ row.endTime |parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="预计销售金额"
              min-width="100px"
              sortable="custom"
              align="right"
              prop="ProductSpecs"
            >
              <template slot-scope="{ row }">
                <span>{{ row.totalIncome | toTwoNum }}</span>
              </template>
            </el-table-column>
            <el-table-column label="部门" align="center" min-width="100px" prop="EnumCostType">
              <template slot-scope="{ row }">
                <span>{{ row.departmentNames }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="品规"
              min-width="150px"
              align="center"
              prop="ProductSpecs"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productSpecs }}</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" align="center" sortable="custom" min-width="80px" prop="EnumStatus">
              <template slot-scope="{ row }">
                <span>{{ row.enumStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="120" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i v-if="$isPermitted($store.getters.user, 'RebateProject_Button_Edit')" class="el-icon-edit-outline eltablei" @click="handleUpdate(row)" />
                <i class="el-icon-document eltablei" @click="handleDetail(row)" />
                <i v-if="$isPermitted($store.getters.user, 'RebateProject_Button_Del')" class="el-icon-delete eltablei" @click="handleDelete(row)" />
              </template>
            </el-table-column>

          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
    <AddProject ref="refAddProject" @success="handleFilter" />
    <ProjectDetail ref="refProjectDetail" />
    <el-dialog
      :visible="showExportModal"
      :close-on-click-modal="false"
      title="导出项目"
      width="800"
      @close="handleExportCancel"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>
<script>
import ProjectService from '@/api/project'
import ProductService from '@/api/product'
import MasterDataService from '@/api/masterData'
import MaintenanceService from '@/api/maintenance'
import Pagination from '@/components/Pagination'

import AddProject from './components/addProject'
import ProjectDetail from './components/projectDetail'
import CustomExport from '@/components/Export/CustomExport'
export default {
  name: 'ProjectQuery',
  components: {
    Pagination,
    AddProject,
    ProjectDetail,
    CustomExport
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      rebateProjectList: [],
      productAndSpecList: [],
      productAndSpecLoading: false,
      manufacturerProductAndSpecId: [],
      deptList: [],
      costTypeList: [],
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: [],
      statusOptions: []
    }
  },
  created() {
    this.initManufacturerAndProductAndSpec()
    this.handleFilter()
    this.initDept()
    this.initCostType()
    this.initStatusOptions()
  },
  methods: {
    initManufacturerAndProductAndSpec() {
      this.productAndSpecLoading = true

      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    initDept() {
      MaintenanceService.QueryDepartmentForProject()
        .then((result) => {
          this.deptList = result.filter(item => item.parentKey !== undefined)
        })
        .catch(() => {

        })
    },
    initCostType() {
      var param = {
        enumType: 'CostType'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.costTypeList = result.data.datas
        })
        .catch((error) => {
          console.log(error)
        })
    },
    initStatusOptions() {
      var param = {
        enumType: 'RebateProjectStatus'
      }
      MasterDataService.GetEnumInfos(param)
        .then(result => {
          this.statusOptions = result.data.datas
          this.$forceupdate()
        })
        .catch(() => { })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      if (this.manufacturerProductAndSpecId) {
        const [manufacturerId, productId, productSpecId] = this.manufacturerProductAndSpecId
        this.listQuery.manufacturerId = manufacturerId
        this.listQuery.productId = productId
        this.listQuery.productSpecId = productSpecId
      }

      this.listLoading = true
      this.listQuery.quotaType = 1
      ProjectService.QueryRebateProject(this.listQuery).then(res => {
        this.listLoading = false
        this.rebateProjectList = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(() => {
        this.listLoading = true
      })
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    handleAddProject() {
      this.$refs.refAddProject.initPage(null)
    },
    handleUpdate(row) {
      this.$refs.refAddProject.initPage(row.id)
    },
    handleDetail(row) {
      this.$refs.refProjectDetail.initPage(row.id)
    },
    handleDelete(row) {
      this.$confirm('确定删除此项目吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        ProjectService.DeleteRebateProject(row).then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('删除成功', 'success')
          }
        })
          .catch(error => {
            this.listLoading = false
            console.log(error)
          })
      }).catch(error => {
        this.listLoading = false
        if (!error.succeed) {
          this.$notice.message('取消删除', 'info')
        }
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      ProjectService.GetProjectExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      this.listQuery.checkedColumns = checkColumns
      ProjectService.ExportProject(this.listQuery)
        .then(result => {
          this.listQuery.checkedColumns = null
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '项目信息.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '项目信息.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    selectUpdate() {
      this.$forceUpdate()
    }
  }
}
</script>
<style scoped>
.flexwarp {
    display: flex;
    flex-wrap: wrap;
}

</style>

