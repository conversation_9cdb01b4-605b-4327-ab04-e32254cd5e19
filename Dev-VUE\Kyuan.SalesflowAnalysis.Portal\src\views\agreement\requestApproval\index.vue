<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filter-container">
        <el-col :span="span">
          <el-select
            v-model="listQuery.departmentId"
            value-key="value"
            class="filter-item"
            placeholder="部门"
            clearable
            @change="selectUpdate"
          >
            <el-option
              v-for="item in deptList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.requestCode"
            clearable
            placeholder="申请单号"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="listQuery.requestFormRebateAgreementName"
            clearable
            placeholder="协议名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="6">
          <el-cascader
            ref="refProductAndSpec"
            v-model="manufacturerProductAndSpecId"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            placeholder="厂商 / 产品 / 规格"
            style="width:100%"
            clearable
            class="filter-item"
            :props="{ multiple: true, checkStrictly: false ,expandTrigger: 'hover', emitPath: true }"
            @change="handleProductChange"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.enumFormStatus"
            class="filter-item"
            placeholder="状态"
            clearable
          >
            <el-option
              v-for="item in enumFormStatusList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="listQuery.rebateReceiverName"
            clearable
            placeholder="返利接收方"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.isExceedBasePrice"
            class="filter-item"
            placeholder="超出项目补偿单价"
            clearable
          >
            <el-option
              v-for="item in trueAndFalseList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="requestApprovalList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              fixed="left"
              label="申请单号"
              sortable="custom"
              min-width="120px"
              header-align="center"
              align="left"
              prop="RequestCode"
            >
              <template slot-scope="{ row }">
                <span>{{ row.requestCode }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="协议名称"
              sortable="custom"
              min-width="220px"
              header-align="center"
              align="left"
              prop="RequestFormRebateAgreement.Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.requestFormRebateAgreementName }}</span>
              </template>
            </el-table-column>

            <el-table-column label="部门" align="center" sortable="custom" min-width="100px" prop="RequestFormRebateAgreement.Department.Name">
              <template slot-scope="{ row }">
                <span>{{ row.departmentName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="返利接收方"
              sortable="custom"
              min-width="150px"
              header-align="center"
              align="left"
              prop="RequestFormRebateAgreement.RebateReceiver.Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.rebateReceiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="品规"
              min-width="150px"
              align="center"
              prop="RequestFormRebateAgreement.ProductSpecDesc"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productSpecDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="申请人"
              min-width="120px"
              align="center"
              prop="ProposerName"
            >
              <template slot-scope="{ row }">
                <span>{{ row.proposerName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="单据类型"
              min-width="120px"
              align="center"
              prop="EnumFormTypeDesc"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumFormTypeDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="单据状态"
              min-width="120px"
              align="center"
              prop="EnumFormStatusDesc"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumFormStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="是否超出项目补偿单价"
              min-width="160px"
              align="center"
              prop="isExceedBasePrice"
            >
              <template slot-scope="{ row }">
                <span>{{ row.isExceedBasePrice?'是': '否' }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="60px" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i class="el-icon-document eltablei" title="查看明细" @click="reviewDetail(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
    <RequestFormView ref="dialogRequestFormView" />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

import RequestFormRebateService from '@/api/requestFormRebate'
import MaintenanceService from '@/api/maintenance'
import ProductService from '@/api/product'

import RequestFormView from './components/requestFormView'

export default {
  name: 'RequestApproval',
  components: {
    Pagination,
    RequestFormView
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      requestApprovalList: [],
      deptLoading: false,
      deptList: [],
      enumFormStatusList: [{ desc: '审批中', value: 1 }, { desc: '已完成', value: 2 }],
      productAndSpecList: [],
      productAndSpecLoading: false,
      manufacturerProductAndSpecId: [],
      trueAndFalseList: [
        {
          value: true,
          label: '是'
        },
        {
          value: false,
          label: '否'
        }
      ]
    }
  },
  created() {
    this.initDept()
    this.initProductAndSpec()
    this.handleFilter()
  },
  methods: {
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch(() => {
          this.deptLoading = false
        })
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch(() => {
          this.productAndSpecLoading = false
        })
    },
    getList() {
      if (this.manufacturerProductAndSpecId) {
        this.listQuery.ProductSpecIds = this.manufacturerProductAndSpecId.map(element => element[2])
      }

      this.listLoading = true
      this.listQuery.quotaType = 1
      RequestFormRebateService.QueryRequestFormRebateAgreementApproval(this.listQuery).then(res => {
        this.listLoading = false
        this.requestApprovalList = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(() => {
        this.listLoading = true
      })
    },
    selectUpdate() {
      this.$forceUpdate()
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    reviewDetail(row) {
      this.$refs.dialogRequestFormView.initPage(row.id)
    },
    handleProductChange() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    }
  }
}
</script>

