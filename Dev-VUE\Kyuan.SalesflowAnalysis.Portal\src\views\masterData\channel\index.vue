<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="4">
          <el-date-picker
            v-model="listQuery.StartTime"
            format="yyyy-MM"
            value-format="yyyy-MM"
            type="month"
            clearable
            placeholder="渠道月份"
            style="width: 100%;"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.receiverName"
            clearable
            placeholder="客户名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProductAndSpec"
            :key="productAndSpecKey"
            v-model="productAndSpecId"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            placeholder="产品/规格"
            class="filter-item"
            clearable
            :props="{ checkStrictly: true, expandTrigger: 'hover' }"
            @change="productChange"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select v-model="listQuery.isUse" placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.enumTier"
            clearable
            placeholder="渠道级别"
          >
            <el-option
              v-for="item in tierList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'ProductChannel_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'ProductChannel_Button_Add')"
            type="primary"
            icon="el-icon-plus"
            @click="$refs.createFormRef.handleCreate()"
          >
            新增渠道
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'ProductChannel_Button_Export')"
            :loading="btnExportLoading"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column fixed="left" label="序号" type="index" align="center" :index="indexMethod" />
            <el-table-column
              label="省份"
              sortable="custom"
              header-align="center"
              min-width="100"
              align="center"
              prop="Receiver.Province.NameCn"
            >
              <template slot-scope="{ row }">
                <span>{{ row.receiverProvinceName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="产品"
              sortable="custom"
              min-width="150px"
              header-align="center"
              align="center"
              prop="ProductSpec.Product.NameCn"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productNameCn +"/"+row.spec }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="经销商"
              min-width="200px"
              sortable="custom"
              prop="Receiver.Name"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.receiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="上游商业"
              min-width="200px"
              prop="Distributor.Name"
              header-align="center"
              align="center"
              sortable="custom"
            >
              <template slot-scope="{ row }">
                <span>{{ row.distributorName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="生效日期" sortable="custom" min-width="100px" prop="StartTime" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.startTime | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="截止日期" sortable="custom" min-width="100px" prop="StopTime" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.stopTime | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="渠道级别"
              sortable="custom"
              min-width="100px"
              prop="EnumTier"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumTierDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="90px"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i v-if="row.isUse && $isPermitted($store.getters.user, 'ProductChannel_Button_Edit')" class="el-icon-edit-outline eltablei" title="变更渠道" @click="$refs.changeChannel.initData(row)" />
                <i v-if="row.isUse && $isPermitted($store.getters.user, 'ProductChannel_Button_Del')" class="el-icon-circle-close eltablei" title="停用" @click="$refs.stopChannel.initData(row)(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <createForm ref="createFormRef" @success="getList" />
    <changeChannel ref="changeChannel" @success="getList" />
    <stopChannel ref="stopChannel" @success="getList" />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import channelService from '@/api/channel'
import ProductService from '@/api/product'
import createForm from './components/createForm'
import changeChannel from './components/changeChannel.vue'
import stopChannel from './components/stopChannel.vue'
import MasterDataService from '@/api/masterData'

export default {
  name: 'Channel',
  components: {
    Pagination,
    createForm,
    changeChannel,
    stopChannel
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime',
        StartTime: undefined,
        ProductSpecIds: undefined,
        isUse: true
      },
      listLoading: false,
      list: [],
      // manufacturerLoading: false,
      productAndSpecLoading: false,
      // manufacturerList: [],
      productAndSpecList: null,
      productAndSpecId: [],
      btnExportLoading: false,
      dialogEditFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑',
        create: '新增'
      },
      modifyDialogTitle: '',
      modifyDialogIsReadonly: false,
      itemId: null,
      showExportModal: false,
      columnDictionary: {},
      dialogImportVisible: false,
      productAndSpecKey: 0,
      options: [{
        value: true,
        label: '正常'
      }, {
        value: false,
        label: '已停用'
      }],
      tierList: []
    }
  },
  created() {
    this.initProductAndSpec()
    this.handleFilter()
    this.initTier()
  },
  methods: {
    initTier() {
      var param = {
        enumType: 'Tier',
        group: 'ChannelManager'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.tierList = result.data.datas
        })
        .catch(() => {
        })
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      if (this.productAndSpecId) {
        this.listQuery.ProductSpecIds = this.productAndSpecId.map(element => element[2])
      }
      channelService.QueryChannels(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    onHidden() {
      this.dialogEditFormVisible = false
    },
    onRefresh() {
      this.getList()
      this.dialogEditFormVisible = false
    },
    // 显示导出model
    onShowExportModal() {
      channelService.ExportReceiverProductChannel(this.listQuery)
        .then(result => {
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '产品渠道.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '产品渠道.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    productChange() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    }

  }
}
</script>
