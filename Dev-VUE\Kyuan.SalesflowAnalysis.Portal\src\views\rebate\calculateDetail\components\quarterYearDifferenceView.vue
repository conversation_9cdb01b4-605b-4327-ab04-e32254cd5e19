<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      title="年度统算差额明细查看"
      :visible="dialogVisible"
      width="80%"
      @close="handleClose"
    >
      <el-row style="margin-bottom:20px;">
        <el-col :span="24">
          <el-table
            :data="rebateResultList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
          >
            <el-table-column fixed label="序号" type="index" align="center" />
            <el-table-column label="返利协议名称" min-width="220px" header-align="center" align="left">
              <template slot-scope="{ row }">
                <span>{{ row.rebateAgreementName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="起始日期" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span v-if="row.startDate">{{
                  row.startDate | parseTime("{y}-{m}-{d}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="截止日期" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span v-if="row.endDate">{{
                  row.endDate | parseTime("{y}-{m}-{d}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="返利接收方名称" align="left" header-align="center" min-width="220px">
              <template slot-scope="{ row }">
                <span>{{ row.rebateReceiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="产品名称/规格" align="center" min-width="130px">
              <template slot-scope="{ row }">
                <span>{{ row.productAndSpec }}</span>
              </template>
            </el-table-column>
            <el-table-column label="计算周期" min-width="100px" header-align="center" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.enumRebateComputeCycleFlagDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="返利金额" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span>{{ row.rebateAmount | toMoney }}元</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-row>
        <el-form
          ref="dataForm"
          :model="rebateResult"
          label-position="left"
          label-width="100px"
          class="el-dialogform"
          style="width:95%"
        >
          <el-col :span="span">
            <el-form-item label="季度结算金额：">
              {{ rebateResult.quarterAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="年度统算金额：">
              {{ rebateResult.yearAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="统算差额：">
              {{ rebateResult.difference }}
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleClose()"> 关闭 </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import RebateService from '@/api/rebate'

export default {
  data() {
    return {
      span: 8,
      dialogVisible: false,
      rebateResultList: [],
      rebateResult: {
        quarterAmount: 0,
        yearAmount: 0,
        difference: 0
      },
      filter: {
        pageIndex: 1,
        pageSize: 1000,
        order: '-EnumRebateComputeCycleFlag'
      },
      listLoading: false
    }
  },
  methods: {
    initPage(agreementId) {
      this.getResultDetailList(agreementId)
      this.dialogVisible = true
    },
    getResultDetailList(agreementId) {
      this.listLoading = true
      this.filter.rebateAgreementId = agreementId
      RebateService.QueryRebateResultByAgreementId(this.filter).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.rebateResultList = result.data.datas

          const quarters = this.rebateResultList.filter(o => o.enumRebateComputeCycleFlag === 2)
          this.rebateResult.quarterAmount = quarters.reduce((sum, current) => sum + current.rebateAmount, 0)
          const year = this.rebateResultList.find(o => o.enumRebateComputeCycleFlag === 1)
          this.rebateResult.yearAmount = year.rebateAmount

          this.rebateResult.difference = (this.rebateResult.yearAmount - this.rebateResult.quarterAmount).toFixed(4)
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    }
  }
}
</script>
<style scoped>
</style>
