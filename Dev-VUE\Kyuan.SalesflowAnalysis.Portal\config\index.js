module.exports = {
  /**
   * @description 配置显示在浏览器标签的title
   */
  title: '科园流向分析系统',

  /**
   * @description 配置存储token的itemName
   */
  tokenName: 'Kyuan_sfa_token',

  /**
   * @description api请求基础路径
   */
  apiUrl: {
    dev: 'https://localhost:5001/',
    pro: 'https://www.kyddi.cn:8311/api/'
    // 'http://xl7.corp.shinsoft.net:9203/api/'
    // http://xl8.corp.shinsoft.net:9224/Kyuan/SalesflowAnalysis/api/
    // 本地测试
    // http://************:8909/
    // UAT
    // https://www.kyddi.cn:8311/api/
    // 生产环境
    // https://www.kyddi.cn:8808/api/
  },

  /**
   * @type {Integer} 60000
   * @description 设置API超时时间，单位豪秒
   */
  apiDuration: 60 * 3 * 1000,

  /**
   * @description 默认打开的首页的路由name值，默认为home
   */
  homeName: 'home',
  /**
   * @description 默认打开的登录的路由name值，默认为login
   */
  loginName: 'login',
  /**
   * @description 下载附件的API地址
   */
  downloadFileApi: 'File/DownloadFile',

  /**
   * @type {boolean} true | false
   * @description Whether show the top banner
   */
  showBanner: true,

  /**
   * @type {boolean} true | false
   * @description Whether show the settings right-panel
   */
  showSettings: false,

  /**
   * @type {boolean} true | false
   * @description Whether show the search in Header
   */
  showSearch: false,

  /**
   * @type {boolean} true | false
   * @description Whether need tagsView
   */
  tagsView: true,

  /**
   * @type {boolean} true | false
   * @description Whether fix the header
   */
  fixedHeader: true,

  /**
   * @type {boolean} true | false
   * @description Whether show the logo in sidebar
   */
  sidebarLogo: true,

  /**
   * @type {boolean} true | false
   * @description Whether show the button of back to top
   */
  showBackToTop: false,

  /**
   * @type {string | array} 'production' | ['production', 'development']
   * @description Need show err logs component.
   * The default is only used in the production env
   * If you want to also use it in dev, you can pass ['production', 'development']
   */
  errorLog: 'production'
}
