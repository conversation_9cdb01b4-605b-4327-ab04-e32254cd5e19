import HttpApi from './libs/api.request'

const controller = 'HospitalStore'

const api = new HttpApi(controller)

export default {
  QueryHospitalStore(params) {
    return api.get('QueryHospitalStore', params)
  },
  GetTheLatestHospitalStoreCycle() {
    return api.get('GetTheLatestHospitalStoreCycle')
  },
  GetHospitalStoreExportColumn() {
    return api.get('GetHospitalStoreExportColumn')
  },
  ExportHospitalStore(params) {
    return api.post('ExportHospitalStore',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  QueryImportHospitalStoreMasterTemp(params) {
    return api.get('QueryImportHospitalStoreMasterTemp', params)
  },
  QueryImportHospitalStoreError(params) {
    return api.get('QueryImportHospitalStoreError', params)
  },
  ExportImportHospitalStoreError(params) {
    return api.post('ExportImportHospitalStoreError',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  GetHospitalStore(params) {
    return api.get('GetHospitalStore', params)
  },
  UpdateHospitalStore(params) {
    return api.post('UpdateHospitalStore', params)
  },
  DeleteHospitalStore(params) {
    return api.post('DeleteHospitalStore', params)
  }
}
