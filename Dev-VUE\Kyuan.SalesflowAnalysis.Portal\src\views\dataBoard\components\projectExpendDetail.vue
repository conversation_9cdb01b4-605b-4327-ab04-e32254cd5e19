<template>
  <div class="panel-group">
    <el-card style="height: 380px;">
      <div slot="header" class="clearfix cardHeader">
        <span>项目支出费用明细</span>
      </div>

      <div ref="refProjectExpendChart" style="width: 100%; height: 350px;" />
    </el-card>

  </div>
</template>

<script>
import echarts from 'echarts'
import dashboardService from '@/api/dashboard'
export default {
  data() {
    return {
      filter: {},
      legendData: [],
      xAxisData: []
    }
  },
  methods: {
    initPage(param) {
      this.filter = param
      dashboardService.GetDataBoardProjectExpend(this.filter)
        .then((res) => {
          this.initChart(res)
        })
        .catch(() => {

        })
    },
    initChart(model) {
      this.xAxisData = []
      model.projectNames.forEach(item => {
        var tempStr = `${item}[预计/实际]`
        this.xAxisData.push(tempStr)
      })

      var series = []
      model.detailModels.forEach(element => {
        this.legendData.push(element.costName)

        var quotaItem = {
          data: element.expectedCosts,
          type: 'bar',
          stack: '预计',
          name: element.costName,
          emphasis: {
            focus: 'series'
          }
        }
        series.push(quotaItem)

        var quantityItem = {
          data: element.actualCosts,
          type: 'bar',
          stack: '实际',
          name: element.costName,
          emphasis: {
            focus: 'series'
          }
        }
        series.push(quantityItem)
      })
      // var achievementRateItem = {
      //   data: model.achievementRates,
      //   name: '达成率',
      //   type: 'line',
      //   yAxisIndex: 1,
      //   interval: 5
      // }
      // series.push(achievementRateItem)
      this.initRegionDataChart(series)
    },
    initRegionDataChart(series) {
      const stackInfo = {}
      for (let i = 0; i < series[0].data.length; ++i) {
        for (let j = 0; j < series.length; ++j) {
          const stackName = series[j].stack
          if (!stackName) {
            continue
          }
          if (!stackInfo[stackName]) {
            stackInfo[stackName] = {
              stackStart: [],
              stackEnd: []
            }
          }
          const info = stackInfo[stackName]
          const data = series[j].data[i]
          if (data && data !== '-') {
            if (info.stackStart[i] == null) {
              info.stackStart[i] = j
            }
            info.stackEnd[i] = j
          }
        }
      }
      for (let i = 0; i < series.length; ++i) {
        const data = series[i].data

        for (let j = 0; j < series[i].data.length; ++j) {
          data[j] = {
            value: data[j],
            itemStyle: {
              borderRadius: [0, 0, 0, 0]
            }
          }
        }
      }

      var option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          textStyle: {
            align: 'left'
          },
          formatter: function(params) {
            var result = params[0].name + '<br/>'
            var j = 0
            var quota = ''
            var quantity = ''
            for (var i = 0; i < params.length; i++) {
              if (params[i].value !== '-') {
                if (params[i].seriesIndex % 2 === 0) {
                  quota = params[i].value
                } else {
                  quantity = params[i].value
                }
                j++
                if (j === 2) {
                  result +=
											`<span style="background: ${params[i].color}; height:10px; width: 10px; border-radius: 50%;display: inline-block;margin-right:10px;"></span> ${params[i].seriesName}[预计/实际] ：${quota.toFixed(2)}/${quantity.toFixed(2)}<br/>`
                  j = 0
                  quota = ''
                  quantity = ''
                }
              }
            }
            return result
          },
          confine: true, // 加入这一句话
          position: function(point, params, dom, rect, size) {
            // 自定义提示框位置
            return [point[0], '10%'] // 第一个值是水平位置，第二个值是垂直位置
          }
        },
        legend: {
          data: this.legendData
        },
        grid: {
          top: '18%',
          left: '5%',
          right: '3%',
          bottom: '3%',
          containLabel: true,
          scale: true
        },
        xAxis: {
          type: 'category',
          data: this.xAxisData,
          axisLabel: {
            interval: 0,
            rotate: 35,
            fontSize: 10
          }
        },
        yAxis: [{
          type: 'value'
        }
        // {
        //   type: 'value',
        //   name: '',
        //   axisLabel: {
        //     formatter: '{value} %'
        //   }
        // }
        ],
        series: series
      }

      if (echarts.getInstanceByDom(this.$refs.refProjectExpendChart)) {
        echarts.dispose(this.$refs.refProjectExpendChart)
      }
      const myChartIncome = echarts.init(this.$refs.refProjectExpendChart)
      myChartIncome.setOption(option)
    }

  }
}
</script>

  <style lang="scss" scoped>
  .cardHeader {
    height: 5px;
  }
  </style>

