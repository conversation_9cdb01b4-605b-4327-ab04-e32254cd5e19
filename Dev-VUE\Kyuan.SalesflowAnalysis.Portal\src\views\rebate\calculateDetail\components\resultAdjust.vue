<template>
  <div>
    <el-dialog title="返利结果调整" width="60%" :close-on-click-modal="false" :visible="resultAdjustVisible" @close="handleCancle()">
      <el-form ref="dataForm" :rules="rules" :model="rebateResultDetailModel" label-position="right" label-width="120px" class="el-dialogform">
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="协议名称">
              {{ rebateResultModel.rebateAgreementName }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="返利金额">
              {{ rebateResultModel.rebateAmount | toMoney }}元
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="调整原因" prop="modifyReasonId">
              <el-select
                v-model="rebateResultDetailModel.modifyReasonId"
                style="width: 100%"
                class="filter-item"
                placeholder="调整原因"
                clearable
                @change="selectUpdate"
              >
                <el-option
                  v-for="item in modifyReasonList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="产品/规格" prop="productAndSpecId">
              <el-cascader
                v-model="rebateResultDetailModel.productAndSpecId"
                :options="productAndSpecList"
                placeholder="产品/规格"
                clearable
                class="filter-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="支付方" prop="paidToReceiverId">
              <el-select
                v-model="rebateResultDetailModel.paidToReceiverId"
                style="width:100%"
                placeholder="支付方"
                clearable
                @change="selectUpdate"
              >
                <el-option
                  v-for="item in paidToReceiverList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="目标终端" prop="receiverId">
              <el-select
                v-model="rebateResultDetailModel.receiverId"
                style="width:100%"
                placeholder="目标终端"
                clearable
                @change="selectUpdate"
              >
                <el-option
                  v-for="item in receiverList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="调整金额" prop="rebateAmount">
              <el-input
                v-model="rebateResultDetailModel.rebateAmount"
                placeholder="调整金额"
              >
                <i slot="suffix">元</i>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="rebateResultModel.rebateAgentType==1||rebateResultModel.rebateAgentType==2" :span="span">
            <el-form-item label="代付类型">
              {{ rebateResultModel.rebateAgentTypeDesc }}
            </el-form-item>
          </el-col>
          <el-col v-if="rebateResultModel.rebateAgentType==1" :span="span">
            <el-form-item label="代付方">
              <el-input
                v-model="agentReceiverName"
                placeholder="代付方"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col v-if="rebateResultModel.rebateAgentType==2" :span="span">
            <el-form-item label="代付方" prop="rebateAgentId">
              <el-select
                v-model="rebateResultDetailModel.rebateAgentId"
                style="width: 100%"
                class="filter-item"
                placeholder="代付方"
                clearable
                @change="selectUpdate"
              >
                <el-option
                  v-for="item in rebateAgentList"
                  :key="item.id"
                  :label="item.rebateReceiverName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                v-model="rebateResultDetailModel.modifyRemark"
                type="textarea"
                placeholder="备注"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="选择文件">
              <ImageUpload
                ref="upload"
                @getUploadFile="getUploadFile"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleCancle()">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleSave()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import MasterDataService from '@/api/masterData'
import RebateService from '@/api/rebate'
import ImageUpload from '@/views/components/uploadImage'
import FileService from '@/api/file'

export default {
  name: '',
  components: {
    ImageUpload
  },
  props: {
  },
  data() {
    return {
      resultAdjustVisible: false,
      span: 12,
      rules: {
        modifyReasonId: [
          { required: true, message: '请选择调整原因', trigger: 'change' }
        ],
        rebateAmount: [
          { required: true, message: '请输入调整金额', trigger: 'blur' },
          { pattern: /(^-?(?:\d+|\d{1,3}(?:,\d{3})+)(?:.\d{1,2})?$)/, message: '调整金额仅支持正负数，最多2位小数' }
        ],
        paidToReceiverId: [
          { required: true, message: '请选择支付方', trigger: 'change' }
        ],
        rebateAgentId: [
          { required: true, message: '请选择代付方', trigger: 'change' }
        ],
        receiverId: [
          { required: true, message: '请选择返利接收方', trigger: 'change' }
        ],
        productAndSpecId: [
          { required: true, message: '请选择产品/规格', trigger: 'change' }
        ]
      },
      productAndSpecList: [],
      rebateResultModel: {},
      rebateResultDetailModel: {
        productAndSpecId: []
      },
      modifyReasonList: [],
      rebateAgentList: [],
      agentReceiverName: '',
      receiverList: [],
      paidToReceiverList: [],
      fileList: [],
      method: 'RebateResultAdjust',
      controller: 'Rebate'
    }
  },
  created() {
    this.initModifyReason()
    this.initAgentType()
  },
  methods: {
    init(data) {
      this.rebateResultModel = data
      this.initRebateAgent(data)
      this.initRebateSpec(data.rebateAgreementId)
      this.initRebateResultReciver(data.rebateAgreementId)
      this.initRebateResultPayReceiver(data.rebateAgreementId)
      this.resultAdjustVisible = true
      this.rebateResultDetailModel.rebateResultId = data.id
      this.rebateResultDetailModel.isModify = true
    },
    initModifyReason() {
      MasterDataService.GetDicts({ parentCode: 'RebateModifyReason' }).then(res => {
        this.modifyReasonList = res.data
      })
    },
    initRebateSpec(val) {
      var param = {
        rebateAgreementId: val
      }
      RebateService.QueryRebateAgreementProductSpec(param).then(res => {
        this.productAndSpecList = res
      }).catch(() => {

      })
    },
    initAgentType() {
      var param = {
        enumType: 'AgentType'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.costTypeList = result.data.datas
        })
        .catch((error) => {
          console.log(error)
        })
    },
    initRebateAgent(param) {
      RebateService.QueryRebateAgent(param)
        .then(res => {
          if (this.rebateResultModel.rebateAgentType === 1) {
            var agent = res.filter(item => item.isSpecified)[0]
            if (agent != null) {
              this.agentReceiverName = agent.rebateReceiverName
              this.rebateResultDetailModel.rebateAgentId = agent.id
            }
          } else {
            this.rebateAgentList = res
          }
        })
        .catch(() => {
          this.listLoading = true
        })
    },
    initRebateResultReciver(agreementId) {
      RebateService.QueryRebateResultReceiver({ id: agreementId }).then(res => {
        this.receiverList = res.data
      }).catch(() => {

      })
    },
    initRebateResultPayReceiver(agreementId) {
      RebateService.QueryRebateResultPayReceiver({ id: agreementId }).then(res => {
        this.paidToReceiverList = res.data
      }).catch(() => {

      })
    },
    handleSave() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.fileList === null || this.fileList.length === 0) {
            this.$notice.message('请上传文件', 'error')
            return
          }

          if (this.rebateResultDetailModel.productAndSpecId.length > 1) {
            this.rebateResultDetailModel.productSpecId = this.rebateResultDetailModel.productAndSpecId[1]
          }
          const formData = new FormData()
          formData.append('rebateResultId', this.rebateResultDetailModel.rebateResultId)
          formData.append('modifyReasonId', this.rebateResultDetailModel.modifyReasonId)
          formData.append('productSpecId', this.rebateResultDetailModel.productSpecId)
          formData.append('paidToReceiverId', this.rebateResultDetailModel.paidToReceiverId)
          formData.append('receiverId', this.rebateResultDetailModel.receiverId)
          formData.append('rebateAmount', this.rebateResultDetailModel.rebateAmount)
          formData.append('remark', this.rebateResultDetailModel.modifyRemark)

          FileService.uploadImages(this.fileList, formData, this.controller, this.method).then(res => {
            if (res.succeed) {
              this.close()
              this.$emit('success')
              this.showMessage('保存成功', 'success')
            } else {
              this.ShowTip(res)
            }
          })
        }
      })
    },
    getUploadFile(val) {
      this.fileList = val
    },
    close() {
      this.resultAdjustVisible = false
      this.rebateResultDetailModel = {
        productAndSpecId: []
      }
      this.fileList = []
      this.$refs.upload.handleClear()
      this.$refs['dataForm'].resetFields()
    },
    handleCancle() {
      this.close()
    },
    selectUpdate() {
      this.$forceUpdate()
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    }
  }

}
</script>
<style lang="css">
</style>
