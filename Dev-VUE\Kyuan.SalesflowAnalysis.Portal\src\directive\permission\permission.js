import store from '@/store'

export default {
  inserted(el, binding, vnode) {
    const { value } = binding
    const permission = store.getters && store.getters.user && store.getters.user.permission

    if (value && value instanceof Array && value.length > 0) {
      const permissionValues = value

      const hasPermission = Object.keys(permission).some(p => {
        return permissionValues.includes(p)
      })

      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`need permissions! Like v-permission="['admin','editor']"`)
    }
  }
}
