import HttpApi from './libs/api.request'

const controller = 'Cycle'

const api = new HttpApi(controller)

export default {
  GetPrevCycle() {
    return api.get('GetPrevCycle')
  },
  GetCurrentCycle() {
    return api.get('GetCurrentCycle')
  },
  GetTheLatestWaitingForApprovalCycle() {
    return api.get('GetTheLatestWaitingForApprovalCycle')
  },
  GetTheLatestSummarizedCycle() {
    return api.get('GetTheLatestSummarizedCycle')
  },
  GetTheLatestProcessedCycle() {
    return api.get('GetTheLatestProcessedCycle')
  }
}
