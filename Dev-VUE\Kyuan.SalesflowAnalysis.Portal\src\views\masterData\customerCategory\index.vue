<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex" style="margin-bottom:-20px;">
        <el-col :span="span">
          <el-input
            v-model="filter.receiverName"
            clearable
            placeholder="客户分类名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'TargetReceiver_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'TargetReceiver_Button_Add')"
            type="primary"
            icon="el-icon-plus"
            @click="handleCreate"
          >
            新增
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              :index="indexMethod"
              type="index"
              align="center"
            />
            <el-table-column
              sortable="custom"
              prop="Code"
              label="编码"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.code }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Name"
              label="客户分类名称"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="IsTarget"
              label="是否目标客户"
              min-width="90px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.isTarget }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="ProductSpec.Spec"
              label="科园非自营"
              min-width="120px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.isOther }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i v-if="$isPermitted($store.getters.user, 'TargetReceiver_Button_Edit')" class="el-icon-edit-outline eltablei" @click="handleUpdate(row)" />
                <i v-if="$isPermitted($store.getters.user, 'TargetReceiver_Button_Del')" class="el-icon-delete eltablei" @click="handleDelete(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="filter.pageIndex"
            :limit.sync="filter.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <ModifyTargetReceiver
      v-if="dialogEditFormVisible"
      :id="itemId"
      :title="modifyDialogTitle"
      :view-model="modifyDialogIsReadonly"
      @hidden="onHidden()"
      @refresh="onRefresh()"
    />
    <el-dialog
      title="导入目标客户"
      :close-on-click-modal="false"
      :visible="dialogImportVisible"
      width="80%"
      class="popup-search"
      @close="closeImportDialog"
    >
      <ImportTargetReceiver ref="refImportTargetReceiver" :show-import="dialogImportVisible" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeImportDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出目标客户"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import MaintenanceService from '@/api/maintenance'
import ProductService from '@/api/product'
import MasterDataService from '@/api/masterData'
import TargetReceiverService from '@/api/targetReceiver'

import ModifyTargetReceiver from './components/modifyTargetReceiver'
import ImportTargetReceiver from './components/importTargetReceiver'
import CustomExport from '@/components/Export/CustomExport'

export default {
  name: 'TargetReceiver',
  components: {
    Pagination,
    ModifyTargetReceiver,
    ImportTargetReceiver,
    CustomExport
  },
  data() {
    return {
      span: 4,
      deptList: [],
      deptLoading: false,
      list: [],
      total: 0,
      listLoading: true,
      filter: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      productAndSpecLoading: false,
      productAndSpecList: null,
      productAndSpecId: [],
      customerCategoryList: [],
      dialogEditFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑目标客户',
        create: '新增目标客户'
      },
      itemId: null,
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: {},
      dialogImportVisible: false,
      enumHeadStatusList: [],
      enumHeadStatusLoading: false
    }
  },
  created() {
    this.initDept()
    this.initProductAndSpec()
    this.initCustomerCategory()
    this.initHeadStatus()
    this.getList()
  },
  methods: {
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch(() => {
          this.deptLoading = false
        })
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      ProductService.QueryProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    initCustomerCategory() {
      TargetReceiverService.QueryCustomerCategorySelect().then(res => {
        this.customerCategoryList = res
      })
    },
    initHeadStatus() {
      this.enumHeadStatusLoading = true
      MasterDataService.GetEnumInfos({ enumType: 'HeadStatus' })
        .then((result) => {
          this.enumHeadStatusLoading = false
          this.enumHeadStatusList = result.data.datas
        })
        .catch((error) => {
          this.enumHeadStatusLoading = false
          console.log(error)
        })
    },
    indexMethod(index) {
      return (this.filter.pageIndex - 1) * this.filter.pageSize + index + 1
    },
    getList() {
      this.listLoading = true
      if (this.productAndSpecId) {
        const [productId, productSpecId] = this.productAndSpecId
        this.filter.productId = productId
        this.filter.productSpecId = productSpecId
      }
      TargetReceiverService.QueryTargetReceiver(this.filter)
        .then((result) => {
          this.listLoading = false

          this.list = result.data.datas
          this.total = result.data.recordCount
          this.filter.pageIndex = result.data.pageIndex
        })
        .catch((error) => {
          console.log(error)
          this.loading = false
        })
    },
    handleFilter() {
      this.filter.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.filter.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.filter.order = orderSymbol + column.prop
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    productChange() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    handleDelete(row) {
      this.$confirm('确定删除此目标客户吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        TargetReceiverService.DeleteTargetReceiver(row).then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('删除成功', 'success')
          }
        })
          .catch(error => {
            this.listLoading = false
            console.log(error)
          })
      }).catch(error => {
        this.listLoading = false
        if (!error.succeed) {
          this.$notice.message('取消删除', 'info')
        }
      })
    },
    handleCreate() {
      this.itemId = null
      this.modifyDialogIsReadonly = false
      this.dialogEditFormVisible = true
      this.dialogStatus = 'create'
      this.modifyDialogTitle = this.textMap[this.dialogStatus]
    },
    handleUpdate(row) {
      this.itemId = row.id
      this.modifyDialogIsReadonly = false
      this.dialogEditFormVisible = true
      this.dialogStatus = 'update'
      this.modifyDialogTitle = this.textMap[this.dialogStatus]
    },
    onHidden() {
      this.dialogEditFormVisible = false
    },
    onRefresh() {
      this.getList()
      this.dialogEditFormVisible = false
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      TargetReceiverService.GetTargetReceiverExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      this.filter.checkedColumns = checkColumns
      TargetReceiverService.ExportTargetReceivers(this.filter)
        .then((result) => {
          this.filter.checkedColumns = null
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '目标客户.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '目标客户.csv'
          }
          fileDownload(result.data, fileName)
        })
        .catch((error) => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    handleImport() {
      this.dialogImportVisible = true
    },
    closeImportDialog() {
      this.$refs.refImportTargetReceiver.clear()
      this.dialogImportVisible = false
      this.handleFilter()
    }
  }
}
</script>

