<template>
  <div>
    <el-upload
      ref="upload"
      class="upload-demo"
      action="customize"
      :multiple="false"
      :show-file-list="false"
      :auto-upload="true"
      :limit="1"
      :file-list="fileList"
      :http-request="uploadFile"
      :before-upload="beforeUpload"
    >
      <el-button size="small" type="primary" icon="el-icon-upload">选择文件</el-button>
    </el-upload>
  </div>
</template>

<script>
import fileApi from '@/api/file'
export default {
  props: {
    controller: {
      type: String,
      default: ''
    },
    method: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fileList: [],
      accept: ['.xlsx', '.jpg', '.txt', '.doc'],
      baseSize: 1024,
      limitSize: 20
    }
  },
  methods: {
    uploadFile(params) {
      const file = params.file
      this.$refs.upload.clearFiles()
      fileApi.uploadTemplate(file, null, this.controller, this.method)
        .then(result => {
          if (result.succeed) {
            this.$emit('uploadSuccess', result.data)
          }
        }).catch(res => {
          console.log(res)
        })
    },
    beforeUpload(file) {
      const ext = file.name.substring(file.name.lastIndexOf('.'))
      const isAccept = this.accept.findIndex(f => f === ext) > -1

      if (!isAccept) {
        this.$notice.message(`导入文件格式仅支持${this.accept.join(',')}`, 'error')
        return false
      }
      if (file.size === 0) {
        this.$notice.message('请不要上传空文件', 'error')
        return false
      }
      const isLessLimitSize = file.size / this.baseSize < this.baseSize * this.limitSize

      if (!isLessLimitSize) {
        this.$notice.message(`${'上传文件大小不能超过 '}${this.limitSize}${'M!'}`, 'error')
        return false
      }
      return true
    }
  }
}
</script>
