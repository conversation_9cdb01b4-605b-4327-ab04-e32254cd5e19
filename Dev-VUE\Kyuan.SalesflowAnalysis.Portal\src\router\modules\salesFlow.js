/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    permissions: ['admin','editor']    control the page permissions (you can set multiple permissions)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

import Layout from '@/layout'

const salesFlowRoutes = [
  {
    path: '/salesFlow',
    component: Layout,
    redirect: '/salesFlow/index',
    hidden: false,
    meta: {
      title: '流向管理',
      sort: 20,
      icon: 'chart'
    },
    children: [
      {
        name: 'Template',
        path: 'template',
        component: () => import('@/views/salesFlow/template/index'),
        meta: {
          title: '流向模板',
          icon: 'template',
          sort: 1,
          noCache: false,
          permissions: ['Salesflow_Template']
        }
      },
      {
        name: 'ImportSalesFlow',
        path: 'importSalesFlow',
        component: () => import('@/views/salesFlow/import/index'),
        meta: {
          title: '流向导入',
          icon: 'import',
          sort: 2,
          noCache: false,
          permissions: ['Salesflow_Import']
        }
      },
      {
        name: 'ReceiverAlias',
        path: 'receiverAlias',
        component: () => import('@/views/masterData/receiverAlias/index'),
        meta: {
          title: '收货方别名管理',
          icon: 'receiver-alias',
          sort: 3,
          noCache: false,
          permissions: ['Maintenance_ReceiverAlias']
        }
      },
      {
        name: 'SalesFlowQuery',
        path: 'salesFlowQuery',
        component: () => import('@/views/salesFlow/salesFlowQuery/index'),
        meta: {
          title: '流向查询',
          icon: 'nested',
          sort: 4,
          noCache: false,
          permissions: ['Salesflow_Query']
        }
      },
      {
        name: 'SalesFlowWarning',
        path: 'salesFlowWarning',
        component: () => import('@/views/salesFlow/salesFlowWarning/index'),
        meta: {
          title: '异常数据预警',
          icon: 'bug',
          sort: 5,
          noCache: false,
          permissions: ['Salesflow_Warning']
        }
      },
      {
        name: 'SalesFlowApprove',
        path: 'salesFlowApprove',
        component: () => import('@/views/salesFlow/salesFlowApprove/index'),
        meta: {
          title: '申诉审批',
          icon: 'approve',
          sort: 6,
          noCache: false,
          permissions: ['Salesflow_Approval']
        }
      }
    ]
  }
]

export default salesFlowRoutes
