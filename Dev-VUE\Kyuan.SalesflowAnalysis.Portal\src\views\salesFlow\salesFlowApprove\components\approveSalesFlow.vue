<template>
  <div>
    <el-dialog :title="title" width="75%" :close-on-click-modal="false" :visible="true" @close="cancle()">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="tempData.tempFormModel"
        label-position="right"
        label-width="135px"
        class="el-dialogform"
      >
        <el-row :gutter="24">
          <el-col :span="span">
            <el-form-item label="发货方名称">
              <span> {{ tempData.tempFormModel.distributorName }} </span>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="销售日期">
              <span> {{ tempData.tempFormModel.saleDate | parseTime('{y}-{m}-{d}') }} </span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="span">
            <el-form-item label="收货方名称">
              <span> {{ tempData.tempFormModel.receiverName }} </span>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="调整后收货方">
              <span v-if="tempData.tempFormModel.isChangedReceiver">
                {{ tempData.tempFormModel.changedReceiverName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="span">
            <el-form-item label="申诉人">
              <span> {{ tempData.tempFormModel.proposerName }} </span>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="产品/规格">
              <span> {{ tempData.tempFormModel.productNameCn + ' / '+ tempData.tempFormModel.productSpec }} </span>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="产品批号">
              <span> {{ tempData.tempFormModel.batchNumber }} </span>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="产品效期">
              <span v-if="tempData.tempFormModel.expireDate!== undefined">
                {{ tempData.tempFormModel.expireDate | parseTime('{y}-{m}-{d}') }} </span>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="销售数量">
              <span> {{ tempData.tempFormModel.quantity }} </span>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="实际销售数量">
              <span v-if="!tempData.tempFormModel.isChangedQuantity"> {{ tempData.tempFormModel.actualQuantity }}
              </span>
              <span v-if="tempData.tempFormModel.isChangedQuantity">
                {{ tempData.tempFormModel.actualQuantity }} </span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="申诉说明">
              <span> {{ tempData.tempFormModel.remark }} </span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="证明文件">
              <div class="imgbox">
                <div v-for="(url, index) in imageList" :key="index" class="block">
                  <el-image
                    style="width: 200px; height: 200px;object-fit: cover;"
                    :src="url"
                    :preview-src-list="getSrcList(index)"
                  />
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col
            v-if="tempData.tempFormModel.enumComplaintsFormStatus!== $constDefinition.complaintsFormStatus.request || tempData.tempFormModel.enumComplaintsFormStatus === $constDefinition.complaintsFormStatus.request &&tempData.tempFormModel.enumComplaintsFormStatus!==$constDefinition.complaintsFormStatus.request || tempData.tempFormModel.enumComplaintsFormStatus === $constDefinition.complaintsFormStatus.cancel"
            :span="24"
          >
            <el-form-item label="审批记录">
              <el-row>
                <el-col :span="24">
                  <el-table
                    :data="tempData.tempFormModel.approvalHistoryModels"
                    stripe
                    border
                    fit
                    highlight-current-row
                    style="width: 100%;"
                    :default-sort="{prop: 'createTime', order: 'descending'}"
                    :header-cell-class-name="'tableStyle'"
                  >
                    <el-table-column label="审批人" align="center" width="120px">
                      <template slot-scope="{ row }">
                        <span>{{ row.approvaler }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="审批意见" align="left" header-align="center">
                      <template slot-scope="{ row }">
                        <span>{{ row.remark }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="审批" align="center" header-align="center" width="100px">
                      <template slot-scope="{ row }">
                        <span>{{ row.enumApprovalActionTypeDesc }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="审批时间" align="center" header-align="center" width="140px">
                      <template slot-scope="{ row }">
                        <span>{{ row.approvalDate }}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col v-if="!viewModel" :span="24">
            <el-form-item label="审批意见：" prop="approvalRemark">
              <el-input v-model="tempData.tempFormModel.approvalRemark" clearable placeholder="审批意见" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="viewModel"
          icon="el-icon-close"
          @click="cancle()"
        >
          关闭
        </el-button>
        <el-button v-if="!viewModel" :loading="btnSaveLoading" icon="el-icon-close" @click="approve(false)">
          拒绝
        </el-button>
        <el-button
          v-if="!viewModel "
          :loading="btnSaveLoading"
          type="primary"
          icon="el-icon-check"
          @click="approve(true)"
        >
          通过
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  mapState
} from 'vuex'
import SalesFlowComplaintsRequestService from '@/api/salesFlowComplaintsRequest'

export default {
  name: '',
  components: {},
  props: {
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    viewModel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      span: 12,
      rules: {
        approvalRemark: [{
          message: '请输入审批意见',
          trigger: 'blur'
        },
        {
          max: 500,
          message: '审批意见最多只允许500个字符',
          trigger: 'blur'
        }
        ]
      },
      tempData: {
        tempFormModel: {}
      },
      btnSaveLoading: false,
      imageList: []
    }
  },
  computed: {
    ...mapState({
      downloadFileUrl: state => state.settings.downloadFileUrl
    })
  },
  watch: {
    id(val) {
      this.tempData.tempFormModel.id = val
    }
  },
  mounted() {},
  created() {
    this.init()
  },
  methods: {
    init() {
      if (!this.id) {
        this.clear()
        return
      }
      this.get(this.id)
    },
    get(id) {
      this.btnSaveLoading = true
      SalesFlowComplaintsRequestService.GetSalesFlowComplaintsRequest({
        id: id
      }).then(result => {
        this.tempData.tempFormModel = result.data
        for (var i = 0; i < this.tempData.tempFormModel.complaintImageList.length; i++) {
          this.imageList.push('data:image/png;base64,' + this.tempData.tempFormModel.complaintImageList[i])
        }
        this.btnSaveLoading = false
      }).catch(error => {
        this.btnSaveLoading = false
        console.log(error)
      })
    },
    approve(approveStatus) {
      if (approveStatus) {
        this.update()
      } else {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            if (this.tempData.tempFormModel.approvalRemark === undefined || this.tempData.tempFormModel.approvalRemark.trim() === '') {
              this.$notice.message('请输入审批意见。', 'error')
              return
            } else {
              this.refuse()
            }
          }
        })
      }
    },
    update() {
      this.btnSaveLoading = true
      SalesFlowComplaintsRequestService.ApproveSalesFlowComplaintsRequest(this.tempData.tempFormModel).then(
        result => {
          this.btnSaveLoading = false
          if (result.succeed) {
            this.tempData.tempFormModel = result.data
            this.$notice.message('审批成功', 'success')
            this.close()
          } else {
            if (result.type !== -3) {
              this.$notice.resultTip(result)
            }
          }
        })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('审批失败。', 'error')
          }
        })
    },
    refuse() {
      this.btnSaveLoading = true
      SalesFlowComplaintsRequestService.RefuseSalesFlowComplaintsRequest(this.tempData.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempData.tempFormModel = result.data
          this.$notice.message('拒绝成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('拒绝失败。', 'error')
          }
        })
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.tempData.tempFormModel = {}
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    },
    getSrcList(index) {
      return this.imageList.slice(index).concat(this.imageList.slice(0, index))
    }
  }

}

</script>
<style scoped>
.imgbox {
  display: flex;
}
.block {
  width: 80px;
  height: 80px;
  margin-right: 20px;
}
</style>
