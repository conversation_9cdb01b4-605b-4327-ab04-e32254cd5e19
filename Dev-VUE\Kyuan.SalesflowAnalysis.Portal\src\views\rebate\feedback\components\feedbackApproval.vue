<template>
  <div>
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="跟踪文件审核"
      :close-on-click-modal="false"
      :visible="formVisable"
      width="50%"
      @close="close"
    >
      <el-form
        ref="dataForm"
        :model="model"
        label-position="left"
        label-width="100px"
        class="el-dialogform"
        style="width:95%"
      >
        <el-row type="flex" class="filter-container">
          <el-col :span="24">
            <el-form-item label="协议名称">
              {{ model.rebateAgreementName }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="接收方名称">
              {{ model.rebateReceiverName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始时间">
              {{ model.startDateDesc }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="返利截止时间">
              {{ model.endDateDesc }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="返利金额">
              {{ model.rebateAmount | toTwoNum | toThousandFilter }}元
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="跟踪票据">
              <div class="imgbox">
                <div v-for="(url, index) in imageList" :key="index" class="block">
                  <el-image
                    style="width: 100%; height: 100%;object-fit: cover;"
                    :src="url"
                    :preview-src-list="getSrcList(index)"
                  />
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="外勤备注">
              {{ model.rebateFeedbackRemark }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="审核意见">
              <el-input
                v-model="model.rebateFeedbackReviewRemark"
                :disabled="model.enumRebateFeedbackStatus !== $constDefinition.rebateFeedbackStatus.Unreviewed"
                type="textarea"
                :rows="3"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">

        <el-button v-if="model.enumRebateFeedbackStatus === $constDefinition.rebateFeedbackStatus.Unreviewed" type="primary" icon="el-icon-check" @click="approval()">
          通过
        </el-button>
        <el-button v-if="model.enumRebateFeedbackStatus === $constDefinition.rebateFeedbackStatus.Unreviewed" type="primary" icon="el-icon-check" @click="reject()">
          拒绝
        </el-button>
        <!-- <el-button icon="el-icon-close" @click="close()">
          关闭
        </el-button> -->
      </div>
    </el-dialog>
  </div>
</template>
<script>

import RebateService from '@/api/rebate'
export default {
  data() {
    return {
      formVisable: false,
      imageList: [], // 图片列表
      model: {}
    }
  },
  created() {
  },
  methods: {
    init(id) {
      this.getRebateResult(id)
      this.formVisable = true
    },
    getRebateResult(id) {
      RebateService.GetRebateResult({ id: id }).then((result) => {
        this.model = result.data
        for (var i = 0; i < this.model.trackingImageList.length; i++) {
          this.imageList.push('data:image/png;base64,' + this.model.trackingImageList[i])
        }
      })
        .catch((error) => {
          console.log(error)
        })
    },
    getSrcList(index) {
      return this.imageList.slice(index).concat(this.imageList.slice(0, index))
    },
    approval() {
      RebateService.ApprovalRebateResultFeedback(this.model).then((result) => {
        if (result.succeed) {
          this.showMessage('审核成功', 'success')
          this.close()
        }
      })
    },
    reject() {
      if (this.model.rebateFeedbackReviewRemark === '' || this.model.rebateFeedbackReviewRemark === null || this.model.rebateFeedbackReviewRemark === undefined) {
        this.$notice.message('请填写审批意见', 'info')
        return
      }
      RebateService.RejectRebateResultFeedback(this.model).then((result) => {
        if (result.succeed) {
          this.showMessage('拒绝成功', 'success')
          this.close()
        }
      })
    },
    close() {
      this.formVisable = false
      this.model = {}
      this.imageList = []
      this.$emit('success')
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    }
  }

}
</script>
<style scoped>
.imgbox {
  display: flex;
}
.block {
  width: 80px;
  height: 80px;
  margin-right: 20px;
}
</style>
