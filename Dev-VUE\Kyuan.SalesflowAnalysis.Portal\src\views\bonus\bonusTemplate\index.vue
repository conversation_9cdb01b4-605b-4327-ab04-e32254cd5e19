<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="span">
          <el-select
            v-model="listQuery.departmentId"
            :loading="deptLoading"
            class="filter-item"
            placeholder="部门"
            clearable
          >
            <el-option
              v-for="item in deptList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.categoryId"
            :loading="calculateCategoryLoading"
            class="filter-item"
            placeholder="奖励类型"
            clearable
            @change="selectUpdate"
          >
            <el-option
              v-for="item in calculateCategoryQueryList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Template_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button v-if="$isPermitted($store.getters.user, 'Template_Button_Save')" type="primary" icon="el-icon-plus" @click="handleCreate">
            新增
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              label="名称"
              sortable="custom"
              min-width="220px"
              header-align="center"
              align="center"
              prop="Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="部门"
              sortable="custom"
              min-width="100px"
              prop="Department.Name"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.departmentName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="最少月份"
              sortable="custom"
              min-width="100px"
              prop="MinimumMonth"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.minimumMonth }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="基础数据模板"
              sortable="custom"
              min-width="150px"
              prop="VariableTemplate.FileName"
              align="center"
            >
              <template slot-scope="{ row }">
                <span class="link-type" @click="handleDownLoadTemplate(row.variableTemplateId,row.variableTemplateFileName)">{{ row.variableTemplateFileName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="计算公式模板"
              sortable="custom"
              min-width="150px"
              prop="ResultTemplate.FileName"
              align="center"
            >
              <template slot-scope="{ row }">
                <span class="link-type" @click="handleDownLoadTemplate(row.resultTemplateId,row.resultTemplateFileName)">{{ row.resultTemplateFileName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="是否停用"
              sortable="custom"
              min-width="100px"
              prop="isStop"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.isStopDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="状态"
              sortable="custom"
              min-width="90px"
              prop="enumStatus"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="是否已使用"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.isUseDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="110"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i v-if="$isPermitted($store.getters.user, 'Template_Button_Stop')&&row.isStop" class="el-icon-circle-check eltablei" title="启用" @click="handleEnable(row)" />
                <i v-if="$isPermitted($store.getters.user, 'Template_Button_Stop')&&!row.isStop" class="el-icon-circle-close eltablei" title="停用" @click="handleDisable(row)" />
                <i
                  v-if="row.isStop && $isPermitted($store.getters.user, 'Template_Button_Save')"
                  class="el-icon-edit-outline eltablei"
                  title="编辑"
                  @click="handleUpdate(row)"
                />
                <i v-if="row.isStop && $isPermitted($store.getters.user, 'Template_Button_Delete')" class="el-icon-delete eltablei" title="删除" @click="handleDelete(row)" />
                <i v-if="row.isStop && $isPermitted($store.getters.user, 'Template_Button_Configure')" class="el-icon-tickets eltablei" title="配置" @click="handleConfigure(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <modifyBonusTemplate
      v-if="dialogEditFormVisible"
      :id="itemId"
      :title="modifyDialogTitle"
      :view-model="modifyDialogIsReadonly"
      @hidden="onHidden()"
      @refresh="onRefresh()"
    />

    <ConfigureVariable ref="configureVariable" @success="configureSuccess" />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import MaintenanceService from '@/api/maintenance'
import BonusService from '@/api/bonus'
import modifyBonusTemplate from './components/modifyBonusTemplate'
import ConfigureVariable from './components/configureVariable'
import FileService from '@/api/file'

export default {
  components: {
    Pagination,
    modifyBonusTemplate,
    ConfigureVariable
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      list: [],
      deptList: [],
      deptLoading: false,
      calculateCategoryLoading: false,
      calculateCategoryQueryList: [],
      btnExportLoading: false,
      dialogEditFormVisible: false,
      dialogConfigureDetailFormVisible: false,
      dialogStatus: '',
      modifyDialogTitle: '',
      modifyDialogIsReadonly: false,
      itemId: null,
      showExportModal: false,
      textMap: {
        update: '编辑奖励模板',
        create: '新增奖励模板'
      }
    }
  },
  created() {
    this.initDept()
    this.initBonusCategory()
    this.handleFilter()
  },
  methods: {
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch(() => {
          this.deptLoading = false
        })
    },
    initBonusCategory() {
      BonusService.QueryBonusCategorySelect()
        .then((result) => {
          this.calculateCategoryQueryList = result
        })
        .catch(() => {
        })
    },
    selectUpdate() {
      this.$forceUpdate()
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      BonusService.QueryBonusTemplate(this.listQuery)
        .then((result) => {
          this.listLoading = false
          if (result.succeed) {
            this.list = result.data.datas
            this.total = result.data.recordCount
            this.listQuery.pageIndex = result.data.pageIndex
          } else {
            this.$notice.resultTip(result)
          }
        })
        .catch((error) => {
          console.log(error)
          this.listLoading = false
        })
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    handleCreate() {
      this.itemId = null
      this.modifyDialogIsReadonly = false
      this.dialogEditFormVisible = true
      this.dialogStatus = 'create'
      this.modifyDialogTitle = this.textMap[this.dialogStatus]
    },
    handleUpdate(row) {
      this.itemId = row.id
      this.modifyDialogIsReadonly = true
      this.dialogEditFormVisible = true
      this.dialogStatus = 'update'
      this.modifyDialogTitle = this.textMap[this.dialogStatus]
    },
    handleDelete(row) {
      this.$confirm('确定删除此模板信息吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.listLoading = true
          BonusService.DeleteBonusTemplate(row)
            .then((result) => {
              this.listLoading = false
              if (result.succeed) {
                this.getList()
                this.$notice.message('删除成功', 'success')
              }
            })
            .catch((error) => {
              this.listLoading = false
              console.log(error)
            })
        })
        .catch((error) => {
          this.listLoading = false
          if (!error.succeed) {
            this.$notice.message('取消删除', 'info')
          }
        })
    },
    onHidden() {
      this.dialogEditFormVisible = false
    },
    onRefresh() {
      this.getList()
      this.dialogEditFormVisible = false
    },
    handleConfigure(row) {
      this.$refs.configureVariable.initPage(row)
    },
    configureSuccess() {
      this.handleFilter()
    },
    handleDownLoadTemplate(templateId, templateName) {
      FileService.downloadAttachment(templateId).then(res => {
        const fileDownload = require('js-file-download')
        var filename = templateName
        fileDownload(res.data, filename)
      })
        .catch(() => {
        })
    },
    handleDisable(row) {
      BonusService.DisableBonusTemplate(row).then(result => {
        if (result.succeed) {
          this.$notice.message('停用成功', 'success')
          this.getList()
        } else {
          this.$notice.resultTip(result)
        }
      })
        .catch(error => {
          if (!error.processed) {
            this.$notice.message('停用失败。', 'error')
          }
        })
    },
    handleEnable(row) {
      BonusService.EnableBonusTemplate(row).then(result => {
        if (result.succeed) {
          this.$notice.message('启用成功', 'success')
          this.getList()
        } else {
          this.$notice.resultTip(result)
        }
      })
        .catch(error => {
          if (!error.processed) {
            this.$notice.message('启用失败。', 'error')
          }
        })
    }
  }
}
</script>
<style scoped>
  a:hover{
   text-decoration:underline
}
</style>
