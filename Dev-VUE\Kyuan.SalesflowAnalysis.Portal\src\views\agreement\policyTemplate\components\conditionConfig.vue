<template>
  <div>
    <el-dialog
      :title="title"
      width="60%"
      :close-on-click-modal="false"
      :visible="showAddDialog"
      @close="cancle()"
    >
      <el-form ref="dataForm" :rules="rules" :model="rebateAgreementTerm" label-position="right" label-width="80px" class="el-dialogform">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="24">
            <el-form-item label="条件" prop="conditionalDescription">
              <el-col :span="18" style="padding-left: 0px; padding-right: 0px;">
                <el-input
                  v-model="rebateAgreementTerm.conditionalDescription"
                  :readonly="true"
                  placeholder="请配置条件公式"
                />
              </el-col>
              <el-col :span="span">
                <el-button
                  style="width:100%;"
                  type="primary"
                  title="编辑条件公式"
                  @click="handelEditCondition"
                >编辑
                </el-button>
              </el-col>
              <el-col :span="span">
                <el-button
                  style="width:100%;"
                  type="primary"
                  title="清空条件公式"
                  @click="handelClearCondition"
                >清空
                </el-button>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="结果" prop="calculatedDescription">
              <el-col :span="18" style="padding-left: 0px; padding-right: 0px;">
                <el-input
                  v-model="rebateAgreementTerm.calculatedDescription"
                  :readonly="true"
                  placeholder="请配置结果公式"
                />
              </el-col>
              <el-col :span="span">
                <el-button
                  style="width:100%;"
                  type="primary"
                  title="编辑结果公式"
                  @click="handelEditResult"
                >编辑
                </el-button>
              </el-col>
              <el-col :span="span">
                <el-button
                  style="width:100%;"
                  type="primary"
                  title="清空结果公式"
                  @click="handelClearResult"
                >清空
                </el-button>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="rebateAgreementTerm.remark"
                type="textarea"
                :rows="2"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handlerSave()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <!--编辑公式-->
    <formulaConfig
      ref="refFormulaConfig"
      :display-condition="displayCondition"
      @success="formulaConfigSuccess"
    />
  </div>
</template>
<script>
import formulaConfig from './formulaConfig'
export default {
  components: {
    formulaConfig
  },
  data() {
    const vaildConditionalDescription = (rule, value, callback) => {
      var flags = ['+', '-', '×', '÷', '>', '<', '≥', '≤', '=', '≠']
      if (!flags.some(item => { return value.includes(item) })) {
        callback('表达式中必须包含条件运算符')
      }
      callback()
    }
    return {
      span: 3,
      rules: {
        conditionalDescription: [
          {
            required: true,
            type: 'string',
            message: '请配置条件公式',
            trigger: 'change'
          },
          {
            validator: vaildConditionalDescription,
            trigger: 'change'
          }
        ],
        calculatedDescription: [
          {
            required: true,
            type: 'string',
            message: '请配置结果公式',
            trigger: 'change'
          }
        ]
      },
      rebateAgreementTerm: {
        conditionalDescription: '',
        conditionalFormula: '',
        conditionalHtml: '',
        calculatedDescription: '',
        calculatedFormula: '',
        calculatedHtml: ''
      },
      dialogFormulaConfigVisible: false,
      displayCondition: false,
      showAddDialog: false,
      title: '',
      conditionalFormulaStr: '',
      calculatedFormulaStr: '',
      timer: null
    }
  },
  methods: {
    init(dialogTitle, rowData) {
      this.title = dialogTitle
      if (rowData !== null) {
        this.rebateAgreementTerm = rowData
      }
      this.showAddDialog = true
    },
    cancle() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      Object.keys(this.rebateAgreementTerm).forEach((key) => (this.rebateAgreementTerm[key] = ''))
      this.showAddDialog = false
    },
    handlerSave() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 保存
          this.$emit('add', JSON.parse(JSON.stringify(this.rebateAgreementTerm)))

          this.cancle()
        }
      })
    },
    handelEditCondition() {
      this.displayCondition = true
      this.$refs.refFormulaConfig.init('条件配置', this.rebateAgreementTerm.conditionalHtml)
    },
    handelEditResult() {
      this.$refs.refFormulaConfig.init('结果配置', this.rebateAgreementTerm.calculatedHtml)
      this.displayCondition = false
    },
    formulaConfigSuccess(val) {
      if (val.length <= 0) { return }
      this.formulaStr = val
      const formulas = val.split('|')
      if (this.displayCondition) {
        this.conditionalFormulaStr = val
        this.rebateAgreementTerm.conditionalDescription = formulas[0]
        this.rebateAgreementTerm.conditionalFormula = formulas[1]
        this.rebateAgreementTerm.conditionalHtml = formulas[2]
      } else {
        this.calculatedFormulaStr = val
        this.rebateAgreementTerm.calculatedDescription = formulas[0]
        this.rebateAgreementTerm.calculatedFormula = formulas[1]
        this.rebateAgreementTerm.calculatedHtml = formulas[2]
      }
    },
    handelClearCondition() {
      this.rebateAgreementTerm.conditionalDescription = ''
      this.rebateAgreementTerm.conditionalFormula = ''
      this.rebateAgreementTerm.conditionalHtml = ''
    },
    handelClearResult() {
      this.rebateAgreementTerm.calculatedDescription = ''
      this.rebateAgreementTerm.calculatedFormula = ''
      this.rebateAgreementTerm.calculatedHtml = ''
    }
  }
}
</script>
