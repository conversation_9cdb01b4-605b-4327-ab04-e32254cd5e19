<template>
  <div>
    <el-dialog
      :title="title"
      width="60%"
      :close-on-click-modal="false"
      append-to-body
      :visible="showAddDialog"
      @close="cancle()"
    >
      <el-form ref="dataForm" :rules="rules" :model="conditionModel" label-position="right" label-width="80px" class="el-dialogform">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="条件" prop="condition">
              <el-col :span="18" style="padding-left: 0px; padding-right: 0px;">
                <el-input
                  v-model="conditionModel.condition"
                  :readonly="true"
                  placeholder="请配置条件公式"
                />
              </el-col>
              <el-col :span="3">
                <el-button
                  icon="el-icon-plus"
                  style="width:100%;"
                  type="primary"
                  title="编辑条件公式"
                  @click="handelEditCondition"
                >编辑
                </el-button>
              </el-col>
              <el-col :span="3">
                <el-button
                  icon="el-icon-plus"
                  style="width:100%;"
                  type="primary"
                  title="清空条件公式"
                  @click="handelClearCondition"
                >清空
                </el-button>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="结果" prop="result">
              <el-col :span="18" style="padding-left: 0px; padding-right: 0px;">
                <el-input
                  v-model="conditionModel.result"
                  :readonly="true"
                  placeholder="请配置结果公式"
                />
              </el-col>
              <el-col :span="3">
                <el-button
                  icon="el-icon-plus"
                  style="width:100%;"
                  type="primary"
                  title="编辑结果公式"
                  @click="handelEditResult"
                >编辑
                </el-button>
              </el-col>
              <el-col :span="3">
                <el-button
                  icon="el-icon-plus"
                  style="width:100%;"
                  type="primary"
                  title="清空结果公式"
                  @click="handelClearResult"
                >清空
                </el-button>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="conditionModel.remark"
                type="textarea"
                :rows="2"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handlerSave()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <!--编辑公式-->
    <formulaConfig
      v-if="dialogFormulaConfigVisible"
      :title="formulaConfigDialogTitle"
      :display-condition="displayCondition"
      @success="formulaConfigSuccess"
      @hidden="onHidden()"
    />
  </div>
</template>
<script>
import formulaConfig from './formulaConfig'
export default {
  components: {
    formulaConfig
  },
  data() {
    return {
      span: 24,
      rules: {
        condition: [
          {
            required: true,
            type: 'string',
            message: '请配置条件公式',
            trigger: 'change'
          }
        ],
        result: [
          {
            required: true,
            type: 'string',
            message: '请配置结果公式',
            trigger: 'change'
          }
        ]
      },
      conditionModel: {
        condition: '',
        result: ''
      },
      dialogFormulaConfigVisible: false,
      displayCondition: false,
      showAddDialog: false,
      title: ''
    }
  },
  mounted() {
  },
  created() {
    // this.init('测试', null)
  },
  methods: {
    init(dialogTitle, rowData) {
      this.title = dialogTitle
      if (rowData !== null) {
        this.conditionModel.condition = rowData.condition
        this.conditionModel.result = rowData.result
      }
      this.showAddDialog = true
    },
    cancle() {
      this.conditionModel.condition = ''
      this.conditionModel.result = ''
      this.showAddDialog = false
    },
    handlerSave() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 保存
          this.$notice.message('保存成功', 'success')
          this.showAddDialog = false
        }
      })
    },
    closeFormulaConfigDialog() {
      this.dialogFormulaConfig = false
    },
    handelEditCondition() {
      this.formulaConfigDialogTitle = '条件配置'
      this.dialogFormulaConfigVisible = true
      this.displayCondition = true
    },
    handelEditResult() {
      this.formulaConfigDialogTitle = '结果配置'
      this.dialogFormulaConfigVisible = true
      this.displayCondition = false
    },
    onHidden() {
      this.dialogFormulaConfigVisible = false
    },
    formulaConfigSuccess(val) {
      if (this.displayCondition) {
        this.conditionModel.condition = val
      } else {
        this.conditionModel.result = val
      }
    },
    handelClearResult() {
      this.conditionModel.result = ''
    },
    handelClearCondition() {
      this.conditionModel.condition = ''
    }
  }
}
</script>
