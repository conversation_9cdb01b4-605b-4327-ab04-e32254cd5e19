<template>

  <div class="upload-container">
    <el-upload
      ref="upload"
      v-bind="$attrs"
      action=""
      list-type="picture-card"
      :show-file-list="true"
      :file-list="fileList"
      :on-preview="handlePictureCardPreview"
      :on-remove="handleRemove"
      :http-request="uploadFile"
      :class="{'auto-upload':autoUpload,'limited':limit && fileList.length >= limit}"
      :auto-upload="autoUpload"
    >
      <i class="el-icon-plus" />
    </el-upload>
    <el-dialog :visible.sync="dialogVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import fileApi from '@/api/file'

export default {
  name: 'ImageUpload',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    limit: {
      type: Number,
      default: undefined
    },
    autoUpload: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      fileList: [],
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  computed: {
    ...mapState({
      downloadFileUrl: state => state.settings.downloadFileUrl
    })
  },
  watch: {
    value: {
      handler(newValue, oldValue) {
        const vLen = newValue.length
        if (vLen === 0) {
          this.fileList = []
        } else {
          for (let i = 0; i < vLen; i++) {
            const v = newValue[i]
            const index = this.fileList.findIndex(item => v.id === item.id)
            if (index > i) {
              // 删除
              this.fileList.splice(i, index - i)
            } else if (index < 0) {
              // 添加
              v.name = v.fileName
              v.url = `${this.downloadFileUrl}/${v.id}`
              this.fileList.splice(i, 0, v)
            }
          }
          const fLen = this.fileList.length
          if (fLen > vLen) {
            this.fileList.splice(vLen, fLen - vLen)
          }
        }
      },
      deep: true
    }
  },
  created() {
    this.fileList = this.value.map(item => {
      return {
        id: item.id,
        name: item.fileName,
        url: `${this.downloadFileUrl}/${item.id}`,
        isImage: item.contentType.indexOf('image') === 0
      }
    })
  },
  methods: {
    uploadFile(params) {
      const file = params.file
      const fileType = file.type
      const isImage = fileType.indexOf('image') !== -1

      if (!isImage) {
        this.$alert('请选择图片!', '提示', { type: 'error' })
        return
      }
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        this.$alert('上传图片大小不能超过 2MB!', '提示', { type: 'error' })
        return
      }
      fileApi.upload(file).then(result => {
        if (result.succeed) {
          const data = result.data

          if (data) {
            for (const key of Object.keys(data)) {
              file[key] = data[key]
            }
            file.url = `${this.downloadFileUrl}/${file.id}`
            file.isImage = file.contentType.indexOf('image') === 0
            this.value.push(data)
            // this.fileList.push(file)
          }
        }
      })
    },
    handleRemove(file, fileList) {
      const index = this.value.findIndex(item => file.id === item.id)
      this.value.splice(index, 1)
      // this.fileList.splice(index, 1)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    emitInput(val) {
      this.$emit('input', val)
    }
  }
}
</script>

<style lang="scss" scoped>
    @import "~@/styles/mixin.scss";
    .upload-container {
        position: relative;
        @include clearfix;
    }

</style>

