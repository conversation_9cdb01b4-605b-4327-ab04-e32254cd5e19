<template>
  <div style="background-color: #f5f6f8;">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>大终端</span>
      </div>
      <div>
        <el-form
          ref="bigDataForm"
          :model="bigTemp"
          label-position="right"
          label-width="150px"
          class="el-dialogform-settinglist"
        >
          <el-row>
            <el-col v-for="(item,index) of bigBonusLevels" :key="index" :span="span">
              <el-form-item :label="item.title">
                <el-input
                  v-model="item.amount"
                  clearable
                  :placeholder="item.title"
                  maxlength="14"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24" class="el-colRight" style="margin-bottom:10px">
              <el-button
                class="filter-item"
                type="primary"
                icon="el-icon-plus"
                @click="handleAddBigSetting"
              >
                添加基数
              </el-button>
            </el-col>
            <el-col :span="24">
              <el-form-item label="基数">
                <el-table
                  :data="bigSettings"
                  stripe
                  border
                  fit
                  highlight-current-row
                  style="width: 100%;"
                  :default-sort="{prop: 'createTime', order: 'descending'}"
                  :header-cell-class-name="'tableStyle'"
                  :row-class-name="handleRowClass"
                >
                  <el-table-column
                    fixed
                    label="序号"
                    type="index"
                    align="center"
                  />
                  <el-table-column label="厂商" min-width="120px" align="center">
                    <template slot-scope="{ row }">
                      <el-select
                        v-model="row.manufacturerId"
                        style="width: 100%"
                        class="filter-item"
                        placeholder="厂商"
                        @change="manufacturerChange(row)"
                      >
                        <el-option
                          v-for="item in manufacturers"
                          :key="item.key"
                          :label="item.value"
                          :value="item.key"
                        />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="产品/规格" min-width="150px" align="center">
                    <template slot-scope="{ row }">
                      <el-cascader
                        ref="refBigProductAndSpec"
                        v-model="row.productAndSpecId"
                        style="width:100%"
                        :options="row.productAndSpecList"
                        placeholder="产品/规格"
                        :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
                        @change="handleProductChange(row)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="数量大于" min-width="80px" align="center">
                    <template slot-scope="{ row }">
                      <el-input
                        v-model="row.minQty"
                        clearable
                        placeholder="数量大于"
                        maxlength="14"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
                    <template slot-scope="row">
                      <i class="el-icon-delete eltablei" @click="handleDeleteBigSetting(row.$index)" />
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
              <el-col />
            </el-col>
          </el-row>
        </el-form>
        <div class="el-colCenter">
          <el-button
            v-if="$isPermitted($store.getters.user, 'BigTerminalSetting_Button_Save')"
            type="primary"
            icon="el-icon-check"
            @click="handlerSaveBigSetting"
          >
            保存
          </el-button>
        </div>
      </div>
    </el-card>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>小终端</span>
      </div>
      <div>
        <el-form
          ref="smallDataForm"
          :model="smallTemp"
          label-position="right"
          label-width="160px"
          class="el-dialogform-settinglist"
        >
          <el-row>
            <el-col v-for="(item,index) of smallBonusLevels" :key="index" :span="span">
              <el-form-item :label="item.title">
                <el-input
                  v-model="item.amount"
                  clearable
                  :placeholder="item.title"
                  maxlength="14"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24" class="el-colRight" style="margin-bottom:10px">
              <el-button
                class="filter-item"
                type="primary"
                icon="el-icon-plus"
                @click="handleAddSmallSetting"
              >
                添加基数
              </el-button>
            </el-col>
            <el-col :span="24">
              <el-form-item label="基数">
                <el-table
                  :data="smallSettings"
                  stripe
                  border
                  fit
                  highlight-current-row
                  style="width: 100%;"
                  :default-sort="{prop: 'createTime', order: 'descending'}"
                  :header-cell-class-name="'tableStyle'"
                  :row-class-name="handleRowClass"
                >
                  <el-table-column
                    fixed
                    label="序号"
                    type="index"
                    align="center"
                  />
                  <el-table-column label="厂商" min-width="120px" align="center">
                    <template slot-scope="{ row }">
                      <el-select
                        v-model="row.manufacturerId"
                        style="width: 100%"
                        class="filter-item"
                        placeholder="厂商"
                        @change="manufacturerChange(row)"
                      >
                        <el-option
                          v-for="item in manufacturers"
                          :key="item.key"
                          :label="item.value"
                          :value="item.key"
                        />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="产品/规格" min-width="150px" align="center">
                    <template slot-scope="{ row }">
                      <el-cascader
                        ref="refSmallProductAndSpec"
                        v-model="row.productAndSpecId"
                        style="width:100%"
                        :options="row.productAndSpecList"
                        placeholder="产品/规格"

                        :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
                        @change="handleSmallProductChange(row)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="数量大于等于" min-width="80px" align="center">
                    <template slot-scope="{ row }">
                      <el-input
                        v-model="row.minQty"
                        clearable
                        placeholder="数量大于等于"
                        maxlength="14"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="数量小于等于" min-width="80px" align="center">
                    <template slot-scope="{ row }">
                      <el-input
                        v-model="row.maxQty"
                        clearable
                        placeholder="数量小于等于"
                        maxlength="14"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
                    <template slot-scope="row">
                      <i class="el-icon-delete eltablei" @click="handleDeleteSmallSetting(row.$index)" />
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
              <el-col />
            </el-col>
          </el-row>
        </el-form>
        <div class="el-colCenter">
          <el-button
            v-if="$isPermitted($store.getters.user, 'SmallTerminalSetting_Button_Save')"
            type="primary"
            icon="el-icon-check"
            @click="handlerSaveSmallSetting"
          >
            保存
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>
<script>
import BonusSevices from '@/api/bonus'
import ProductService from '@/api/product'
import ManufacturerService from '@/api/manufacturer'

export default {
  name: 'AmplitudeSetting',
  data() {
    return {
      span: 8,
      bigTemp: {},
      smallTemp: {},
      manufacturers: [],
      allProductAndSpecList: [],
      bigSettings: [],
      smallSettings: [],
      bigBonusLevels: [],
      smallBonusLevels: []
    }
  },
  created() {
    this.initProductAndSpec()
    this.initManufacturer()
    this.initBigSetting()
    this.initSmallSetting()
  },
  methods: {
    initManufacturer() {
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturers = result
        })
        .catch(() => {
        })
    },
    initProductAndSpec() {
      ProductService.QueryProductAndSpecCascader()
        .then((result) => {
          this.allProductAndSpecList = result
        })
        .catch(() => {
        })
    },
    initBigSetting() {
      BonusSevices.GetSalesIncrementalBonusSetting({ receiverType: 2 }).then((result) => {
        this.bigTemp = result.data
        this.bigSettings = result.data.details
        this.bigBonusLevels = result.data.bonusLevels

        this.bigBonusLevels.forEach((item) => {
          item.title = `${item.upperRank}-${item.lowerRank}名奖励（元）`
        })
      })
    },
    initSmallSetting() {
      BonusSevices.GetSalesIncrementalBonusSetting({ receiverType: 1 }).then((result) => {
        this.smallTemp = result.data
        this.smallSettings = result.data.details
        this.smallBonusLevels = result.data.bonusLevels
        this.smallBonusLevels.forEach((item) => {
          item.title = `${item.upperRank}-${item.lowerRank}名奖励（元）`
        })
      })
    },
    handlerSaveBigSetting() {
      // 保存
      for (var j = 0; j < this.bigBonusLevels.length; j++) {
        if (this.bigBonusLevels[j].amount === undefined ||
                this.bigBonusLevels[j].amount === null ||
                this.bigBonusLevels[j].amount === '') {
          this.showMessage(`请填写大终端${this.bigBonusLevels[j].title}`, 'error')
          return false
        } else {
          if (!(/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/.test(this.bigBonusLevels[j].amount))) {
            this.showMessage(`大终端${this.bigBonusLevels[j].title}数量应为正整数`, 'error')
            return false
          }
        }
      }
      for (var i = 0; i < this.bigSettings.length; i++) {
        if (this.bigSettings[i].productSpecId === undefined ||
                this.bigSettings[i].productSpecId === null ||
                this.bigSettings[i].minQty === undefined ||
                this.bigSettings[i].minQty === null ||
                this.bigSettings[i].minQty === '') {
          this.showMessage('请将基数设置填写完整', 'error')
          return false
        }
        if (!(/(^[1-9]\d*$)/.test(this.bigSettings[i].minQty))) {
          this.showMessage('数量应为正整数', 'error')
          return false
        }

        this.bigSettings[i].minQty = parseInt(this.bigSettings[i].minQty)

        var smallSetting = this.smallSettings.filter(item => { return item.productSpecId === this.bigSettings[i].productSpecId })
        if (smallSetting && smallSetting.length >= 1) {
          if (this.bigSettings[i].minQty < smallSetting[0].maxQty) {
            var product = this.allProductAndSpecList.filter(item => { return item.value === this.bigSettings[i].productAndSpecId[0] })
            var productSpec = product[0].children.filter(item => { return item.value === this.bigSettings[i].productSpecId })
            this.showMessage('产品【' + product[0].label + '（' + productSpec[0].label + '）】大终端产品数量必须大于或等于小终端的最大数量', 'error')
            return false
          }
        }
      }
      if (this.bigSettings.length === 0) {
        this.showMessage('请添加基数设置', 'error')
        return false
      }

      var specIds = this.bigSettings.map(function(item) { return item.productSpecId })
      if (new Set(specIds).size !== specIds.length) {
        this.showMessage('不能添加重复的产品规格', 'error')
        return false
      }

      this.bigTemp.details = this.bigSettings
      this.bigTemp.bonusLevels = this.bigBonusLevels

      BonusSevices.SaveSalesIncrementalBonusSetting(this.bigTemp)
        .then((result) => {
          if (result.succeed) {
            this.$emit('success')
            this.showMessage('保存成功', 'success')
          } else {
            this.ShowTip(result)
          }
        })
        .catch(() => {
        })
    },
    handlerSaveSmallSetting() {
      // 保存

      for (var l = 0; l < this.smallBonusLevels.length; l++) {
        if (this.smallBonusLevels[l].amount === undefined ||
                this.smallBonusLevels[l].amount === null ||
                this.smallBonusLevels[l].amount === '') {
          this.showMessage(`请填写小终端${this.smallBonusLevels[l].title}`, 'error')
          return false
        } else {
          if (!(/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/.test(this.smallBonusLevels[l].amount))) {
            this.showMessage(`小终端${this.smallBonusLevels[l].title}数量应为正整数`, 'error')
            return false
          }
        }
      }
      for (var i = 0; i < this.smallSettings.length; i++) {
        if (this.smallSettings[i].productSpecId === undefined ||
                this.smallSettings[i].productSpecId === null ||
                this.smallSettings[i].minQty === undefined ||
                this.smallSettings[i].minQty === null ||
                this.smallSettings[i].minQty === '' ||
                this.smallSettings[i].maxQty === undefined ||
                this.smallSettings[i].maxQty === null ||
                this.smallSettings[i].maxQty === '') {
          this.showMessage('请将基数设置填写完整', 'error')
          return false
        }
        if (!(/(^[1-9]\d*$)/.test(this.smallSettings[i].minQty))) {
          this.showMessage('数量应为正整数', 'error')
          return false
        }

        if (!(/(^[1-9]\d*$)/.test(this.smallSettings[i].maxQty))) {
          this.showMessage('数量应为正整数', 'error')
          return false
        }
        this.smallSettings[i].maxQty = parseInt(this.smallSettings[i].maxQty)
        this.smallSettings[i].minQty = parseInt(this.smallSettings[i].minQty)

        var bigSetting = this.bigSettings.filter(item => { return item.productSpecId === this.smallSettings[i].productSpecId })
        if (bigSetting && bigSetting.length >= 1) {
          if (bigSetting[0].minQty < this.smallSettings[i].maxQty) {
            var product = this.allProductAndSpecList.filter(item => { return item.value === this.smallSettings[i].productAndSpecId[0] })
            var productSpec = product[0].children.filter(item => { return item.value === this.smallSettings[i].productSpecId })
            this.showMessage('产品【' + product[0].label + '（' + productSpec[0].label + '）】大终端产品数量必须大于或等于小终端的最大数量', 'error')
            return false
          }
        }
      }
      if (this.smallSettings.length === 0) {
        this.showMessage('请添加基数设置', 'error')
        return false
      }

      var specIds = this.smallSettings.map(function(item) { return item.productSpecId })
      if (new Set(specIds).size !== specIds.length) {
        this.showMessage('不能添加重复的产品规格', 'error')
        return false
      }
      this.smallTemp.details = this.smallSettings
      this.smallTemp.bonusLevels = this.smallBonusLevels

      BonusSevices.SaveSalesIncrementalBonusSetting(this.smallTemp)
        .then((result) => {
          if (result.succeed) {
            this.$emit('success')
            this.showMessage('保存成功', 'success')
          } else {
            this.ShowTip(result)
          }
        })
        .catch(() => {
        })
    },
    handleProductChange(row) {
      if (row.productAndSpecId !== null && row.productAndSpecId.length > 1) {
        row.productId = row.productAndSpecId[0]
        row.productSpecId = row.productAndSpecId[1]
      }
      this.$refs.refBigProductAndSpec.dropDownVisible = false
    },
    handleSmallProductChange(row) {
      if (row.productAndSpecId !== null && row.productAndSpecId.length > 1) {
        row.productId = row.productAndSpecId[0]
        row.productSpecId = row.productAndSpecId[1]
      }
      this.$refs.refSmallProductAndSpec.dropDownVisible = false
    },
    manufacturerChange(row) {
      row.productAndSpecId = []
      row.productId = null
      row.productSpecId = null
      row.productAndSpecList = this.allProductAndSpecList.filter(item => { return item.parentId === row.manufacturerId })
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleAddBigSetting() {
      var temp = {}
      this.bigSettings.push(temp)
    },
    handleAddSmallSetting() {
      var temp = {}
      this.smallSettings.push(temp)
    },
    handleDeleteBigSetting(index) {
      this.$confirm('确定删除此大终端设置吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.bigSettings.splice(index, 1)
      }).catch(error => {
        if (!error.succeed) {
          this.showMessage('取消删除', 'info')
        }
      })
    },
    handleDeleteSmallSetting(index) {
      this.$confirm('确定删除此小终端设置吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.smallSettings.splice(index, 1)
      }).catch(error => {
        if (!error.succeed) {
          this.showMessage('取消删除', 'info')
        }
      })
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    }
  }
}
</script>
<style scoped>
  .text {
    font-size: 14px;
  }
  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both
  }

  .el-dialogform-settinglist {
    width: 100%;

}

  .box-card {
    width: 96%;
    margin-bottom: 10px;
    margin-left: 30px;
  }
</style>
