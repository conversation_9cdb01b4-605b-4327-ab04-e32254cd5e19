import cfg from '@cfg'

export function getToken() {
  const token = sessionStorage.getItem(cfg.tokenName)
  if (token) return token
  else return false
}

export function setToken(token) {
  sessionStorage.setItem(cfg.tokenName, token)
}

export function removeToken() {
  return sessionStorage.removeItem(cfg.tokenName)
}

export function isUserPermitted(user, permission) {
  if (permission) {
    if (user && user.permission) {
      if (typeof permission === 'string') {
        return Object.keys(user.permission).includes(permission)
      } else {
        return Object.keys(permission).every(p => (permission[p] instanceof Array && permission[p].length > 0 && permission[p].every(tag => user.permission[p].includes(tag))) || Object.keys(user.permission).includes(p))
      }
    } else {
      return false
    }
  } else {
    return true
  }
}
