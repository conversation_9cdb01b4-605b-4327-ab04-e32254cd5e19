<template>
  <div class="list-container">
    <el-row type="flex" justify="end" :gutter="10" style="margin-bottom:10px">
      <el-col :span="3.5">
        <el-button
          :loading="btnAddLoading"
          class="filter-item"
          type="primary"
          icon="el-icon-plus"
          @click="handleCreate"
        >
          新增
        </el-button>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-table
          :data="list"
          stripe
          border
          fit
          highlight-current-row
          style="width: 100%;"
          :default-sort="{prop: 'createTime', order: 'descending'}"
          :header-cell-class-name="'tableStyle'"
          :row-class-name="handleRowClass"
          @sort-change="sortChange"
        >
          <el-table-column fixed label="序号" type="index" align="center" />
          <el-table-column sortable="custom" prop="Product.NameCn" label="产品名称" min-width="100px" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.productNameCn }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="Product.CommonName" label="通用名" min-width="150px" align="left" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.productCommonName }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="Code" label="规格编码" min-width="100px" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.code }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="Spec" label="包装规格" min-width="100px" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.spec }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="Unit" label="包装单位" min-width="80px" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.unit }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="DosageFormCode" label="剂型" min-width="70px" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.dosageFormName }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="PharmaceuticalFactory" label="生产厂家" min-width="100px" header-align="center" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.pharmaceuticalFactory }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            header-align="center"
            width="100"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="{ row }">
              <i class="el-icon-edit-outline eltablei" title="编辑" @click="handleUpdate(row)" />
              <i class="el-icon-delete eltablei" title="删除" @click="handleDelete(row)" />
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      :close-on-click-modal="false"
      :title="textMap[dialogStatus]"
      :visible="dialogSpecEditFormVisible"
      width="80%"
      @close="btnClose"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="90px"
        class="el-dialogform"
      >
        <el-row>
          <el-col :span="span">
            <el-form-item label="产品名称">
              {{ temp.productNameCn }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="通用名">
              {{ temp.productCommonName }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="规格编码" prop="code">
              <el-input
                v-model="temp.code"
                clearable
                placeholder="规格编码"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="包装规格" prop="spec">
              <el-input
                v-model="temp.spec"
                clearable
                placeholder="包装规格"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="包装单位" prop="unit">
              <el-input
                v-model="temp.unit"
                clearable
                placeholder="包装单位"
                maxlength="10"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="剂型" prop="dosageFormCode">
              <el-select
                v-model="temp.dosageFormCode"
                style="width: 100%"
                class="filter-item"
                placeholder="剂型"
                clearable
              >
                <el-option
                  v-for="item in dosageFormList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="生产厂家" prop="pharmaceuticalFactory">
              <el-input
                v-model="temp.pharmaceuticalFactory"
                clearable
                placeholder="生产厂家"
                maxlength="300"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnClose()"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="dialogStatus === 'create' ? createData() : updateData()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import ProductService from '@/api/product'
import MasterDataService from '@/api/masterData'

export default {
  props: {
    productId: {
      type: String,
      default: ''
    },
    showDialog: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      span: 12,
      list: [],
      dosageFormList: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      btnAddLoading: false,
      dialogStatus: '',
      dialogSpecEditFormVisible: false,
      textMap: {
        update: '编辑',
        create: '新增'
      },
      temp: {
        productId: '',
        productNameCn: '',
        productCommonName: '',
        code: '',
        spec: '',
        unit: '',
        dosageFormCode: '',
        pharmaceuticalFactory: ''
      },
      productInfo: {},
      rules: {
        code: [
          {
            required: true,
            type: 'string',
            message: '请输入规格规格',
            trigger: 'blur'
          }
        ],
        spec: [
          {
            required: true,
            type: 'string',
            message: '请输入包装规格',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  watch: {
    productId: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        if (val !== '') {
          this.listQuery.productId = val
          this.queryDosageForm()
          this.getProduct(val)
          this.getList()
        }
      }
    },
    showDialog(nv, ov) {
      if (nv) {
        this.list = []
      }
    }

  },
  methods: {
    getList() {
      ProductService.QueryProductSpecs(this.listQuery).then(res => {
        this.list = res.data.datas
      }).catch(res => {})
    },
    getProduct(productId) {
      ProductService.GetProduct({
        id: productId
      }).then(res => {
        this.productInfo = res.data
        this.temp.productId = res.data.id
        this.temp.productNameCn = res.data.nameCn
        this.temp.productCommonName = res.data.commonName
      }).catch(res => {})
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    resetTemp() {
      this.temp.code = ''
      this.temp.spec = ''
      this.temp.unit = ''
      this.temp.dosageFormCode = ''
      this.temp.pharmaceuticalFactory = ''
    },
    btnClose() {
      this.dialogSpecEditFormVisible = false
    },
    queryDosageForm() {
      MasterDataService.GetDicts({ parentCode: 'DosageForm' }).then(res => {
        this.dosageFormList = res.data
      })
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogSpecEditFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          ProductService.AddProductSpec(this.temp)
            .then((result) => {
              if (result.succeed) {
                this.dialogSpecEditFormVisible = false
                this.getList()
                this.showMessage('创建成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
              this.loading = false
            })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.dialogStatus = 'update'
      this.temp.edit = false
      this.dialogSpecEditFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          ProductService.UpdateProductSpec(this.temp)
            .then((result) => {
              if (result.succeed) {
                this.dialogSpecEditFormVisible = false
                this.getList()
                this.showMessage('更新成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
              this.loading = false
            })
        }
      })
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },

    handleDelete(row) {
      this.$confirm('确定删除此包装规格吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const tempRow = Object.assign({}, row)

          ProductService.DeleteProductSpec(tempRow)
            .then((result) => {
              if (result.succeed) {
                this.getList()
                this.showMessage('删除成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
              this.loading = false
            })
        })
        .catch((error) => {
          if (!error.succeed) {
            this.showMessage('取消删除', 'info')
          }
        })
    }

  }
}
</script>
<style scoped>
  .el-dialog-s {
  z-index: 12;
}
</style>
