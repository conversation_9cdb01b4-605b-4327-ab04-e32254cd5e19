<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      title="流向明细"
      :visible="dialogSalesFlowVisible"
      width="80%"
      @close="handleClose"
    >
      <el-row class="query-title" type="flex" :gutter="10">
        <el-col :span="12">
          <label>项目名称：{{ rebateResult.rebateAgreementName }}</label>
        </el-col>
        <el-col :span="8">
          <label>计算周期：{{ rebateResult.startDate| parseTime('{y}-{m}-{d}') }}~{{ rebateResult.endDate| parseTime('{y}-{m}-{d}') }}</label>
        </el-col>
        <el-col :span="4" style="text-align: right; margin-bottom: 5px;">
          <el-button
            :loading="btnExportLoading"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>

      <el-row style="margin-bottom:20px;">
        <el-col :span="24">
          <el-table
            :data="rebateSalesFlowList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
          >
            <el-table-column fixed label="序号" type="index" align="center" />

            <el-table-column label="流向月份" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span>{{ row.yearMonthSplicing }}</span>
              </template>
            </el-table-column>
            <el-table-column label="发货方名称" align="left" header-align="center" min-width="130px">
              <template slot-scope="{ row }">
                <span>{{ row.distributorName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="收货方名称" align="left" header-align="center" min-width="130px">
              <template slot-scope="{ row }">
                <span>{{ row.receiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="厂家" align="center" header-align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span>{{ row.manufacturerName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="产品名称" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span>{{ row.productName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="包装规格" align="center" min-width="120px">
              <template slot-scope="{ row }">
                <span>{{ row.productSpecName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="数量" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span>{{ row.quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column label="单位" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span>{{ row.productSpecUnit }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleClose()"> 关闭 </el-button>
      </div>
    </el-dialog>
    <!--导出-->
    <el-dialog
      :visible="showExportModal"
      :close-on-click-modal="false"
      title="导出项目"
      width="800"
      @close="handleExportCancel"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>
<script>
import RebateService from '@/api/rebate'
import CustomExport from '@/components/Export/CustomExport'

export default {
  components: {
    CustomExport
  },
  data() {
    return {
      span: 12,
      dialogSalesFlowVisible: false,
      rebateSalesFlowList: [],
      rebateAgreementName: null,
      rebateResult: {},
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: [],
      filter: {
        rebateResultId: null
      }
    }
  },
  methods: {
    initPage(data) {
      this.rebateResult = data
      this.getSalesBonusList(data.id)
      this.dialogSalesFlowVisible = true
      
      this.filter.rebateResultId = data.id
    },
    getSalesBonusList(id) {
      this.listLoading = true
      RebateService.QueryRebateSalesFlow({ rebateResultId: id }).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.rebateSalesFlowList = result.data.datas
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleClose() {
      this.dialogSalesFlowVisible = false
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      RebateService.GetRebateSalesFlowColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      this.filter.checkedColumns = checkColumns
      RebateService.ExportRebateSalesFlow(this.filter)
        .then(result => {
          this.filter.checkedColumns = null
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '返利流向.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '返利流向.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    }
  }
}
</script>
<style scoped>
.el-collapse-item ::v-deep .el-collapse-item__header {
  font-size: 14px !important;
  font-weight: 600 !important;
}
</style>
