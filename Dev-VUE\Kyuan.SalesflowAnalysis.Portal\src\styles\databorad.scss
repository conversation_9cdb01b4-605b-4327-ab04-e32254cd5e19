.component-body{
  background-color: white;
  border-radius: 5px;
  width: 100%;
  font-size: .7rem;
  height: 100%;
  overflow: hidden;

  .compenent-title{
    display: flex;
    flex-direction: row;
    margin: 0 auto;
    padding: 10px 0 ;
    height: 40px;
    width: 95%;
    justify-content: space-between;
    border-bottom: 2px solid #CACACA;

    .title-left{
      display: flex;
      flex-direction: row;      
    }
    .title-right{
      padding-right: 5px;
      a{color: #4395D3;}
    }
  }

  .el-table{
    font-size: .9em !important;
    .cell{
      padding-left: 3px;
      padding-right: 3px;
    }
  }
  .el-progress{
    margin: 0 auto;
  }
}

.waringCount{
  margin-left: 10px;
  font-size: 1.3em;
  color: red;
  font-weight: bolder;
}

.report-list{
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .report-list-item{
    padding: 5px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    border-bottom: 1px solid #CACACA;
    .report-content{
      padding: 10px 0px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    .item-left{
      flex: 3;
      display: flex;
      flex-direction: column;
      justify-content: center;
      
      //  div{
      //   text-align: left;
      //  }
    }

    .item-right{
      flex: 1;
      clear: both;
      text-align: inherit;
      display: flex;
      flex-direction: column;
      justify-content: center;
      max-width: 100px;
      position: relative;
      .circleCenter {
        position: absolute;
        text-align: center;  
        top: 50%;
        left: 60%;
        width: 100%;
        transform: translate(-50%, -50%);
        div {
          font-size: 1.5em;
          font-weight: 600;
        }
        span {
          font-size: .9em;
        }
      }

    }
  }
  }
  .report-list-item:nth-child(2n+1){
    .item-left{
      max-width: 220px;
    }
    .report-row{
        div{
        text-align: right;
        max-width: 10em;
       }
       div:last-child{
        margin-right: 3em;
       }
    }
  }
  .report-list-item:last-child{
    border: 0px;
  }
}
.report-row{
  display: flex;
  flex-direction: row;
  line-height: 150%;
  justify-content: space-between;
  div{
    text-align: right;
    flex: 1;
    color: #3b3b3b;
  }
  div:nth-child(2n){
    color: #9E9E9E;
    font-size: 1em;
  }
}

.report-row-dislocation{
  display: flex;
  flex-direction: column;
  line-height: 150%;
  div:first-child{
    text-align: left;
    flex: 1;
    color: #3b3b3b;
  }
  div:last-child{
    text-align: right !important;
    padding-right: 10px;
    color: #9E9E9E;
    font-size: 1em;
  }
}
.font-bold{
  font-weight: bolder;
  color: #000000;
}

.item2{
  flex: 1;
  clear: both;
  text-align: inherit;
  display: flex;
  flex-direction: column;
  justify-content: center;
  max-width: 100px;
  position: relative;
  .circleCenter2 {
    position: absolute;
    text-align: center;  
    top: 50%;
    left: 50%;
    width: 100%;
    transform: translate(-50%, -50%);
    div {
      font-size: 1.5em;
      font-weight: 600;
    }
    span {
      font-size: .9em;
    }
  }

}


