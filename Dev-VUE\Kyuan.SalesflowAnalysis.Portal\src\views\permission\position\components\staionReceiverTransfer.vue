<template>
  <div>
    <el-dialog custom-class="el-dialog-s" :title="title" width="50%" append-to-body :close-on-click-modal="false" :visible="showDialog" @close="closeDialog">
      <el-form
        ref="dataForm"
        :model="dataModel"
        label-position="right"
        label-width="100px"
        class="el-dialogform"
        style="width: 90%;"
        :rules="rules"
      >
        <el-row class="filter-container">
          <el-col :span="span">
            <el-form-item label="岗位" prop="stationId">
              <TreeSelect
                v-model="dataModel.station"
                :options="stationArray"
                placeholder="岗位"
                :multiple="false"
                :max-height="200"
                value-format="object"
                :flat="true"
                searchable
                @input="selectStation"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="移岗月份" prop="month">
              <el-date-picker
                v-model="dataModel.month"
                type="month"
                value-format="yyyy-MM"
                placeholder="移岗月份"
                :picker-options="datePickerOptions"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-circle-check" type="primary" @click="btnBatchTransfer()"> 批量移岗 </el-button>
        <el-button icon="el-icon-close" @click="closeDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import MaintenanceService from '@/api/maintenance'
export default {
  data() {
    const disabledDate = (time) => {
      if (this.station) {
        var date = new moment(this.station.startDate).valueOf()
        return time.getTime() < date || time.getTime() > Date.now()
      } else {
        return time.getTime() > Date.now()
      }
    }
    return {
      span: 22,
      showDialog: false,
      title: '批量移岗',
      dataModel: { stationId: '', month: '' },
      datePickerOptions: {
        disabledDate(time) {
          return disabledDate(time)
        }
      },
      rules: {
        stationId: [
          {
            required: true,
            message: '请选择岗位',
            type: 'string',
            trigger: 'blur'
          }
        ],
        month: [
          {
            required: true,
            message: '请选择移岗月份',
            type: 'string',
            trigger: 'change'
          }
        ]
      },
      stationArray: [],
      currStation: {},
      station: {}
    }
  },
  computed: {
  },
  methods: {
    init(stations, currStation) {
      this.showDialog = true
      this.currStation = currStation
      var allStation = JSON.parse(JSON.stringify(stations))
      var deptStation = []
      deptStation.push(this.getDeptStation(allStation, currStation))
      this.stationArray = this.chearEmityChildren(deptStation, currStation)
      if (this.$refs.dataForm) {
        this.$refs.dataForm.resetFields()
      }
    },
    getDeptStation(station, currStation) {
      if (station instanceof Array) {
        station.map(item => {
          this.getDeptStation(item, currStation)
        })
      } else {
        if (station.deptId === currStation.deptId) {
          return station
        }
        if (station.children.length > 0) {
          for (var item of station.children) {
            var resultStation = this.getDeptStation(item, currStation)
            if (resultStation) {
              return resultStation
            }
          }
        } else {
          delete station.children
        }
      }
      return null
    },
    chearEmityChildren(stations, currStation) {
      return stations.map(item => {
        if (item.children && item.children.length > 0) {
          this.chearEmityChildren(item.children, currStation)
        } else {
          delete item.children
        }
        // if () {
        //   item.isDisabled = true
        // }
        if (item.pkid === currStation.id || item.enumType === 1 || item.positionGrade === '0') {
          item.isDisabled = true
        } else {
          item.isDisabled = false
        }
        item.isDefaultExpanded = true
        return item
      })
    },
    selectStation(sel) {
      if (sel) {
        this.$set(this.dataModel, 'stationId', sel.pkid)
        this.getStation(sel.pkid)
      } else {
        this.$set(this.dataModel, 'stationId', '')
        this.station = {}
      }
      this.$refs['dataForm'].validateField(['stationId'])
    },
    getStation(id) {
      MaintenanceService.GetStation({ id: id })
        .then((result) => {
          if (result.succeed) {
            this.station = result.data
            this.$set(this.dataModel, 'employeeId', result.data.currentEmployee.employeeId)
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    btnBatchTransfer() {
      // var station = this.getStationId(this.stationArray, this.dataModel.stationId)
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$emit('btnBatchTransfer', this.dataModel)
        }
      })
    },
    closeDialog() {
      this.showDialog = false
      this.dataModel = { stationId: '', month: '' }
    }
  }
}
</script>
