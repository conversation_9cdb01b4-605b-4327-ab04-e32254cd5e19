<template>
  <div>
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="停用渠道"
      :close-on-click-modal="false"
      :visible="dialogVisible"
      width="60%"
      class="popup-search"
      @close="closeDialog"
    >

      <el-form ref="dataForm" :rules="rules" :model="tempModel" label-position="right" label-width="120px">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="12">
            <el-form-item label="省份/城市">
              {{ tempModel.receiverProvinceName }} / {{ tempModel.receiverCityName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品">
              {{ tempModel.productNameCn }} / {{ tempModel.spec }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上游商业">
              {{ tempModel.originalDistributorName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="经销商名称">
              {{ tempModel.receiverName }}
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="停用日期" prop="month">
              <el-date-picker
                v-model="tempModel.month"
                type="month"
                clearable
                value-format="yyyy-MM"
                style="width: 100%"
                :picker-options="datePickerOptions"
                placeholder="停用日期"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeDialog">
          关闭
        </el-button>
        <el-button :loading="btnLoading" type="primary" icon="el-icon-check" @click="submitForm">
          保存
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import channelService from '@/api/channel'

export default {

  data() {
    return {
      dialogVisible: false,
      btnLoading: false,
      distributorTypes: [],
      datePickerOptions: {
        disabledDate(time) {
          const now = new Date()
          return time.getTime() > new Date(now.getFullYear(), now.getMonth(), 1) - 8.64e6
        }
      },
      rules: {
        month: [
          {
            required: true,
            type: 'string',
            message: '请选择停用日期',
            trigger: 'change'
          }
        ]
      },
      tempModel: {}
    }
  },
  methods: {
    initData(row) {
      channelService.GetChannel({ id: row.id })
        .then((result) => {
          this.tempModel = result.data
          this.dialogVisible = true
          this.$refs.dataForm.resetFields()
        })
        .catch(() => {
        })
    },
    // 保存
    submitForm() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          channelService.StopChannel(this.tempModel)
            .then((result) => {
              if (result.succeed) {
                this.$message({
                  message: '变更成功',
                  type: 'success'
                })
                this.btnLoading = false
                this.closeDialog()
                this.$emit('success')
              } else {
                this.btnLoading = false
                this.ShowTip(result)
              }
            })
            .catch(() => {
              this.btnLoading = false
            })
        }
      })
    },
    closeDialog() {
      this.dialogVisible = false
      this.tempModel = {}
      this.$refs.dataForm.resetFields()
    },
    // 选择收货方
    handleSelectReceiver() {
      this.distributorTypes = [10, 20]
      this.dialogDistributorVisible = true
    },
    closeDistributorDialog() {
      this.dialogDistributorVisible = false
      this.$refs.refSelectDistributor.clear()
      this.distributorTypes = []
    },
    selectDistributorSuccess(val) {
      if (val.id === this.tempModel.receiverId) {
        this.$notice.message('经销商不能和客户相同', 'error')
      } else {
        this.$set(this.tempModel, 'distributorId', val.id)
        this.$set(this.tempModel, 'distributorName', val.name)
        this.closeDistributorDialog()
      }
    }
  }
}
</script>

