<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="span">
          <el-input
            v-model="listQuery.code"
            clearable
            placeholder="字典编码"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.name"
            clearable
            placeholder="字典名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Dict_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Dict_Button_Add')"
            type="primary"
            icon="el-icon-plus"
            @click="handleCreate"
          >
            新增
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              :index="indexMethod"
              type="index"
              align="center"
            />
            <el-table-column
              sortable="custom"
              prop="Code"
              label="字典编码"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.code }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Name"
              label="字典名称"
              align="center"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="140"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i v-if="$isPermitted($store.getters.user, 'Dict_Button_Edit')" class="el-icon-edit-outline eltablei" title="编辑" @click="handleUpdate(row)" />
                <i v-if="$isPermitted($store.getters.user, 'Dict_Button_Del')" class="el-icon-delete eltablei" title="删除" @click="handleDelete(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :close-on-click-modal="false"
      title="新增字典"
      :visible="dialogAddFormVisible"
      width="50%"
      @close="btnClose"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="90px"
        class="el-dialogform"
      >
        <el-form-item label="字典编码" prop="code">
          <el-input
            v-model="temp.code"
            clearable
            placeholder="产品名称"
            maxlength="50"
          />
        </el-form-item>
        <el-form-item label="字典名称" prop="name">
          <el-input
            v-model="temp.name"
            clearable
            placeholder="通用名"
            maxlength="100"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnClose()"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleSave()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <EditDict ref="dialogEditDict" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import MasterDataService from '@/api/masterData'
import EditDict from './components/editDict.vue'

export default {
  name: 'Dict',
  components: {
    Pagination,
    EditDict
  },
  data() {
    return {
      span: 4,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      dialogAddFormVisible: false,
      temp: {},
      rules: {
        code: [
          {
            required: true,
            type: 'string',
            message: '字典编码',
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            type: 'string',
            message: '字典名称',
            trigger: 'blur'
          }
        ]
      }

    }
  },
  created() {
    this.getList()
  },
  methods: {
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList() {
      this.listLoading = true

      MasterDataService.QueryDict(this.listQuery)
        .then((result) => {
          this.listLoading = false

          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        })
        .catch((error) => {
          console.log(error)
          this.loading = false
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleCreate() {
      this.dialogAddFormVisible = true
    },
    handleUpdate(row) {
      this.$refs.dialogEditDict.initPage(row.id)
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },
    handleDelete(row) {
      this.$confirm('确定删除此字典吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const tempRow = Object.assign({}, row)

          MasterDataService.DeleteDict(tempRow)
            .then((result) => {
              if (result.succeed) {
                this.getList()
                this.showMessage('删除成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
            })
        })
        .catch((error) => {
          if (!error.succeed) {
            this.showMessage('取消删除', 'info')
          }
        })
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleSave() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          MasterDataService.AddDict(this.temp)
            .then((result) => {
              if (result.succeed) {
                this.btnClose()
                this.getList()
                this.showMessage('创建成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
            })
        }
      })
    },
    btnClose() {
      this.dialogAddFormVisible = false
      this.temp = {}
      this.$refs['dataForm'].resetFields()
    }
  }
}
</script>
