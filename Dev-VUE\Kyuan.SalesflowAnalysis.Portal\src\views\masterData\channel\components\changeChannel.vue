<template>
  <div>
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="变更渠道"
      :close-on-click-modal="false"
      :visible="dialogVisible"
      width="60%"
      class="popup-search"
      @close="closeDialog"
    >

      <el-form ref="dataForm" :rules="rules" :model="tempModel" label-position="right" label-width="120px">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="12">
            <el-form-item label="省份/城市">
              {{ tempModel.receiverProvinceName }} / {{ tempModel.receiverCityName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品">
              {{ tempModel.productNameCn }} / {{ tempModel.spec }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="经销商名称">
              {{ tempModel.receiverName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原上游商业">
              {{ tempModel.originalDistributorName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上游商业" prop="distributorName">
              <el-col :span="16" style="padding-left: 0px; padding-right: 0px;">
                <el-input v-model="tempModel.distributorName" :readonly="true" placeholder="上游商业" />
              </el-col>
              <el-col :span="6">
                <el-button
                  icon="el-icon-search"
                  style="width: 100%; "
                  type="primary"
                  title="选择上游商业"
                  @click="handleSelectReceiver"
                />
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="生效日期" prop="month">
              <el-date-picker
                v-model="tempModel.month"
                type="month"
                clearable
                value-format="yyyy-MM"
                style="width: 100%"
                :picker-options="datePickerOptions"
                placeholder="生效日期"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeDialog">
          关闭
        </el-button>
        <el-button :loading="btnLoading" type="primary" icon="el-icon-check" @click="submitForm">
          保存
        </el-button>
      </div>
    </el-dialog>
    <!--选择经销商-->
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="选择经销商"
      :close-on-click-modal="false"
      :visible="dialogDistributorVisible"
      width="80%"
      class="popup-search"
      @close="closeDistributorDialog"
    >
      <SelectReceiver
        ref="refSelectDistributor"
        :show-dialog="dialogDistributorVisible"
        :distributor-types="distributorTypes"
        :is-stopped="false"
        @success="selectDistributorSuccess"
      />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeDistributorDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import channelService from '@/api/channel'
import SelectReceiver from '@/views/components/selectReceiver'
export default {
  components: {
    SelectReceiver
  },
  data() {
    return {
      dialogDistributorVisible: false,
      dialogVisible: false,
      btnLoading: false,
      distributorTypes: [],
      datePickerOptions: {
        disabledDate(time) {
          const now = new Date()
          return time.getTime() > new Date(now.getFullYear(), now.getMonth(), 1) - 8.64e6
        }
      },
      rules: {
        distributorName: [
          {
            required: true,
            type: 'string',
            message: '请输入上游商业',
            trigger: 'change'
          }
        ],
        month: [
          {
            required: true,
            type: 'string',
            message: '请输入生效日期',
            trigger: 'change'
          }
        ]
      },
      tempModel: {}
    }
  },
  methods: {
    initData(row) {
      channelService.GetChannel({ id: row.id })
        .then((result) => {
          this.tempModel = result.data
          this.dialogVisible = true
          this.$refs.dataForm.resetFields()
        })
        .catch(() => {
        })
    },
    // 保存
    submitForm() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          channelService.ChangeChannel(this.tempModel)
            .then((result) => {
              if (result.succeed) {
                this.$message({
                  message: '变更成功',
                  type: 'success'
                })
                this.btnLoading = false
                this.closeDialog()
                this.$emit('success')
              } else {
                this.btnLoading = false
                this.ShowTip(result)
              }
            })
            .catch(() => {
              this.btnLoading = false
            })
        }
      })
    },
    closeDialog() {
      this.dialogVisible = false
      this.tempModel = {}
      this.$refs.dataForm.resetFields()
    },
    // 选择收货方
    handleSelectReceiver() {
      this.distributorTypes = [10, 20]
      this.dialogDistributorVisible = true
    },
    closeDistributorDialog() {
      this.dialogDistributorVisible = false
      this.$refs.refSelectDistributor.clear()
      this.distributorTypes = []
    },
    selectDistributorSuccess(val) {
      if (val.id === this.tempModel.receiverId) {
        this.$notice.message('经销商不能和上游商业相同', 'error')
      } else {
        this.$set(this.tempModel, 'distributorId', val.id)
        this.$set(this.tempModel, 'distributorName', val.name)
        this.closeDistributorDialog()
      }
    }
  }
}
</script>

