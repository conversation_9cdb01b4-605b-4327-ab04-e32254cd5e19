<!--客户同比销量涨幅排名-->
<template>
  <div class="panel-group">
    <el-card style="height: 250px;">
      <div slot="header" class="clearfix cardHeader">
        <span>客户销售额同比涨幅排名</span>
        <el-button class="detailButton" type="text" @click="handleMore">详情</el-button>
      </div>
      <div>
        <el-table :data="salesRevenueSameCompareList" style="width:100%;" max-height="200">
          <el-table-column fixed="left" label="部门" show-overflow-tooltip prop="departmentName" width="80px" align="center" />
          <el-table-column fixed="left" prop="receiverName" label="客户" show-overflow-tooltip align="left" header-align="center">
            <template slot-scope="scope">
              <div class="cell-ellipsis">{{ scope.row.receiverName }}</div>
            </template>
          </el-table-column>
          <el-table-column fixed="left" label="产品" show-overflow-tooltip prop="productName" width="80px" align="center" />
          <el-table-column fixed="left" prop="currentSalesAmount" label="当前周期(元)" width="90px" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              <div class="cell-ellipsis">{{ scope.row.currentSalesAmount | toMoney }}</div>
            </template>
          </el-table-column>
          <el-table-column fixed="left" prop="compareSalesAmount" label="同比周期(元)" width="90px" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              <div class="cell-ellipsis">{{ scope.row.compareSalesAmount | toMoney }}</div>
            </template>
          </el-table-column>
          <el-table-column label="同比增长率" prop="compareSalesAmountRate" width="80px" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              <div class="cell-ellipsis">{{ scope.row.compareSalesAmountRate | toTwoNum }}%</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    <!--弹出dialog:详情页-->
    <el-dialog append-to-body title="客户销售额同比涨幅排名" width="60%" :close-on-click-modal="false" :visible="showDialog" @close="cancleDialog()">
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="4">
            <el-select
              v-model="filter.departmentId"
              value-key="value"
              class="filter-item"
              placeholder="部门"
              style="width:100%"
              clearable
            >
              <el-option
                v-for="item in deptList"
                :key="item.key"
                :label="item.name"
                :value="item.key"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-input
              v-model="filter.receiverName"
              clearable
              placeholder="客户名称"
              class="filter-item"
            />
          </el-col>
          <el-col :span="8">
            <el-cascader
              ref="refProductAndSpec"
              v-model="manufacturerProductAndSpecId"
              :options="productAndSpecList"
              placeholder="厂商 / 产品 / 规格"
              style="width: 100%"
              clearable
              class="filter-item"
              :props="{
                multiple: true,
                checkStrictly: false,
                expandTrigger: 'hover',
                emitPath: true,
              }"
            />
          </el-col>
          <el-col :span="4">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-search"
              @click="search"
            >
              查询
            </el-button>
          </el-col>
        </el-row>
        <el-row type="flex" :gutter="10" justify="end">
          <el-col :span="3.5">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-download"
              @click="handleExport"
            >
              导出
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table
              :data="salesRevenueSameCompareDetailList"
              stripe
              fit
              border
              highlight-current-row
              style="width: 100%"
              :header-cell-class-name="'tableStyle'"
            >
              <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
              <el-table-column label="部门" width="120" header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.departmentName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="客户" min-width="150" header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.receiverName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="产品" width="120" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.productName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="当前周期(元)" width="120" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.currentSalesAmount | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column label="同比周期(元)" width="120" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.compareSalesAmount | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column label="同比增长率" width="120" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.compareSalesAmountRate | toTwoNum }}%</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col v-if="totalCount > 0" class="el-colRight">
            <pagination
              v-show="totalCount > 0"
              :total="totalCount"
              :page.sync="filter.pageIndex"
              :limit.sync="filter.pageSize"
              @pagination="search"
            />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleDialog()">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import dashboardService from '@/api/dashboard'
import Pagination from '@/components/Pagination'
export default {
  components: {
    Pagination
  },
  data() {
    return {
      parentFilter: {},
      filter: {
        timeRange: [],
        productSpecIds: [],
        departmentId: null,
        receiverName: null,
        pageIndex: 1,
        pageSize: 10
      },
      salesRevenueSameCompareList: [],
      salesRevenueSameCompareLoading: false,
      showDialog: false,
      deptList: [],
      productAndSpecList: [],
      manufacturerProductAndSpecId: [],
      salesRevenueSameCompareDetailList: [], // 明细页数据源
      totalCount: 0 // 数据总数
    }
  },
  methods: {
    initPage(filter, deptList, productAndSpecList) {
      this.parentFilter = filter
      this.setFilter()
      this.deptList = deptList
      this.productAndSpecList = productAndSpecList
      this.manufacturerProductAndSpecId = this.parentFilter.manufacturerProductAndSpecId
      this.QueryTopOfYearOnYearIncreaseOnReceiverSales()
    },
    QueryTopOfYearOnYearIncreaseOnReceiverSales() {
      this.salesRevenueSameCompareLoading = true
      dashboardService.QueryTopOfYearOnYearIncreaseOnReceiverSales(this.filter)
        .then((res) => {
          this.salesRevenueSameCompareList = res
          this.salesRevenueSameCompareLoading = false
          this.initChart()
        })
        .catch(() => {
          this.salesRevenueSameCompareLoading = false
        })
    },
    QueryRankingOfYearOnYearIncreaseOnReceiverSales() {
      this.salesRevenueSameCompareLoading = true
      dashboardService.QueryRankingOfYearOnYearIncreaseOnReceiverSales(this.filter)
        .then((res) => {
          this.salesRevenueSameCompareDetailList = res.data.datas
          this.totalCount = res.data.recordCount
          this.salesRevenueSameCompareLoading = false
        })
        .catch(() => {
          this.salesRevenueSameCompareLoading = false
        })
    },
    cancleDialog() {
      this.showDialog = false
      this.salesRevenueSameCompareDetailList = []
      this.manufacturerProductAndSpecId = this.parentFilter.manufacturerProductAndSpecId
      this.filter.departmentId = null
      this.filter.receiverName = ''
      this.filter.productSpecIds = []
    },
    search() {
      if (this.manufacturerProductAndSpecId) {
        this.filter.productSpecIds = this.manufacturerProductAndSpecId.map(element => element[2])
      }
      this.QueryRankingOfYearOnYearIncreaseOnReceiverSales()
    },
    handleExport() {
      if (this.manufacturerProductAndSpecId) {
        this.filter.productSpecIds = this.manufacturerProductAndSpecId.map(element => element[2])
      }

      dashboardService.ExportRankingOfYearOnYearIncreaseOnReceiverSales(this.filter)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '客户销售额同比涨幅排名.xlsx'

          fileDownload(result.data, filename)
        })
        .catch(() => {

        })
    },
    indexMethod(index) {
      return (
        (this.filter.pageIndex - 1) * this.filter.pageSize + index + 1
      )
    },
    handleMore() {
      this.showDialog = true
      this.filter.pageIndex = 1
      this.setFilter()
      this.search()
    },
    setFilter() {
      this.filter.departmentId = this.parentFilter.departmentId
      this.filter.productSpecIds = this.parentFilter.productSpecIds
      this.filter.provinceIds = this.parentFilter.provinceIds
      this.filter.timeRange = this.parentFilter.timeRange
      this.filter.provinceIds = this.parentFilter.provinceIds
      this.manufacturerProductAndSpecId = this.parentFilter.manufacturerProductAndSpecId
      this.filter.isSelectedAllProvince = this.parentFilter.isSelectedAllProvince
    }
  }
}
</script>

<style lang="scss" scoped>
.cardHeader {
  height: 5px;
}
.el-table .el-table__body td .cell-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}
::v-deep .el-card__body {
  padding: 5px 10px 10px 5px !important;
}
.detailButton {
  float: right;
  padding: 3px 0
}
</style>

