<!--部门收支明细-->
<template>
  <div class="panel-group">
    <el-card style="height:380px;">
      <div slot="header" class="clearfix cardHeader">
        <span>部门收支明细报表</span>
      </div>
      <div>
        <el-collapse v-model="activeNames" accordion @change="handleChange">
          <el-collapse-item
            v-for="item in deptList"
            :key="item.key"
            :title="item.title"
            :name="item.key"
          >
            <div>
              <el-table :data="list" style="width:100%;overflow-y:auto;" max-height="260">
                <el-table-column label="大区" prop="region" width="80px">
                  <template slot-scope="{ row }">
                    <span>{{ row.areaName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="实际销售金额/预计销售金额(元)" prop="realIncome" header-align="center" align="right" width="190px">
                  <template slot-scope="{ row }">
                    <span>{{ row.actualIncome | toMoney }} / {{ row.estimatedIncome | toMoney }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="达成率" prop="rate" header-align="center" align="right" width="90px">
                  <template slot-scope="{ row }">
                    <span>{{ row.achievedRatio | toTwoNum }}%</span>
                  </template>
                </el-table-column>
                <el-table-column label="服务费收入/实际支出(元)" prop="realInvestment" header-align="center" align="right" width="180px">
                  <template slot-scope="{ row }">
                    <span>{{ row.actualServiceFeeIncome | toMoney }} / {{ row.actualInvestment | toMoney }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="投入产出比" prop="realInvestmentProportion" header-align="center" align="right">
                  <template slot-scope="{ row }">
                    <span>{{ row.returnOnInvestment | toTwoNum }}%</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
  </div>
</template>

<script>
import DashboardService from '@/api/dashboard'

export default {
  data() {
    return {
      filter: {
        timeRange: [],
        productSpecIds: null,
        departmentId: null,
        areaIds: null,
        provinceId: null
      },
      year: 0,
      deptList: [],
      list: [],
      allDepartmentList: [],
      activeNames: null,
      incomeAndInvestmentLoading: false
    }
  },
  created() {

  },
  methods: {
    initPage(param, departments) {
      this.filter = param
      this.year = param.timeRange[1].substring(0, 4)
      this.queryIncomeAndInvestments(departments)
    },
    queryIncomeAndInvestments(departments) {
      departments.forEach(element => {
        element.title = `${this.year}年度${element.name}收支明细`
      })
      this.activeNames = departments[0].key
      this.deptList = departments

      DashboardService.QueryIncomeAndInvestmentsByDepartment(this.filter)
        .then((res) => {
          this.incomeAndInvestmentLoading = false
          this.allDepartmentList = res
          this.list = this.allDepartmentList.filter(x => x.departmentId === departments[0].key)
        })
        .catch(() => {
          this.incomeAndInvestmentLoading = false
        })
    },
    handleChange() {
      this.list = this.allDepartmentList.filter(x => x.departmentId === this.activeNames)
    }
  }
}
</script>

<style lang="scss" scoped>
.cardHeader {
  height: 5px;
}
.el-card ::v-deep .el-card__header {
  border-bottom:0px;
}
.el-card ::v-deep .el-card__body {
  padding: 0px 10px 0px 10px;
}
</style>

