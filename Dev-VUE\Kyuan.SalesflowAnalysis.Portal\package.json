{"name": "kyuan-sales-flow-analysis_portal", "version": "1.0.0", "description": "科园商业流向分析系统", "author": "Shinsoft", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --fix --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "new": "plop"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "@vue/babel-preset-app": "^4.5.15", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "0.19.2", "body-parser": "^1.19.0", "clipboard": "2.0.6", "codemirror": "5.52.2", "driver.js": "0.9.8", "dropzone": "5.7.0", "echarts": "^4.9.0", "element-ui": "2.13.0", "exceljs": "^4.4.0", "file-saver": "2.0.2", "fuse.js": "5.1.0", "js-cookie": "2.2.1", "js-file-download": "^0.4.12", "jsonlint": "1.6.3", "jszip": "3.3.0", "moment": "^2.29.1", "normalize.css": "8.0.1", "nprogress": "0.2.0", "path-to-regexp": "6.1.0", "screenfull": "5.0.2", "script-loader": "0.7.2", "showdown": "1.9.1", "sortablejs": "1.10.2", "tui-editor": "1.4.10", "vue": "^2.6.11", "vue-count-to": "1.0.13", "vue-echarts": "^6.0.0-rc.6", "vue-loader": "^15.8.3", "vue-router": "3.1.6", "vue-splitpane": "1.0.6", "vuedraggable": "2.23.2", "vuex": "3.1.3", "webpack": "^4.23.0", "xe-utils": "^3.5.24", "xlsx": "0.15.6"}, "devDependencies": {"@babel/core": "7.9.0", "@babel/register": "7.9.0", "@vue/cli-plugin-babel": "4.2.3", "@vue/cli-plugin-eslint": "^4.2.3", "@vue/cli-plugin-unit-jest": "4.2.3", "@vue/cli-service": "4.2.3", "@vue/test-utils": "1.0.0-beta.32", "autoprefixer": "^9.7.5", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.1.0", "babel-jest": "25.2.6", "chalk": "4.0.0", "chokidar": "3.3.1", "connect": "3.7.0", "core-js": "^3.6.4", "eslint": "6.8.0", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "4.0.4", "husky": "^9.1.5", "lint-staged": "10.1.1", "mockjs": "1.1.0", "node-sass": "^4.13.1", "plop": "2.6.0", "runjs": "^4.4.2", "sass-loader": "^8.0.2", "script-ext-html-webpack-plugin": "2.1.4", "serve-static": "^1.14.1", "svg-sprite-loader": "4.2.2", "svgo": "1.3.2", "vue-cli": "^2.9.6", "vue-template-compiler": "2.6.11"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}