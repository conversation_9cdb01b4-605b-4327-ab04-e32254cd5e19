<template>
  <div>
    <el-dialog custom-class="el-dialog-s" title="查看" width="60%" append-to-body :close-on-click-modal="false" :visible="showAddDialog" @close="handleCloseProjectDialog">
      <el-form
        ref="dataForm"
        :model="rebateProject"
        label-position="right"
        label-width="100px"
        class="el-dialogform"
      >
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="项目名称" prop="name">
              {{ rebateProject.name }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="部门" prop="checkedDepartments">
              {{ rebateProject.departmentNames }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="起始日期" prop="startTime">
              {{ fomatDate(rebateProject.startTime) }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="截止日期" prop="endTime">
              {{ fomatDate(rebateProject.endTime) }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="费用类型" prop="enumCostType">
              {{ rebateProject.enumCostTypeDesc }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="状态" prop="enumStatus">
              {{ rebateProject.enumStatusDesc }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="已选产品" prop="productIds">
              {{ rebateProject.productSpecs }}
            </el-form-item></el-col>
        </el-row>
      </el-form>
      <el-row type="flex" justify="end">
        <el-col :span="1" />
        <el-col :span="22">
          <el-divider content-position="left">
            <span>
              实施阶段
            </span>
          </el-divider>
        </el-col>
        <el-col :span="1" />
      </el-row>
      <el-row type="flex" justify="end">
        <el-col :span="1" />
        <el-col :span="22">
          <el-tabs v-model="activeName" type="border-card">
            <el-tab-pane v-for="item in rebateProject.rebateProjectPhaseList" :key="item.tempId" :label="`${fomatDate(item.startTime)}至${fomatDate(item.endTime)}`" :name="item.title">
              <el-row :gutter="10">
                <el-col :span="8">
                  <span>预计销售金额：{{ item.totalIncome | toTwoNum }}元</span>
                </el-col>
                <el-col :span="8">
                  <span>预计成本：{{ item.estimateAmount | toTwoNum }}元</span>
                </el-col>
                <el-col :span="8">
                  <span>返点比例：{{ item.rebateRatio }}%</span>
                </el-col>
              </el-row>
              <el-row :gutter="10" style="padding-top: 10px;">
                <el-col :span="8">
                  <span>实际销售金额：{{ item.actualTotalIncome | toTwoNum }}元</span>
                </el-col>
                <el-col :span="8">
                  <span>实际成本：{{ item.actualAmount | toTwoNum }}元</span>
                </el-col>
                <el-col :span="8">
                  <span>返利金额：{{ item.actualTotalInvolvement | toTwoNum }}元</span>
                </el-col>
              </el-row>
              <el-divider />
              <el-row v-for="departmentItem in item.rebateProjectPhaseDepartmentList" :key="departmentItem.departmentId" style="margin-top: 15px;">
                <el-row>
                  <el-col :span="12">
                    <span>部门名称：{{ departmentItem.departmentName }}</span>
                  </el-col>
                  <el-col :span="12">
                    <span>部门投入比例：{{ departmentItem.rebateRatio }}%</span>
                  </el-col>
                </el-row>
                <el-row style="margin-top:10px">
                  <el-col :span="24">
                    <el-table
                      :data="departmentItem.rebateProjectPhaseProductSpecList"
                      stripe
                      border
                      fit
                      highlight-current-row
                      style="width: 100%;"
                      :default-sort="{prop: 'productNameCn', order: 'descending'}"
                      :header-cell-class-name="'tableStyle'"
                      :row-class-name="handleRowClass"
                    >
                      <el-table-column
                        fixed
                        label="序号"
                        type="index"
                        align="center"
                      />  <el-table-column label="产品名称" align="center" min-width="100px">
                        <template slot-scope="{ row }">
                          <span>{{ row.productNameCn }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="规格" align="center" min-width="100px">
                        <template slot-scope="{ row }">
                          <span>{{ row.spec }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="厂商" align="center" min-width="80px">
                        <template slot-scope="{ row }">
                          <span>{{ row.manufacturerName }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="核算单价(元)" align="center" min-width="90px">
                        <template slot-scope="{ row }">
                          <span>{{ row.accountingBase | toTwoNum }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="返点比例(%)" align="center" min-width="90px">
                        <template slot-scope="{ row }">
                          <span>{{ row.rebateRatio }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="返点单价(元)" align="center" min-width="90px">
                        <template slot-scope="{ row }">
                          <span>{{ row.rebatePrice | toTwoNum }}</span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-col>
                </el-row>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </el-col>
        <el-col :span="1" />
      </el-row>
      <el-row type="flex" justify="end" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleCloseProjectDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import ProjectService from '@/api/project'
import moment from 'moment'

export default {
  data() {
    return {
      span: 12,
      showAddDialog: false,
      rebateProject: {
        rebateProjectProductList: [],
        rebateProjectDepartmentList: [],
        rebateProjectPhaseList: []
      },
      activeNames: [],
      activeName: ''
    }
  },
  methods: {
    initPage(id) {
      this.getRebateProject(id)
      this.showAddDialog = true
    },
    getRebateProject(id) {
      ProjectService.GetRebateProject({ id: id })
        .then((result) => {
          this.rebateProject = result.data
          this.rebateProjectPhaseListFomat()
        })
        .catch(() => {
        })
    },
    // 实施阶段数据处理
    rebateProjectPhaseListFomat() {
      // 实施阶段只有最后一个显示删除属性处理
      for (let index = 0; index < this.rebateProject.rebateProjectPhaseList.length; index++) {
        this.activeNames.push(this.rebateProject.rebateProjectPhaseList[index].tempId)
        this.rebateProject.rebateProjectPhaseList[index].title = '实施阶段' + (index + 1)

        if (index === 0) {
          this.activeName = this.rebateProject.rebateProjectPhaseList[index].title
        }

        if (index === this.rebateProject.rebateProjectPhaseList.length - 1) {
          this.rebateProject.rebateProjectPhaseList[index].isShowDelete = true
        } else {
          this.rebateProject.rebateProjectPhaseList[index].isShowDelete = false
        }
      }
    },
    handleCloseProjectDialog() {
      this.showAddDialog = false
    },
    // 格式化日期
    fomatDate(date) {
      return moment(date).format('YYYY-MM-DD')
    },

    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    }
  }
}
</script>
<style scoped>
.el-collapse-item ::v-deep .el-collapse-item__header {
  font-size: 14px !important;
  font-weight: 600 !important;
}
</style>
