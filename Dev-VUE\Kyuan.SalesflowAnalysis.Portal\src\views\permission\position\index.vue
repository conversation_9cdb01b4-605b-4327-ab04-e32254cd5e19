<template>
  <div>
    <div class="split-container">
      <split-pane split="vertical" :min-percent="20" :default-percent="30" @resize="resize">
        <template slot="paneL">
          <div class="left-container">
            <!-- <el-aside width="590px" style="background-color: white;margin-left:-26px;"> -->
            <el-input v-model="filterText" placeholder="岗位名称或负责人姓名" />
            <el-tree
              ref="tree"
              style="height:100vh;"
              :data="datatree"
              node-key="id"
              :current-node-key="currentKey"
              :props="defaultProps"
              :filter-node-method="filterNode"
              :render-content="renderContent"
              :expand-on-click-node="false"
              :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              @node-click="handleNodeClick"
              @node-expand="handleNodeExpand"
            />
            <!-- </el-aside> -->
          </div>
        </template>
        <template slot="paneR">
          <div class="right-container">
            <el-col>
              <el-row :gutter="10" class="stationInfo">
                <el-col class="el-icon-s-data station-title">
                  岗位信息
                </el-col>
                <el-col style="margin-bottom: 10px;">
                  <el-col class="product-content" :span="8" :title="station.name">岗位：{{ station.name | ellipsis
                  }}</el-col>
                  <el-col class="product-content" :span="8">创建日期：<span v-if="station.startDate">{{ station.startDate |
                    parseTime('{y}-{m}-{d}')}}</span></el-col>
                  <el-col class="product-content" :span="8">编码：{{ station.code }}</el-col>
                  <el-col class="product-content" :span="8">所属部门：{{ station.deptName }}</el-col>
                  <el-col class="product-content" :span="8">职位：{{ station.positionName }}</el-col>
                  <el-col class="product-content" :span="8">是否虚拟岗：{{ station.isVirtualStationDesc }}</el-col>
                </el-col>
              </el-row>
              <el-row :gutter="10" class="stationInfo" style="position:relative;">
                <el-col class="el-icon-s-custom station-title">
                  人员
                </el-col>
                <el-col style="margin-bottom: 10px;" :span="24">
                  <el-col class="product-content" :span="6">员工编号：{{ station.currentEmployee ?
                    station.currentEmployee.employeeJobNo : '' }}</el-col>
                  <el-col class="product-content" :span="6">员工姓名：{{ station.currentEmployee ?
                    station.currentEmployee.employeeDisplayName : '' }}</el-col>
                  <el-col :span="6" class="staion-button">
                    <el-button
                      v-if="station.currentEmployee && station.id && $isPermitted($store.getters.user, 'Organization_Button_DelPerson')"
                      type="primary"
                      @click="btnDeleteEmployeeStation(station.id, station.currentEmployee.id)"
                    >删除</el-button>
                    <el-button
                      v-if="station.id && station.enableAddStationEmployee && $isPermitted($store.getters.user, 'Organization_Button_AddPerson')"
                      type="primary"
                      @click="btnAddEmployeeStaion(station.id)"
                    >添加</el-button>
                  </el-col>
                </el-col>
              </el-row>
              <el-row :gutter="10" class="stationInfo">
                <el-col class="el-icon-location station-title">
                  区域
                </el-col>
                <el-col>
                  <span v-for="item in station.provinces" :key="item.id" class="span-padding">{{ item.nameCn }}</span>
                </el-col>
                <el-col style="height:16px;" />
              </el-row>
              <!-- 客户需求暂时隐藏产品 -->
              <el-row v-if="false" :gutter="10" class="stationInfo">
                <el-col class="el-icon-s-goods station-title">
                  产品
                </el-col>
                <el-col v-for="item in station.products" :key="item.id" class="product-content">
                  <span v-for="spec in item.specification" :key="spec.id" class="span-padding">{{ item.nameCn }}（{{
                    spec.spec }}）</span>
                </el-col>
                <el-col style="height:10px;" />
              </el-row>
              <el-row v-if="enableEditZd" :gutter="10" class="stationInfo">
                <el-col class="el-icon-office-building station-title">
                  {{ receiverType }}
                </el-col>
                <el-row type="flex" :gutter="10" justify="end" style="margin-top:20px;">
                  <el-col :span="3.5">
                    <el-button
                      v-if="station.id && $isPermitted($store.getters.user, 'Organization_Button_AddTerminal')"
                      class="filter-item"
                      type="primary"
                      icon="el-icon-plus"
                      @click="btnAddStaionReceiver"
                    >
                      添加{{ receiverType }}
                    </el-button>
                  </el-col>
                  <el-col :span="3.5">
                    <el-button
                      v-if="station.id && $isPermitted($store.getters.user, 'Organization_Button_DelTerminal')"
                      class="filter-item"
                      type="primary"
                      icon="el-icon-edit"
                      @click="showStaionReceiverTransfer"
                    >
                      批量移岗
                    </el-button>
                  </el-col>
                  <el-col :span="3.5">
                    <el-button
                      v-if="station.id && $isPermitted($store.getters.user, 'Organization_Button_MoveTerminal')"
                      class="filter-item"
                      type="primary"
                      icon="el-icon-delete"
                      @click="batchDeleteStationReceiver"
                    >
                      批量删除{{ receiverType }}
                    </el-button>
                  </el-col>
                </el-row>
                <el-table
                  ref="refTable"
                  :data="stationReceiverList.filter(data => !searchKey || data.receiverName.includes(searchKey))"
                  stripe
                  border
                  fit
                  highlight-current-row
                  style="width: 99%;margin-bottom: 10px;"
                  :default-sort="{ prop: 'createTime', order: 'descending' }"
                  :header-cell-class-name="'tableStyle'"
                  :row-class-name="handleRowClass"
                  @selection-change="handleSelectionChange"
                >
                  <el-table-column align="left" header-align="left" type="selection" width="40px" />
                  <el-table-column prop="Receiver.Province.NameCn" label="省份" align="center" width="100px">
                    <template slot-scope="{ row }">
                      <span>{{ row.provinceName }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="Receiver.Name" align="left" header-align="center" min-width="240px">
                    <template slot="header" slot-scope="{ row }">
                      <el-input
                        v-model="searchKey"
                        style="width:80%"
                        size="mini"
                        :placeholder="'请输入' + receiverType + '名称搜索'"
                        clearable
                      />
                    </template>
                    <template slot-scope="{ row }">
                      <span>{{ row.receiverName }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    fixed="right"
                    label="操作"
                    align="center"
                    header-align="center"
                    width="80px"
                    class-name="small-padding fixed-width"
                  >
                    <template slot-scope="{ row }">
                      <i
                        v-if="$isPermitted($store.getters.user, 'Organization_Button_DelTerminal')"
                        class="el-icon-delete eltablei"
                        title="删除"
                        @click="deleteStationReceiver(row)"
                      />
                    </template>
                  </el-table-column>
                </el-table>

              </el-row>
            </el-col>
            <!-- </el-main> -->
          </div>
        </template>
      </split-pane>
    </div>

    <el-dialog
      :close-on-click-modal="false"
      :title="textMap[dialogStatus]"
      :visible="dialogNodeFormVisible"
      width="70%"
      @close="btnClose"
    >
      <el-form
        ref="stationForm"
        :rules="stationRules"
        :model="temp"
        label-position="right"
        label-width="100px"
        class="el-dialogform"
      >
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="24">
            <el-form-item label="上级岗位">
              <span>{{ temp.fullName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位名称" prop="name">
              <el-input v-model="temp.name" clearable maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建日期" prop="startDate">
              <el-date-picker
                v-model="temp.startDate"
                type="month"
                style="width: 100%"
                :disabled="isUpdateStation"
                :picker-options="pickerOptions"
                placeholder="选择日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位" prop="positionId">
              <el-select
                v-model="temp.positionId"
                class="filter-item"
                style="width: 100%"
                placeholder="请选择职位"
                :disabled="isUpdatePosition"
                clearable
              >
                <el-option v-for="item in positionList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位编码" prop="code">
              <el-input v-model="temp.code" maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否虚拟岗" prop="isVirtualStation">
              <el-select
                v-model="temp.isVirtualStation"
                style="width: 100%"
                class="filter-item"
                placeholder="是否虚拟岗"
                clearable
              >
                <el-option v-for="item in isVirtualList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="描述">
              <el-input v-model="temp.remark" type="textarea" :rows="3" clearable maxlength="500" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="选择省份" prop="provinces">
              <el-checkbox
                v-model="checkAllArea"
                :indeterminate="isIndeterminateArea"
                @change="handleCheckAllProvinceChange"
              >全选</el-checkbox>
              <el-col v-for="(area, index) in areas" :key="index">
                <el-checkbox
                  v-model="area.checkAll"
                  :indeterminate="area.isIndeterminateProvince"
                  @change="area => { handleCheckedAreaChange(area, index) }"
                >{{ area.name }}</el-checkbox>
                <el-checkbox-group
                  v-model="area.checkProvinces"
                  style="margin-left:10px;"
                  @change="pro => { handleCheckedProvinceChange(pro, index) }"
                >
                  <el-checkbox v-for="item in area.provinces" :key="item.key" :label="item.key">{{ item.value
                  }}</el-checkbox>
                </el-checkbox-group>
              </el-col>
            </el-form-item></el-col>
          <!-- 客户需求暂时隐藏产品 -->
          <el-col v-if="false" :span="24">
            <el-form-item label="选择产品" prop="products">
              <el-checkbox
                v-model="checkAllProduct"
                :indeterminate="isIndeterminateProduct"
                @change="handleCheckAllProductChange"
              >全选</el-checkbox>
              <el-col v-for="(pro, index) in products" :key="index">
                <el-checkbox
                  v-model="pro.checkAll"
                  :indeterminate="pro.isIndeterminate"
                  @change="pro => { handleCheckedSpecChange(pro, index) }"
                >{{ pro.nameCn }}</el-checkbox>
                <el-checkbox-group
                  v-model="pro.checkProductSpecs"
                  style="margin-left:10px;"
                  @change="pro => { handleCheckedProductChange(pro, index) }"
                >
                  <el-checkbox v-for="sp in pro.specification" :key="sp.id" :label="sp.id">{{ sp.spec }}</el-checkbox>
                </el-checkbox-group>
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnClose()"> 关闭 </el-button>
        <el-button type="primary" icon="el-icon-check" @click="dialogStatus === 'create' ? createData() : updateData()">
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      title="选择人员"
      :visible="dialogEmployeeStation"
      width="40%"
      @close="btnCloseEmployeeStation"
    >
      <el-form
        ref="employeeForm"
        :rules="employeeRules"
        :model="employeeStation"
        label-position="right"
        label-width="100px"
        class="el-dialogform"
      >
        <el-row :gutter="10">
          <el-col :span="18">
            <el-form-item label="上岗日期" prop="startDate">
              <el-date-picker
                v-model="employeeStation.startDate"
                type="month"
                style="width: 100%"
                :picker-options="pickerOptions"
                placeholder="上岗日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4" />
          <el-col :span="18">
            <el-form-item label="人员信息" prop="employeeId">
              <el-input v-model="employeeStation.employeeDisplayName" readonly clearable maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="chooseEmployee()">选择人员</el-button>
          </el-col>
          <el-col :span="24">
            <el-form-item label="人员入职时间">
              <span v-if="employeeStation.hireDate">{{ employeeStation.hireDate | parseTime('{y}-{m}-{d}') }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnCloseEmployeeStation()"> 关闭 </el-button>
        <el-button type="primary" icon="el-icon-check" @click="createEmployeeStation()">
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      title="人员离岗日期"
      :visible="dialogEmployeeStationDelete"
      width="40%"
      @close="btnCloseEmployeeStationDelete"
    >
      <el-form
        ref="stopEmployeeForm"
        :rules="stopEmployeeRules"
        :model="stopEmployeeStation"
        label-position="right"
        label-width="100px"
        class="el-dialogform"
      >
        <el-row :gutter="10">
          <el-col :span="18">
            <el-form-item label="离岗日期" prop="endDate">
              <el-date-picker
                v-model="stopEmployeeStation.endDate"
                type="date"
                style="width: 100%"
                placeholder="离岗日期"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4" />
          <el-col :span="24">
            <el-form-item label="人员离职时间">
              <span>{{ station.currentEmployee ? station.employeeDepartureDate : '' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnCloseEmployeeStationDelete()"> 关闭 </el-button>
        <el-button type="primary" icon="el-icon-check" @click="deleteEmployeeStation()">
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      title="分配人员"
      :visible="dialogEmployee"
      width="80%"
      class="popup-search"
      @close="btnCloseEmployee"
    >
      <div class="search-container-bg">
        <el-row :gutter="10">
          <el-col :span="span">
            <el-select
              v-model="employeeQuery.departmentId"
              :loading="deptLoading"
              class="filter-item"
              placeholder="部门"
              clearable
              @input="displayNameChange"
            >
              <el-option v-for="item in deptList" :key="item.key" :label="item.value" :value="item.key" />
            </el-select>
          </el-col>
          <el-col :span="span">
            <el-input
              v-model="employeeQuery.displayName"
              clearable
              placeholder="姓名"
              class="filter-item"
              @input="displayNameChange"
              @keyup.enter.native="getEmployeeList"
            />
          </el-col>
          <el-col :span="span">
            <el-input
              v-model="employeeQuery.jobNo"
              clearable
              placeholder="工号"
              class="filter-item"
              @input="displayNameChange"
              @keyup.enter.native="getEmployeeList"
            />
          </el-col>
          <el-col :span="span">
            <el-button class="filter-item-button" type="primary" icon="el-icon-search" @click="getEmployeeList">
              查询
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table
              :data="employeeList"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%;"
              :default-sort="{ prop: 'createTime', order: 'descending' }"
              :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass"
              @sort-change="employeeSortChange"
            >
              <el-table-column fixed label="序号" :index="indexMethodEmployee" type="index" align="center" />
              <el-table-column sortable="custom" prop="JobNo" label="员工编号" align="center" min-width="80px">
                <template slot-scope="{ row }">
                  <span>{{ row.jobNo }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="DisplayName" label="员工姓名" align="center" min-width="80px">
                <template slot-scope="{ row }">
                  <span>{{ row.displayName }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="Department.Name" label="部门" align="center" min-width="80px">
                <template slot-scope="{ row }">
                  <span>{{ row.departmentName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="角色" align="center" min-width="100px">
                <template slot-scope="{ row }">
                  <span>{{ row.roleNames }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="Mobile" label="电话" align="center" min-width="100px">
                <template slot-scope="{ row }">
                  <span>{{ row.mobile }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="Email" label="邮箱" align="center" min-width="150px">
                <template slot-scope="{ row }">
                  <span>{{ row.email }}</span>
                </template>
              </el-table-column>
              <el-table-column
                fixed="right"
                label="操作"
                align="center"
                header-align="center"
                width="100"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="{ row }">
                  <i class="el-icon-circle-check eltablei" title="选择" @click="selectedEmployee(row)" />
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col class="el-colRight">
            <pagination
              v-show="employeeTotal > 0"
              :total="employeeTotal"
              :page.sync="employeeQuery.pageIndex"
              :limit.sync="employeeQuery.pageSize"
              @pagination="getEmployeeList"
            />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnCloseEmployee()"> 关闭 </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      :title="'分配' + receiverType"
      :visible="dialogReceiver"
      width="80%"
      class="popup-search"
      @close="btnCloseReceiver"
    >
      <div class="search-container-bg">
        <el-row :gutter="10">
          <el-col :span="span">
            <el-cascader
              ref="refProvinceCity"
              v-model="receiverQuery.provinceAndCityId"
              style="width: 100%"
              :options="provinceAndCityList"
              :props="{ checkStrictly: true, expandTrigger: 'hover' }"
              placeholder="省份/城市"
              clearable
              class="filter-item"
              @change="closeProvinceCityCascader"
            />
          </el-col>
          <el-col :span="span">
            <el-input
              v-model="receiverQuery.receiverName"
              clearable
              :placeholder="receiverType+'名称'"
              class="filter-item"
              @keyup.enter.native="getReceiverList"
            />
          </el-col>
          <el-col :span="span">
            <el-button class="filter-item-button" type="primary" icon="el-icon-search" @click="getReceiverList">
              查询
            </el-button>
          </el-col>
          <el-col :span="span">
            <el-dropdown placement="bottom-start">
              <span class="el-dropdown-link">
                已选择{{ receiverType }}<i class="el-icon-arrow-down el-icon--right" />
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="item in addSelectRows" :key="item.id" :command="item.id">
                  <div class="slot-menu">
                    <div class="slot-menu-left">
                      {{ item.name }}
                    </div>
                    <div class="slot-menu-right" @click="handleChooseDel(item)">
                      <i
                        class="el-icon-close eltablei"
                        title="删除"
                      />
                    </div>
                  </div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </el-col>
        </el-row>
      </div>
      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table
              ref="refAddTable"
              :data="receiverList"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%;"
              :default-sort="{ prop: 'createTime', order: 'descending' }"
              :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass"
              row-key="id"
              @sort-change="receiverListSortChange"
              @selection-change="handleRowChange"
            >
              <el-table-column align="left" header-align="left" type="selection" :reserve-selection="true" width="40px" />
              <el-table-column sortable="custom" prop="Province.NameCn" label="省份" align="center" min-width="100px">
                <template slot-scope="{ row }">
                  <span>{{ row.provinceNameCn }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="City.NameCn" label="城市" align="center" min-width="100px">
                <template slot-scope="{ row }">
                  <span>{{ row.cityNameCn }}</span>
                </template>
              </el-table-column>
              <el-table-column
                sortable="custom"
                prop="Name"
                :label="receiverType+'名称'"
                align="left"
                header-align="center"
                min-width="200px"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.name }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col class="el-colRight">
            <pagination
              v-show="receiverTotal > 0"
              :total="receiverTotal"
              :auto-scroll="false"
              :page.sync="receiverQuery.pageIndex"
              :limit.sync="receiverQuery.pageSize"
              @pagination="getReceiverList"
            />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-circle-check" type="primary" @click="handleBatchAddReceiver()"> 批量挂岗 </el-button>
        <el-button icon="el-icon-close" @click="btnCloseReceiver()"> 关闭 </el-button>
      </div>
    </el-dialog>
    <staionReceiverTransfer ref="staionReceiverTransfer" @btnBatchTransfer="handleStaionReceiverTransfer" />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import MaintenanceService from '@/api/maintenance'
import ReceiverService from '@/api/receiver'
import LocationService from '@/api/location'
import splitPane from 'vue-splitpane'
import staionReceiverTransfer from './components/staionReceiverTransfer.vue'

export default {
  name: 'Position',
  components: {
    Pagination,
    splitPane,
    staionReceiverTransfer
  },
  filters: {
    ellipsis(value) {
      if (!value) return ''
      if (value.length > 13) {
        return value.slice(0, 15) + '...'
      }
      return value
    }
  },
  data() {
    var validateProducts = (rule, value, callback) => {
      if (this.temp.productSpecs.length > 0) {
        callback()
      } else {
        callback(new Error('请至少选择一个产品'))
      }
    }
    var validateReceiver = (rule, value, callback) => {
      if (this.stationReceiver.receiverId !== undefined) {
        callback()
      } else {
        callback(new Error('请选择' + this.receiverType))
      }
    }
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      employeeTotal: 0,
      employeeQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      receiverTotal: 0,
      receiverQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      stationReceiverTotal: 0,
      stationReceiverQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      list: [],
      filterText: '',
      datatree: [],
      currentKey: '',
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      defaultexpandedkeys: [],
      currentNode: null,
      btnExportLoading: false,
      dialogNodeFormVisible: false,
      dialogEmployee: false,
      dialogReceiver: false,
      manufacturerLoading: false,
      dialogStatus: '',
      temp: {
        id: undefined,
        name: '',
        fullName: '',
        positionId: undefined,
        positionName: '',
        parentId: undefined,
        deptId: undefined,
        isAllProduct: false,
        valid: true,
        remark: '',
        startDate: '',
        productSpecs: [],
        provinces: []
      },
      employeeStation: {
        id: undefined,
        employeeId: undefined,
        stationId: undefined,
        startDate: '',
        // endDate: '',
        valid: true,
        employeeDisplayName: '',
        hireDate: ''
      },
      stopEmployeeStation: {
        id: undefined,
        employeeId: undefined,
        stationId: undefined,
        endDate: '',
        valid: false
      },
      stationReceiver: {
        id: undefined,
        receiverId: undefined,
        stationId: undefined,
        employeeId: undefined
      },
      employeeList: [],
      dialogEmployeeStation: false,
      dialogEmployeeStationDelete: false,

      textMap: {
        update: '编辑子岗位',
        create: '新增子岗位',
        distributionBusiness: '分配终端',
        distributionUser: '分配人员',
        chooseUser: '选择人员',
        deleteUser: '删除人员'
      },
      checkAllArea: false,
      checkedProvinces: [],
      areas: [],
      isIndeterminateArea: false,
      checkAllProduct: false,
      checkedProduct: [],
      products: [],
      isIndeterminateProduct: false,
      receiverType: '',
      stationRules: {
        startDate: [
          {
            required: true,
            message: '请选择创建时间',
            trigger: 'blur'
          }
        ],
        code: [
          {
            required: true,
            type: 'string',
            message: '请输入岗位编码',
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            type: 'string',
            message: '请输入岗位名称',
            trigger: 'blur'
          }
        ],
        positionId: [
          {
            required: true,
            type: 'string',
            message: '请选择职位',
            trigger: 'blur'
          }
        ],
        provinces: [
          { type: 'array', required: true, message: '请至少选择一个省份城市', trigger: 'blur' }
        ],
        products: [
          { required: true, validator: validateProducts, trigger: 'blur' }
        ],
        isVirtualStation: [
          {
            required: true,
            message: '是否虚拟岗',
            trigger: 'change'
          }
        ]
      },
      employeeRules: {
        employeeId: [
          {
            required: true,
            type: 'string',
            message: '请选择人员',
            trigger: 'change'
          }
        ],
        startDate: [
          {
            required: true,
            type: 'date',
            message: '请选择上岗日期',
            trigger: 'blur'
          }
        ]
      },
      stopEmployeeRules: {
        endDate: [
          {
            required: true,
            type: 'date',
            message: '请选择离岗日期',
            trigger: 'blur'
          }
        ]
      },
      receiverRules: {
        receiverName: [
          {
            required: true,
            type: 'string',
            validator: validateReceiver,
            trigger: ['blur', 'change']
          }
        ]
      },
      modifyDialogTitle: '',
      modifyDialogIsReadonly: false,
      itemId: null,
      manufacturerList: [],
      columnDictionary: {},
      positionList: [],
      station: {
        currentEmployee: {}
      },
      stationReceiverList: [],
      receiverList: [],
      provinceAndCityList: [],
      parentStartDate: '',
      pickerOptions: this.stationStartDate(),
      historyProductsCount: 0,
      historyProvincesCount: 0,
      isUpdateStation: false,
      isUpdatePosition: false,
      existChildren: false,
      parentDeptId: '',
      treeNodeId: '',
      enableEditZd: true,
      deptList: [],
      deptLoading: false,
      isVirtualList: [{
        value: true,
        label: '是'
      },
      {
        value: false,
        label: '否'
      }],
      searchKey: '',
      editSelectRows: [],
      addSelectRows: []
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.initDept()
    this.getList()
  },
  methods: {
    resize() {
    },
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch((error) => {
          this.deptLoading = false
          console.log(error)
        })
    },
    renderContent(h, { node, data, store }) {
      return (
        <span class='custom-tree-node'>
          <span class='tree-node-label' title={node.label}>{node.label}</span>
          <span>
            <el-button title='添加' style={data.enableAddStation && this.$isPermitted(this.$store.getters.user, 'Organization_Button_AddNode') ? 'color:#516e92;' : 'display:none'} icon='el-icon-plus' size='mini' type='text' on-click={() => this.handleCreateNode(data)}></el-button>
            <el-button title='编辑' style={data.enableEdit && this.$isPermitted(this.$store.getters.user, 'Organization_Button_EditNode') ? 'color:#516e92;' : 'display:none'} icon='el-icon-edit-outline' size='mini' type='text' on-click={() => this.handleUpdate(data)}></el-button>
            <el-button title='删除' style={data.enableEdit && data.children.length === 0 && this.$isPermitted(this.$store.getters.user, 'Organization_Button_DelNode') ? 'color:#516e92;' : 'display:none'} icon='el-icon-close' size='mini' type='text' on-click={() => this.handleDelete(data)}></el-button>
          </span>
        </span>)
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    handleNodeClick(data, node) {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
      this.stationReceiverQuery = {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
      this.parentDeptId = data.deptId
      this.parentStartDate = data.stationStartDate ?? ''
      if (data.enumType === 2) {
        this.getReceiverType(data.deptCode)
        this.getStation(data.pkid)
      } else {
        this.getPosition(data.deptCode)
      }
      this.searchKey = ''
      this.editSelectRows = []
      this.addSelectRows = []
      this.$nextTick(() => {
        if (this.enableEditZd) {
          this.$refs.refTable.clearSort()
        }
      })
    },
    handleNodeExpand(data, node) {
      if (data.enumType === 1 && node.level !== 1) {
        const deptNodes = this.$refs.tree.getNode(this.datatree[0].id)
        if (deptNodes.childNodes) {
          deptNodes.childNodes.forEach(e => {
            if (e === node) {
              this.changeTreeNodeStatus(e)
            } else {
              e.expanded = false
            }
          })
        }
      }
    },
    // 展开顶级部门和下级第一个部门所有节点
    expandFirstDeptNodes(tree) {
      const deptNodes = this.$refs.tree.getNode(tree[0].id)
      deptNodes.expanded = true
      if (deptNodes.childNodes && deptNodes.childNodes[0]) {
        this.changeTreeNodeStatus(deptNodes.childNodes[0])
      }
    },
    // 展开指定部门所有节点
    expandOneDeptNodes(id) {
      const topDeptNode = this.$refs.tree.getNode(this.datatree[0].id)
      topDeptNode.expanded = true
      const deptNodes = this.$refs.tree.getNode(id)
      deptNodes.expanded = true
      if (deptNodes) {
        this.changeTreeNodeStatus(deptNodes)
      }
    },
    // 改变节点的状态
    changeTreeNodeStatus(node) {
      node.expanded = true
      for (let i = 0; i < node.childNodes.length; i++) {
        // 改变节点的自身expanded状态
        node.childNodes[i].expanded = this.expandAll
        // 遍历子节点
        if (node.childNodes[i].childNodes.length > 0) {
          this.changeTreeNodeStatus(node.childNodes[i])
        }
      }
    },
    getReceiverType(deptCode) {
      if (deptCode === this.$constDefinition.departmentCode.Commerce) {
        this.receiverType = '经销商'
        this.receiverQuery.receiverTypes = [10]
      } else if (deptCode === this.$constDefinition.departmentCode.Digital) {
        this.receiverType = '经销商/终端'
        this.receiverQuery.receiverTypes = [10, 20, 30, 40]
      } else {
        this.receiverType = '终端'
        this.receiverQuery.receiverTypes = [20, 30, 40]
      }
    },
    changeBackgroundColor(id) {
      this.currentKey = id
      this.$refs.tree.setCurrentKey(this.currentKey)
    },
    stationStartDate() {
      const _this = this
      return {
        disabledDate(time) {
          if (_this.parentStartDate) {
            // 8.64e7=1000*60*60*24一天
            return time.getTime() > Date.now() || time.getTime() <= new Date(_this.parentStartDate) - 8.64e7
          } else {
            return time.getTime() > Date.now()
          }
        }
      }
    },
    initProvinceAndCity() {
      LocationService.QueryProvinceCityCascader()
        .then((result) => {
          this.provinceAndCityList = result
        })
        .catch((error) => {
          console.log(error)
        })
    },
    getStation(id) {
      MaintenanceService.GetStation({ id: id })
        .then((result) => {
          if (result.succeed) {
            this.station = result.data
            this.enableEditZd = result.data.positionGrade !== '0'
            if (!this.receiverType) {
              this.getReceiverType(result.data.deptCode)
            }
          } else {
            this.ShowTip(result)
          }
        })
        .catch((error) => {
          console.log(error)
        })
      this.stationReceiverQuery.stationId = id
      this.getStationReceiver()
    },
    getStationReceiver() {
      MaintenanceService.QueryStationReceiverByStationId(this.stationReceiverQuery)
        .then((result) => {
          this.stationReceiverList = result
          this.$forceUpdate()
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleCheckAllProvinceChange(val) {
      this.areas.forEach((item, index) => {
        this.areas[index].checkAll = val
        this.handleCheckedAreaChange(val, index)
      })
    },
    handleCheckedProvinceChange(value, index) {
      const checkedCount = value.length
      this.areas[index].checkAll =
        checkedCount === this.areas[index].provinces.length
      this.areas[index].isIndeterminateProvince =
        checkedCount > 0 &&
        checkedCount < this.areas[index].provinces.length
      this.topProvinceCheckBoxCheck()
    },
    handleCheckedAreaChange(val, index) {
      this.areas[index].checkProvinces = (val
        ? this.areas[index].provinces
        : []
      ).map(item => item.key)
      this.areas[index].isIndeterminateProvince = false
      this.topProvinceCheckBoxCheck()
    },
    topProvinceCheckBoxCheck() {
      const allSelectLen = this.areas.filter(item => item.checkAll)
        .length
      if (allSelectLen === this.areas.length) {
        this.checkAllArea = true
        this.isIndeterminateArea = false
      } else {
        this.checkAllArea = false
        this.isIndeterminateArea =
          this.areas.findIndex(item => item.isIndeterminateProvince) >= 0 ||
          this.areas.findIndex(item => item.checkAll) >= 0
      }
    },
    handleCheckAllProductChange(val) {
      this.products.forEach((item, index) => {
        this.products[index].checkAll = val
        this.handleCheckedSpecChange(val, index)
      })
    },
    handleCheckedSpecChange(val, index) {
      this.products[index].checkProductSpecs = (val
        ? this.products[index].specification
        : []
      ).map(item => item.id)
      this.products[index].isIndeterminate = false
      this.topCheckBoxCheck()
    },
    handleCheckedProductChange(value, index) {
      const checkedCount = value.length
      this.products[index].checkAll =
        checkedCount === this.products[index].specification.length
      this.products[index].isIndeterminate =
        checkedCount > 0 &&
        checkedCount < this.products[index].specification.length
      this.topCheckBoxCheck()
    },
    topCheckBoxCheck() {
      const allSelectLen = this.products.filter(item => item.checkAll)
        .length
      if (allSelectLen === this.products.length) {
        this.checkAllProduct = true
        this.isIndeterminateProduct = false
      } else {
        this.checkAllProduct = false
        this.isIndeterminateProduct =
          this.products.findIndex(item => item.isIndeterminate) >= 0 ||
          this.products.findIndex(item => item.checkAll) >= 0
      }
    },
    getProvinces(id, parentId, isAddStaion, enumType, checkProvinces, deptId) {
      var param = {
        id: id,
        parentId: parentId,
        isAddStation: isAddStaion,
        enumType: enumType,
        deptId: deptId
      }
      MaintenanceService.QueryProvinceSelect(param).then(result => {
        if (result.length > 0) {
          const tempArr = []
          let childCount = 0
          result.forEach(item => {
            var provinces = item.provinces.map(e => {
              return e.key
            })
            childCount += provinces.length
            var isCheck = false
            var i = 0
            var newAreas = []
            if (checkProvinces) {
              checkProvinces.forEach(x => {
                if (provinces.indexOf(x.id) !== -1) {
                  newAreas.push(x.id)
                  isCheck = true
                  i++
                }
              })
            }
            tempArr.push({
              // 子项的全选状态
              checkAll: i === provinces.length,
              // 子项的默认选中的checkbox
              checkProvinces: isCheck ? newAreas : [],
              isIndeterminateProvince: i > 0 && provinces.length > i,
              id: item.id,
              name: item.name,
              provinces: item.provinces
            })
          })
          if (checkProvinces) {
            this.checkAllArea = childCount === checkProvinces.length
            this.isIndeterminateArea = checkProvinces.length > 0 && childCount !== checkProvinces.length
          }
          this.areas = tempArr
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    getProducts(id, parentId, isAddStaion, enumType, products) {
      var param = {
        id: id,
        parentId: parentId,
        isAddStation: isAddStaion,
        enumType: enumType
      }
      MaintenanceService.QueryProductSelect(param).then(result => {
        if (result) {
          const tempArr = []
          let childCount = 0
          result.data.forEach(item => {
            var specification = item.specification.map(e => {
              return e.id
            })
            childCount += specification.length
            var isCheck = false
            var i = 0
            var newProducts = []
            if (products) {
              products.forEach(x => {
                if (specification.indexOf(x) !== -1) {
                  newProducts.push(x)
                  isCheck = true
                  i++
                }
              })
            }
            tempArr.push({
              // 子项的全选状态
              checkAll: i === specification.length,
              // 子项的默认选中的checkbox
              checkProductSpecs: isCheck ? newProducts : [],
              isIndeterminate: i > 0 && specification.length > i,
              id: item.id,
              nameCn: item.nameCn,
              specification: item.specification
            })
          })
          if (products) {
            this.checkAllProduct = childCount === products.length
            this.isIndeterminateProduct = products.length > 0 && childCount !== products.length
          }
          this.products = tempArr

          this.$nextTick(() => {
            this.handleCheckAllProductChange(true)
          })
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    getPosition(code) {
      this.positionList = []
      var param = {
        category: code,
        order: '+grade'
      }
      MaintenanceService.QueryPosition(param).then(result => {
        if (result.succeed) {
          if (this.currentNode && this.currentNode.positionGrade !== undefined) {
            this.positionList = result.data.datas.filter(f => {
              return f.grade === parseInt(this.currentNode.positionGrade) || f.grade === parseInt(this.currentNode.positionGrade) + 1
            })
          } else {
            this.positionList = result.data.datas
          }
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    getPositionByParentNode(code, parentGrade) {
      var param = {
        category: code,
        order: '+grade'
      }

      MaintenanceService.QueryPosition(param).then(result => {
        if (result.succeed) {
          this.positionList = []
          if (parentGrade === '0') {
            this.positionList = result.data.datas.filter(f => {
              return f.code === 'Retail_Regional_Assistant' || f.grade > parseInt(parentGrade)
            })
          } else {
            this.positionList = result.data.datas.filter(f => {
              return f.grade > parseInt(parentGrade)
            })
          }
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    getList(nodeid, deptId) {
      MaintenanceService.QueryOrganization().then(result => {
        if (result.succeed) {
          this.datatree = result.data
          if (deptId) {
            this.$nextTick(() => {
              this.expandOneDeptNodes(deptId)
              if (nodeid) {
                this.changeBackgroundColor(nodeid.toUpperCase())
                this.$refs.refTable.clearSort()
              }
            })
          } else {
            if (this.datatree && this.datatree[0]) {
              this.$nextTick(() => {
                this.expandFirstDeptNodes(this.datatree)
                this.$refs.refTable.clearSort()
              })
            }
            result.data.some(p => {
              if (p.children) {
                p.children.some(s => {
                  if (s.children && s.children[0]) {
                    this.getStation(s.children[0].pkid)
                    this.$nextTick(() => {
                      this.$refs.refTable.clearSort()
                      this.changeBackgroundColor(s.children[0].id)
                    })
                    return true
                  }
                })
              }
            })
          }
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    indexMethodEmployee(index) {
      return (this.employeeQuery.pageIndex - 1) * this.employeeQuery.pageSize + index + 1
    },
    indexMethodReceiver(index) {
      return (this.receiverQuery.pageIndex - 1) * this.receiverQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    receiverSortChange(column, prop, order) {
      this.stationReceiverQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.stationReceiverQuery.order = orderSymbol + column.prop
      this.getStationReceiver()
    },
    employeeSortChange(column, prop, order) {
      this.employeeQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.employeeQuery.order = orderSymbol + column.prop
      this.getEmployeeList()
    },
    receiverListSortChange(column, prop, order) {
      this.receiverQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.receiverQuery.order = orderSymbol + column.prop
      this.getReceiverList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        name: '',
        fullName: '',
        positionId: undefined,
        positionName: '',
        parentId: undefined,
        deptId: undefined,
        isAllProduct: false,
        remark: '',
        startDate: '',
        productSpecs: [],
        provinces: []
      }
    },
    handleCreateNode(row) {
      this.parentStartDate = row.stationStartDate ?? ''
      this.isUpdateStation = false
      this.isUpdatePosition = false
      this.isIndeterminateArea = false
      this.isIndeterminateProduct = false
      this.checkAllArea = false
      this.checkAllProduct = false
      this.getProvinces(row.pkid, row.parentId, true, row.enumType, null, row.deptId)
      this.getProducts(row.pkid, row.parentId, true, row.enumType)

      if (row.enumType !== 1) {
        this.getPositionByParentNode(row.deptCode, row.positionGrade)
      } else {
        this.getPosition(row.deptCode)
      }
      this.resetTemp()
      this.temp.fullName = row.fullName
      this.temp.parentId = row.enumType === 2 ? row.pkid : undefined
      this.temp.deptId = row.pkid
      this.checkedProvinces = []
      this.products.forEach(e => {
        e.checkAll = false
        e.checkProductSpecs = []
      })
      this.areas.forEach(e => {
        e.checkAll = false
        e.checkProvinces = []
      })
      this.dialogStatus = 'create'
      this.dialogNodeFormVisible = true

      this.$nextTick(() => {
        this.$refs['stationForm'].clearValidate()
      })
    },
    btnClose() {
      this.dialogNodeFormVisible = false
    },
    btnAddEmployeeStaion(stationId) {
      this.dialogEmployeeStation = true
      this.employeeStation.stationId = stationId
      this.employeeStation.employeeId = undefined
      this.employeeStation.employeeDisplayName = ''
      this.employeeStation.startDate = ''
      this.employeeStation.hireDate = ''
      this.$nextTick(() => {
        this.$refs['employeeForm'].clearValidate()
      })
    },
    btnCloseEmployeeStation() {
      this.dialogEmployeeStation = false
    },
    btnCloseEmployee() {
      this.dialogEmployee = false
    },
    chooseEmployee() {
      this.dialogEmployee = true
      this.employeeQuery.displayName = ''
      this.employeeQuery.jobNo = ''
      this.employeeQuery.departmentId = null
      this.employeeQuery.pageIndex = 1
      this.getEmployeeList()
    },
    getEmployeeList() {
      MaintenanceService.QueryEmployeeInService(this.employeeQuery).then(result => {
        if (result.succeed) {
          this.employeeList = result.data.datas
          this.employeeTotal = result.data.recordCount
          this.employeeQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    selectedEmployee(row) {
      this.employeeStation.employeeId = row.id
      this.employeeStation.employeeDisplayName = row.displayName
      this.employeeStation.hireDate = row.hireDate
      this.dialogEmployee = false
    },
    createEmployeeStation() {
      this.$refs['employeeForm'].validate((valid) => {
        if (valid) {
          this.employeeStation.startDate = new Date(this.employeeStation.startDate)
          MaintenanceService.AddEmployeeStation(this.employeeStation)
            .then((result) => {
              if (result.succeed) {
                this.dialogEmployeeStation = false
                this.showMessage('保存成功', 'success')
                this.datatree = []
                this.station = {}
                this.getList(this.employeeStation.stationId + result.data.id, this.parentDeptId)
                this.getStation(this.employeeStation.stationId)
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
            })
        }
      })
    },
    btnDeleteEmployeeStation(stationId, id) {
      this.dialogEmployeeStationDelete = true
      this.stopEmployeeStation.endDate = ''
      this.stopEmployeeStation.stationId = stationId
      this.stopEmployeeStation.id = id
      this.$nextTick(() => {
        this.$refs['stopEmployeeForm'].clearValidate()
      })
    },
    btnCloseEmployeeStationDelete() {
      this.dialogEmployeeStationDelete = false
    },
    deleteEmployeeStation() {
      this.$refs['stopEmployeeForm'].validate((valid) => {
        if (valid) {
          this.$confirm('确定删除负责人吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.stopEmployeeStation.endDate = new Date(this.stopEmployeeStation.endDate)
            MaintenanceService.StopEmployeeStation(this.stopEmployeeStation).then(result => {
              if (result.succeed) {
                this.dialogEmployeeStationDelete = false
                this.datatree = []
                this.station = {}
                this.getList(this.stopEmployeeStation.stationId, this.parentDeptId)
                this.getStation(this.stopEmployeeStation.stationId)
                this.$notice.message('删除成功', 'success')
              }
            })
              .catch(error => {
                console.log(error)
              })
          }).catch(error => {
            if (!error.succeed) {
              this.$notice.message('取消删除', 'info')
            }
          })
        }
      })
    },
    btnAddStaionReceiver() {
      this.stationReceiver = {}
      this.stationReceiver.receiverName = ''
      this.stationReceiver.receiverId = undefined
      this.receiverQuery.pageIndex = 1
      this.initProvinceAndCity()
      this.getReceiverList()
      this.addSelectRows = []

      this.dialogReceiver = true
    },

    getReceiverList() {
      const ids = []
      this.stationReceiverList.forEach(e => {
        if (e.receiverId) {
          ids.push(e.receiverId)
        }
      })
      // this.receiverQuery.existReceierIds = ids
      if (this.receiverQuery.provinceAndCityId) {
        this.receiverQuery.provinceId = this.receiverQuery.provinceAndCityId[0]
        this.receiverQuery.cityId = this.receiverQuery.provinceAndCityId[1]
      }
      this.receiverQuery.isStopped = false
      ReceiverService.QueryReceiverTerminalList(this.receiverQuery).then(result => {
        if (result.succeed) {
          this.receiverList = result.data.datas
          this.receiverTotal = result.data.recordCount
          this.receiverQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    btnCloseReceiver() {
      this.receiverQuery = {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
      this.$refs.refAddTable.clearSelection()
      this.dialogReceiver = false
    },
    createData() {
      this.getCheckProductSpecs()
      this.getCheckProvinces()
      this.$refs['stationForm'].validate((valid) => {
        if (valid) {
          MaintenanceService.AddStation(this.temp)
            .then((result) => {
              if (result.succeed) {
                this.dialogNodeFormVisible = false
                this.getList(result.data.id, this.parentDeptId)
                this.getStation(result.data.id)
                this.showMessage('保存成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
              this.loading = false
            })
        }
      })
    },
    handleUpdate(row) {
      this.getPosition(row.deptCode)
      this.currentNode = row
      this.treeNodeId = row.id
      this.isUpdateStation = true
      this.isUpdatePosition = !row.enableAddStation || row.children.length !== 0
      this.existChildren = row.children.length > 0
      MaintenanceService.GetStation({ id: row.pkid })
        .then((result) => {
          if (result.succeed) {
            this.temp = Object.assign({}, result.data)
            this.temp.fullName = row.fullName.substring(0, row.fullName.lastIndexOf('/'))
            this.dialogStatus = 'update'
            this.temp.edit = false
            var resultProvinces = result.data.provinces
            this.getProvinces(row.pkid, row.parentId, false, row.enumType, resultProvinces, row.deptId)
            if (result.data.products.length > 0) {
              var products = []
              result.data.products.forEach(e => {
                e.specification.forEach(x => {
                  products.push(x.id)
                })
              })
              this.getProducts(row.pkid, row.parentId, false, row.enumType, products)
            } else {
              this.getProducts(row.pkid, row.parentId, false, row.enumType)
            }
            this.historyProductsCount = products ? products.length : 0
            this.historyProvincesCount = resultProvinces ? resultProvinces.length : 0
            this.dialogNodeFormVisible = true
            this.$nextTick(() => {
              this.$refs['stationForm'].clearValidate()
            })
          } else {
            this.ShowTip(result)
          }
        })
        .catch((error) => {
          console.log(error)
          this.loading = false
        })
    },
    updateData() {
      this.getCheckProductSpecs()
      this.getCheckProvinces()
      this.$refs['stationForm'].validate((valid) => {
        if (valid) {
          if (this.existChildren && (this.checkedProvinces.length < this.historyProvincesCount || this.temp.productSpecs.length < this.historyProductsCount)) {
            this.$confirm('当前操作会联动取消子级岗位的区域信息或产品信息，确定保存吗?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              MaintenanceService.UpdateStation(this.temp)
                .then((result) => {
                  if (result.succeed) {
                    this.dialogNodeFormVisible = false
                    this.getList(this.treeNodeId, this.parentDeptId)
                    if (this.station) {
                      this.getStation(this.station.id)
                    }
                    this.showMessage('更新成功', 'success')
                  } else {
                    this.ShowTip(result)
                  }
                })
                .catch((error) => {
                  console.log(error)
                  this.loading = false
                })
            }).catch(() => {

            })
          } else {
            MaintenanceService.UpdateStation(this.temp)
              .then((result) => {
                if (result.succeed) {
                  this.dialogNodeFormVisible = false
                  this.getList(this.treeNodeId, this.parentDeptId)
                  if (this.station) {
                    this.getStation(this.station.id)
                  }
                  this.showMessage('更新成功', 'success')
                } else {
                  this.ShowTip(result)
                }
              })
              .catch((error) => {
                console.log(error)
                this.loading = false
              })
          }
        }
      })
    },
    handleDelete(row) {
      this.$confirm('确定删除此岗位吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        MaintenanceService.DeleteStation({ id: row.pkid }).then(result => {
          if (result.succeed) {
            this.getList('', row.deptId)
            this.station = []
            this.$notice.message('删除成功', 'success')
          }
        })
          .catch(error => {
            console.log(error)
          })
      }).catch(error => {
        if (!error.succeed) {
          this.$notice.message('取消删除', 'info')
        }
      })
    },
    getCheckProductSpecs() {
      const res = this.products.map(item => {
        if (item.checkProductSpecs && item.checkProductSpecs.length > 0) {
          return item.checkProductSpecs
        }
      })
      var products = []
      res.flat().filter(item => {
        if (item) {
          return { id: item }
        }
      }).forEach(e => {
        const pro = {
          id: e
        }
        products.push(pro)
      })
      this.temp.productSpecs = products
    },
    getCheckProvinces() {
      const res = this.areas.map(item => {
        if (item.checkProvinces && item.checkProvinces.length > 0) {
          return item.checkProvinces
        }
      })
      var provinces = []
      res.flat().filter(item => {
        if (item) {
          return { id: item }
        }
      }).forEach(e => {
        const pro = {
          id: e
        }
        provinces.push(pro)
      })
      this.temp.provinces = provinces
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },
    onHidden() {
      this.dialogNodeFormVisible = false
    },
    onRefresh() {
      this.getList()
      this.dialogNodeFormVisible = false
    },
    closeProvinceCityCascader() {
      this.$refs.refProvinceCity.dropDownVisible = false
    },
    displayNameChange() {
      this.$forceUpdate()
    },
    handleSelectionChange(val) {
      this.editSelectRows = val
    },
    handleBatchAddReceiver() {
      const stationReceivers = []
      this.addSelectRows.forEach(row => {
        const stationReceiverTemp = {}
        stationReceiverTemp.receiverId = row.id
        stationReceiverTemp.receiverName = row.name
        stationReceiverTemp.stationId = this.station.id
        if (this.station.currentEmployee) {
          stationReceiverTemp.employeeId = this.station.currentEmployee.employeeId
        }
        stationReceiverTemp.receiverType = this.receiverType
        stationReceivers.push(stationReceiverTemp)
      })
      var postData = {
        receiverType: this.receiverType,
        stationReceiverModels: stationReceivers
      }
      if (this.station.currentEmployee) {
        postData.employeeId = this.station.currentEmployee.employeeId
      }
      MaintenanceService.BatchAddStationReceiver(postData)
        .then((result) => {
          if (result.succeed) {
            this.dialogReceiver = false
            this.showMessage('保存成功', 'success')
            this.stationReceiverQuery.stationId = this.station.id
            this.$refs.refAddTable.clearSelection()
            this.addSelectRows = []
            this.getStationReceiver()
            this.receiverQuery = {
              pageIndex: 1,
              pageSize: 10,
              order: '-CreateTime'
            }
          } else {
            this.ShowTip(result)
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    handleRowChange(selection) {
      // 在当前页的集合中找到未选中的项
      var notInCurrent = this.receiverList.filter(item => !selection.some(ele => ele.id === item.id))

      // 未选中
      if (notInCurrent && notInCurrent.length > 0) {
        notInCurrent.map(row => {
          const fitemIndex = this.addSelectRows.findIndex((item) => {
            return item.id === row.id
          })
          // 找到了就删除掉
          if (fitemIndex >= 0) {
            this.addSelectRows.splice(fitemIndex, 1)
          }
        })
      }
      // 已选中的不在选中集合中加入
      var notInSelectRows = selection.filter(item => !this.addSelectRows.some(ele => ele.id === item.id))

      if (notInSelectRows && notInSelectRows.length > 0) {
        notInSelectRows.map(sel => {
          this.addSelectRows.push(sel)
        })
      }
    },
    handleChooseDel(row) {
      var index = this.addSelectRows.findIndex(f => { return f.id === row.id })
      this.addSelectRows.splice(index, 1)
      const receiver = this.receiverList.find(e => e.id === row.id)
      if (receiver) {
        this.$refs.refAddTable.toggleRowSelection(receiver, false)
      }
    },
    // 移岗
    showStaionReceiverTransfer() {
      if (this.editSelectRows.length === 0) {
        this.$message.error('至少选择一个终端')
        return false
      }
      this.$refs.staionReceiverTransfer.init(this.datatree[0], this.station)
    },
    handleStaionReceiverTransfer(data) {
      data.stationReceiverModels = this.editSelectRows
      data.receiverType = this.receiverType
      MaintenanceService.BatchTransferStationReceiver(data)
        .then((result) => {
          if (result.succeed) {
            this.$message.success('移岗成功')
            this.stationReceiverQuery.pageIndex = 1
            this.getStationReceiver()
            this.$refs.staionReceiverTransfer.closeDialog()
            this.$refs.refTable.clearSelection()
            this.editSelectRows = []
          } else {
            this.ShowTip(result)
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    deleteStationReceiver(row) {
      this.$confirm('确定删除该终端吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var param = {
          id: row.id
        }
        MaintenanceService.DeleteStationReceiver(param).then(result => {
          if (result.succeed) {
            this.getStation(row.stationId)
            this.$notice.message('删除成功', 'success')
          }
        })
          .catch(error => {
            console.log(error)
          })
      }).catch(error => {
        console.log(error)
      })
    },
    batchDeleteStationReceiver() {
      if (this.editSelectRows.length === 0) {
        this.$message.error('至少选择一个终端')
        return false
      }
      this.$confirm('确定删除勾选的终端吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        MaintenanceService.BatchDeleteStationReceiver(this.editSelectRows).then(result => {
          if (result.succeed) {
            this.$notice.message('删除成功', 'success')
            this.stationReceiverQuery.pageIndex = 1
            this.getStationReceiver()
            this.editSelectRows = []
          }
        })
          .catch(error => {
            console.log(error)
          })
      }).catch(error => {
        console.log(error)
      })
    }
  }
}

</script>
<style>
.stationInfo {
  padding-top: 17px;
  padding-left: 10px;
  height: auto;
  min-height: 70px;
  background-color: white;
  margin-bottom: 20px;
  color: #606266;
  font-size: 12px;
}

.station-title {
  font-weight: bold;
  padding-bottom: 15px;
}

.station {
  padding-top: 17px;
  height: auto;
  min-height: 50px;
}

.staion-button {
  position: absolute;
  left: 70%;
  top: 64%;
  transform: translate(-50%, -50%)
}

.edit-button {
  position: absolute;
  left: 78%;
  top: 48%;
  transform: translate(-50%, -50%)
}

.custom-tree-node {
  width: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between;
  padding-right: 8px;
  font-size: 12px;
}

.custom-tree-node:nth-child(1) {
  width: 55% !important;
}

.custom-tree-node:nth-child(2) {
  width: 30% !important;
}

.tree-node-label {
  width: 88%;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.span-padding {
  padding-right: 10px;
}

.filter-item {
  margin: 0 10px 5px 0;
}

.product-content {
  padding-bottom: 10px;
}

.el-tree-node.is-current>.el-tree-node__content {
  background-color: #f5f6f8 !important;
}

.split-container {
  position: relative;
  height: 100vh;
  min-height: 550px;
  padding: 0px 0px 0px 10px;
}

.left-container {
  height: 100%;
  /*width:'590px';*/
  overflow: auto;
  background-color: white;
  /* margin-left:-26px; */
}

.right-container {
  background-color: #f5f6f8;
  height: 100%;
  overflow: auto;
  padding: 0px 20px;
}

.clearfix {
  font-weight: normal;
}
.el-dropdown-link {
    cursor: pointer;
    color: #516e92;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .slot-menu{
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
}
.slot-menu-left{
  line-height: 200%;
  flex: 9;
}
.slot-menu-right{
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 10px;
}
</style>
