<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="span">
          <el-cascader
            ref="refProductAndSpec"
            v-model="productAndSpecId"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            :props="{checkStrictly: true ,expandTrigger: 'hover' }"
            placeholder="产品/规格"
            clearable
            class="filter-item"
            @change="productAndSpecChange"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.complaintsFormStatus"
            :loading="enumComplaintsFormStatusLoading"
            class="filter-item"
            placeholder="审批状态"
            clearable
          >
            <el-option
              v-for="item in enumComplaintsFormStatusList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.receiverName"
            clearable
            placeholder="收货方名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.proposer"
            clearable
            placeholder="申诉人"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'Approval_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Approval_Button_Completed')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-folder-checked"
            :disabled="appeal.months.length===0"
            @click="handleAppealComplete"
          >
            申诉完成
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button v-if="$isPermitted($store.getters.user, 'Approval_Button_Download')" class="filter-item-button" type="primary" icon="el-icon-folder-checked" @click="handleDownloadZip">
            申诉附件打包下载
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Approval_Button_Export')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            :v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column fixed label="序号" type="index" align="center" :index="indexMethod" />
            <el-table-column label="申诉单号" align="center" sortable="custom" min-width="120px" prop="ApplyNo">
              <template slot-scope="{ row }">
                <span>{{ row.applyNo }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="发货方名称"
              align="left"
              header-align="center"
              sortable="custom"
              min-width="240px"
              prop="Distributor.Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.distributorName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="收货方名称"
              align="left"
              header-align="center"
              sortable="custom"
              min-width="240px"
              prop="Receiver.Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.receiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="调整后收货方"
              align="left"
              header-align="center"
              sortable="custom"
              min-width="150px"
              prop="ChangedReceiver.Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.changedReceiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="产品规格"
              align="center"
              header-align="center"
              sortable="custom"
              min-width="120px"
              prop="ProductSpec.Spec"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productName + ' / '+ row.productSpec }}</span>
              </template>
            </el-table-column>
            <el-table-column label="销售日期" align="center" sortable="custom" min-width="100px" prop="SaleDate">
              <template slot-scope="{ row }">
                <span>{{ row.saleDate | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="销售数量"
              align="right"
              header-align="center"
              sortable="custom"
              min-width="100px"
              prop="Quantity"
            >
              <template slot-scope="{ row }">
                <span>{{ row.quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="实际销售数量"
              align="right"
              header-align="center"
              sortable="custom"
              min-width="120px"
              prop="ActualQuantity"
            >
              <template slot-scope="{ row }">
                <span>{{ row.actualQuantity }}</span>
              </template>
            </el-table-column>
            <el-table-column label="申诉月份" align="center" sortable="custom" min-width="100px" prop="SaleDate">
              <template slot-scope="{ row }">
                <span>{{ row.saleDate | parseTime('{y}-{m}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="申诉人" align="center" sortable="custom" min-width="90px" prop="Proposer.DisplayName">
              <template slot-scope="{ row }">
                <span>{{ row.proposerName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="状态"
              align="center"
              sortable="custom"
              min-width="90px"
              prop="EnumComplaintsFormStatus"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumComplaintsFormStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="单据类型" align="center" sortable="custom" min-width="100px" prop="EnumTypeDesc">
              <template slot-scope="{ row }">
                <span>{{ row.enumTypeDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="100"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i
                  v-if="$isPermitted($store.getters.user, 'Approval_Button_Approval') && row.isCanApprove"
                  class="el-icon-document-checked eltablei"
                  title="审批"
                  @click="handleUpdate(row)"
                />
                <i v-if="$isPermitted($store.getters.user, 'Approval_Button_Download')" class="el-icon-download eltablei" title="下载申诉附件" @click="handleDownLoadForRequest(row)" />
                <i v-if="!row.isCanApprove" class="el-icon-document eltablei" title="查看" @click="handleView(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <approveSalesFlow
      v-if="dialogEditFormVisible"
      :id="itemId"
      :title="modifyDialogTitle"
      :view-model="modifyDialogIsReadonly"
      @hidden="onHidden()"
      @refresh="onRefresh()"
    />
    <DownloadZip ref="refDownloadZip" />
    <el-dialog
      :close-on-click-modal="false"
      title="选择申诉完成月份"
      :visible="dialogAppealFormVisible"
      width="40%"
      @close="btnAppealClose"
    >
      <el-form ref="appealForm" :rules="appealRules" :model="appeal" label-position="right" label-width="90px">
        <el-form-item>
          <el-checkbox-group v-model="checkedMonths">
            <el-checkbox v-for="month in appeal.months" :key="month.key" :label="month.key">{{ month.value }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnAppealClose()"> 关闭 </el-button>
        <el-button type="primary" icon="el-icon-check" @click="btnSave()">
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出流向申诉审批"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import MasterDataService from '@/api/masterData'
import ProductService from '@/api/product'
import SalesFlowComplaintsRequestService from '@/api/salesFlowComplaintsRequest'
import approveSalesFlow from './components/approveSalesFlow'
import DownloadZip from './components/downloadZip'
import HomeService from '@/api/home'

import CustomExport from '@/components/Export/CustomExport'

export default {
  components: {
    Pagination,
    approveSalesFlow,
    DownloadZip,
    CustomExport
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        complaintsFormStatus: 0,
        order: '-CreateTime'
      },
      listLoading: false,
      list: [],
      enumComplaintsFormStatusLoading: false,
      enumComplaintsFormStatusList: [],
      productAndSpecLoading: false,
      productAndSpecList: null,
      productAndSpecId: [],
      dialogEditFormVisible: false,
      dialogStatus: '',
      textMap: {
        approve: '申诉审批',
        view: '查看'
      },
      modifyDialogTitle: '',
      modifyDialogIsReadonly: false,
      itemId: null,
      dialogAppealFormVisible: false,
      appeal: {
        months: []
      },
      appealRules: {

      },
      checkedMonths: [],
      homePermission: {},
      btnExportLoading: false,
      columnDictionary: [],
      showExportModal: false
    }
  },
  created() {
    if (this.$route.query.fromPage && this.$route.query.status) {
      this.listQuery.complaintsFormStatus = this.$route.query.status
    }
    this.initComplaintsFormStatus()
    this.getHomePermission()
    this.initProductAndSpec()
    this.handleAppealMonth()
  },
  methods: {
    getHomePermission() {
      HomeService.GetCurrentEmployeeRole().then(res => {
        this.homePermission = res.data

        // 数据管理员 默认已审批的数据
        if (this.homePermission.isSysAdmin || this.homePermission.isDataAdmin) {
          this.listQuery.complaintsFormStatus = 20
        } else if (this.homePermission.isAssistant) {
          // 助理显示已提交的
          this.listQuery.complaintsFormStatus = 10
        } else {
          this.$notice.message('该用户非助理或数据管理员角色', 'error')
          this.listQuery.complaintsFormStatus = 10
        }
        this.handleFilter()
      }).catch(error => {
        console.log(error)
      })
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      const para = {
        manufacturerId: this.listQuery.manufacturerId
      }
      ProductService.QueryProductAndSpecCascader(para)
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    initComplaintsFormStatus(groupName) {
      this.enumComplaintsFormStatusLoading = true
      const param = {
        enumType: 'ComplaintsFormStatus'
      }
      if (groupName) {
        param.group = groupName
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.enumComplaintsFormStatusLoading = false
          this.enumComplaintsFormStatusList = result.data.datas
        })
        .catch((error) => {
          this.enumComplaintsFormStatusLoading = false
          console.log(error)
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      if (this.productAndSpecId) {
        const [productId, productSpecId] = this.productAndSpecId
        this.listQuery.productId = productId
        this.listQuery.productSpecId = productSpecId
      }
      this.listLoading = true
      SalesFlowComplaintsRequestService.QuerySalesFlowComplaintsRequest(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    handleUpdate(row) {
      this.itemId = row.id
      this.modifyDialogIsReadonly = false
      this.dialogEditFormVisible = true
      this.dialogStatus = 'approve'
      this.modifyDialogTitle = this.textMap[this.dialogStatus]
    },
    handleView(row) {
      this.itemId = row.id
      this.modifyDialogIsReadonly = true
      this.dialogEditFormVisible = true
      this.dialogStatus = 'view'
      this.modifyDialogTitle = this.textMap[this.dialogStatus]
    },
    onHidden() {
      this.dialogEditFormVisible = false
    },
    onRefresh() {
      this.getList()
      this.dialogEditFormVisible = false
    },
    handleDownloadZip() {
      this.$refs.refDownloadZip.initPage()
    },
    productAndSpecChange() {
      this.refs.refProductAndSpec.dropDownVisible = false
    },
    handleAppealComplete() {
      this.handleAppealMonth()
      this.checkedMonths = []
      this.dialogAppealFormVisible = true
      this.$nextTick(() => {
        this.$refs['appealForm'].clearValidate()
      })
    },
    handleAppealMonth() {
      SalesFlowComplaintsRequestService.QueryAppealMonthCascader().then(result => {
        this.listLoading = false

        this.appeal.months = result
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    btnAppealClose() {
      this.dialogAppealFormVisible = false
    },
    btnSave() {
      if (this.checkedMonths.length === 0) {
        this.$notice.message('请选择月份', 'error')
        return
      }
      SalesFlowComplaintsRequestService.CheckSalesFlowComplaintsRequest(this.checkedMonths).then(res => {
        if (res.succeed) {
          // 存在待审批的单据
          if (res.data) {
            this.$confirm('有未审批的单据，是否强制申诉完成?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              SalesFlowComplaintsRequestService.SaveAppealCompletedAndRejectForm(this.checkedMonths).then(result => {
                this.dialogAppealFormVisible = false
                if (result.succeed) {
                  this.$notice.message('保存成功', 'success')
                  this.close()
                }
                if (result.type !== -3) {
                  this.$notice.resultTip(result)
                }
              })
            })
          } else {
            // 不存在待审批的单据
            SalesFlowComplaintsRequestService.SaveAppealCompleted(this.checkedMonths).then(result => {
              this.dialogAppealFormVisible = false
              if (result.succeed) {
                this.$notice.message('保存成功', 'success')
                this.close()
              }
              if (result.type !== -3) {
                this.$notice.resultTip(result)
              }
            })
              .catch(error => {
                console.log(error)
                this.dialogAppealFormVisible = false
              })
          }
        }
      }).catch(error => {
        console.log(error)
      })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      SalesFlowComplaintsRequestService.GetSalesFlowComplaintsRequestExportColumn().then(result => {
        this.btnExportLoading = false
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      this.listQuery.checkedColumns = checkColumns

      SalesFlowComplaintsRequestService.ExportSalesFlowComplaintsRequest(this.listQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '流向申诉审批.xlsx'

          this.listQuery.checkedColumns = null
          fileDownload(result.data, filename)
        })
        .catch(() => {
          this.listQuery.checkedColumns = null
        })
    },
    handleDownLoadForRequest(row) {
      SalesFlowComplaintsRequestService.DownloadComplaintsZipForRequest(row)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = `${row.applyNo}申诉文件.zip`
          fileDownload(result.data, filename)
          this.dialogFormVisible = false
        })
        .catch(() => {
        })
    }
  }
}

</script>
