<template>
  <div>
    <el-dialog :title="title" width="70%" :close-on-click-modal="false" :visible="true" @close="cancle()">
      <el-form ref="dataForm" :rules="rules" :model="tempData.tempFormModel" label-position="right" label-width="100px">
        <el-row type="flex">
          <el-col :span="12">
            <el-form-item label="厂商" prop="manufacturerId">
              <el-select
                v-model="tempData.tempFormModel.manufacturerId"
                style="width: 100%"
                class="filter-item"
                placeholder="厂商"
                @change="manufacturerChange"
              >
                <el-option
                  v-for="item in manufacturerList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品/规格" prop="productAndSpecId">
              <el-cascader
                ref="refProductAndSpec"
                :key="productAndSpecKey"
                v-model="tempData.tempFormModel.productAndSpecId"
                style="width: 100%"
                :options="productAndSpecList"
                placeholder="产品/规格"
                clearable
                class="filter-item"
                :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
                @change="productChange"
              /></el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="通用名">
              <el-input
                v-model="tempData.tempFormModel.commonName"
                disabled
                placeholder="通用名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="考核价(元）" prop="price">
              <el-input
                v-model="tempData.tempFormModel.price"
                clearable
                placeholder="考核价(元）"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务费(元）" prop="price">
              <el-input
                v-model="tempData.tempFormModel.serviceFee"
                clearable
                placeholder="服务费(元）"
                maxlength="14"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="价格时间" prop="timeRange">
              <el-date-picker
                v-model="tempData.tempFormModel.timeRange"
                style="width: 100%"
                clearable
                type="daterange"
                range-separator="至"
                start-placeholder="价格起始时间"
                end-placeholder="价格截止时间"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                @blur="timeRangeChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          v-if="!viewModel"

          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>

import AccountingPriceService from '@/api/accountingPrice'

export default {
  name: '',
  components: {
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    viewModel: {
      type: Boolean,
      default: false
    },
    manufacturerList: {
      type: Array,
      default: null
    },
    allProductAndSpecList: {
      type: Array,
      default: null
    }
  },
  data() {
    const validateSpec = (rule, value, callback) => {
      if (!this.tempData.tempFormModel.productAndSpecId.length) {
        callback(new Error('请选择产品/规格'))
      } else {
        callback()
      }
    }
    return {
      rules: {
        manufacturerId: [
          { required: true, message: '请选择厂商', trigger: 'change' }
        ],
        productAndSpecId: [
          { type: 'array', required: true, validator: validateSpec, trigger: 'change' }
        ],
        price: [
          {
            required: true, trigger: 'blur', message: '请填写考核价'
          },
          { pattern: /((^[1-9]\d*))(\.\d{0,4}){0,1}$/, message: '考核价仅支持正数，最多4位小数' }
        ],
        serviceFee: [
          {
            required: true, trigger: 'blur', message: '请填写服务费'
          },
          { pattern: /((^[1-9]\d*))(\.\d{0,4}){0,1}$/, message: '服务费仅支持正数，最多4位小数' }
        ],
        timeRange: [
          { required: true, message: '请选择价格时间', trigger: 'blur' }
        ]
      },
      tempData: { tempFormModel: {}},
      productAndSpecKey: 0,

      productAndSpecList: []
    }
  },
  watch: {
    id(val) {
      this.tempData.tempFormModel.id = val
    }
  },
  mounted() {
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      if (!this.id) {
        this.clear()
        return
      }
      this.get(this.id)
    },
    manufacturerChange() {
      this.tempData.tempFormModel.productAndSpecId = []
      this.tempData.tempFormModel.commonName = ''
      ++this.productAndSpecKey
      this.productAndSpecList = this.allProductAndSpecList.filter(item => { return item.parentId === this.tempData.tempFormModel.manufacturerId })
    },
    productChange(value) {
      if (!value || value.length === 0) {
        this.tempData.tempFormModel.commonName = ''
        return
      } else {
        var product = this.allProductAndSpecList.find(i => { return i.value === value[0] })
        this.tempData.tempFormModel.commonName = product.remark
        this.$forceUpdate()
      }
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    timeRangeChange() {
      this.$forceUpdate()
    },
    get(id) {
      AccountingPriceService.GetAccountingPrice({ id: id }).then(result => {
        this.tempData.tempFormModel = result.data
        this.tempData.tempFormModel.timeRange = [new Date(result.data.startDate), new Date(result.data.endDate)]
        this.manufacturerChange()
        this.tempData.tempFormModel.productAndSpecId = [result.data.productId, result.data.productSpecId]
        this.productChange(this.tempData.tempFormModel.productAndSpecId)
      }).catch(error => {
        console.log(error)
      })
    },
    save() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.tempData.tempFormModel.productSpecId = this.tempData.tempFormModel.productAndSpecId[1]
          this.tempData.tempFormModel.startDate = this.tempData.tempFormModel.timeRange[0]
          this.tempData.tempFormModel.endDate = this.tempData.tempFormModel.timeRange[1]
          if (!this.tempData.tempFormModel.id) {
            this.addNew()
          } else {
            this.update()
          }
        }
      })
    },
    addNew() {
      AccountingPriceService.AddAccountingPrice(this.tempData.tempFormModel).then(result => {
        if (result.succeed) {
          this.tempData.tempFormModel = result.data
          this.$notice.message('新增成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          if (!error.processed) {
            this.$notice.message('新增失败。', 'error')
          }
        })
    },
    update() {
      AccountingPriceService.UpdateAccountingPrice(this.tempData.tempFormModel).then(result => {
        if (result.succeed) {
          this.tempData.tempFormModel = result.data
          this.$notice.message('修改成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          if (!error.processed) {
            this.$notice.message('修改失败。', 'error')
          }
        })
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }

      this.tempData.tempFormModel = {}
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    }
  }

}
</script>
