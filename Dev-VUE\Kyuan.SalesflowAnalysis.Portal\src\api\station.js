import HttpApi from './libs/api.request'

const controller = 'Station'

const api = new HttpApi(controller)

export default {
  QueryStation(params) {
    return api.get('QueryStation', params)
  },
  GetStationExportColumn() {
    return api.get('GetStationExportColumn')
  },
  ExportStation(params) {
    return api.post('ExportStation',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  }
}
