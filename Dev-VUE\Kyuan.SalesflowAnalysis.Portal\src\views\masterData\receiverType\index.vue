<template>
  <div>
    <div class="split-container">
      <split-pane split="vertical" :min-percent="20" :default-percent="30" @resize="resize">
        <template slot="paneL">
          <div class="left-container">
            <el-input
              v-model="filterText"
              placeholder="收货方类型名称"
            />
            <el-tree
              ref="tree"
              style="height:100vh;"
              :data="datatree"
              node-key="id"
              :current-node-key="currentKey"
              :props="defaultProps"
              :filter-node-method="filterNode"
              :render-content="renderContent"
              :expand-on-click-node="false"
              @node-click="handleNodeClick"
              @node-expand="handleNodeExpand"
            />
          </div>
        </template>
        <template slot="paneR">
          <div class="right-container">
            <el-col>
              <el-row :gutter="10" class="receiverTypeInfo">
                <el-col class="el-icon-s-data receiverType-title">
                  收货方类型信息
                </el-col>
                <el-col style="margin-bottom: 10px;">
                  <el-col v-if="receiverType.parentName" class="product-content" :span="12">收货方上级类型名称：{{ receiverType.parentName }}</el-col>
                  <el-col class="product-content" :span="12">收货方类型编码：{{ receiverType.code }}</el-col>
                  <el-col class="product-content" :span="12">收货方类型名称：{{ receiverType.name }}</el-col>
                  <el-col class="product-content" :span="12">收货方类型简称：{{ receiverType.shortName }}</el-col>
                  <el-col class="product-content" :span="24">描述：{{ receiverType.remark }}</el-col>
                </el-col>
              </el-row>
            </el-col>
          </div>
        </template>
      </split-pane>
    </div>
    <el-dialog
      :close-on-click-modal="false"
      :title="textMap[dialogStatus]"
      :visible="dialogNodeFormVisible"
      width="70%"
      @close="btnClose"
    >
      <el-form
        ref="receiverTypeForm"
        :rules="receiverTypeRules"
        :model="receiverTypeTemp"
        label-position="right"
        label-width="100px"
        class="el-dialogform"
      >
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col v-if="receiverTypeTemp.parentName" :span="24">
            <el-form-item label="上级类型">
              <span>{{ receiverTypeTemp.parentName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型编码" prop="code">
              <el-input
                v-model="receiverTypeTemp.code"
                clearable
                maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型名称" prop="name">
              <el-input
                v-model="receiverTypeTemp.name"
                clearable
                maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型简称" prop="shortName">
              <el-input
                v-model="receiverTypeTemp.shortName"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="描述">
              <el-input
                v-model="receiverTypeTemp.remark"
                type="textarea"
                :rows="3"
                clearable
                maxlength="500"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnClose()"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="dialogStatus === 'create' ? createData() : updateData()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import MaintenanceService from '@/api/maintenance'
import splitPane from 'vue-splitpane'

export default {
  name: 'Position',
  components: {
    splitPane
  },
  filters: {
    ellipsis(value) {
      if (!value) return ''
      if (value.length > 13) {
        return value.slice(0, 15) + '...'
      }
      return value
    }
  },
  data() {
    return {
      span: 4,
      filterText: '',
      datatree: [],
      currentKey: '',
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      receiverType: { code: '', parentName: '', name: '', shortName: '', remark: '' },
      dialogNodeFormVisible: false,
      dialogStatus: '',
      receiverTypeTemp: {
        id: undefined,
        parentId: undefined,
        parentName: '',
        code: '',
        name: '',
        shortName: '',
        remark: ''
      },
      textMap: {
        update: '编辑收货方类型',
        create: '新增收货方子类型'
      },
      receiverTypeRules: {
        name: [
          {
            required: true,
            type: 'string',
            message: '请输入类型名称',
            trigger: 'blur'
          }
        ],
        code: [
          {
            required: true,
            type: 'string',
            message: '请输入类型编码',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getTreeList()
  },
  methods: {
    resize() {
    },
    renderContent(h, { data, node, store }) {
      return (
        <span class='custom-tree-node'>
          <span class='tree-node-label' title={node.label}>{node.label}</span>
          <span>
            <el-button style='color:blue;' title='添加' style={ data.level !== 3 && this.$isPermitted(this.$store.getters.user, 'ReceiverType_Button_Add') ? '' : 'display:none'} icon='el-icon-plus' size='mini' type='text' on-click={ () => this.handleCreateNode(data) }></el-button>
            <el-button style='color:blue;' title='编辑' style={ this.$isPermitted(this.$store.getters.user, 'ReceiverType_Button_Edit') ? '' : 'display:none'} icon='el-icon-edit-outline' size='mini' type='text' on-click={ () => this.handleUpdate(data) }></el-button>
            <el-button style='color:blue;' title='删除' style={ data.children.length === 0 && this.$isPermitted(this.$store.getters.user, 'ReceiverType_Button_Delete') ? '' : 'display:none'} icon='el-icon-close' size='mini' type='text' on-click={ () => this.handleDelete(data) }></el-button>
          </span>
        </span>)
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    handleNodeClick(data, node) {
      this.receiverType.parentName = data.parentName
      this.receiverType.code = data.code
      this.receiverType.name = data.name
      this.receiverType.shortName = data.shortName
      this.receiverType.remark = data.remark
    },
    handleNodeExpand(data, node) {
      const nodes = this.$refs.tree.getNode(this.datatree[0].id)
      if (nodes.childNodes) {
        nodes.childNodes.forEach(e => {
          if (e === node) {
            this.changeTreeNodeStatus(e)
          } else {
            e.expanded = false
          }
        })
      }
    },
    // 展开顶级所有节点
    expandFirstNodes(tree) {
      const nodes = this.$refs.tree.getNode(tree[0].id)
      nodes.expanded = true
      if (nodes.childNodes && nodes.childNodes[0]) {
        this.changeTreeNodeStatus(nodes.childNodes[0])
      }
    },
    // 展开指定所有节点
    expandOneNodes(id) {
      const node = this.$refs.tree.getNode(id)
      this.receiverType.code = node.data.code
      this.receiverType.name = node.data.name
      this.receiverType.shortName = node.data.shortName
      this.receiverType.parentName = node.data.parentName
      this.receiverType.remark = node.data.remark

      const parentNode = this.$refs.tree.getNode(node.data.parentId)
      parentNode.expanded = true

      const grandParentNode = this.$refs.tree.getNode(parentNode.data.parentId)

      if (grandParentNode) grandParentNode.expanded = true

      if (parentNode) {
        this.changeTreeNodeStatus(parentNode)
      }
    },
    // 改变节点的状态
    changeTreeNodeStatus(node) {
      node.expanded = true
      for (let i = 0; i < node.childNodes.length; i++) {
        // 改变节点的自身expanded状态
        node.childNodes[i].expanded = this.expandAll
        // 遍历子节点
        if (node.childNodes[i].childNodes.length > 0) {
          this.changeTreeNodeStatus(node.childNodes[i])
        }
      }
    },
    getTreeList(nodeId) {
      this.listLoading = true
      MaintenanceService.QueryReceiverTypeTree().then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.datatree = result.data

          this.$nextTick(() => {
            if (nodeId) {
              this.expandOneNodes(nodeId)
            } else {
              this.receiverType.code = this.datatree[0].code
              this.receiverType.name = this.datatree[0].name
              this.receiverType.shortName = this.datatree[0].shortName
              this.receiverType.parentName = this.datatree[0].parentName
              this.receiverType.remark = this.datatree[0].remark
              this.expandFirstNodes(this.datatree)
            }
          })
        }
      }).catch(() => {
        this.listLoading = false
      })
    },
    resetTemp() {
      this.receiverTypeTemp = {
        id: undefined,
        parentId: undefined,
        code: '',
        parentName: '',
        name: '',
        shortName: '',
        remark: ''
      }
    },
    handleCreateNode(row) {
      this.dialogStatus = 'create'
      this.resetTemp()
      this.dialogNodeFormVisible = true
      this.$nextTick(() => {
        this.$refs['receiverTypeForm'].clearValidate()
      })
      this.receiverTypeTemp.parentName = row.name
      this.receiverTypeTemp.parentId = row.id
    },
    btnClose() {
      this.dialogNodeFormVisible = false
    },
    createData() {
      this.$refs['receiverTypeForm'].validate((valid) => {
        if (valid) {
          MaintenanceService.AddReceiverType(this.receiverTypeTemp)
            .then((result) => {
              if (result.succeed) {
                this.dialogNodeFormVisible = false
                this.showMessage('保存成功', 'success')
                this.getTreeList(result.data.id)
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
              this.loading = false
            })
        }
      })
    },
    handleUpdate(row) {
      this.treeNodeId = row.id

      MaintenanceService.GetReceiverType({ id: row.id })
        .then((result) => {
          if (result.succeed) {
            this.receiverTypeTemp = Object.assign({}, result.data)
            this.dialogStatus = 'update'
            this.dialogNodeFormVisible = true

            this.$nextTick(() => {
              this.$refs['receiverTypeForm'].clearValidate()
            })
          } else {
            this.ShowTip(result)
          }
        })
        .catch((error) => {
          console.log(error)
          this.loading = false
        })
    },
    updateData() {
      this.$refs['receiverTypeForm'].validate((valid) => {
        if (valid) {
          this.$confirm('确定修改此收货方类型吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            MaintenanceService.UpdateReceiverType(this.receiverTypeTemp)
              .then((result) => {
                if (result.succeed) {
                  this.dialogNodeFormVisible = false
                  this.showMessage('更新成功', 'success')
                  this.getTreeList(result.data.id)
                } else {
                  this.ShowTip(result)
                }
              })
              .catch(() => {
                this.loading = false
              })
          }).catch(() => {
            this.listLoading = false
          })
        }
      })
    },
    handleDelete(row) {
      this.$confirm('确定删除此收货方类型吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        MaintenanceService.DeleteReceiverType({ id: row.id }).then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getTreeList()

            this.$notice.message('删除成功', 'success')
          }
        })
          .catch(() => {
            this.listLoading = false
          })
      }).catch(error => {
        this.listLoading = false
        if (!error.succeed) {
          this.$notice.message('取消删除', 'info')
        }
      })
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    }
  }
}
</script>
<style>
.receiverTypeInfo{
  padding-top:17px;
  padding-left:10px;
  height: auto;
  min-height: 70px;
  background-color: white;
  margin-bottom: 20px;
  color: #606266;
  font-size: 12px;
}
.receiverType-title{
  font-weight:bold;
  padding-bottom: 15px;
}
.receiverType{
  padding-top:17px;
  height: auto;
  min-height: 50px;
}
.staion-button{
  position:absolute;
  left:70%;
  top:64%;
  transform:translate(-50%,-50%)
}
.edit-button{
  position:absolute;
  left:78%;
  top:48%;
  transform:translate(-50%,-50%)
}
.custom-tree-node{
  width:100%;
  flex: 1;
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between;
  padding-right: 8px;
  font-size: 12px;
}
.custom-tree-node:nth-child(1){
  width:55% !important;
}
.custom-tree-node:nth-child(2){
  width: 30% !important;
}
.tree-node-label{
  width:88%;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.span-padding{
  padding-right:10px;
}
.filter-item{
  margin: 0 10px 5px 0;
}
.product-content{
  padding-bottom: 10px;
}
.el-tree-node.is-current > .el-tree-node__content {
  background-color: #f5f6f8 !important;
}

  .split-container {
    position: relative;
    height:100vh;
    min-height: 550px;
    padding: 0px 0px 0px 10px;
  }

.left-container {
     height: 100%;
    /*width:'590px';*/
    overflow:auto;
    background-color: white;
    /* margin-left:-26px; */
  }

  .right-container {
    background-color: #f5f6f8;
    height: 100%;
    overflow:auto;
    padding: 0px 20px;
  }

  .clearfix
  {
    font-weight: normal;
  }
</style>

