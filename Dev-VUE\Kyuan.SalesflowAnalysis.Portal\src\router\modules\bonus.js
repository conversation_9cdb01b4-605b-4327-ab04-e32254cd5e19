/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    permissions: ['admin','editor']    control the page permissions (you can set multiple permissions)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

import Layout from '@/layout'

const bonusRoutes = [
  {
    path: '/bonus',
    component: Layout,
    redirect: '/bonus/index',
    hidden: false,
    meta: {
      title: '奖励管理',
      sort: 40,
      icon: 'money'
    },
    children: [
      {
        name: 'BaseLine',
        path: 'baseLine',
        component: () => import('@/views/bonus/baseLine/index'),
        meta: {
          title: '基线管理',
          icon: 'brokenline',
          sort: 2,
          noCache: false,
          permissions: ['Bonus_Quota']
        }
      },
      {
        name: 'Compute',
        path: 'compute',
        component: () => import('@/views/bonus/compute/index'),
        meta: {
          title: '奖励计算',
          icon: 'money-2',
          sort: 6,
          noCache: false,
          permissions: ['Bonus_Compute']
        }
      },
      {
        name: 'MyBonusQuery',
        path: 'myBonusQuery',
        component: () => import('@/views/bonus/bonusQuery/index'),
        meta: {
          title: '我的奖励',
          icon: 'money-1',
          sort: 9,
          noCache: false,
          permissions: ['MyBonus_Query']
        }
      },
      {
        name: 'MyBonusExamineQuery',
        path: 'MyBonusExamineQuery',
        component: () => import('@/views/bonus/bonusExamine/index'),
        meta: {
          title: '奖励审批',
          icon: 'excel',
          sort: 10,
          noCache: false,
          permissions: ['MyBonusExamine_Query']
        }
      },
      {
        name: 'BonusTemplate',
        path: 'BonusTemplate',
        component: () => import('@/views/bonus/bonusTemplate/index'),
        meta: {
          title: '奖励模板',
          icon: 'nested',
          sort: 10,
          noCache: false,
          permissions: ['Bonus_Template']
        }
      }
    ]
  }
]

export default bonusRoutes
