import HttpApi from './libs/api.request'

const controller = 'StationReceiver'

const api = new HttpApi(controller)

export default {
  QueryStationReceiver(params) {
    return api.get('QueryStationReceiver', params)
  },
  GetStationReceiverExportColumn() {
    return api.get('GetStationReceiverExportColumn')
  },
  ExportStationReceiver(params) {
    return api.post('ExportStationReceiver',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  GetStationReceiverByID(params) {
    return api.get('GetStationReceiverByID', params)
  },
  UpdateStationReceiver(params) {
    return api.post('UpdateStationReceiver', params)
  }
}
