<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      title="返利明细"
      :visible="dialogRebateResultDetailVisible"
      width="80%"
      @close="handleClose"
    >
      <el-row style="margin-bottom:20px;">
        <el-col :span="24">
          <el-table
            :data="rebateResultDetailList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            row-key="id"
            :tree-props="{children: 'rebateResultReceiverDetails'}"
            :expand-row-keys="expands"
          >
            <el-table-column label="产品/规格" align="center" header-align="center" min-width="120px">
              <template slot-scope="{ row }">
                <span v-if="row.productName">{{ row.productName }}({{ row.productSpecName }})</span>
              </template>
            </el-table-column>
            <el-table-column label="支付方" align="center" header-align="center" min-width="200px">
              <template slot-scope="{ row }">
                <span>{{ row.paidToReceiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="终端" align="center" header-align="center" min-width="200px">
              <template slot-scope="{ row }">
                <span>{{ row.receiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column v-if="rebateAgentType !=0" label="代付方" align="left" header-align="center" min-width="160px">
              <template slot-scope="{ row }">
                <span>{{ row.rebateAgentName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="返利金额" align="right" header-align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span>{{ row.rebateAmount | toMoney }}元</span>
              </template>
            </el-table-column>
            <el-table-column label="调整时间" align="center" header-align="center" min-width="120px">
              <template slot-scope="{ row }">
                <span v-if="row.operationTime ">{{ row.operationTime |parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="调整原因" align="center" header-align="center" min-width="140px">
              <template slot-scope="{ row }">
                <span>{{ row.modifyReason }}</span>
              </template>
            </el-table-column>
            <el-table-column label="调整人" align="center" header-align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span v-if="row.operatorName">{{ row.operatorName }}({{ row.operatorJobNO }})</span>
              </template>
            </el-table-column>
            <el-table-column label="备注" align="left" header-align="center" min-width="140px">
              <template slot-scope="{ row }">
                <span>{{ row.Remark }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="60px" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i v-if="row.isModify" class="el-icon-document eltablei" title="查看证明" @click="reviewCertificate(row.id)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleClose()"> 关闭 </el-button>
      </div>
    </el-dialog>
    <RebateResultChangeCertificate ref="refRebateResultChangeCertificate" />
  </div>
</template>
<script>
import RebateService from '@/api/rebate'
import RebateResultChangeCertificate from './rebateResultChangeCertificate'

export default {
  components: {
    RebateResultChangeCertificate
  },
  data() {
    return {
      span: 12,
      dialogRebateResultDetailVisible: false,
      rebateAgreementTemplateTypeCode: '',
      rebateAgentType: 0,
      rebateResultDetailList: [],
      resultId: null,
      listQuery: {
        pageIndex: 1,
        pageSize: 1000,
        order: '-CreateTime'
      },
      listLoading: false,
      expands: []
    }
  },
  methods: {
    initPage(resultId, rebateAgentType) {
      this.resultId = resultId
      this.getResultDetailList()
      this.rebateAgentType = rebateAgentType
    },
    getResultDetailList() {
      this.listLoading = true
      this.listQuery.rebateResultId = this.resultId
      RebateService.QueryRebateResultDetail(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.rebateResultDetailList = result.data.datas
          this.rebateAgreementTemplateTypeCode = this.rebateResultDetailList[0].rebateAgreementTemplateTypeCode
          this.dialogRebateResultDetailVisible = true
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleClose() {
      this.dialogRebateResultDetailVisible = false
      this.rebateResultDetailList = []
      this.expands = []
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    clickRowHandle(row, column, event) {
      if (this.expands.includes(row.id)) {
        this.expands = this.expands.filter((val) => val !== row.id)
      } else {
        this.expands.push(row.id)
      }
    },
    reviewCertificate(id) {
      this.$refs.refRebateResultChangeCertificate.init(id)
    }
  }
}
</script>
<style scoped>
</style>
