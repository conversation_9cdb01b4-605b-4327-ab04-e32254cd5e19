<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="span">
          <el-cascader
            ref="refManufacturerAndProductAndSpec"
            v-model="manufacturerAndProductAndSpecId"
            :loading="manufacturerLoading"
            :options="manufacturerAndProductAndSpecList"
            placeholder="厂商 / 产品 / 规格"
            style="width:100%"
            clearable
            class="filter-item"
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            @change="handleProductChange"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.departmentId"
            value-key="value"
            class="filter-item"
            placeholder="部门"
            clearable
            @change="departmentChange"
          >
            <el-option
              v-for="item in deptList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.areaIds"
            class="filter-item"
            placeholder="大区"
            multiple
            clearable
            @change="areaChange"
          >
            <el-option
              v-for="item in areaList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.provinceIds"
            class="filter-item"
            placeholder="省份"
            multiple
            clearable
          >
            <el-option
              v-for="item in provinceList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>

        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.receiverName"
            clearable
            placeholder="客户"
            class="filter-item"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.customerCategoryIds"
            value-key="value"
            class="filter-item"
            placeholder="客户分类"
            multiple
            clearable
          >
            <el-option
              v-for="item in customerCategoryList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.isHasPrincipal"
            class="filter-item"
            placeholder="是否存在负责人"
            clearable
          >
            <el-option
              v-for="item in isHasPrincipalList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.PrincipalName"
            clearable
            placeholder="负责人"
            class="filter-item"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.timeType"
            class="filter-item"
            placeholder="时间筛选"
          >
            <el-option
              v-for="item in timeTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col v-show="listQuery.timeType===2" :span="span">
          <el-date-picker
            v-model="listQuery.year"
            class="filter-item"
            type="year"
            placeholder="选择年"
            format="yyyy"
            value-format="yyyy"
          />
        </el-col>
        <el-col v-show="listQuery.timeType===1" :span="8">
          <el-date-picker
            v-model="listQuery.months"
            class="filter-item"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            format="yyyy-MM"
            value-format="yyyy-MM"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'SalesAchievementReport_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'SalesAchievementReport_Button_Generate')"
            :loading="btnExportLoading"
            class="filter-item-button"
            type="primary"
            icon="el-icon-download"
            @click="handleGenerateReport"
          >
            生成报表
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'SalesAchievementReport_Button_Export')"
            :loading="btnExportLoading"
            class="filter-item-button"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-row>
          <el-col :span="24">
            <label class="dataupdate">最新计算日期：{{ dataUpdateTime }}</label>
            <label class="dataupdate">总销售金额：{{ totalSaleAmount | toIntegerNum | toThousandFilter }}</label>
            <label class="dataupdate">总指标金额：{{ totalQuotaAmount | toIntegerNum | toThousandFilter }}</label>
            <label class="dataupdate">总达成率：{{ totalRate }}</label>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-table
              v-loading="listLoading"
              :data="list"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%;"
              :default-sort="{prop: 'createTime', order: 'descending'}"
              :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass"
              @sort-change="sortChange"
            >
              <el-table-column
                fixed
                label="序号"
                type="index"
                align="center"
                :index="indexMethod"
              />
              <el-table-column
                label="厂家"
                min-width="100px"
                header-align="center"
                align="center"
                prop="manufacturerName"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.manufacturerName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="产品"
                min-width="100px"
                header-align="center"
                align="center"
                prop="productNameCn"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.productNameCn }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="规格"
                min-width="100px"
                header-align="center"
                align="center"
                prop="spec"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.spec }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="部门"
                min-width="100px"
                header-align="center"
                align="center"
                prop="DepartmentName"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.departmentName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="大区"
                min-width="100px"
                header-align="center"
                align="center"
                prop="AreaName"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.areaName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="省份"
                min-width="100px"
                header-align="center"
                align="center"
                prop="provinceName"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.provinceName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="客户"
                min-width="200px"
                header-align="center"
                align="left"
                prop="ReceiverName"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.receiverName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="客户分类"
                min-width="100px"
                header-align="center"
                align="center"
                prop="CustomerCategory"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.customerCategory }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="月份"
                min-width="120px"
                header-align="center"
                align="center"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.currentDate }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="负责人"
                min-width="100px"
                header-align="center"
                align="center"
                prop="PrincipalName"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.principalName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="销售数量"
                min-width="120px"
                header-align="center"
                align="center"
                prop="saleQty"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.saleQtyFormat }}</span>
                </template>
              </el-table-column>
              <el-table-column label="指标数量" min-width="120px" prop="QuotaQty" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.quotaQtyFormat }}</span>
                </template>
              </el-table-column>
              <el-table-column label="达成率" min-width="100px" prop="AchievementRate" header-align="center" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.achievementRateFormat }}</span>
                </template>
              </el-table-column>
              <el-table-column label="关联药店销量" min-width="120px" prop="relatedSaleQuantity" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.relatedSaleQuantityFormat }}</span>
                </template>
              </el-table-column>
              <el-table-column label="同比销售数量" min-width="100px" prop="YoySaleQty" header-align="center" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.yoySaleQtyFormat }}</span>
                </template>
              </el-table-column>
              <el-table-column label="同比涨幅" min-width="100px" header-align="center" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.yoySaleRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="环比销售数量" min-width="100px" prop="MomSaleQty" header-align="center" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.momSaleQtyFormat }}</span>
                </template>
              </el-table-column>
              <el-table-column label="环比涨幅" min-width="100px" header-align="center" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.momSaleRate }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="销售金额"
                min-width="120px"
                header-align="center"
                align="center"
                prop="saleQty"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.saleAmountFormat }}</span>
                </template>
              </el-table-column>
              <el-table-column label="指标金额" min-width="120px" prop="QuotaAmount" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.quotaAmountFormat }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col class="el-colRight">
            <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
          </el-col>
        </el-row>
      </el-row></div>
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出报表"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
    <el-dialog
      title="手工生成报表记录"
      :close-on-click-modal="false"
      :visible="dialogGenerateRecordVisible"
      width="80%"
      class="popup-search"
      @close="closeGenerateRecordDialog"
    >
      <GenerateReportRecord ref="refImportSalesTracking" :show-import="dialogGenerateRecordVisible" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeGenerateRecordDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import CustomExport from '@/components/Export/CustomExport'
import ProductService from '@/api/product'
import ReportService from '@/api/report'
import MaintenanceService from '@/api/maintenance'
import LocationService from '@/api/location'
import GenerateReportRecord from './components/generateReportRecord'

export default {
  name: 'SalesAchievement',
  components: {
    Pagination,
    CustomExport,
    GenerateReportRecord
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        timeType: 1,
        areaId: null,
        provinceIds: [],
        months: [],
        year: null
      },
      listLoading: false,
      list: [],
      manufacturerLoading: false,
      manufacturerAndProductAndSpecId: [],
      manufacturerAndProductAndSpecList: [],
      productAndSpecList: null,
      productAndSpecId: [],
      productAndSpecKey: 0,
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: {},
      deptList: [],
      provinceList: [],
      allAreaList: [],
      areaList: [],
      customerCategoryList: [],
      dataUpdateTime: '',
      isHasPrincipalList: [
        {
          value: true,
          label: '是'
        },
        {
          value: false,
          label: '否'
        }
      ],
      timeTypeList: [
        {
          value: 1,
          label: '按月'
        },
        {
          value: 2,
          label: '按年'
        }
      ],
      totalSaleAmount: 0,
      totalQuotaAmount: 0,
      totalRate: '/',
      dialogGenerateRecordVisible: false
    }
  },
  created() {
    this.initManufacturerAndProductAndSpec()
    this.initDept()
    this.initCustomerCategory()
    this.queryProvince()
    this.initArea()
    this.getLastSalesFlowDate()
    this.getSalesFlowUpdateDate()
  },
  methods: {
    getSalesFlowUpdateDate() {
      ReportService.GetSalesFlowUpdateDate()
        .then((result) => {
          this.dataUpdateTime = result.data
        })
        .catch((error) => {
          console.log(error)
        })
    },
    getLastSalesFlowDate() {
      ReportService.GetLastSalesFlowDate()
        .then((result) => {
          this.listQuery.months.push(result.data[0])
          this.listQuery.months.push(result.data[0])
          this.handleFilter()
        })
        .catch((error) => {
          console.log(error)
        })
    },
    initManufacturerAndProductAndSpec() {
      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then((result) => {
          this.manufacturerAndProductAndSpecList = result
          this.manufacturerLoading = false
        })
        .catch(() => {
          this.manufacturerLoading = false
        })
    },
    initArea() {
      MaintenanceService.GetAllAreas()
        .then((result) => {
          this.allAreaList = result.data
        })
        .catch((error) => {
          console.log(error)
        })
    },
    initCustomerCategory() {
      MaintenanceService.QueryAllCustomerCategory()
        .then((result) => {
          this.customerCategoryList = result.data
        })
        .catch((error) => {
          console.log(error)
        })
    },
    manufacturerChange() {
      this.productAndSpecId = []
      ++this.productAndSpecKey
      this.initProductAndSpec()
    },
    timeChange() {
      this.$forceUpdate()
    },
    initDept() {
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptList = result
        })
        .catch(() => {

        })
    },
    queryProvince() {
      LocationService.QueryProvince()
        .then((result) => {
          this.provinceList = result
        })
        .catch((error) => {
          console.log(error)
          this.loading = false
        })
    },
    queryProvinceByAreaIds() {
      const para = { areaIds: this.listQuery.areaIds }
      MaintenanceService.QueryProvinceSelectByAreaIds(para)
        .then((result) => {
          this.provinceList = result
        })
        .catch((error) => {
          console.log(error)
          this.loading = false
        })
    },
    handleFilter() {
      // 如果按月筛选，清空年的筛选条件
      if (this.listQuery.timeType === 1) {
        this.listQuery.year = null
      }
      // 如果按年筛选，清空月的筛选条件
      if (this.listQuery.timeType === 2) {
        this.listQuery.months = []
      }
      if (this.listQuery.months.length > 0 || this.listQuery.year !== null) {
        this.listQuery.pageIndex = 1
        this.getList()
      } else {
        this.$notice.message('请选择时间', 'error')
      }
    },
    getList() {
      if (this.manufacturerAndProductAndSpecId) {
        const [manufacturerId, productId, productSpecId] = this.manufacturerAndProductAndSpecId
        this.listQuery.productId = productId
        this.listQuery.productSpecId = productSpecId
        this.listQuery.manufacturerId = manufacturerId
      }
      this.listLoading = true
      ReportService.QuerySalesAchievement(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
          if (result.data.datas.length > 0) {
            this.totalSaleAmount = result.data.datas[0].totalSaleAmount
            this.totalQuotaAmount = result.data.datas[0].totalQuotaAmount
            if (this.totalQuotaAmount === 0) {
              this.totalRate = '/'
            } else {
              this.totalRate = (this.totalSaleAmount / this.totalQuotaAmount * 100).toFixed(2) + '%'
            }
          } else {
            this.totalSaleAmount = 0
            this.totalQuotaAmount = 0
          }
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    productChange() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    departmentChange() {
      this.areaList = this.allAreaList.filter(item => { return item.departmentId === this.listQuery.departmentId })
    },
    areaChange() {
      this.listQuery.provinceIds = []
      if (this.listQuery.areaIds !== null && this.listQuery.areaIds.length > 0) {
        this.queryProvinceByAreaIds()
      } else {
        this.queryProvince()
      }
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      ReportService.GetSalesAchievementExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      var exportParam = JSON.parse(JSON.stringify(this.listQuery))
      exportParam.checkedColumns = checkColumns
      ReportService.ExportSalesAchievement(exportParam)
        .then(result => {
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '销量达成报表.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '销量达成报表.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    handleProductChange() {
      this.$refs.refManufacturerAndProductAndSpec.dropDownVisible = false
    },
    handleGenerateReport() {
      this.dialogGenerateRecordVisible = true
    },
    closeGenerateRecordDialog() {
      this.dialogGenerateRecordVisible = false
    }
  }
}
</script>
<style>
  .dataupdate{
    font-size: 14px;
    line-height: 25px;
    color: #606266;
    margin-right: 20px;
  }
</style>
