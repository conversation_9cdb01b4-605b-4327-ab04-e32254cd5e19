<template>
  <div class="list-container">
    <el-row type="flex" justify="end" :gutter="10" style="margin-bottom:10px">
      <el-col :span="3.5">
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-download"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-table
          :data="list"
          stripe
          border
          fit
          highlight-current-row
          style="width: 100%;"
          :default-sort="{prop: 'createTime', order: 'descending'}"
          :header-cell-class-name="'tableStyle'"
          :row-class-name="handleRowClass"
          @sort-change="sortChange"
        >
          <el-table-column align="center" fixed label="序号" :index="indexMethod" type="index" />
          <el-table-column align="left" sortable="custom" prop="Code" label="Code" min-width="110px" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.code }}</span>
            </template>
          </el-table-column>
          <el-table-column align="left" sortable="custom" prop="Name" label="收货方名称" min-width="180px" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="ProvinceName" label="省份" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.provinceName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="CityName" label="城市" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.cityName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="CountyName" label="区县" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.countyName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="DistributorTypeName" label="经销商类型" min-width="110px">
            <template slot-scope="{ row }">
              <span>{{ row.distributorTypeName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="ReceiverOneLevelTypeName" label="收货方一级类型" min-width="130px">
            <template slot-scope="{ row }">
              <span>{{ row.receiverOneLevelTypeName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="ReceiverTwoLevelTypeName" label="收货方二级类型" min-width="130px">
            <template slot-scope="{ row }">
              <span>{{ row.receiverTwoLevelTypeName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="ReceiverThreeLevelTypeName" label="收货方三级类型" min-width="130px">
            <template slot-scope="{ row }">
              <span>{{ row.receiverThreeLevelTypeName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="EffectiveDate" label="生效日期" min-width="110px">
            <template slot-scope="{ row }">
              <span>{{ row.effectiveDate }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable="custom" prop="ECommerceType" label="是否互联网医院" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.eCommerceType }}</span>
            </template>
          </el-table-column>
          <el-table-column label="地址" align="left" header-align="center" sortable="custom" min-width="280px" prop="Address">
            <template slot-scope="{ row }">
              <span>{{ row.address }}</span>
            </template>
          </el-table-column>
          <el-table-column label="机构等级" align="center" header-align="center" sortable="custom" min-width="120px" prop="GradeLevel">
            <template slot-scope="{ row }">
              <span>{{ row.gradeLevel }}</span>
            </template>
          </el-table-column>
          <el-table-column label="机构经济类型" align="center" header-align="center" sortable="custom" min-width="120px" prop="EconomicType">
            <template slot-scope="{ row }">
              <span>{{ row.economicType }}</span>
            </template>
          </el-table-column>
          <el-table-column label="统一信用代码" align="center" header-align="center" sortable="custom" min-width="120px" prop="TaxRegistrationNo">
            <template slot-scope="{ row }">
              <span>{{ row.taxRegistrationNo }}</span>
            </template>
          </el-table-column>
          <el-table-column label="工商注册号" align="center" header-align="center" sortable="custom" min-width="120px" prop="RegistrationNo">
            <template slot-scope="{ row }">
              <span>{{ row.registrationNo }}</span>
            </template>
          </el-table-column>
          <el-table-column align="left" sortable="custom" prop="ErrorMessage" label="错误信息" min-width="180px" header-align="center">
            <template slot-scope="{ row }">
              <span>{{ row.errorMessage }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col class="el-colRight">
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.pageIndex"
          :limit.sync="listQuery.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
  </div>
</template>
<script>
import ReceiverService from '@/api/receiver'
import Pagination from '@/components/Pagination'

export default {
  components: {
    Pagination
  },
  props: {
    importMasterTempId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        importMasterTempId: null,
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
    }
  },
  watch: {
    importMasterTempId: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        this.listQuery.importMasterTempId = val
        this.getList()
      }
    }
  },
  methods: {
    getList() {
      ReceiverService.QueryImportReceiverError(this.listQuery).then(res => {
        this.list = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(res => {})
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    // 确认导出
    handleExport() {
      ReceiverService.ExportImportReceiverError(this.listQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '收货方错误数据.xlsx'

          fileDownload(result.data, filename)
        })
        .catch((error) => {
          console.log(error)
        })
    }
  }
}
</script>
<style scoped>
  .el-dialog-s {
  z-index: 12;
}
</style>
