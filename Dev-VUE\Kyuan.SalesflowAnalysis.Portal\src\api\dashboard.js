import HttpApi from './libs/api.request'

const controller = 'Dashboard'

const api = new HttpApi(controller)

export default {
  QueryTargetReceiverLowSale(params) {
    return api.post('QueryTargetReceiverLowSale', params)
  },
  ExportTargetReceiverLowSale(params) {
    return api.post('ExportTargetReceiverLowSale',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  GetTargetReceiverLowSaleExtendColomns(params) {
    return api.post('GetTargetReceiverLowSaleExtendColomns', params)
  },
  GetDataBoardSalesGrowth(params) {
    return api.post('GetDataBoardSalesGrowth', params)
  },
  ExportDataBoardSalesGrowth(params) {
    return api.post('ExportDataBoardSalesGrowth',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  QueryIncomeAndInvestmentsByDepartment(params) {
    return api.post('QueryIncomeAndInvestmentsByDepartment', params)
  },
  QueryTopOfYearOnYearIncreaseOnReceiverSales(params) {
    return api.post('QueryTopOfYearOnYearIncreaseOnReceiverSales', params)
  },
  QueryProjectAchieved(params) {
    return api.post('QueryProjectAchieved', params)
  },
  ExportProjectAchieved(params) {
    return api.post('ExportProjectAchieved',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  PreloadSalesData(params) {
    return api.post('PreloadSalesData', params)
  },
  InitDashboardSalesAmount(params) {
    return api.post('InitDashboardSalesAmount', params)
  },
  AchievementRateWarningByMonth(params) {
    return api.post('AchievementRateWarningByMonth', params)
  },
  ExportAchievementRateWarningByMonth(params) {
    return api.post('ExportAchievementRateWarningByMonth',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  QueryTargetReceiverGlideWarning(params) {
    return api.post('QueryTargetReceiverGlideWarning', params)
  },
  ExportTargetReceiverGlideWarning(params) {
    return api.post('ExportTargetReceiverGlideWarning',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  GetTargetReceiverGlideExtendColomns(params) {
    return api.post('GetTargetReceiverGlideExtendColomns', params)
  },
  QueryTargetReceiverPerformance(params) {
    return api.post('QueryTargetReceiverPerformance', params)
  },
  ExportTargetReceiverPerformance(params) {
    return api.post('ExportTargetReceiverPerformance',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  QueryHeadquartersCoverage(params) {
    return api.post('QueryHeadquartersCoverage', params)
  },
  ExportHeadquartersCoverage(params) {
    return api.post('ExportHeadquartersCoverage',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  QueryHeadquartersUnCoverage(params) {
    return api.post('QueryHeadquartersUnCoverage', params)
  },
  QuerySalesGrowthDetail(params) {
    return api.post('QuerySalesGrowthDetail', params)
  },
  GetSysSettingTopCount() {
    return api.get('GetSysSettingTopCount')
  },
  QueryRankingOfYearOnYearIncreaseOnReceiverSales(params) {
    return api.post('QueryRankingOfYearOnYearIncreaseOnReceiverSales', params)
  },
  ExportRankingOfYearOnYearIncreaseOnReceiverSales(params) {
    return api.post('ExportRankingOfYearOnYearIncreaseOnReceiverSales',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  InitDashboardExpensesAmount(params) {
    return api.post('InitDashboardExpensesAmount', params)
  },
  GetDataBoardProjectExpend(params) {
    return api.post('GetDataBoardProjectExpend', params)
  },
  QueryProjectExpectedExpenditureModel(params) {
    return api.post('QueryProjectExpectedExpenditureModel', params)
  },
  GetProjectExpectedExpenditureExportColumn() {
    return api.get('GetProjectExpectedExpenditureExportColumn')
  },
  ExportProjectExpectedExpenditure(params) {
    return api.post('ExportProjectExpectedExpenditure',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  QueryInputOutputRatioByReceiver(params) {
    return api.post('QueryInputOutputRatioByReceiver', params)
  },
  ExportInputOutputRatioByReceiver(params) {
    return api.post('ExportInputOutputRatioByReceiver', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryProductionLowerWarning(params) {
    return api.post('QueryProductionLowerWarning', params)
  },
  ExportProductionLowerWarning(params) {
    return api.post('ExportProductionLowerWarning', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  ClearDashboardRedis(params) {
    return api.get('ClearDashboardRedis', params)
  }
}
