<template>
  <div>
    <el-dialog custom-class="el-dialog-s" title="新增终端潜力" width="70%" append-to-body :close-on-click-modal="false" :visible="showAddDialog" @close="closeAddQuotaDialog">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="receiverQuota"
        label-position="right"
        label-width="110px"
        class="el-dialogform-addQuota"
      >
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="月份" prop="quotaMonth">
              <el-date-picker
                v-model="receiverQuota.quotaMonth"
                style="width:100%"
                type="month"
                clearable
                placeholder="月份"
                value-format="yyyy-MM"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="部门" prop="departmentId">
              <el-select
                v-model="receiverQuota.departmentId"
                class="filter-item"
                placeholder="部门"
                clearable
                @change="selectDepartmentChange"
              >
                <el-option v-for="item in deptList" :key="item.key" :label="item.value" :value="item.key" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="终端名称" prop="receiverName">
              <el-row>
                <el-col :span="20" style="padding-right:5px">
                  <el-input
                    v-model="receiverQuota.receiverName"
                    readonly
                    placeholder="终端名称"
                    maxlength="300"
                  />
                </el-col>
                <el-col :span="4">
                  <el-button
                    class="filter-item"
                    type="primary"
                    icon="el-icon-search"
                    title="选择终端"
                    @click="handleSelectReceiver"
                  />
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="所在岗位">
              {{ stationName }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="省份">
              {{ provinceName }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="负责人">
              {{ responsiblePerson }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="end">
          <el-col :span="3.5" class="el-colRight">
            <el-button
              class="filter-item"
              type="primary"
              icon="el-icon-plus"
              @click="handleAddQuota"
            >
              添加潜力
            </el-button>
          </el-col>
        </el-row>
        <div style="margin-top:10px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="潜力信息">
                <el-table
                  :data="quotas"
                  stripe
                  border
                  fit
                  highlight-current-row
                  style="width: 100%;"
                  :default-sort="{prop: 'createTime', order: 'descending'}"
                  :header-cell-class-name="'tableStyle'"
                  :row-class-name="handleRowClass"
                >
                  <el-table-column
                    fixed
                    label="序号"
                    type="index"
                    align="center"
                  />
                  <el-table-column label="厂商" min-width="150px" align="center">
                    <template slot-scope="{ row }">
                      <el-select
                        v-model="row.manufacturerId"
                        style="width: 100%"
                        class="filter-item"
                        placeholder="厂商"
                        @change="manufacturerChange(row)"
                      >
                        <el-option
                          v-for="item in manufacturers"
                          :key="item.key"
                          :label="item.value"
                          :value="item.key"
                        />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="产品/规格" min-width="150px" align="center">
                    <template slot-scope="{ row }">
                      <el-cascader
                        ref="refProductAndSpec"
                        :key="productAndSpecKey"
                        v-model="row.productAndSpecId"
                        style="width:100%"
                        :options="row.productAndSpecList"
                        placeholder="产品/规格"
                        clearable
                        @change="handleProductChange(row)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="潜力数量" min-width="80px" align="center">
                    <template slot-scope="{ row }">
                      <el-input
                        v-model="row.quantity"
                        clearable
                        placeholder="潜力数量"
                        maxlength="14"
                      />
                    </template>
                  </el-table-column>

                  <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
                    <template slot-scope="row">
                      <i class="el-icon-delete eltablei" @click="handleDeleteQuota(row.$index)" />
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
              <el-col />
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeAddQuotaDialog">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handlerSave"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="选择收货方"
      :close-on-click-modal="false"
      :visible="dialogReceiverVisible"
      width="90%"
      class="popup-search"
      @close="closeReceiverDialog"
    >
      <SelectReceiver ref="refSelectReceiver" :show-dialog="dialogReceiverVisible" :department-id="receiverQuota.departmentId" :is-stopped="false" :distributor-types="distributorTypes" @success="selectReceiverSuccess" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeReceiverDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import BonusSevices from '@/api/bonus'
import ProductService from '@/api/product'
import MaintenanceService from '@/api/maintenance'
import SelectReceiver from '@/views/components/selectReceiver'
import ManufacturerService from '@/api/manufacturer'

export default {
  components: {
    SelectReceiver
  },
  data() {
    return {
      span: 12,
      dialogReceiverVisible: false,
      provinceName: '',
      manufacturers: [],
      distributorTypes: [],
      responsiblePerson: '',
      showAddDialog: false,
      receiverQuota: { receiverName: '', quotaMonth: '', departmentId: null },
      quotas: [],
      allProductAndSpecList: [],
      productAndSpecKey: 0,
      stationName: '',
      rules: {
        receiverName: [
          {
            required: true,
            type: 'string',
            message: '终端名称',
            trigger: 'change'
          }
        ],
        quotaMonth: [
          {
            required: true,
            type: 'string',
            message: '月份',
            trigger: 'change'
          }
        ],
        departmentId: [
          {
            required: true,
            type: 'string',
            message: '部门',
            trigger: 'change'
          }
        ]
      },
      deptList: []
    }
  },
  methods: {
    initPage() {
      this.showAddDialog = true
      this.initManufacturer()
      this.initDept()
      this.initProductAndSpec()
    },
    initDept() {
      MaintenanceService.QueryDepartmentForQuota()
        .then((result) => {
          this.deptList = result
        })
        .catch((error) => {
          console.log(error)
        })
    },
    initManufacturer() {
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturers = result
        })
        .catch(() => {
        })
    },
    initProductAndSpec() {
      ProductService.QueryProductAndSpecCascader()
        .then((result) => {
          this.allProductAndSpecList = result
        })
        .catch(() => {
        })
    },
    handlerSave() {
      // if (this.stationName === undefined || this.stationName === '') {
      //   this.showMessage('所选终端未在岗位上维护。', 'error')
      //   return false
      // }
      for (var i = 0; i < this.quotas.length; i++) {
        if (this.quotas[i].productSpecId === undefined || this.quotas[i].quantity === undefined) {
          this.showMessage('请将潜力信息填写完整。', 'error')
          return false
        }
        if (!(/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/.test(this.quotas[i].quantity))) {
          this.showMessage('潜力数量应为大于等于0的数字，支持两位小数。', 'error')
          return false
        }
        this.quotas[i].quantity = parseFloat(this.quotas[i].quantity)
      }
      if (this.quotas.length === 0) {
        this.showMessage('请添加潜力信息。', 'error')
        return false
      }

      var specIds = this.quotas.map(function(item) { return item.productSpecId })
      if (new Set(specIds).size !== specIds.length) {
        this.showMessage('不能添加重复的产品规格。', 'error')
        return false
      }

      // 保存
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.receiverQuota.quotas = this.quotas

          BonusSevices.AddReceiverQuota(this.receiverQuota)
            .then((result) => {
              if (result.succeed) {
                this.closeAddQuotaDialog()
                this.$emit('success')
                this.showMessage('创建成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
            })
        }
      })
    },
    selectDepartmentChange() {
      this.receiverQuota.receiverId = null
      this.receiverQuota.receiverName = ''
      this.provinceName = ''
      this.responsiblePerson = ''
      this.stationName = ''
    },
    closeAddQuotaDialog() {
      this.receiverQuota = { receiverName: '', quotaMonth: '', departmentId: null }
      this.provinceName = ''
      this.responsiblePerson = ''
      this.stationName = ''
      this.quotas = []
      this.$refs['dataForm'].resetFields()
      this.showAddDialog = false
    },
    handleSelectReceiver() {
      if (this.receiverQuota.departmentId === null || this.receiverQuota.departmentId === undefined) {
        this.showMessage('请先选择部门', 'error')
        return
      }
      this.distributorTypes = [10, 20, 30]
      this.dialogReceiverVisible = true
    },
    selectReceiverSuccess(val) {
      this.receiverQuota.receiverId = val.id
      this.receiverQuota.receiverName = val.name
      this.provinceName = val.provinceNameCn
      this.responsiblePerson = val.employeeDisplayName
      this.stationName = val.stationName
      this.$refs.refSelectReceiver.clear()
      this.dialogReceiverVisible = false
    },
    closeReceiverDialog() {
      this.$refs.refSelectReceiver.clear()
      this.dialogReceiverVisible = false
    },
    handleProductChange(row) {
      if (row.productAndSpecId !== null && row.productAndSpecId.length > 1) {
        row.productId = row.productAndSpecId[0]
        row.productSpecId = row.productAndSpecId[1]
      }
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    manufacturerChange(row) {
      row.productAndSpecId = []
      ++this.productAndSpecKey
      row.productId = null
      row.productSpecId = null
      row.productAndSpecList = this.allProductAndSpecList.filter(item => { return item.parentId === row.manufacturerId })
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleAddQuota() {
      var temp = {}
      this.quotas.push(temp)
    },
    handleDeleteQuota(index) {
      this.$confirm('确定删除此潜力吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.quotas.splice(index, 1)
      }).catch(error => {
        if (!error.succeed) {
          this.showMessage('取消删除', 'info')
        }
      })
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    }
  }
}
</script>
<style scoped>
  .el-dialog-s {
  z-index: 12;
  }
  .el-dialog-ls {
  z-index: 13;
  }

  .el-dialogform-addQuota {
  width: 90%;
  margin-left: 50px;
}
</style>
