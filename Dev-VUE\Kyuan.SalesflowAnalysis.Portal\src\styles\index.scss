@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

//全局通用样式
body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 5px;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}


.filter-container {
  //padding-bottom: 10px;
  flex-wrap: wrap;
  flex-direction: row;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
    width: 100%;
  }
  .filter-item-button {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 20px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

.tableStyle {
  background-color: rgb(240, 241, 243) !important;
  color: #606266;
  font-weight: 600;
}

.cellStyle {
  background-color: #FFFFFF !important;
}

.stripedStyle {
  background-color: #FAFAFA !important;
}

.el-colRight {
  text-align: right;
}

.el-colCenter {
  text-align: center;
}

.elpagination {
  text-align: right;
  margin-top: 5px
}

.eltablei {
  font-size: 16px;
  margin-right: 5px;
  cursor: pointer;
}

.el-dialogform {
  width: 80%;
  margin-left: 50px;
}

.el-timeline {
    margin: 0;
    font-size: 12px; 
    list-style: none;
}

.el-formtitle {
  line-height: 30px !important;
  padding-top: 5px;
  padding-left: 0px !important;
  font-weight: 700;
}

.el-tree-node__label
{
    font-size: 12px; 
}

.el-table--small td, .el-table--small th {
    padding: 4px 0
    }

.el-tabs--card > .el-tabs__header {
    border-bottom: 1px solid #dfe4ed;
    padding: 0;
    position: relative;
    margin: 0px;
    background-color: #f5f6f8;

    .el-tabs__item.is-active {
    color: #606266;
    background-color: #f5f6f8;    
    border-bottom: 1px solid #f5f6f8 !important; 
  }

    .el-tabs__item {
    border-bottom: 1px solid #d8dce5 !important; 
    border-left: 1px solid #dfe4ed !important;    
    color: #d0d0d0;
    -webkit-transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
}
.el-dropdown-menu--small
{
  .el-dropdown-menu__item
  {
    font-size: 12px;
  }
}

.navbar
{
  .right-menu 
  {
      .right-menu-item
      {
        font-size: 12px !important;
      }
  }
} 

.box-card .content {
  line-height: 30px !important;
  padding-left: 10px;
  font-size: 14px;
}

.search-container-bg {
  background-color: #f5f6f8;
  padding: 10px 10px 10px 10px;
  border-bottom: #DBDFE6 solid 1px;

  .mainButtonRow {
    margin-bottom: 20px;
  }
  .minusBottom{
    margin-bottom:-20px;
  }
}

.popup-search{
  .el-dialog__body {
    padding: 0px;
}
}

.list-container {
  padding: 10px 10px 0px 10px
}

#elCard .el-dialog__body {
  padding: 10px 10px !important;
}

//**************去掉Cascader的单选按钮****************//
.el-cascader-panel .el-radio {
  width: 100%;
  height: 100%;
  z-index: 10;
  position: absolute;
  top: 10px;
  right: 10px;
}

.el-cascader-panel .el-radio__input {
  visibility: hidden;
}

.el-cascader-panel .el-cascader-node__postfix {
  top: 10px;
}

//*******************************************************//



//Begin Form表单样式***************************************//
//因为样式引用顺序问题，此处代码不可以放在element-ui.scss中***//
label{
  font-weight: normal;
}

.el-form-item__label{
  font-size: 12px;
}

.el-form-item__content{
  font-size: 12px;
}

.el-checkbox__label{
  font-size: 12px;
}

.el-dialog__body{
  font-size: 12px;
}

.el-tree-node__content{
  height: 20px;
}

//输入框
.el-input--small {
    font-size: 12px;
}

//下拉框
.el-select-dropdown__item{
    font-size: 12px;
}
//cascader
.el-cascader-node__label{
    font-size: 12px;
}
//End Form表单样式***************************************//

//Begin 左侧菜单样式***************************************//
.el-submenu .el-menu-item {
    height: 45px;
    line-height: 45px;
    padding: 0 45px;
    min-width: 200px;
}
//End 左侧菜单样式***************************************//

.clearfix{
  font-size: 14px;
  font-weight: bold;
  color:#909399;
  height: 20px;
  line-height: 20px;
}