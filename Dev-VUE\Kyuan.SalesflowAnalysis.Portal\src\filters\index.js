// import parseTime, formatTime and set to filter
export { parseTime, formatTime } from '@/utils'

/**
 * Show plural label if time is plural number
 * @param {number} time
 * @param {string} label
 * @return {string}
 */
function pluralize(time, label) {
  if (time === 1) {
    return time + label
  }
  return time + label + 's'
}

/**
 * @param {number} time
 */
export function timeAgo(time) {
  const between = Date.now() / 1000 - Number(time)
  if (between < 3600) {
    return pluralize(~~(between / 60), ' minute')
  } else if (between < 86400) {
    return pluralize(~~(between / 3600), ' hour')
  } else {
    return pluralize(~~(between / 86400), ' day')
  }
}

/**
 * Number formatting
 * like 10000 => 10k
 * @param {number} num
 * @param {number} digits
 */
export function numberFormatter(num, digits) {
  const si = [
    { value: 1E18, symbol: 'E' },
    { value: 1E15, symbol: 'P' },
    { value: 1E12, symbol: 'T' },
    { value: 1E9, symbol: 'G' },
    { value: 1E6, symbol: 'M' },
    { value: 1E3, symbol: 'k' }
  ]
  for (let i = 0; i < si.length; i++) {
    if (num >= si[i].value) {
      return (num / si[i].value).toFixed(digits).replace(/\.0+$|(\.[0-9]*[1-9])0+$/, '$1') + si[i].symbol
    }
  }
  return num.toString()
}

/**
 * 10000 => "10,000"
 * @param {number} num
 */
export function toThousandFilter(num) {
  return (+num || 0).toString().replace(/^-?\d+/g, m => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
}

/**
 * Upper case first char
 * @param {String} string
 */
export function uppercaseFirst(string) {
  return string.charAt(0).toUpperCase() + string.slice(1)
}

/**
 * 10000 => "10,000.00"
 * @param {number} num
 */
export function toTwoNum(num) {
  return Number(num).toFixed(2)
}

export function toQuarter(num) {
  const txtQuater = num.toString()
  const year = txtQuater.slice(0, 4)
  const quarter = txtQuater.slice(5, 6)
  return `${year}年${quarter} 季度`
}
// 四舍五入保留整数
export function toIntegerNum(num) {
  return Number(num).toFixed(0)
}

// 保留2位小数，并显示千分位
export function toMoney(num) {
  if (num === undefined) {
    return num
  }
  // 将值转换为字符串并移除所有非数字字符
  num = parseFloat(padDecimal(num)).toFixed(2)

  // 将数字分割成千位，并使用逗号作为分隔符
  num = num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  return num
}

// toFixed四舍五入保留2位如果小数位是3位的话，有些情况第三位是5也会舍弃
function padDecimal(num, precision) {
  precision = precision || 4
  var parts = num.toString().split('.')
  if (parts.length <= 1) {
    return num
  }
  var decimalPart = parts[1]
  var decimalPartLength = decimalPart.length
  if (decimalPartLength < 2) {
    num = num + '0'.repeat(2 - decimalPartLength)
    decimalPartLength = 2
  }

  if (decimalPartLength < precision) {
    return num + '1'.repeat(precision - decimalPartLength)
  }
  return num
}
