<template>
  <el-dialog
    :close-on-click-modal="false"
    title="政策条款模板明细"
    :visible="dialogTemplateViewVisible"
    width="60%"
    @close="cancle"
  >
    <el-form :model="agreementPolicyTypeModel" label-position="right" label-width="100px" class="myself-dialogform">
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="模板名称:" prop="name">
            <label>{{ agreementPolicyTypeModel.name }}</label>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="编码:" prop="code">
            <label>{{ agreementPolicyTypeModel.code }}</label>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="政策条款类型:" prop="code">
            <label>{{ agreementPolicyTypeModel.enumCalculationTemplateTypeDesc }}</label>
          </el-form-item>
        </el-col>
        <el-col :span="12" />
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="适用模板范围:" prop="scope">
            <el-tag
              v-for="tag in agreementPolicyTypeModel.rebateAgreementTemplateTypes"
              :key="tag.id"
              :disable-transitions="false"
            >
              {{ tag.name }}
            </el-tag>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="备注:" prop="name">
            <label>{{ agreementPolicyTypeModel.remark }}</label>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="政策条款:" prop="name">
            <label>{{ agreementPolicyTypeModel.content }}</label>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="政策条款公式:" prop="name">
            <el-table
              :data="agreementPolicyTypeModel.rebateAgreementTermModelList"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%;"
              :header-cell-class-name="'tableStyle'"
            >
              <el-table-column
                fixed
                label="序号"
                type="index"
                align="center"
              />
              <el-table-column label="条件" align="center" min-width="100px">
                <template slot-scope="{ row }">
                  <span>{{ row.conditionalDescription }}</span>
                </template>
              </el-table-column>
              <el-table-column label="结果" align="center" min-width="110px">
                <template slot-scope="{ row }">
                  <span>{{ row.calculatedDescription }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button icon="el-icon-close" @click="cancle()">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import AgreementService from '@/api/agreement'

export default {
  components: {
  },
  data() {
    return {
      dialogTemplateViewVisible: false,
      agreementPolicyTypeModel: {
        rebateAgreementTermModelList: []
      }
    }
  },
  methods: {
    init(data) {
      this.dialogTemplateViewVisible = true
      const para = { id: data }
      this.GetAgreementPolicyTemplate(para)
    },
    GetAgreementPolicyTemplate(id) {
      AgreementService.GetAgreementPolicyTemplate(id).then((result) => {
        this.agreementPolicyTypeModel = result.data
        this.$forceUpdate()
      })
        .catch((error) => {
          console.log(error)
        })
    },
    cancle() {
      this.dialogTemplateViewVisible = false
      this.agreementPolicyTypeModel = {}
    }
  }
}
</script>
