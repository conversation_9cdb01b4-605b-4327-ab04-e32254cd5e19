import HttpApi from './libs/api.request'

const controller = 'TerminalChangeReport'

const api = new HttpApi(controller)

export default {
  QueryTerminalChangeReport(params) {
    return api.get('QueryTerminalChangeReport', params)
  },
  QueryNewTerminal(params) {
    return api.get('QueryNewTerminal', params)
  },
  QueryLoseTerminal(params) {
    return api.get('QueryLoseTerminal', params)
  },
  GetTerminalChangeReportExportColumn() {
    return api.get('GetTerminalChangeReportExportColumn')
  },
  ExportTerminalChangeReport(params) {
    return api.post('ExportTerminalChangeReport',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  ExportNewTerminalReport(params) {
    return api.post('ExportNewTerminalReport',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  ExportLoseTerminalReport(params) {
    return api.post('ExportLoseTerminalReport',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  }
}
