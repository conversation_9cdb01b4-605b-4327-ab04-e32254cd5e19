import HttpApi from './libs/api.request'

const controller = 'ProductAlias'

const api = new HttpApi(controller)

export default {
  QueryProductAlias(params) {
    return api.get('QueryProductAlias', params)
  },
  GetProductAlias(params) {
    return api.get('GetProductAlias', params)
  },
  AddProductAlias(params) {
    return api.post('AddProductAlias', params)
  },
  UpdateProductAlias(params) {
    return api.post('UpdateProductAlias', params)
  },
  DeleteProductAlias(params) {
    return api.post('DeleteProductAlias', params)
  },
  GetProductAliasExportColumn() {
    return api.get('GetProductAliasExportColumn')
  },
  ExportProductAlias(params) {
    return api.post('ExportProductAlias',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  }
}
