<!--新增返利结果-->
<template>
  <div>
    <el-dialog custom-class="el-dialog-s" title="新增返利结果" width="60%" append-to-body :close-on-click-modal="false" :visible="addRebateResultVisible" @close="handleCancle()">
      <el-form ref="dataForm" style="width:90%" :rules="rules" :model="rebateResultModel" label-position="right" label-width="100px" class="el-dialogform">
        <el-row type="flex" class="filter-container">
          <el-col :span="24">
            <el-form-item label="返利协议" prop="rebateAgreementName">
              <el-row>
                <el-col :span="20">
                  <el-input
                    v-model="rebateResultModel.rebateAgreementName"
                    readonly
                    placeholder="返利协议"
                  />
                </el-col>
                <el-col :span="4" style="text-align: right;">
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    @click="handleSelectRebateAgreement()"
                  >
                    选择
                  </el-button>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col v-if="rebateAgreement.code !== null" :span="span">
            <el-form-item label="协议编号">
              {{ rebateAgreement.code }}
            </el-form-item>
          </el-col>
          <el-col v-if="rebateAgreement.paymentCycleName !== null" :span="span">
            <el-form-item label="协议计算周期">
              {{ rebateAgreement.paymentCycleName }}
            </el-form-item>
          </el-col>
          <el-col v-if="rebateAgreement.startDate !== null" :span="span">
            <el-form-item label="协议起始日期">
              {{ rebateAgreement.startDate| parseTime("{y}-{m}-{d}") }}
            </el-form-item>
          </el-col>
          <el-col v-if="rebateAgreement.endDate !== null" :span="span">
            <el-form-item label="协议截止日期">
              {{ rebateAgreement.endDate| parseTime("{y}-{m}-{d}") }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="返利金额" prop="rebateAmount">
              <el-input
                v-model="rebateResultModel.rebateAmount"
                clearable
                disabled
                placeholder="返利金额"
              >
                <i slot="suffix">元</i>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="计算周期类型" prop="enumRebateComputeCycleFlag">
              <el-select
                v-model="rebateResultModel.enumRebateComputeCycleFlag"
                class="filter-item"
                :disabled="allowCheckPaymentCycle"
                placeholder="计算周期类型"
                clearable
                @change="onRebateComputeCycleFlagChange"
              >
                <el-option
                  v-for="item in enumStatusList"
                  :key="item.value"
                  :label="item.desc"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="起始日期" prop="startDate">
              <el-date-picker
                v-model="rebateResultModel.startDate"
                type="date"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                placeholder="起始日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="截止日期" prop="endDate">
              <el-date-picker
                v-model="rebateResultModel.endDate"
                type="date"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                placeholder="截止日期"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="end">
          <el-col :span="3.5" class="el-colRight">
            <el-button
              class="filter-item"
              type="primary"
              icon="el-icon-plus"
              @click="handleAddRebateResultDetail"
            >
              添加返利明细
            </el-button>
          </el-col>
        </el-row>
        <div style="margin-top:10px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="返利明细">
                <el-table
                  :data="rebateResultDetailList"
                  stripe
                  border
                  fit
                  highlight-current-row
                  style="width: 100%;"
                  :default-sort="{prop: 'createTime', order: 'descending'}"
                  :header-cell-class-name="'tableStyle'"
                  :row-class-name="handleRowClass"
                >
                  <el-table-column
                    fixed
                    label="序号"
                    type="index"
                    align="center"
                  />
                  <el-table-column label="产品/规格" min-width="120px" align="center">
                    <template slot-scope="{ row }">
                      <el-cascader
                        ref="refProductAndSpec"
                        :key="productAndSpecKey"
                        v-model="row.productAndSpecId"
                        style="width:100%"
                        :options="productAndSpecList"
                        placeholder="产品/规格"
                        @change="handleProductChange(row)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="支付方" min-width="150px" align="center">
                    <template slot-scope="{ row }">
                      <el-select
                        v-model="row.paidToReceiverId"
                        style="width:100%"
                        placeholder="返利支付方"
                      >
                        <el-option
                          v-for="item in payReceiverList"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                        />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="返利接收方" min-width="150px" align="center">
                    <template slot-scope="{ row }">
                      <el-select
                        v-model="row.receiverId"
                        style="width:100%"
                        placeholder="返利接收方"
                      >
                        <el-option
                          v-for="item in receiverList"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                        />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="返利金额(元)" min-width="100px" align="center">
                    <template slot-scope="{ row }">
                      <el-input
                        v-model="row.rebateAmount"
                        clearable
                        placeholder="返利金额"
                        @change="handleRebateAmountChange"
                      />
                    </template>
                  </el-table-column>

                  <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
                    <template slot-scope="row">
                      <i class="el-icon-delete eltablei" @click="handleDeleteRebateResultDetail(row.$index)" />
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
              <el-col />
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleCancle()">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleSave()"
        >
          保存
        </el-button>
      </div></el-dialog>
    <SelectRebateAgreement ref="refRebateAgreement" @success="selectRebateAgreementSuccess" />
  </div>
</template>
<script>
import SelectRebateAgreement from './selectRebateAgreement'
import MasterDataService from '@/api/masterData'
import RebateService from '@/api/rebate'
import moment from 'moment'

export default {
  name: '',
  components: {
    SelectRebateAgreement
  },
  props: {
  },
  data() {
    return {
      addRebateResultVisible: false,
      span: 12,
      rules: {
        rebateAgreementName: [
          { required: true, message: '请选择协议名称', trigger: 'change' }
        ],
        productSpecId: [
          { required: true, message: '请选择产品规格', trigger: 'change' }
        ],
        startDate: [
          { required: true, message: '请选择起始日期', trigger: 'change' }
        ],
        endDate: [
          { required: true, message: '请选择截止日期', trigger: 'change' }
        ],
        enumRebateComputeCycleFlag: [
          { required: true, message: '请选择计算周期类型', trigger: 'change' }
        ]
      },
      rebateResultModel: {
        rebateAgreementName: '',
        enumRebateComputeCycleFlag: null,
        rebateAmount: null,
        startDate: null,
        endDate: null
      },
      enumStatusList: [],
      rebateAgreement: {
        startDate: null,
        endDate: null,
        paymentCycleName: null,
        code: null
      },
      rebatePaymentCycle: {
        quarterPaymentCode: 'QuarterPayment',
        yearPaymentCode: 'YearPayment',
        quarterYearPaymentCode: 'QuarterYearPayment'
      },
      rebateComputeCycleFlag: {
        year: 1,
        quarter: 2
      },
      allowCheckPaymentCycle: false,
      rebateResultDetailList: [],
      productAndSpecList: [],
      productAndSpecKey: 0,
      receiverList: [],
      payReceiverList: []
    }
  },
  created() {
  },
  methods: {
    initPage() {
      this.addRebateResultVisible = true
      this.initRebateComputeCycleFlag()
    },
    initProductAndSpec() {
      var param = {
        rebateAgreementId: this.rebateAgreement.id
      }
      RebateService.QueryRebateAgreementProductSpec(param)
        .then(res => {
          this.productAndSpecList = res
        })
        .catch(() => {
        })
    },
    initRebateResultReciver() {
      RebateService.QueryRebateResultReceiver({ id: this.rebateAgreement.id }).then(res => {
        this.receiverList = res.data
      }).catch(() => {

      })
    },
    initRebateResultPayReceiver() {
      RebateService.QueryRebateResultPayReceiver({ id: this.rebateAgreement.id }).then(res => {
        this.payReceiverList = res.data
      }).catch(() => {

      })
    },
    handleSelectRebateAgreement() {
      this.$refs.refRebateAgreement.initPage()
    },
    selectRebateAgreementSuccess(val) {
      this.clean()
      this.rebateAgreement = val
      this.rebateResultModel.rebateAgreementId = val.id
      this.rebateResultModel.rebateAgreementName = val.name
      if (this.rebateAgreement.paymentCycleCode === this.rebatePaymentCycle.quarterYearPaymentCode) {
        this.allowCheckPaymentCycle = false
      } else {
        this.allowCheckPaymentCycle = true
        if (this.rebateAgreement.paymentCycleCode === this.rebatePaymentCycle.yearPaymentCode) {
          this.rebateResultModel.enumRebateComputeCycleFlag = this.rebateComputeCycleFlag.year
          this.rebateResultModel.startDate = val.startDate
          this.rebateResultModel.endDate = val.endDate
        } else {
          this.rebateResultModel.enumRebateComputeCycleFlag = this.rebateComputeCycleFlag.quarter
        }
      }
      this.initProductAndSpec()
      this.initRebateResultReciver()
      this.initRebateResultPayReceiver()

      this.$refs.refRebateAgreement.close()
    },
    handleSave() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.rebateResultDetailList === null || this.rebateResultDetailList === undefined || this.rebateResultDetailList.length <= 0) {
            this.showMessage('返利结果明细不可为空。', 'error')
            return false
          }
          for (var i = 0; i < this.rebateResultDetailList.length; i++) {
            if (this.rebateResultDetailList[i].productSpecId === undefined || this.rebateResultDetailList[i].receiverId === undefined || this.rebateResultDetailList[i].rebateAmount === undefined) {
              this.showMessage('请将返利结果明细信息填写完整。', 'error')
              return false
            }
            if (!(/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/.test(this.rebateResultDetailList[i].rebateAmount))) {
              this.showMessage('返利明细金额应为大于等于0的数字，支持两位小数。', 'error')
              return false
            }
          }

          const rebateResultStartDate = moment(this.rebateResultModel.startDate).format('YYYY-MM-DD')
          const rebateResultEndDate = moment(this.rebateResultModel.endDate).format('YYYY-MM-DD')
          const rebateAgreementStartDate = moment(this.rebateAgreement.startDate).format('YYYY-MM-DD')
          const rebateAgreementEndDate = moment(this.rebateAgreement.endDate).format('YYYY-MM-DD')
          if (rebateResultStartDate < rebateAgreementStartDate || rebateResultStartDate >= rebateAgreementEndDate) {
            this.showMessage('返利开始日期不能超出协议日期范围', 'error')
            return false
          }

          if (rebateResultEndDate <= rebateAgreementStartDate || rebateResultEndDate > rebateAgreementEndDate) {
            this.showMessage('返利截止日期不能超出协议日期范围', 'error')
            return false
          }

          if (this.rebateResultModel.startDate > this.rebateResultModel.endDate) {
            this.showMessage('返利截止日期不能早于开始日期', 'error')
            return false
          }

          const hasRepeat = this.rebateResultDetailList.filter((item, index, array) => {
            return array.some((innerItem, innerIndex) => { return innerIndex !== index && innerItem.productSpecId === item.productSpecId && innerItem.receiverId === item.receiverId && innerItem.paidToReceiverId === item.paidToReceiverId })
          })
          if (hasRepeat.length > 0) {
            this.$notice.message('返利明细存在重复数据', 'error')
            return
          }

          this.rebateResultModel.rebateResultDetails = this.rebateResultDetailList
          RebateService.AddRebateResult(this.rebateResultModel).then(result => {
            if (result.succeed) {
              this.rebateResultModel = result.data
              this.$notice.message('新增成功', 'success')
              this.close()
            } else {
              if (result.type !== -3) {
                this.$notice.resultTip(result)
              }
            }
          })
            .catch(error => {
              if (!error.processed) {
                this.$notice.message('新增失败。', 'error')
              }
            })
        }
      })
    },
    clean() {
      this.rebateResultModel = {
        rebateAgreementName: '',
        enumRebateComputeCycleFlag: null,
        rebateAmount: null,
        startDate: null,
        endDate: null
      }
      this.rebateAgreement = {
        startDate: null,
        endDate: null,
        paymentCycleName: null,
        code: null
      }
      this.rebateResultDetailList = []
      this.productAndSpecList = []
      this.receiverList = []
      this.$refs.dataForm.resetFields()
    },
    close() {
      this.addRebateResultVisible = false
      this.clean()
      this.rebateResultDetailList = []
      this.$refs['dataForm'].resetFields()
      this.$emit('refresh')
    },
    handleCancle() {
      this.close()
    },
    initRebateComputeCycleFlag() {
      var param = {
        enumType: 'RebateComputeCycleFlag'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.enumStatusList = result.data.datas
          this.$forceupdate()
        })
        .catch(() => {
        })
    },
    handleProductChange(row) {
      if (row.productAndSpecId !== null && row.productAndSpecId.length > 1) {
        row.productId = row.productAndSpecId[0]
        row.productSpecId = row.productAndSpecId[1]
      }
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    handleAddRebateResultDetail() {
      var rebateResultDetail = {
        receiverId: null
      }
      if (this.receiverList.length === 1) {
        rebateResultDetail.receiverId = this.receiverList[0].id
      }

      if (this.payReceiverList.length === 1) {
        rebateResultDetail.paidToReceiverId = this.payReceiverList[0].id
      }
      this.rebateResultDetailList.push(rebateResultDetail)
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleDeleteRebateResultDetail(index) {
      this.$confirm('确定删除此返利明细吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.rebateResultDetailList.splice(index, 1)
        this.rebateResultModel.rebateAmount = this.rebateResultDetailList.reduce((sum, item) => sum + parseFloat(item.rebateAmount), 0)
      }).catch(error => {
        if (!error.succeed) {
          this.showMessage('取消删除', 'info')
        }
      })
    },
    handleRebateAmountChange(val) {
      if (/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/.test(val)) {
        this.rebateResultModel.rebateAmount = this.rebateResultDetailList.reduce((sum, item) => sum + parseFloat(item.rebateAmount), 0)
      } else {
        this.rebateResultModel.rebateAmount = 0
      }
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },
    onRebateComputeCycleFlagChange(val) {
      if (parseInt(val) === parseInt(this.$constDefinition.rebateComputeCycleFlag.Year)) {
        this.rebateResultModel.startDate = this.rebateAgreement.startDate
        this.rebateResultModel.endDate = this.rebateAgreement.endDate
      } else {
        if (this.rebateResultModel.startDate) {
          this.rebateResultModel.startDate = null
          this.rebateResultModel.endDate = null
        }
      }
    }
  }
}
</script>
<style lang="css">
.uploadCss {
  float: right; color: #516e92 ;padding: 3px 0px;  font-size: 12px;font-weight: 400; background: transparent;  border-color: transparent
}
</style>
