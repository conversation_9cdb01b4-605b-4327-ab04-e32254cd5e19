<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="8">
          <el-date-picker
            v-model="listQuery.timeRange"
            clearable
            class="filter-item"
            style="width:100%"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            format="yyyy-MM"
            value-format="yyyy-MM"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.departmentId"
            :loading="deptLoading"
            class="filter-item"
            placeholder="部门"
            clearable
            @change="selectUpdate"
          >
            <el-option
              v-for="item in deptList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.bonusCategoryId"
            :loading="calculateCategoryLoading"
            class="filter-item"
            placeholder="奖励类型"
            clearable
            @change="selectUpdate"
          >
            <el-option
              v-for="item in calculateCategoryQueryList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.enumCalculateStatus"
            :loading="deptLoading"
            class="filter-item"
            placeholder="状态"
            clearable
            @change="selectUpdate"
          >
            <el-option
              v-for="item in calculateStatusList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'Compute_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Compute_Button_Create')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-c-scale-to-original"
            @click="handleCreate"
          >
            奖励计算
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Compute_Button_Export')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportBonusResultModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              header-align="center"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              label="计算周期"
              header-align="center"
              align="center"
              min-width="90px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.startDate | parseTime('{y}.{m}') }}-{{ row.endDate | parseTime('{y}.{m}') }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Department.Name"
              label="部门"
              header-align="center"
              align="center"
              min-width="80px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.departmentName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="BonusTemplate.BonusCategory.Name"
              label="奖励类型"
              header-align="center"
              align="center"
              min-width="80px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.bonusCategoryName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="BonusTemplate.Name"
              label="奖励模板"
              header-align="center"
              align="center"
              min-width="150px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.bonusTemplateName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="TotalAmount"
              label="奖励总额（元）"
              header-align="center"
              align="right"
              min-width="110px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.totalAmount | toTwoNum | toThousandFilter }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="EnumCalculateStatus"
              label="状态"
              header-align="center"
              align="center"
              min-width="80px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumCalculateStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="CalculateTime"
              label="计算时间"
              align="center"
              min-width="110px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.calculateTime }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="120" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i v-if="row.enumCalculateStatus !== 1 && row.enumCalculateStatus !== 2 && row.enumCalculateStatus !== 3 && row.enumCalculateStatus !== 4 && row.enumCalculateStatus !== 20" class="el-icon-document eltablei" title="查看明细" @click="reviewDetail(row)" />
                <i v-if="row.enumCalculateStatus === 3" class="el-icon-download eltablei" title="下载模板" @click="handleDownLoadTemplate(row)" />
                <i v-if="row.enumCalculateStatus === 3" class="el-icon-upload2 eltablei" title="上传结果" @click="handleUploadShow(row)" />
                <i v-if="(row.enumCalculateStatus === 10 || row.enumCalculateStatus === 34 || row.enumCalculateStatus === 35) && $isPermitted($store.getters.user, 'Compute_Button_Adjust_FinishAdjust')" class="el-icon-finished eltablei" title="调整完成" @click="handleFinishEdit(row)" />
                <i v-if="row.enumCalculateStatus === 33 && $isPermitted($store.getters.user, 'Compute_Button_Approve_FinishApprove')" class="el-icon-circle-check eltablei" title="审批完成" @click="handleApproved(row)" />
                <i v-if="row.enumCalculateStatus === 40 && $isPermitted($store.getters.user, 'Compute_Button_Release_CancelRelease')" class="el-icon-folder-checked eltablei" title="发布外勤查看" @click="reviewWQ(row)" />
                <i v-if="row.enumCalculateStatus === 50 && $isPermitted($store.getters.user, 'Compute_Button_Release_CancelRelease')" class="el-icon-folder-delete eltablei" title="撤销发布" @click="handleCancel(row)" />
                <i v-if="row.enumCalculateStatus === enumCalculateStatus_CalculateError" class="el-icon-notebook-2 eltablei" title="故障日志" @click="handleShowErrorMessage(row)" />
                <i v-if="row.enumCalculateStatus !== 50 && row.enumCalculateStatus !== 1 && row.enumCalculateStatus !== 2 && row.enumCalculateStatus !== 4 && row.enumCalculateStatus !== 30 && row.enumCalculateStatus !== 33 && row.enumCalculateStatus !== 40 && $isPermitted($store.getters.user, 'Compute_Button_Del')" class="el-icon-delete eltablei" title="删除" @click="handleDelete(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :close-on-click-modal="false"
      title="计算奖励"
      :visible="dialogEditFormVisible"
      width="40%"
      @close="btnClose"
    >
      <el-form
        ref="bonusCalculationForm"
        :model="bonusCalculation"
        label-position="right"
        label-width="90px"
        class="el-dialogform"
      >
        <el-form-item label="计算月份" prop="months">
          <el-date-picker
            v-model="bonusCalculation.months"
            clearable
            class="filter-item"
            style="width:100%"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            format="yyyy-MM"
            value-format="yyyy-MM"
            :picker-options="datePickerOptions"
          />
        </el-form-item>
        <el-form-item label="计算部门" prop="departmentId">
          <el-select
            v-model="bonusCalculation.departmentId"
            :loading="deptLoading"
            class="filter-item"
            style="width:100%"
            placeholder="选择部门"
            clearable
            @change="calculateDepartmentChange"
          >
            <el-option
              v-for="item in deptList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="奖励模板" prop="bonusTemplateId">
          <el-select
            :key="bonusCategroyKey"
            v-model="bonusCalculation.bonusTemplateId"
            :loading="calculateCategoryLoading"
            class="filter-item"
            placeholder="奖励模板"
            style="width:100%;"
            clearable
            @change="templateChange"
          >
            <el-option
              v-for="item in bonusTemplateList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="奖励类型">
          <label>{{ bonusCategoryName }}</label>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnClose()"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="createData()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      title="奖励明细"
      :visible="dialogBonusDetailFormVisible"
      width="80%"
      class="popup-search"
      @close="btnBonusDetailClose"
    >
      <div class="search-container-bg">
        <el-row :gutter="10">
          <el-col :span="span">
            <el-input
              v-model="employeeListQuery.displayName"
              clearable
              placeholder="员工姓名"
              class="filter-item"
              @keyup.enter.native="getBonusSummaryDetailList"
            />
          </el-col>
          <el-col :span="span">
            <el-select
              v-model="employeeListQuery.isGrant"
              style="width: 100%"
              class="filter-item"
              placeholder="是否发放"
              clearable
            >
              <el-option
                v-for="item in grantList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="span">
            <el-select
              v-model="employeeListQuery.isWaring"
              style="width: 100%"
              class="filter-item"
              placeholder="是否警告"
              clearable
            >
              <el-option
                v-for="item in warningList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="span">
            <el-select
              v-model="employeeListQuery.enumBonusApprovalStatus"
              :loading="enumBonusApprovalStatusListLoading"
              class="filter-item"
              placeholder="审核状态"
              clearable
            >
              <el-option
                v-for="item in enumBonusApprovalStatusList"
                :key="item.value"
                :label="item.desc"
                :value="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button class="filter-item" type="primary" icon="el-icon-search" @click="getBonusSummaryDetailList">
              查询
            </el-button>
          </el-col>
        </el-row>
        <el-row type="flex" :gutter="10" justify="end" class="mainButtonRow">
          <el-col :span="3.5">
            <el-button
              v-if="$isPermitted($store.getters.user, 'Compute_Button_Export')"
              class="filter-item-button"
              type="primary"
              icon="el-icon-download"
              @click="onShowExportModal"
            >
              导出
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table
              :data="bonusResultEmployeeList"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%;"
              :default-sort="{prop: 'createTime', order: 'descending'}"
              :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass"
              @sort-change="bonusEmployeeSortChange"
            >
              <el-table-column fixed label="序号" :index="indexMethodEmployee" type="index" align="center" />
              <el-table-column sortable="custom" prop="Employee.JobNo" label="工号" align="center" min-width="80px">
                <template slot-scope="{ row }">
                  <span>{{ row.employeeJobNo }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="Employee.DisplayName" label="员工姓名" align="center" min-width="100px">
                <template slot-scope="{ row }">
                  <span>{{ row.employeeDisplayName }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="ActualAmount" label="金额（元）" align="right" header-align="center" min-width="100px">
                <template slot-scope="{ row }">
                  <span>{{ row.actualAmount | toTwoNum | toThousandFilter }}</span>
                </template>
              </el-table-column>
              <el-table-column label="是否发放" align="center" min-width="80px">
                <template slot-scope="{ row }">
                  <span>{{ row.isGrant ? '是' : '否' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="审核状态" align="center" min-width="120px">
                <template slot-scope="{ row }">
                  <span>{{ row.enumBonusApprovalStatusDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column label="警告原因" align="center" min-width="130px">
                <template slot-scope="{ row }">
                  <span>{{ row.enumBonusWaringFlagsDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column label="备注" align="center" min-width="130px">
                <template slot-scope="{ row }">
                  <span>{{ row.adjustReason }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                header-align="center"
                width="120"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="{ row }">
                  <i class="el-icon-document eltablei" title="查看明细" @click="reviewPersonalDetails(row)" />
                  <i v-if="!row.isGrant&&isAllowAdjust && $isPermitted($store.getters.user, 'Compute_Button_Adjust_FinishAdjust')" class="el-icon-document-checked eltablei" title="发放" @click="handleGrant(row)" />
                  <i v-if="row.isGrant&&isAllowAdjust && $isPermitted($store.getters.user, 'Compute_Button_Adjust_FinishAdjust')" class="el-icon-document-delete eltablei" title="取消发放" @click="handleCancelGrant(row)" />
                  <i v-if="isAllowAdjust && $isPermitted($store.getters.user, 'Compute_Button_Adjust_FinishAdjust')" class="el-icon-edit-outline eltablei" title="调整" @click="handleAdjust(row)" />
                  <i v-if="row.bonusResultEmployeeHistory.length>0" class="el-icon-date eltablei" title="调整记录" @click="handleAdjustHistory(row)" />
                  <i v-if="bonusResult.enumCalculateStatus === enumCalculateStatus_WaitingReview && row.enumBonusApprovalStatus === enumBonusApprovalStatus_PendingApproval && $isPermitted($store.getters.user, 'Compute_Button_Approve_FinishApprove')" class="el-icon-circle-check eltablei" title="审批" @click="handleShowApproveEmployeeBonus(row)" />
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col class="el-colRight">
            <pagination v-show="bonusResultEmployeeTotal > 0" :total="bonusResultEmployeeTotal" :page.sync="employeeListQuery.pageIndex" :limit.sync="employeeListQuery.pageSize" @pagination="getBonusSummaryDetailList" />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnBonusDetailClose()"> 关闭 </el-button>
      </div>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      title="奖励调整"
      :visible="dialogAdjustFormVisible"
      width="50%"
      @close="btnAdjustClose"
    >
      <el-form
        ref="adjustForm"
        :rules="adjustRules"
        :model="adjust"
        label-position="right"
        label-width="115px"
        class="el-dialogform"
      >
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="员工姓名">
              <span>{{ bonusResultEmployee.employeeDisplayName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="奖励金额">
              <span>{{ bonusResultEmployee.actualAmount | toTwoNum | toThousandFilter }}元</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="职位">
              <span>{{ bonusResultEmployee.positionName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="地域">
              <span>{{ bonusResultEmployee.provinces }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="奖励金额" prop="newValue">
              <el-input
                v-model="adjust.newValue"
                clearable
                maxlength="10"
              >
                <i slot="suffix">元</i>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="调整原因" prop="remark">
              <el-input
                v-model="adjust.remark"
                type="textarea"
                :rows="2"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnAdjustClose('adjustForm')"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="btnAdjustSave()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      title="调整记录"
      :visible="dialogAdjustHistoryFormVisible"
      width="40%"
      @close="btnAdjustHistoryClose"
    >
      <el-row>
        <el-timeline v-for="(value, key) in bonusHistoryList" :key="key">
          <el-timeline-item :timestamp="value.handleTime | parseTime('{y}-{m}-{d}')" placement="top">
            <el-card>
              <el-row>
                <el-col :span="12" class="timeline-title">
                  {{ value.enumBonusHandleTypeDesc }}
                </el-col>
                <el-col :span="12" class="timeline-title">
                  {{ value.userDisplayName }}
                </el-col>
              </el-row>
              <p v-if="value.oldValue">{{ value.enumBonusHandleTypeDesc }}由{{ value.oldValue }}变更为{{ value.newValue }}</p>
              <p v-if="!value.oldValue">{{ value.enumBonusHandleTypeDesc }}</p>
              <p>{{ value.remark }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnAdjustHistoryClose()"> 关闭 </el-button>
      </div>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      title="人员奖励明细"
      :visible="dialogPersonalDetailFormVisible"
      width="80%"
      @close="btnPersonalDetailsClose"
    >
      <el-row class="query-title" type="flex" :gutter="10">
        <el-col :span="5">
          <label>工号：{{ jobNo }}</label>
        </el-col>
        <el-col :span="6">
          <label>员工姓名：{{ employeeDisplayName }}</label>
        </el-col>
      </el-row>
      <el-row style="margin-bottom:20px;">
        <el-col :span="24">
          <el-table
            :data="bonusResultProjectList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
          >
            <el-table-column fixed label="序号" type="index" align="center" />

            <el-table-column label="计算月份" align="center" min-width="120px">
              <template slot-scope="{ row }">
                <span>{{ row.bonusResultStartDate | parseTime('{y}.{m}') }}-{{ row.bonusResultEndDate | parseTime('{y}.{m}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="实发总奖励金额（元）" align="right" header-align="center" min-width="150px">
              <template slot-scope="{ row }">
                <span>{{ row.bonusAmount | toTwoNum | toThousandFilter }}</span>
              </template>
            </el-table-column>
            <el-table-column label="应发项目奖励金额（元）" align="left" header-align="center" min-width="160px">
              <template slot-scope="{ row }">
                <span v-html="row.projectAmountStr" />
              </template>
            </el-table-column>
            <el-table-column label="岗位" align="center" min-width="120px">
              <template slot-scope="{ row }">
                <span>{{ row.stationNames }}</span>
              </template>
            </el-table-column>
            <el-table-column label="计算开始日期" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span v-if="row.stationStartDate">{{ row.stationStartDate | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="计算截止日期" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span v-if="row.stationEndDate">{{ row.stationEndDate | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否舍弃" align="center" min-width="80px">
              <template slot-scope="{ row }">
                <span>{{ row.isUseDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="警告原因" align="center" min-width="110px">
              <template slot-scope="{ row }">
                <span>{{ row.enumBonusWaringFlagDesc }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnPersonalDetailsClose()"> 关闭 </el-button>
      </div>
    </el-dialog>

    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      :visible="exportBonusResultModal"
      :close-on-click-modal="false"
      title="导出奖励计算"
      width="800"
      @close="handleExportCancelBonusResult"
    >
      <CustomExport
        :column="bonusResultColumnDictionary"
        @exportSubmitEvent="handleExportBonusResult"
        @exportCancelEvent="handleExportCancelBonusResult"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>

    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      :visible="showExportModal"
      :close-on-click-modal="false"
      title="导出奖励明细"
      width="800"
      @close="handleExportCancel"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
    <el-dialog
      title="故障日志"
      width="30%"
      :visible="dialogErrorMessageVisible"
      @close="dialogErrorMessageVisible = false"
    >
      <span>{{ errorMessage }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogErrorMessageVisible = false">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      title="奖励审批"
      :visible="dialogApploveEmployeeBonusVisible"
      width="80%"
      @close="handleCloseApproveEmployeeBonus"
    >
      <el-row class="query-title" type="flex" :gutter="10">
        <el-col :span="5">
          <label>工号：{{ jobNo }}</label>
        </el-col>
        <el-col :span="6">
          <label>员工姓名：{{ employeeDisplayName }}</label>
        </el-col>
      </el-row>

      <el-row style="margin-bottom:20px;">
        <el-col :span="24">
          <el-table
            :data="bonusHistoryList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
          >
            <el-table-column fixed label="序号" type="index" align="center" />

            <el-table-column label="操作人" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span>{{ row.userDisplayName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="处理时间" align="center" min-width="120px">
              <template slot-scope="{ row }">
                <span>{{ row.handleTime | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="处理内容" align="left" header-align="center" min-width="120px">
              <template slot-scope="{ row }">
                <span v-if="row.oldValue">{{ row.enumBonusHandleTypeDesc }}由{{ row.oldValue }}变更为{{ row.newValue }}</span>
                <span v-if="!row.oldValue">{{ row.enumBonusHandleTypeDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="备注" align="left" header-align="center" min-width="150px">
              <template slot-scope="{ row }">
                <span>{{ row.remark }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-form
        ref="approveForm"
        :rules="approveRules"
        :model="approve"
        label-position="right"
        label-width="115px"
        class="el-dialogform"
      >
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="approve.remark"
                type="textarea"
                :rows="2"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleCloseApproveEmployeeBonus()"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleApproveEmployeeBonusSave()"
        >
          审核
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleRejectEmployeeBonusSave()"
        >
          拒绝
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      title="上传文件"
      :visible="dialogUploadFormVisible"
      width="45%"
      @close="handleUploadHidden"
    >
      <el-form
        label-position="right"
        label-width="110px"
        class="el-dialogform"
      >
        <el-form-item label="选择文件" prop="nameCn">
          <file-upload
            ref="upload"
            @getUploadFile="getUploadFile"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleUploadHidden()"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleUploadBonusResult"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import ManufacturerService from '@/api/manufacturer'
import MaintenanceService from '@/api/maintenance'
import MasterDataService from '@/api/masterData'
import BonusService from '@/api/bonus'
import FileService from '@/api/file'
import CustomExport from '@/components/Export/CustomExport'
import FileUpload from '@/views/components/uploadFile'

export default {
  components: {
    Pagination,
    CustomExport,
    FileUpload
  },
  data() {
    return {
      bonusCategroyKey: 0,
      span: 4,
      total: 0,
      bonusResultEmployeeTotal: 0,
      bonusDetailTotal: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      employeeListQuery: {
        displayName: '',
        isGrant: '',
        isWaring: '',
        enumBonusApprovalStatus: '',
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      list: [],
      bonusResultId: undefined,
      bonusResultEmployee: {
        employeeDisplayName: '',
        positionName: '',
        provinces: '',
        activeName: ''
      },
      bonusResultEmployeeList: [],
      bonusResultProjectList: [],
      bonusHistoryList: [],
      jobNo: '',
      errorMessage: '',
      employeeDisplayName: '',
      btnExportLoading: false,
      showExportModal: false,
      dialogErrorMessageVisible: false,
      columnDictionary: {},
      bonusResultColumnDictionary: {},
      dialogEditFormVisible: false,
      dialogBonusDetailFormVisible: false,
      dialogAdjustFormVisible: false,
      dialogAdjustHistoryFormVisible: false,
      dialogPersonalDetailFormVisible: false,
      exportBonusResultModal: false,
      manufacturerLoading: false,
      deptLoading: false,
      calculateCategoryLoading: false,
      calculateCategoryQueryList: [],
      bonusTemplateList: [],
      dialogStatus: '',
      bonusCalculation: {
        months: '',
        departmentId: undefined,
        bonusTemplateId: undefined
      },
      textMap: {
        update: '编辑厂商用户',
        create: '新增厂商用户',
        reset: '重置密码'
      },
      adjustRules: {
        newValue: [
          {
            required: true,
            message: '请输入奖励金额',
            trigger: 'blur'
          },
          { pattern: /((^[0-9]\d*))(\.\d{0,2}){0,1}$/, message: '奖励金额仅支持整数或者两位小数' }
        ],
        remark: [
          {
            required: true,
            message: '请输入调整原因',
            trigger: 'blur'
          },
          { max: 500, message: '调整原因长度必须小于等于 500 个字符', trigger: 'blur' }
        ]
      },
      modifyDialogTitle: '',
      modifyDialogIsReadonly: false,
      itemId: null,
      manufacturerList: [],
      deptList: [],
      calculateStatusList: [],
      adjust: {
        id: undefined,
        newValue: '',
        remark: ''
      },
      activeName: 'first',
      grantList: [{
        value: 'true',
        label: '是'
      },
      {
        value: 'false',
        label: '否'
      }],
      warningList: [
        {
          value: 'true',
          label: '是'
        },
        {
          value: 'false',
          label: '否'
        }
      ],
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6
        }
      },
      bonusResult: null,
      enumCalculateStatus_Calculated: 10, // 已计算
      enumCalculateStatus_CalculateError: 20, // 计算错误
      enumCalculateStatus_WaitingReview: 33, // 待审核(部门总监已审核)
      enumCalculateStatus_NotApproved: 35, // 审核未通过
      enumCalculateStatus_DirectorFailedReview: 34, // 部门总监审核未通过
      isAllowAdjust: false,
      enumBonusApprovalStatusListLoading: false,
      enumBonusApprovalStatusList: [],
      enumBonusApprovalStatus_PendingApproval: 15, // 待审核（部门总监已审核）
      dialogApploveEmployeeBonusVisible: false,
      approve: { remark: undefined },
      dialogUploadFormVisible: false,
      method: 'UploadBonusTemplateResult',
      controller: 'Bonus',
      approveRules: {
        remark: [
          { max: 500, message: '备注长度必须小于等于 500 个字符', trigger: 'blur' }
        ]
      },
      bonusCategoryName: undefined
    }
  },
  created() {
    if (this.$route.query.fromPage) {
      this.listQuery.enumCalculateStatus = this.$constDefinition.calculateStatus.DirectorReview
    }
    this.initManufacturer()
    this.getList()
    this.initDept()
    this.initCalculateStatus()
    this.initBonusCategory()
    this.initBonusApprovalStatus()
  },
  methods: {
    initManufacturer() {
      this.manufacturerLoading = true
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturerLoading = false
          this.manufacturerList = result
        })
        .catch(() => {
          this.manufacturerLoading = false
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch(() => {
          this.deptLoading = false
        })
    },
    initCalculateStatus() {
      var param = {
        enumType: 'CalculateStatus'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.calculateStatusList = result.data.datas
          this.$forceupdate()
        })
        .catch(() => {
        })
    },
    initBonusCategory() {
      this.calculateCategoryLoading = true
      BonusService.QueryBonusCategorySelect()
        .then((result) => {
          this.calculateCategoryLoading = false
          this.calculateCategoryQueryList = result
        })
        .catch((error) => {
          this.calculateCategoryLoading = false
          console.log(error)
        })
    },
    initBonusApprovalStatus() {
      this.enumBonusApprovalStatusListLoading = true
      var param = {
        enumType: 'BonusApprovalStatus'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.enumBonusApprovalStatusListLoading = false
          this.enumBonusApprovalStatusList = result.data.datas
        })
        .catch((error) => {
          this.enumBonusApprovalStatusListLoading = false
          console.log(error)
        })
    },
    getList() {
      this.listLoading = true
      BonusService.QueryBonusResult(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(() => {
        this.listLoading = false
      })
    },
    GetBonusResultEmployee(id) {
      BonusService.GetBonusResultEmployee({ id: id })
        .then((result) => {
          this.bonusResultEmployee = Object.assign({}, result.data)
        })
        .catch(() => {
        })
    },
    getBonusSummaryDetailList() {
      this.listLoading = true
      this.employeeListQuery.bonusResultId = this.bonusResultId
      BonusService.QueryBonusResultEmployee(this.employeeListQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.bonusResultEmployeeList = result.data.datas
          this.bonusResultEmployeeTotal = result.data.recordCount
          this.employeeListQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(() => {
        this.listLoading = false
      })
    },
    getProjectBonusList(id) {
      this.listLoading = true
      BonusService.QueryBonusResultEmployeeDetail({ bonusResultEmployeeId: id }).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.bonusResultProjectList = result.data.datas
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(() => {
        this.listLoading = false
      })
    },
    getBonusHistoryList(id) {
      this.listLoading = true
      BonusService.QueryBonusResultEmployeeHistory({ bonusResultEmployeeId: id }).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.bonusHistoryList = result.data.datas
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(() => {
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    indexMethodEmployee(index) {
      return (this.employeeListQuery.pageIndex - 1) * this.employeeListQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    bonusEmployeeSortChange(column, prop, order) {
      this.employeeListQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.employeeListQuery.order = orderSymbol + column.prop
      this.getBonusSummaryDetailList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    resetBonus() {
      this.bonusCategoryName = undefined
      this.bonusCalculation = {
        months: '',
        departmentId: undefined,
        bonusTemplateId: undefined
      }
    },
    handleCreate() {
      this.resetBonus()
      this.dialogEditFormVisible = true
      this.$nextTick(() => {
        this.$refs['bonusCalculationForm'].clearValidate()
      })
    },
    btnClose() {
      this.dialogEditFormVisible = false
    },
    createData() {
      if (this.bonusCalculation.months === '') {
        this.$notice.message('请选择计算月份', 'error')
        return false
      }
      if (this.bonusCalculation.departmentId === undefined || this.bonusCalculation.departmentId === null) {
        this.$notice.message('请选择计算部门', 'error')
        return false
      }
      if (this.bonusCalculation.bonusTemplateId === undefined || this.bonusCalculation.bonusTemplateId === null) {
        this.$notice.message('请选择奖励模板', 'error')
        return false
      }
      if (this.bonusCalculation.months) {
        const startArr = this.bonusCalculation.months[0].split('-')
        const endArr = this.bonusCalculation.months[1].split('-')
        const startMonth = startArr[0] * 12 + startArr[1]
        const endMonth = endArr[0] * 12 + endArr[1]
        const monthCount = endMonth - startMonth + 1
        const bonusTemplate = this.bonusTemplateList.find(o => {
          return o.key === this.bonusCalculation.bonusTemplateId
        })

        if (bonusTemplate.minimumMonth > monthCount) {
          this.$notice.message(`您选择的计算月份至少要包含${bonusTemplate.minimumMonth}个周期`, 'error')
        } else {
          this.listLoading = true
          this.bonusCalculation.startDate = this.bonusCalculation.months[0]
          this.bonusCalculation.endDate = this.bonusCalculation.months[1]
          BonusService.ComputeBonus(this.bonusCalculation)
            .then((result) => {
              this.listLoading = false
              if (result.succeed) {
                this.dialogEditFormVisible = false
                this.getList()
                this.showMessage('保存成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
              this.listLoading = false
              this.loading = false
            })
        }
      }
    },
    reviewDetail(row) {
      this.dialogBonusDetailFormVisible = true
      this.bonusResult = row
      this.isAllowAdjust = false
      if (this.bonusResult.enumCalculateStatus === this.enumCalculateStatus_Calculated ||
      this.bonusResult.enumCalculateStatus === this.enumCalculateStatus_NotApproved || this.bonusResult.enumCalculateStatus === this.enumCalculateStatus_DirectorFailedReview) {
        this.isAllowAdjust = true
      }
      this.bonusResultId = row.id
      this.employeeListQuery.pageIndex = 1
      this.$nextTick(() => {
        this.getBonusSummaryDetailList()
      })
    },
    handleDelete(row) {
      this.$confirm('确定删除此奖励计算结果吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        BonusService.DelBonusResult(row).then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('删除成功', 'success')
          }
        })
          .catch(() => {
            this.listLoading = false
          })
      }).catch(error => {
        this.listLoading = false
        if (!error.succeed) {
          this.$notice.message('取消删除', 'info')
        }
      })
    },
    btnBonusDetailClose() {
      this.employeeListQuery.displayName = ''
      this.employeeListQuery.isGrant = ''
      this.employeeListQuery.isWaring = ''
      this.employeeListQuery.enumBonusApprovalStatus = ''
      this.dialogBonusDetailFormVisible = false
    },
    reviewWQ(row) {
      this.$confirm('确定发布吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        BonusService.ReleaseField(row).then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('发布成功', 'success')
          }
        })
          .catch(() => {
            this.listLoading = false
          })
      }).catch(error => {
        this.listLoading = false
        if (!error.succeed) {
          this.$notice.message('取消发布', 'info')
        }
      })
    },
    handleFinishEdit(row) {
      this.$confirm('确定奖励调整完毕，提交审核?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        BonusService.FinishEditBonus(row).then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('奖励已提交审核', 'success')
          }
        })
          .catch(() => {
            this.listLoading = false
          })
      })
    },
    handleApproved(row) {
      this.$confirm('确定审核完毕?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        BonusService.ApproveBonus(row).then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('奖励已审核通过', 'success')
          }
        })
          .catch(() => {
            this.listLoading = false
          })
      })
    },
    handleCancel(row) {
      this.$confirm('是否撤销已发布奖励?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        BonusService.CancelReleaseField(row).then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('撤销成功', 'success')
          }
        })
          .catch(() => {
            this.listLoading = false
          })
      })
    },
    handleGrant(row) {
      this.$confirm('是否确认发放' + row.employeeDisplayName + '的奖励?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        BonusService.GrantBonusEmployee(row).then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getBonusSummaryDetailList()
            this.$notice.message('发放成功', 'success')
          }
        })
          .catch(() => {
            this.listLoading = false
          })
      }).catch(error => {
        this.listLoading = false
        if (!error.succeed) {
          this.$notice.message('取消发放', 'info')
        }
      })
    },
    handleCancelGrant(row) {
      this.$confirm('是否确认取消发放' + row.employeeDisplayName + '的奖励?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        BonusService.CancelGrantBonusEmployee(row).then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getBonusSummaryDetailList()
            this.$notice.message('取消发放成功', 'success')
          }
        })
          .catch(() => {
            this.listLoading = false
          })
      })
    },
    reviewPersonalDetails(row) {
      this.dialogPersonalDetailFormVisible = true
      this.jobNo = row.employeeJobNo
      this.employeeDisplayName = row.employeeDisplayName
      this.$nextTick(() => {
        this.getProjectBonusList(row.id)
      })
    },
    btnPersonalDetailsClose() {
      this.dialogPersonalDetailFormVisible = false
    },
    handleAdjust(row) {
      this.dialogAdjustFormVisible = true
      this.bonusResultEmployee.id = row.id
      this.GetBonusResultEmployee(row.id)
    },
    btnAdjustClose() {
      this.dialogAdjustFormVisible = false
      this.adjust.newValue = ''
      this.adjust.remark = ''
      this.$refs['adjustForm'].resetFields()
    },
    btnAdjustSave() {
      this.$refs['adjustForm'].validate((valid) => {
        if (valid) {
          this.listLoading = true
          this.adjust.oldValue = this.bonusResultEmployee.actualAmount + ''
          this.adjust.bonusResultEmployeeId = this.bonusResultEmployee.id
          this.adjust.handlerEmployeeId = this.bonusResultEmployee.employeeId
          BonusService.AddBonusResultEmployeeHistory(this.adjust)
            .then((result) => {
              this.listLoading = false
              if (result.succeed) {
                this.dialogAdjustFormVisible = false
                this.getBonusSummaryDetailList()
                this.showMessage('保存成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
              this.listLoading = false
              this.loading = false
            })
        }
      })
    },
    handleShowErrorMessage(row) {
      this.errorMessage = row.errorMessage
      this.dialogErrorMessageVisible = true
    },
    handleAdjustHistory(row) {
      this.dialogAdjustHistoryFormVisible = true
      this.getBonusHistoryList(row.id)
    },
    btnAdjustHistoryClose() {
      this.dialogAdjustHistoryFormVisible = false
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },
    onHidden() {
      this.dialogEditFormVisible = false
    },
    onRefresh() {
      this.getList()
      this.dialogEditFormVisible = false
    },
    // 显示导出model TODO
    onShowExportModal() {
      this.btnExportLoading = true
      BonusService.GetBonusResultEmployeeDetailExportColumn({ bonusResultId: this.bonusResult.id }).then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出 TODO
    handleExport(checkColumns) {
      this.showExportModal = false
      this.employeeListQuery.checkedColumns = checkColumns
      BonusService.ExportBonusResultEmployeeDetail(this.employeeListQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '奖励明细.xlsx'

          this.employeeListQuery.checkedColumns = null
          fileDownload(result.data, filename)
        })
        .catch(() => {
          this.employeeListQuery.checkedColumns = null
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    // 显示奖励计算导出model
    onShowExportBonusResultModal() {
      this.btnExportLoading = true
      BonusService.GetBonusResultExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.bonusResultColumnDictionary = result.data
        this.exportBonusResultModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出奖励计算
    handleExportBonusResult(checkColumns) {
      this.exportBonusResultModal = false
      this.listQuery.checkedColumns = checkColumns
      BonusService.ExportBonusResult(this.listQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '奖励计算.xlsx'

          fileDownload(result.data, filename)
          this.listQuery.checkedColumns = null
        })
        .catch(() => {
          this.listQuery.checkedColumns = null
        })
    },
    handleExportCancelBonusResult() {
      this.exportBonusResultModal = false
    },
    handleShowApproveEmployeeBonus(row) {
      this.jobNo = row.employeeJobNo
      this.employeeDisplayName = row.employeeDisplayName
      this.getBonusHistoryList(row.id)
      this.bonusResultEmployee.id = row.id
      this.GetBonusResultEmployee(row.id)
      this.dialogApploveEmployeeBonusVisible = true
    },
    handleCloseApproveEmployeeBonus() {
      this.dialogApploveEmployeeBonusVisible = false
      this.approve.remark = ''
      this.$refs['approveForm'].resetFields()
    },
    handleApproveEmployeeBonusSave() {
      this.$refs['approveForm'].validate((valid) => {
        if (valid) {
          this.approve.bonusResultEmployeeId = this.bonusResultEmployee.id
          BonusService.ApproveEmployeeBonus(this.approve)
            .then((result) => {
              if (result.succeed) {
                this.handleCloseApproveEmployeeBonus()
                this.getBonusSummaryDetailList()
                this.showMessage('审批成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
            })
        }
      })
    },
    handleRejectEmployeeBonusSave() {
      if (!this.approve.remark) {
        this.$notice.message('拒绝时必须填写备注', 'error')
        return
      }
      this.$refs['approveForm'].validate((valid) => {
        if (valid) {
          this.approve.bonusResultEmployeeId = this.bonusResultEmployee.id
          BonusService.RejectEmployeeBonus(this.approve)
            .then((result) => {
              if (result.succeed) {
                this.handleCloseApproveEmployeeBonus()
                this.getBonusSummaryDetailList()
                this.showMessage('拒绝成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
            })
        }
      })
    },
    selectUpdate() {
      this.$forceUpdate()
    },
    calculateDepartmentChange() {
      this.calculateCategoryLoading = true
      this.bonusCategoryName = undefined
      this.bonusCalculation.bonusTemplateId = undefined
      const para = { departmentId: this.bonusCalculation.departmentId }
      BonusService.QueryBonusTemplateSelect(para)
        .then((result) => {
          this.calculateCategoryLoading = false
          this.bonusTemplateList = result
        })
        .catch((error) => {
          this.calculateCategoryLoading = false
          console.log(error)
        })
    },
    handleDownLoadTemplate(row) {
      FileService.downloadAttachment(row.attachmentId).then(res => {
        const fileDownload = require('js-file-download')
        var filename = row.attachmentFileName
        fileDownload(res.data, filename)
      })
        .catch(() => {
        })
    },
    handleUploadShow(row) {
      this.dialogUploadFormVisible = true
      this.bonusResultId = row.id
    },
    handleUploadHidden() {
      this.$refs.upload.handleRemove()
      this.dialogUploadFormVisible = false
    },
    getUploadFile(val) {
      this.file = val
    },
    handleUploadBonusResult() {
      if (this.file === null) {
        this.$notice.message('请选择文件。', 'error')
      } else {
        const formData = new FormData()
        formData.append('bonusResultId', this.bonusResultId)
        FileService.uploadTemplate(this.file.raw, formData, this.controller, this.method).then(res => {
          this.handleUploadHidden()
          this.handleFilter()
          this.$refs.upload.handleRemove()
          this.file = null
        })
      }
    },
    templateChange() {
      this.bonusCategoryName = undefined
      const bonusTemplate = this.bonusTemplateList.find(o => {
        return o.key === this.bonusCalculation.bonusTemplateId
      })
      this.bonusCategoryName = bonusTemplate.bonusCategoryName
    }
  }
}
</script>
<style>
.query-title{
  margin-bottom: 17px;
}
.filter-item{
  margin: 0 10px 5px 0;
}
.table-title{
  font-weight:bold;
  font-size:13px;
  margin-bottom: 20px;
}

.timeline-title{
  font-weight:bold;
}
</style>
