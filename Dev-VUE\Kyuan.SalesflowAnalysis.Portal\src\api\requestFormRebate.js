import HttpApi from './libs/api.request'

const controller = 'RequestFormRebate'

const api = new HttpApi(controller)

export default {
  QueryRequestFormRebateAgreementApproval(params) {
    return api.get('QueryRequestFormRebateAgreementApproval', params)
  },
  QueryRequestFormRebateAgreement(params) {
    return api.get('QueryRequestFormRebateAgreement', params)
  },
  GetRequestFormRebateAgreement(params) {
    return api.get('GetRequestFormRebateAgreement', params)
  }
}
