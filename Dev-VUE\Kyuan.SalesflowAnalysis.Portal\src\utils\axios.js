import axios from 'axios'
import { MessageBox, Loading, Message } from 'element-ui'
import cfg from '@cfg'

const services = {
  loading: {}
}

class HttpRequest {
  constructor(baseUrl, requestFunc, responseFunc, errorFunc) {
    this.baseUrl = baseUrl
    this.requestFunc = requestFunc
    this.responseFunc = responseFunc
    this.errorFunc = errorFunc
    this.queue = {}
  }

  getInsideConfig() {
    const config = {
      baseURL: this.baseUrl,
      headers: {
        //
      }
    }
    return config
  }

  destroy(url) {
    delete this.queue[url]
    if (!Object.keys(this.queue).length) {
      // Spin.hide()
      // 全局遮罩层，关闭
      services.loading.close()
    }
  }
  // 销毁所有请求
  destroyAll() {
    this.queue = {}
    if (!Object.keys(this.queue).length) {
      // Spin.hide()
      // 全局遮罩层，关闭
      services.loading.close()
    }
  }

  interceptors(instance) {
    // 请求拦截
    instance.interceptors.request.use(
      config => {
        // 添加全局的loading...
        if (!Object.keys(this.queue).length) {
          // Spin.show() // 不建议开启，因为界面不友好
          // 全局遮罩层，开启
          services.loading = Loading.service({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
        }
        this.queue[config.url] = true

        if (this.requestFunc && typeof this.requestFunc === 'function') {
          this.requestFunc(config)
        }

        return config
      },
      error => {
        return Promise.reject(error)
      }
    )

    // 响应拦截
    instance.interceptors.response.use(
      response => {
        if (response.config.url.startsWith(response.config.baseURL)) {
          response.config.url = response.config.url.substr(response.config.baseURL.length)
        }

        this.destroy(response.config.url)

        if (this.responseFunc && typeof this.responseFunc === 'function') {
          this.responseFunc(response)
        }

        if (
          response.headers['content-type'].indexOf('application/octet-stream') >= 0 ||
          response.headers['content-type'].indexOf('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') >= 0
        ) {
          return response
        }

        const result = JSON.decryptCycle(response.data)

        const config = response.config

        delete response.data

        if (config.processResult &&
          Object.prototype.hasOwnProperty.call(result, 'succeed') &&
          Object.prototype.hasOwnProperty.call(result, 'type') &&
          Object.prototype.hasOwnProperty.call(result, 'messages')
        ) {
          // 处理返回结果
          if ((!result.succeed || result.type === 2) && result.type !== -3) {
            // 需要提示,confirm不处理
            let type = 'warning'
            let title = '警告'
            const message = result.messages.toString().replace(/,/g, '<br/>')

            switch (result.type) {
              case -5: {
                type = 'error'
                title = '错误'
                break
              }
              case -4: {
                type = 'warning'
                title = '警告'
                break
              }
              case 2: {
                type = 'info'
                title = '信息'
                break
              }
            }

            MessageBox({
              dangerouslyUseHTMLString: true,
              message,
              title,
              confirmButtonText: '关闭',
              type,
              showCancelButton: false
            })

            result.processed = true

            if (!result.succeed) {
              result.message = '系统错误'
              result.response = response
              return Promise.reject(result)
            }
          }
        }

        delete response.config
        delete response.request
        if (!Array.isArray(result)) {
          result.$response = response
        }

        return result
      },
      error => {
        if (error.message.includes('timeout')) {
          error.message = '请求超时，请联系管理员检查。'
          this.destroyAll()
          Message({
            message: error.message,
            type: 'error',
            duration: 5 * 1000
          })
        }

        if (error && error.response) {
          switch (error.response.status) {
            case 400: error.message = '请求错误'; break
            case 401: error.message = '未授权，请登录'; break
            case 403: error.message = '拒绝访问'; break
            case 404:
              if (error.request.responseType === 'arraybuffer') { error.message = `未找到目标下载文件` } else { error.message = `请求地址出错: ${error.response.config.url}` } break
            case 408: error.message = '请求超时'; break
            case 500: error.message = `系统处理过程遇到错误，请联系管理员检查。错误信息为:  ${error.response.data}`; break
            case 501: error.message = '服务未实现'; break
            case 502: error.message = '网关错误'; break
            case 503: error.message = '服务不可用'; break
            case 504: error.message = '网关超时'; break
            case 505: error.message = 'HTTP版本不受支持'; break
            default: break
          }

          if (error.response.config && error.response.config.url) {
            if (error.response.config.url.startsWith(error.response.config.baseURL)) {
              error.response.config.url = error.response.config.url.substr(error.response.config.baseURL.length)
            }
            this.destroy(error.response.config.url)
          }

          if (this.errorFunc && typeof this.errorFunc === 'function') {
            this.errorFunc(error)
          }
        } else if (error && error.isAxiosError) {
          if (error.config && error.config.url) {
            if (error.config.url.startsWith(error.config.baseURL)) {
              error.config.url = error.config.url.substr(error.config.baseURL.length)
            }
            this.destroy(error.config.url)
          }
        }

        return Promise.reject(error)
      }
    )
  }

  request(options) {
    let instance

    if (Object.prototype.hasOwnProperty.call(services, this.baseUrl)) {
      instance = services[this.baseUrl]
    } else {
      instance = axios.create({
        baseURL: this.baseUrl,
        // withCredentials: true, // send cookies when cross-domain requests
        timeout: cfg.apiDuration || 60000 // 设置时间超时，单位毫秒
      })
      this.interceptors(instance)
      services[this.baseUrl] = instance
    }

    return instance(options)
  }
}

export default HttpRequest
