<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container minusBottom" type="flex">
        <el-col :span="8">
          <el-date-picker
            v-model="listQuery.checkedMonths"
            clearable
            class="filter-item"
            style="width:100%"
            type="monthrange"
            range-separator="至"
            start-placeholder="导入开始月份"
            end-placeholder="导入结束月份"
            format="yyyy-MM"
            value-format="yyyy-MM"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="listQuery.salesDateRange"
            clearable
            class="filter-item"
            style="width:100%"
            type="daterange"
            range-separator="至"
            start-placeholder="销售开始日期"
            end-placeholder="销售结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProductAndSpec"
            v-model="productAndSpecId"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            placeholder="产品/规格"
            clearable
            class="filter-item"
            @change="closeProductCascader"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refDistributorProvinceCity"
            v-model="distributorProvinceCityId"
            :loading="provinceCityLoading"
            :options="provinceCityList"
            class="filter-item"
            placeholder="发货方省份/城市"
            clearable
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            @change="closeDistributorCascader"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.distributorName"
            clearable
            placeholder="发货方名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refReceiverProvinceCity"
            v-model="receiverProvinceCityId"
            :loading="provinceCityLoading"
            :options="provinceCityList"
            class="filter-item"
            placeholder="收货方省份/城市"
            clearable
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            @change="closeReceiverCascader"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.receiverName"
            clearable
            placeholder="收货方名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'ManufacturerSalesflow_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column label="导入月份" align="center" sortable="custom" min-width="100px" prop="Cycle.YearMonthSplicing">
              <template slot-scope="{ row }">
                <span>{{ row.yearMonthSplicing }}</span>
              </template>
            </el-table-column>
            <el-table-column label="发货方省份" align="center" sortable="custom" min-width="110px" prop="Distributor.Province.NameCn">
              <template slot-scope="{ row }">
                <span>{{ row.distributorProvinceName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="发货方城市" align="center" sortable="custom" min-width="110px" prop="Distributor.City.NameCn">
              <template slot-scope="{ row }">
                <span>{{ row.distributorCityName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="发货方" align="left" sortable="custom" header-align="center" min-width="200px" prop="Distributor.Name">
              <template slot-scope="{ row }">
                <span>{{ row.distributorName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="收货方省份" align="center" sortable="custom" min-width="110px" prop="Receiver.Province.NameCn">
              <template slot-scope="{ row }">
                <span>{{ row.receiverProvinceName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="收货方城市" align="center" sortable="custom" min-width="110px" prop="Receiver.City.NameCn">
              <template slot-scope="{ row }">
                <span>{{ row.receiverCityName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="收货方" align="left" sortable="custom" header-align="center" min-width="200px" prop="Receiver.Name">
              <template slot-scope="{ row }">
                <span>{{ row.receiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="产品名称" align="center" sortable="custom" min-width="100px" prop="ProductSpec.Product.NameCn">
              <template slot-scope="{ row }">
                <span>{{ row.productNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column label="包装规格" align="center" sortable="custom" min-width="100px" prop="ProductSpec.Spec">
              <template slot-scope="{ row }">
                <span>{{ row.specification }}</span>
              </template>
            </el-table-column>
            <el-table-column label="批次" align="center" sortable="custom" min-width="100px" prop="BatchNumber">
              <template slot-scope="{ row }">
                <span>{{ row.batchNumber }}</span>
              </template>
            </el-table-column>
            <el-table-column label="产品效期" align="center" sortable="custom" min-width="100px" prop="ExpireDate">
              <template slot-scope="{ row }">
                <span v-if="row.expireDate">{{ row.expireDate | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="销售日期" align="center" sortable="custom" min-width="100px" prop="SaleDate">
              <template slot-scope="{ row }">
                <span>{{ row.saleDate | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="销售数量" align="right" sortable="custom" header-align="center" min-width="100px" prop="Quantity">
              <template slot-scope="{ row }">
                <span>{{ row.quantity }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import ProductService from '@/api/product'
import LocationService from '@/api/location'
import SalesFlowService from '@/api/salesFlow'

export default {
  name: 'ManufacturerSalesFlowQuery',
  components: {
    Pagination
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      list: [],
      manufacturerLoading: false,
      productAndSpecLoading: false,
      productAndSpecList: null,
      productAndSpecId: [],
      provinceCityLoading: false,
      provinceCityList: [],
      distributorProvinceCityId: null,
      receiverProvinceCityId: null
    }
  },
  created() {
    this.initProductAndSpec()
    this.initProvinceCity()
    this.handleFilter()
  },
  methods: {
    initProductAndSpec() {
      this.productAndSpecLoading = true
      ProductService.QueryManufacturerProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    initProvinceCity() {
      this.provinceCityLoading = true
      LocationService.QueryProvinceCityCascader()
        .then((result) => {
          this.provinceCityList = result
          this.provinceCityLoading = false
        })
        .catch((error) => {
          this.provinceCityLoading = false
          console.log(error)
        })
    },
    manufacturerChange() {
      this.initProductAndSpec()
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      if (this.productAndSpecId) {
        const [productId, productSpecId] = this.productAndSpecId
        this.listQuery.productId = productId
        this.listQuery.productSpecId = productSpecId
      }
      if (this.distributorProvinceCityId) {
        const [distributorProvinceId, distributorCityId] = this.distributorProvinceCityId
        this.listQuery.distributorProvinceId = distributorProvinceId
        this.listQuery.distributorCityId = distributorCityId
      }
      if (this.receiverProvinceCityId) {
        const [receiverProvinceId, receiverCityId] = this.receiverProvinceCityId
        this.listQuery.receiverProvinceId = receiverProvinceId
        this.listQuery.receiverCityId = receiverCityId
      }
      this.listLoading = true
      SalesFlowService.QueryManufacturerSalesFlow(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    closeDistributorCascader() {
      this.$refs.refDistributorProvinceCity.dropDownVisible = false
    },
    closeReceiverCascader() {
      this.$refs.refReceiverProvinceCity.dropDownVisible = false
    },
    closeProductCascader() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    }
  }
}
</script>
