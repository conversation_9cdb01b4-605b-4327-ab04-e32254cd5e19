<template>
  <div>
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="批量导入"
      :close-on-click-modal="false"
      :visible="importDialogVisible"
      width="80%"
      class="popup-search"
      @close="closeImportDialog"
    >
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="8">
            <el-date-picker
              v-model="listQuery.importTime"
              clearable
              class="filter-item"
              type="daterange"
              range-separator="至"
              start-placeholder="导入开始时间"
              end-placeholder="导入结束时间"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              :picker-options="datePickerOptions"
            />
          </el-col>
          <el-col :span="span">
            <el-input
              v-model="listQuery.operator"
              clearable
              placeholder="操作人"
              class="filter-item"
              @keyup.enter.native="handleFilter"
            />
          </el-col>
          <el-col :span="span">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-search"
              @click="handleFilter"
            >
              查询
            </el-button>
          </el-col>
        </el-row>
        <el-row type="flex" justify="end" :gutter="10">
          <el-col :span="3.5">
            <file-upload
              :controller="controller"
              :method="method"
              @uploadSuccess="uploadSuccess"
              @uploadFail="uploadFail"
            />
          </el-col>
          <el-col :span="3.5">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-document"
            >
              <a href="/template/TerminalQuotaTemplate.xlsx" download="终端潜力导入模板">模板下载</a>
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table
              v-loading="listLoading"
              :data="list"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%;"
              :default-sort="{prop: 'createTime', order: 'descending'}"
              :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass"
              @sort-change="sortChange"
            >
              <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
              <el-table-column sortable="custom" prop="Attachment.FileName" label="原始文件名称" min-width="180px" align="left" header-align="center">
                <template slot-scope="{ row }">
                  <span class="link-type" @click="handleDownloadFile(row)">{{ row.attachmentFileName }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="ImportTime" label="导入时间" min-width="140px" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.importTime }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="TotalCount" label="导入总数" min-width="80px" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.totalCount }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="ErrorCount" label="错误数" min-width="80px" align="right" header-align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.errorCount }}</span>
                </template>
              </el-table-column>
              <el-table-column sortable="custom" prop="Operator" label="操作人" min-width="80px" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.operator }}</span>
                </template>
              </el-table-column>
              <el-table-column
                fixed="right"
                label="操作"
                align="center"
                header-align="center"
                width="80"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="{ row }">
                  <i v-if="row.errorCount>0" class="el-icon-document eltablei" title="查看错误信息" @click="handleErrorInfo(row)" />
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col class="el-colRight">
            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="listQuery.pageIndex"
              :limit.sync="listQuery.pageSize"
              @pagination="getList"
            />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeImportDialog()"> 关闭 </el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="错误信息"
      :close-on-click-modal="false"
      :visible="receiverErrorInfoVisable"
      width="80%"
      class="popup-search"
      @close="closeReceiverErrorInfoDialog"
    >
      <ReceiverQuotaError :import-master-temp-id="importMasterTempId" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeReceiverErrorInfoDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import BonusService from '@/api/bonus'
import Pagination from '@/components/Pagination'
import FileService from '@/api/file'
import FileUpload from '@/views/components/autoUploadFile'
import ReceiverQuotaError from './importReceiverQuotaError'

export default {
  components: {
    Pagination,
    FileUpload,
    ReceiverQuotaError
  },
  data() {
    return {
      span: 4,
      importMasterTempId: '',
      listLoading: true,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      list: [],
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6
        }
      },
      receiverErrorInfoVisable: false,
      importDialogVisible: false,
      enumQuotaType: 1,
      file: null,
      method: 'AddQuotaToTemp',
      controller: 'Bonus'
    }
  },
  methods: {
    initPage() {
      this.importDialogVisible = true
      this.getList()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList() {
      this.listLoading = true
      this.listQuery.quotaType = 1
      BonusService.QueryImportQuotaMasterTemp(this.listQuery).then(res => {
        this.listLoading = false
        this.list = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(() => {
        this.listLoading = true
      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleErrorInfo(row) {
      this.importMasterTempId = row.id
      this.receiverErrorInfoVisable = true
    },
    closeReceiverErrorInfoDialog() {
      this.receiverErrorInfoVisable = false
      this.importMasterTempId = ''
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleDownloadFile(row) {
      FileService.downloadAttachment(row.attachmentId).then(res => {
        const fileDownload = require('js-file-download')
        var filename = row.attachmentFileName
        fileDownload(res.data, filename)
      })
    },
    closeImportDialog() {
      this.importDialogVisible = false
      this.$emit('success')
    },
    uploadSuccess() {
      this.getList()
    },
    uploadFail() {
      this.getList()
    }
  }
}
</script>
<style scoped>
  .el-dialog-s {
  z-index: 11;
}
</style>
