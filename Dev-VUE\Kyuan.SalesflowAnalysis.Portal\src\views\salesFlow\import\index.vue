<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="8">
          <el-date-picker
            v-model="listQuery.importTime"
            clearable
            class="filter-item"
            type="daterange"
            range-separator="至"
            start-placeholder="导入开始时间"
            end-placeholder="导入结束时间"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :picker-options="datePickerOptions"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.attachmentFileName"
            clearable
            placeholder="原始文件名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.operator"
            clearable
            placeholder="操作人"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Import_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" justify="end" :gutter="10">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Import_Button_BatchImport')"
            type="primary"
            icon="el-icon-upload2"
            @click="handleImportShow"
          >
            批量导入
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
            <el-table-column sortable="custom" prop="Attachment.FileName" label="原始文件名称" header-align="center" align="left" min-width="240px">
              <template slot-scope="{ row }">
                <span class="link-type" @click="handleDownloadFile(row)">{{ row.attachmentFileName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="SalesFlowTemplateMapping.Name" label="模板名称" header-align="center" align="left" min-width="150px">
              <template slot-scope="{ row }">
                <span>{{ row.salesFlowTemplateMappingName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="SalesFlowVersion.Name" label="导入版本" min-width="120px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.salesFlowVersionName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="SalesFlowVersion.EnumStatus" label="版本状态" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.salesFlowVersionEnumStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="ImportTime" label="导入时间" min-width="140px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.importTime }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="TotalCount" label="导入总数" min-width="100px" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.totalCount }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="ErrorCount" label="错误数" min-width="90px" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.errorCount }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="HandledCount" label="已处理" min-width="90px" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.handledCount }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="Operator" label="操作人" min-width="90px" header-align="center" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.operator }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="80"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i v-if="row.salesFlowVersionStatus !==20 && row.errorCount !== row.handledCount && $isPermitted($store.getters.user, 'Import_Button_Handle')" class="el-icon-edit eltablei" title="处理错误" @click="handleError(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      append-to-body
      title="错误信息"
      :close-on-click-modal="false"
      :visible="errorInfoVisable"
      width="90%"
      class="popup-search"
      @close="closeErrorInfoDialog"
    >
      <HandleSalesFlowList :import-sales-flow-master-temp-id.sync="importSalesFlowMasterTempId" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeErrorInfoDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      title="上传文件"
      :visible="dialogUploadFormVisible"
      width="45%"
      @close="handleImportHidden"
    >
      <el-form
        label-position="right"
        label-width="90px"
        class="el-dialogform"
      >
        <el-form-item label="选择模板">
          <el-select
            v-model="salesFlowTemplateMappingId"
            style="width: 100%"
            class="filter-item"
            placeholder="选择模板"
          >
            <el-option
              v-for="item in salesFlowTemplateList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否覆盖">
          <el-switch v-model="isCover" @change="coverChange" />
        </el-form-item>
        <el-form-item label="选择版本">
          <el-checkbox-group v-model="version" :disabled="!isCover">
            <el-checkbox v-for="item in versionList" :key="item.key" :label="item.key">{{ item.value }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="选择文件">
          <file-upload
            ref="upload"
            @getUploadFile="getUploadFile"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleImportHidden()"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleuploadSalesFlow"
        >
          导入
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import SalesFlowService from '@/api/salesFlow'
import HandleSalesFlowList from './components/handleSalesFlowList'
import Pagination from '@/components/Pagination'
import FileUpload from '@/views/components/uploadFile'
import SalesFlowTemplateMappingService from '@/api/salesFlowTemplateMapping'
import FileService from '@/api/file'

export default {
  name: 'ImportSalesFlow',
  components: {
    Pagination,
    FileUpload,
    HandleSalesFlowList
  },
  data() {
    return {
      span: 4,
      temp: {},
      salesFlowTemplateList: [],
      importSalesFlowMasterTempId: '',
      salesFlowTemplateMappingId: '',
      listLoading: true,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      list: [],
      isCover: false,
      version: [],
      versionList: [],
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6
        }
      },
      dialogUploadFormVisible: false,
      errorInfoVisable: false,
      file: null,
      method: 'AddSalesFlolwToTemp',
      controller: 'SalesFlow'
    }
  },
  created() {
    this.getList()
    this.initSalesFlowTemplateMapping()
  },
  methods: {
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getVersionList() {
      SalesFlowService.QuerySalesFlowVersion().then(res => {
        this.versionList = res
      }).catch(() => {
      })
    },
    getList() {
      this.listLoading = true
      SalesFlowService.QueryImportSalesFlowMasterTemp(this.listQuery).then(res => {
        this.listLoading = false
        this.list = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(res => {
        this.listLoading = false
      })
    },
    initSalesFlowTemplateMapping() {
      SalesFlowTemplateMappingService.QuerySalesFlowTemplateMappingSelect()
        .then((result) => {
          this.salesFlowTemplateList = result
        })
        .catch((error) => {
          console.log(error)
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleError(row) {
      this.importSalesFlowMasterTempId = row.id
      this.errorInfoVisable = true
    },
    closeErrorInfoDialog() {
      this.importSalesFlowMasterTempId = ''
      this.getList()
      this.errorInfoVisable = false
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleDownloadFile(row) {
      FileService.downloadAttachment(row.attachmentId).then(res => {
        const fileDownload = require('js-file-download')
        var filename = row.attachmentFileName
        fileDownload(res.data, filename)
      })
    },
    getUploadFile(val) {
      this.file = val
    },
    handleImportShow() {
      this.getVersionList()
      this.dialogUploadFormVisible = true
    },
    handleImportHidden() {
      this.$refs.upload.handleRemove()
      this.salesFlowTemplateMappingId = ''
      this.isCover = false
      this.version = []
      this.versionList = []
      this.dialogUploadFormVisible = false
      this.handleFilter()
    },
    handleuploadSalesFlow() {
      if (this.salesFlowTemplateMappingId === '') {
        this.$notice.message('请选择模板。', 'error')
      } else if (this.isCover && this.version.length === 0) {
        this.$notice.message('请选择版本。', 'error')
      } else if (this.file === null) {
        this.$notice.message('请选择文件。', 'error')
      } else {
        const formData = new FormData()
        formData.append('templateId', this.salesFlowTemplateMappingId)
        formData.append('isCover', this.isCover)
        formData.append('version', this.version)
        FileService.uploadTemplate(this.file.raw, formData, this.controller, this.method).then(res => {
          this.handleImportHidden()
          this.handleFilter()
          this.salesFlowTemplateMappingId = ''
          this.isCover = false
          this.version = []
          this.versionList = []
          this.file = null
          this.$refs.upload.handleRemove()
        }).catch(() => {
          this.file = null
          this.isCover = false
          this.version = []
          this.$refs.upload.handleRemove()
        })
      }
    },
    coverChange() {
      if (!this.isCover) {
        this.version = []
      }
    }
  }
}
</script>
