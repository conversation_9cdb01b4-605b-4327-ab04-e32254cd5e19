<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filter-container">
        <el-col :span="span">
          <el-input
            v-model="listQuery.rebateAgreementCode"
            clearable
            placeholder="协议编码"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.rebateAgreementName"
            clearable
            placeholder="协议名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.rebateReceiverName"
            clearable
            placeholder="乙方名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="listQuery.dateRange"
            clearable
            class="filter-item"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row type="flex" class="filter-container">
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
            @selection-change="handleSelectionChange"
          >
            <!-- <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" /> -->
            <el-table-column
              type="selection"
              width="55"
            />
            <el-table-column sortable="custom" label="部门" min-width="100px" align="center" prop="RebateAgreement.Department.Name">
              <template slot-scope="{ row }">
                <span>{{ row.departmentName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" label="协议编码" min-width="100px" align="center" prop="RebateAgreement.Code">
              <template slot-scope="{ row }">
                <span>{{ row.rebateAgreementCode }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" label="协议名称" min-width="160px" align="center" prop="RebateAgreement.Name">
              <template slot-scope="{ row }">
                <span>{{ row.rebateAgreementName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" label="起始时间" min-width="100px" align="center" prop="CalculatedStartDate">
              <template slot-scope="{ row }">
                <span>{{ row.calculatedStartDate | parseTime("{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" label="截止时间" min-width="100px" prop="CalculatedEndDate" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.calculatedEndDate | parseTime("{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" label="支付周期" min-width="100px" align="center" prop="EnumRebateComputeCycleFlag" header-align="center">
              <template slot-scope="{ row }">
                <span>{{ row.enumRebateComputeCycleFlagDesc }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>

  </div>
</template>
<script>
import RebateService from '@/api/rebate'
import Pagination from '@/components/Pagination'

export default {
  components: {
    Pagination
  },
  props: {
    showImport: {
      type: Boolean,
      default: null
    }
  },
  data() {
    return {
      span: 4,
      listLoading: true,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      list: [],
      importVisable: false,
      multipleSelection: []
    }
  },
  watch: {
    showImport: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        this.importVisable = val
        if (this.importVisable === true) {
          this.getList()
        }
      }
    }
  },
  methods: {
    init() {
      this.getList()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList() {
      this.listLoading = true
      RebateService.QueryToBeCalculatedRebateResultTask(this.listQuery).then(res => {
        this.listLoading = false
        this.list = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(res => {})
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    clear() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleCheck() {
      if (this.multipleSelection.length === 0) {
        this.showMessage('请选择需要计算的任务', 'error')
        return
      }
      var taskIds = []
      this.multipleSelection.forEach(item => {
        taskIds.push(item.id)
      })
      this.$emit('success', JSON.parse(JSON.stringify(taskIds)))
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    }
  }
}
</script>
<style scoped>
  .el-dialog-s {
  z-index: 11;
}
</style>
