<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      title="返利计算任务查看"
      :visible="dialogVisible"
      width="80%"
      @close="handleClose"
    >
      <el-row style="margin-bottom:20px;">
        <el-col :span="24">
          <el-table
            :data="rebateResultTasks"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
          >
            <el-table-column fixed label="序号" type="index" align="center" />
            <el-table-column label="返利协议名称" min-width="220px" header-align="center" align="left">
              <template slot-scope="{ row }">
                <span>{{ row.rebateAgreementName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="起始日期" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span v-if="row.calculatedEndDate">{{
                  row.calculatedStartDate | parseTime("{y}-{m}-{d}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="截止日期" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span v-if="row.calculatedEndDate">{{
                  row.calculatedEndDate | parseTime("{y}-{m}-{d}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="计算周期" min-width="100px" header-align="center" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.enumRebateComputeCycleFlagDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="计算状态" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span>{{ row.enumStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="计算日期" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span v-if="row.calculateDate">{{
                  row.calculateDate | parseTime("{y}-{m}-{d}")
                }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-row>
        <el-form
          ref="dataForm"
          :model="rebateResult"
          label-position="left"
          label-width="100px"
          class="el-dialogform"
          style="width:95%"
        >
          <div slot="footer" class="dialog-footer">
            <el-button icon="el-icon-close" @click="handleClose()"> 关闭 </el-button>
          </div>
        </el-form></el-row></el-dialog>
  </div>
</template>
<script>
import RebateService from '@/api/rebate'

export default {
  data() {
    return {
      span: 8,
      dialogVisible: false,
      rebateResultTasks: [],
      filter: {
        pageIndex: 1,
        pageSize: 1000,
        order: '-EnumRebateComputeCycleFlag'
      },
      listLoading: false,
      rebateResult: null
    }
  },
  methods: {
    initPage(agreementId) {
      this.getResultTaskList(agreementId)
      this.dialogVisible = true
    },
    getResultTaskList(agreementId) {
      this.listLoading = true
      this.filter.rebateAgreementId = agreementId
      RebateService.QueryRebateResultTaskByAgreementId(this.filter).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.rebateResultTasks = result.data.datas
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleClose() {
      this.dialogVisible = false
      this.rebateResultTasks = []
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    }
  }
}
</script>
<style scoped>
</style>
