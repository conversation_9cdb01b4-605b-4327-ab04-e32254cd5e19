<template>
  <div class="search-container-bg">
    <el-row type="flex" :gutter="10" class="home-flex-container">
      <el-col :span="6">
        <el-date-picker
          v-model="filter.timeRange"
          class="filter-item"
          style="width:100%"
          type="monthrange"
          :clearable="false"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          format="yyyy-MM"
          value-format="yyyy-MM"
        />
      </el-col>
      <el-col :span="4">
        <customProductSpecMutiple :product-spec-all-checked="productSpecAllChecked" @input="handleProductSpecChange" />
      </el-col>
      <el-col :span="4">
        <el-select
          v-model="filter.departmentId"
          value-key="value"
          class="filter-item"
          placeholder="部门"
          style="width:100%"
          :clearable="isManager && deptList.length>1"
          @change="departmentChange"
        >
          <el-option
            v-for="item in deptList"
            :key="item.key"
            :label="item.name"
            :value="item.key"
          />
        </el-select>
      </el-col>
      <el-col v-if="isManager || isDepartmentManager" :span="3">
        <el-select
          v-model="filter.areaIds"
          class="filter-item"
          placeholder="大区"
          multiple
          style="width:100%"
          :disabled="filter.departmentId === null || filter.departmentId ===undefined || filter.departmentId === ''"
          clearable
          @change="areaChange"
        >
          <el-option
            v-for="item in areaList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-col>
      <el-col :span="5">
        <customProvinceMutiple
          v-model="filter.selectProvinceIds"
          :province-list="provinceList"
          @input="handleProvinceChange"
        />
      </el-col>
      <el-col :span="2">
        <el-button v-if="$isPermitted($store.getters.user, 'Dashboard_Button_Query')" :loading="dataLoading" class="filter-item-button" type="primary" icon="el-icon-search" @click="search">
          查询
        </el-button>
      </el-col>
    </el-row>
    <el-row>
      <el-col v-if="$isPermitted($store.getters.user, 'Dashboard_Button_ClearCache')" :span="24" style="text-align: right;">
        <span style="font-size: 11px;padding-right:5px; line-height: 32px;">数据更新至： {{ salesFlowCutoffDate }}</span>
        <el-button v-if="$isPermitted($store.getters.user, 'Dashboard_Button_ClearCache')" icon="el-icon-refresh" style="float: right;" :loading="dataLoading" class="filter-item-button" type="primary" @click="clearCache">
          清除缓存
        </el-button>
      </el-col>
      <el-col v-if="!$isPermitted($store.getters.user, 'Dashboard_Button_ClearCache')" :span="24" style="text-align: right;">
        <span style="float: right;font-size: 11px;padding-right:5px; line-height: 32px;">数据更新至： {{ salesFlowCutoffDate }}</span>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <incomeExpenses ref="refIncomeExpenses" />
      </el-col>
    </el-row>
    <el-row type="flex" :gutter="10" class="home-flex-container">
      <el-col :span="8">
        <div>
          <glideWarning ref="refGlideWarning" title="目标客户销量下滑预警" />
        </div>
      </el-col>
      <el-col :span="8">
        <div>
          <productionLowerWarning ref="refProductionLowerWarning" title="岗位产值过低预警" />
        </div>
      </el-col>
      <el-col :span="8">
        <div>
          <monthWarning ref="refMonthWarning" />
        </div>
      </el-col>
    </el-row>
    <el-row type="flex" :gutter="10" class="home-flex-container">
      <el-col :span="12">
        <div>
          <lowSalesWarning ref="refTargetReceiverLowSalesWarning" title="目标客户销量过低预警" />
        </div>
      </el-col>
      <el-col :span="12">
        <div>
          <salesSameCompare ref="refSalesSameCompare" />
        </div>
      </el-col>
    </el-row>
    <el-row v-show="hasRetailDepartment" :gutter="10" type="flex" class="home-flex-container">
      <el-col :span="13">
        <performanceWarning ref="refPerformanceWarning" />
      </el-col>
      <el-col :span="11">
        <chainHeadquartersCoverage ref="refChainHeadquartersCoverage" />
      </el-col>
    </el-row>
    <el-row type="flex" :gutter="10" class="home-flex-container">
      <el-col :span="12">
        <salesGrowthRate1 ref="refSalesGrowthRate1" />
      </el-col>
      <el-col v-if="isManager" :span="12">
        <projectRate ref="refProjectRate" />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import ProductService from '@/api/product'
import MaintenanceService from '@/api/maintenance'
import LocationService from '@/api/location'
import dashboardService from '@/api/dashboard'
import ReportService from '@/api/report'
import HomeService from '@/api/home'

import salesSameCompare from './components/salesSameCompare'
import projectRate from './components/projectRate'

import salesGrowthRate1 from './components/salesGrowthRate1'

// 新组件
import lowSalesWarning from './components/lowSalesWarning.vue'
import monthWarning from './components/monthWarning.vue'
import performanceWarning from './components/performanceWarning.vue'
import chainHeadquartersCoverage from './components/chainHeadquartersCoverage.vue'
import glideWarning from './components/glideWarning.vue'
import incomeExpenses from './components/incomeExpenses.vue'
import productionLowerWarning from './components/productionLowerWarning.vue'

import customProductSpecMutiple from '@/components/CustomProductSpecMutiple'
import customProvinceMutiple from '@/components/CustomProvinceMutiple'

export default {
  name: 'Home',
  components: {
    salesSameCompare,
    projectRate,
    salesGrowthRate1,
    lowSalesWarning,
    monthWarning,
    performanceWarning,
    chainHeadquartersCoverage,
    glideWarning,
    incomeExpenses,
    productionLowerWarning,
    customProductSpecMutiple,
    customProvinceMutiple
  },
  data() {
    return {
      dataLoading: false,
      productAndSpecList: [],
      filter: {
        productAndSpecList: [],
        timeRange: [],
        isSelectedAllProvince: true
      },
      deptList: [],
      areaList: [],
      allAreaList: [],
      provinceList: [],
      analyticsCardData: {},
      analyticsCardDataLoading: false,
      salesFlowCutoffDate: '',
      employeeRole: {},
      isManager: false,
      isDepartmentManager: false,
      topCount: 1,
      hasRetailDepartment: false,
      productSpecAllChecked: true,
      productSpecIds: []
    }
  },
  created() {
    this.getSalesFlowUpdateDate()
  },
  async mounted() {
    const date = new Date()
    const year = date.getFullYear()

    const month = date.getMonth() <= 1 ? 1 : date.getMonth() - 1
    this.filter.timeRange = [`${year}-${'01'}`, `${year}-${month}`]

    await this.initProductAndSpec()
    await this.getSysSettingTopCount()
    await this.getEmployeeRole()
    await this.initDept()
    await this.initArea()
    await this.search()
  },
  methods: {
    async getSysSettingTopCount() {
      await dashboardService.GetSysSettingTopCount(this.filter)
        .then((res) => {
          this.topCount = res.data
        })
    },
    async getEmployeeRole() {
      await HomeService.GetCurrentEmployeeRole().then(async res => {
        this.employeeRole = res.data
        if (this.employeeRole.isSFE ||
        this.employeeRole.isSeniorManagement ||
        this.employeeRole.isSysAdmin ||
        this.employeeRole.isDataAdmin ||
        this.employeeRole.isDepartmentManager
        ) {
          this.isManager = true
        }
        // 部门总监
        this.isDepartmentManager = this.employeeRole.isDepartmentManager
      })
    },
    success(value) {
      if (value) {
        this.$nextTick(() => {
          this.$refs.refSalesGrowthRate.initPage(this.filter)
        })
      } else {
        this.$nextTick(() => {
          this.$refs.refSalesGrowthRate1.initPage(this.filter)
        })
      }
    },
    search() {
      if (this.filter.selectProvinceIds !== undefined && this.filter.selectProvinceIds !== null) {
        this.filter.provinceIds = this.filter.selectProvinceIds
      } else {
        this.$notice.message('省份不可为空，请选择省份信息', 'error')
        return
      }

      if (this.productSpecIds === undefined || this.productSpecIds.length <= 0) {
        this.$notice.message('产品规格不可为空，请选择品规信息', 'error')
        return
      } else if (this.productSpecAllChecked) {
        this.filter.productSpecIds = []
      } else {
        this.filter.productSpecIds = this.productSpecIds
      }

      if (this.filter.selectProvinceIds.length === this.provinceList.length) {
        this.filter.isSelectedAllProvince = true
      } else {
        this.filter.isSelectedAllProvince = false
      }

      this.initDashboard()
    },
    getSalesFlowUpdateDate() {
      ReportService.GetSalesFlowUpdateDate()
        .then((res) => {
          this.salesFlowCutoffDate = res.data
        })
    },
    async initDept() {
      await MaintenanceService.QueryDepartmentForReport()
        .then(async result => {
          this.deptList = result
          // 判断是否包含零售部，不含零售部，零售部下的特有看板模块不显示
          var retailIndex = this.deptList.findIndex(x => x.value === this.$constDefinition.departmentCode.Retail)
          if (retailIndex !== -1) {
            this.hasRetailDepartment = true
          }

          if (!this.isManager) {
            if (this.deptList.length > 0) {
              this.filter.departmentId = this.deptList[0].key
            }
          } else {
            if (this.deptList.length === 1) {
              this.filter.departmentId = this.deptList[0].key
            }
          }
          await this.queryProvinceByDepartmentAndAreas()
        })
        .catch(() => {
        })
    },
    async initArea() {
      await MaintenanceService.QueryAreasForReport()
        .then(async result => {
          this.allAreaList = result
          if (this.isDepartmentManager) {
            this.filter.areaIds = []
            this.areaList = this.allAreaList.filter(item => { return item.departmentId === this.filter.departmentId })
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    async initProductAndSpec() {
      await ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then(result => {
          this.productAndSpecList = result
        })
        .catch(() => {
        })
    },
    initDashboard() {
      dashboardService.PreloadSalesData(this.filter)
        .then((res) => {
          this.$nextTick(() => {
            const filterTemp = JSON.parse(JSON.stringify(this.filter))
            this.$refs.refIncomeExpenses.initPage(filterTemp)
            this.$refs.refSalesGrowthRate1.initPage(filterTemp)
            this.$refs.refProductionLowerWarning.initPage(filterTemp, this.productAndSpecList, this.topCount)
            this.$refs.refMonthWarning.initPage(filterTemp, this.productAndSpecList, this.topCount)
            this.$refs.refSalesSameCompare.initPage(filterTemp, this.deptList, this.productAndSpecList)
            if (this.isManager) {
              this.$refs.refProjectRate.initPage(filterTemp, this.topCount)
            }
            this.$refs.refTargetReceiverLowSalesWarning.initPage(filterTemp, this.productAndSpecList, this.topCount)
            this.$refs.refGlideWarning.initPage(filterTemp, this.productAndSpecList, this.topCount)
            this.$refs.refPerformanceWarning.initPage(filterTemp, this.productAndSpecList, this.topCount)
            this.$refs.refChainHeadquartersCoverage.initPage(filterTemp, this.productAndSpecList, this.topCount)
            this.$refs.refProductionLowerWarning.initPage(filterTemp, this.provinceList, this.deptList, this.topCount)
          })
        })
        .catch(() => {
          this.analyticsCardDataLoading = false
        })
    },
    departmentChange() {
      this.filter.selectProvinceIds = []

      if (this.isManager || this.isDepartmentManager) {
        this.filter.areaIds = []
        this.areaList = this.allAreaList.filter(item => { return item.departmentId === this.filter.departmentId })
      }
      this.queryProvinceByDepartmentAndAreas()

      // 判断是否包含零售部，不含零售部，零售部下的特有看板模块不显示
      var retailIndex = this.deptList.findIndex(x => x.value === this.$constDefinition.departmentCode.Retail && (this.filter.departmentId === '' || this.filter.departmentId === null || x.key === this.filter.departmentId))
      if (retailIndex !== -1) {
        this.hasRetailDepartment = true
      } else {
        this.hasRetailDepartment = false
      }
    },
    areaChange() {
      this.filter.selectProvinceIds = []
      if (this.filter.areaIds === null || this.filter.areaIds === undefined || this.filter.areaIds.length === 0) {
        this.provinceList = []
      }
      this.queryProvinceByDepartmentAndAreas()
    },
    async queryProvinceByDepartmentAndAreas() {
      this.dataLoading = true
      await LocationService.QueryProvinceSelectForDataReport(this.filter)
        .then((result) => {
          this.provinceList = result
          this.filter.selectProvinceIds = result.map(item => { return item.key })
          this.dataLoading = false
        })
        .catch((error) => {
          console.log(error)
          this.dataLoading = false
        })
    },
    handleProvinceChange(provinceIDs) {
      this.filter.selectProvinceIds = provinceIDs
    },
    handleProductSpecChange(specIds, isCheckChanged) {
      this.productSpecIds = specIds
      this.productSpecAllChecked = isCheckChanged
    },
    clearCache() {
      dashboardService.ClearDashboardRedis()
    }
  }
}
</script>
  <style scoped>
  .box ul {
    display: grid;
    justify-content: space-evenly;
    padding: 10px;
    grid-template-columns: repeat(auto-fill, 160px);
    grid-gap: 10px;
    text-align: center;
  }
  .box li {
    width:160px;
    list-style: none;
    margin-bottom: 10px;
  }

  .scrollable-div {
    background-color: white;
    height: 50%; /* 设置div的高度 */
    overflow-y: auto; /* 当内容超出垂直高度时显示滚动条 */
}

  .appImgContainer{
    height: 120px;
    padding :5px 0px;
  }

  .appImgContainer img{
    width: 100px;
    height: 100px;
  }

  .appName{
    font-size: 18px;
    font-weight: bold;
  }
  .home-flex-container{
    flex-wrap: wrap;
  }

   .home-flex-container .el-col{
      margin-bottom: 10px;
    }
  </style>
