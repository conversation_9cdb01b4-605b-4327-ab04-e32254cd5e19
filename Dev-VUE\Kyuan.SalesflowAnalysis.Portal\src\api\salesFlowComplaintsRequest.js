import HttpApi from './libs/api.request'

const controller = 'SalesFlowComplaintsRequest'

const api = new HttpApi(controller)

export default {
  QuerySalesFlowComplaintsRequest(params) {
    return api.get('QuerySalesFlowComplaintsRequest', params)
  },
  GetSalesFlowComplaintsRequest(params) {
    return api.get('GetSalesFlowComplaintsRequest', params)
  },
  ApproveSalesFlowComplaintsRequest(params) {
    return api.post('ApproveSalesFlowComplaintsRequest', params)
  },
  RefuseSalesFlowComplaintsRequest(params) {
    return api.post('RefuseSalesFlowComplaintsRequest', params)
  },
  DownloadComplaintsZip(params) {
    return api.post('DownloadComplaintsZip',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  DownloadComplaintsZipForRequest(params) {
    return api.post('DownloadComplaintsZipForRequest',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  QueryAppealMonthCascader() {
    return api.get('QueryAppealMonthCascader')
  },

  SaveAppealCompleted(params) {
    return api.post('SaveAppealCompleted', params)
  },

  CheckSalesFlowComplaintsRequest(params) {
    return api.post('CheckSalesFlowComplaintsRequest', params)
  },

  SaveAppealCompletedAndRejectForm(params) {
    return api.post('SaveAppealCompletedAndRejectForm', params)
  },
  GetSalesFlowComplaintsRequestExportColumn() {
    return api.get('GetSalesFlowComplaintsRequestExportColumn')
  },
  ExportSalesFlowComplaintsRequest(params) {
    return api.post('ExportSalesFlowComplaintsRequest', {
      data: params,
      responseType: 'arraybuffer'
    })
  }
}
