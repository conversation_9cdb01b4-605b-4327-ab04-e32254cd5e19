<template>
  <div>
    <el-dialog custom-class="el-dialog-s" :title="title" width="80%" append-to-body :close-on-click-modal="false" :visible="showAddDialog" @close="closeAddAgreementDialog">
      <el-form
        ref="dataForm"
        :model="agreement"
        label-position="right"
        label-width="100px"
        class="el-dialogform"
      >
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="协议标题" prop="name">
              <el-input
                v-model="agreement.name"
                placeholder="协议标题"
                class="filter-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="部门" prop="departmentId">
              <el-select
                v-model="agreement.departmentId"
                placeholder="部门"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in deptList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="代付方" prop="behalfPay">
              <el-row>
                <el-col :span="20" style="padding-right:5px">
                  <el-input
                    v-model="agreement.behalfPay"
                    readonly
                    placeholder="代付方"
                    maxlength="300"
                  />
                </el-col>
                <el-col :span="4">
                  <el-button
                    class="filter-item"
                    type="primary"
                    icon="el-icon-search"
                    title="选择代付方"
                    @click="handleSelectReceiver"
                  />
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="返利接收方" prop="payee">
              <el-row>
                <el-col :span="20" style="padding-right:5px">
                  <el-input
                    v-model="agreement.payee"
                    readonly
                    placeholder="返利接收方"
                    maxlength="300"
                  />
                </el-col>
                <el-col :span="4">
                  <el-button
                    class="filter-item"
                    type="primary"
                    icon="el-icon-search"
                    title="选择接收方"
                    @click="handleSelectReceiver"
                  />
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="起始日期" prop="startDate">
              <el-date-picker
                v-model="agreement.startDate"
                type="date"
                style="width: 100%"
                placeholder="起始日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="截止日期" prop="endDate">
              <el-date-picker
                v-model="agreement.endDate"
                type="date"
                style="width: 100%"
                placeholder="截止日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="支付周期" prop="enumPaymentCycle">
              <el-select
                v-model="agreement.enumPaymentCycle"
                placeholder="支付周期"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in paymentCycleList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="协议文本">
              <el-button
                type="primary"
                icon="el-icon-upload2"
              >
                上传文件
              </el-button>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="">
              <el-table
                :data="files"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
              >
                <el-table-column label="文件名称" min-width="180px" align="center">
                  <template slot-scope="{ row }">
                    <a
                      style="text-decoration:underline"
                      @click="handleDownLoadTemplate(row.id,row.fileName)"
                    >{{ row.fileName }}</a>
                  </template>
                </el-table-column>
                <el-table-column label="上传时间" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.createTime | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-row type="flex" justify="end">
        <el-col :span="24" class="el-colRight">
          <el-button
            class="filter-item"
            type="primary"
            icon="el-icon-search"
          >
            历史协议
          </el-button>
          <el-button
            class="filter-item"
            type="primary"
            icon="el-icon-plus"
          >
            新增政策
          </el-button>
        </el-col>
      </el-row>
      <el-collapse v-model="activeName" accordion style="margin-top:10px">
        <el-collapse-item title="政策一：阶梯式销量返利" name="1" style="font-size: 12px;">
          <el-row type="flex" justify="end">
            <el-col :span="24" class="el-colRight">
              <el-button
                class="filter-item"
                type="primary"
                icon="el-icon-edit"
              >
                编辑
              </el-button>
              <el-button
                class="filter-item"
                type="primary"
                icon="el-icon-delete"
              >
                删除
              </el-button>
            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col>
              <span>品规列表</span>
            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col :span="24">
              <el-table
                :data="products"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
              >
                <el-table-column
                  fixed
                  label="序号"
                  type="index"
                  align="center"
                />
                <el-table-column label="产品名称" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.productNameCn }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="规格" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.spec }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="厂商" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.manufacturerName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="供货价(元)" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.supplyPrice }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="销售下限" min-width="80px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.minimumPurchaseQuantity }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="折扣金额(元)" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.rebateUnitPrice }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="总指标" min-width="80px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.quotaQuantity }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="一季度指标" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.q1Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="二季度指标" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.q2Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="三季度指标" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.q3Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="四季度指标" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.q4Quota }}</span>
                  </template>
                </el-table-column>
              </el-table>

            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col>
              <span>目标终端</span>
            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col :span="24">
              <el-table
                :data="targetTerminals"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
              >
                <el-table-column
                  fixed
                  label="序号"
                  type="index"
                  align="center"
                />
                <el-table-column label="编码" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.code }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="终端名称" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.name }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="省份" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.provinceName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="城市" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.cityName }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col>
              <span>计算公式</span>
            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col :span="24">
              <el-table
                :data="formulas"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
              >
                <el-table-column
                  fixed
                  label="序号"
                  type="index"
                  align="center"
                />
                <el-table-column label="条件" align="center" min-width="300px">
                  <template slot-scope="{ row }">
                    <span>{{ row.condition }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="结果" align="center" min-width="200px">
                  <template slot-scope="{ row }">
                    <span>{{ row.expression }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-collapse-item>

        <el-collapse-item title="政策二：销量组" name="2">
          <el-row type="flex" justify="end">
            <el-col :span="24" class="el-colRight">
              <el-button
                class="filter-item"
                type="primary"
                icon="el-icon-edit"
              >
                编辑
              </el-button>
              <el-button
                class="filter-item"
                type="primary"
                icon="el-icon-delete"
              >
                删除
              </el-button>
            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col>
              <span>品规列表</span>
            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col :span="24">
              <el-table
                :data="products"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
              >
                <el-table-column
                  fixed
                  label="序号"
                  type="index"
                  align="center"
                />
                <el-table-column label="产品名称" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.productNameCn }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="规格" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.spec }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="厂商" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.manufacturerName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="供货价(元)" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.supplyPrice }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="销售下限" min-width="80px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.minimumPurchaseQuantity }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="折扣金额(元)" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.rebateUnitPrice }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="总指标" min-width="80px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.quotaQuantity }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="一季度指标" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.q1Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="二季度指标" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.q2Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="三季度指标" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.q3Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="四季度指标" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.q4Quota }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col>
              <span>计算公式</span>
            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col :span="24">
              <el-table
                :data="formulas"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
              >
                <el-table-column
                  fixed
                  label="序号"
                  type="index"
                  align="center"
                />
                <el-table-column label="条件" align="center" min-width="300px">
                  <template slot-scope="{ row }">
                    <span>{{ row.condition }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="结果" align="center" min-width="200px">
                  <template slot-scope="{ row }">
                    <span>{{ row.expression }}</span>
                  </template>
                </el-table-column>
              </el-table>

            </el-col>
          </el-row>
        </el-collapse-item>
      </el-collapse>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeAddAgreementDialog">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <EditRebateAgreementPolicy ref="refEditRebateAgreementPolicy" />
  </div>
</template>
<script>
import MaintenanceService from '@/api/maintenance'
import EditRebateAgreementPolicy from './editRebateAgreementPolicy'

export default {
  components: {
    EditRebateAgreementPolicy
  },
  data() {
    return {
      span: 12,
      agreement: {},
      deptList: [],
      paymentCycleList: [
        {
          '$id': '2',
          'key': 'ee395bcb-15bb-41f8-bf3b-aee6127a485a',
          'value': '年付'
        },
        {
          '$id': '3',
          'key': 'ee395bcb-15bb-41f8-bf3b-aee6127a485b',
          'value': '季付'
        }
      ],
      title: '新增协议',
      showAddDialog: false,
      activeName: '1',
      files: [{ fileName: '三方协议文件.pdf', createTime: '2022-01-01 10:40:00' }],
      products: [{ productNameCn: '来士普', spec: '10mg*7s', manufacturerName: '灵北', supplyPrice: '77.08', minimumPurchaseQuantity: 500, rebateUnitPrice: 5, quotaQuantity: 400, q1Quota: 100, q2Quota: 100 },
        { productNameCn: '易倍申', spec: '10mg*28片', manufacturerName: '灵北', supplyPrice: '252.08', minimumPurchaseQuantity: 700, rebateUnitPrice: 3, quotaQuantity: 800, q1Quota: 300, q2Quota: 200 }],
      targetTerminals: [],
      formulas: [{ condition: '销售达成＞=100% and ＜80%', expression: '销售量*2' }]
    }
  },
  methods: {
    initPage(title) {
      this.title = title
      this.showAddDialog = true
      this.initDept()
    },
    initDept() {
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptList = result
        })
        .catch(() => {

        })
    },
    handleSelectReceiver() {

    },
    closeAddAgreementDialog() {
      this.showAddDialog = false
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleAddPolicy() {
      this.$refs.refEditRebateAgreementPolicy.init('新增政策')
    }
  }
}
</script>
<style scoped>
.el-collapse-item ::v-deep .el-collapse-item__header {
  font-size: 14px !important;
  font-weight: 600 !important;
}
</style>
