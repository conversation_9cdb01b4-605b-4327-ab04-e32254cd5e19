import HttpApi from './libs/api.request'

const controller = 'SalesFlowTemplateMapping'

const api = new HttpApi(controller)

export default {
  QueryStandardTemplateSelect(params) {
    return api.get('QueryStandardTemplateSelect', params)
  },
  QuerySalesFlowTemplateMappingSelect(params) {
    return api.get('QuerySalesFlowTemplateMappingSelect', params)
  },
  QuerySalesFlowTemplateMapping(params) {
    return api.get('QuerySalesFlowTemplateMapping', params)
  },
  GetSalesFlowTemplateMapping(params) {
    return api.get('GetSalesFlowTemplateMapping', params)
  },
  AddSalesFlowTemplateMapping(params) {
    return api.post('AddSalesFlowTemplateMapping', params)
  },
  UpdateSalesFlowTemplateMapping(params) {
    return api.post('UpdateSalesFlowTemplateMapping', params)
  },
  DeleteSalesFlowTemplateMapping(params) {
    return api.post('DeleteSalesFlowTemplateMapping', params)
  }
}
