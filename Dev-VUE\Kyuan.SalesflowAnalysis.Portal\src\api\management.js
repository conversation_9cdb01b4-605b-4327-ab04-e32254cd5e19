import HttpApi from './libs/api.request'

const controller = 'Management'

const api = new HttpApi(controller)

export default {
  QueryRole(params) {
    return api.get('QueryRole', params)
  },
  AddRole(params) {
    return api.post('AddRole', params)
  },
  UpdateRole(params) {
    return api.post('UpdateRole', params)
  },
  DeleteRole(params) {
    return api.post('DeleteRole', params)
  },
  GetRolePermissions(params) {
    return api.get('GetRolePermissions', params)
  },
  QueryPermissions(params) {
    return api.get('QueryPermissions', params)
  },
  SetRolePermissions(params) {
    return api.post('SetRolePermissions', params)
  },
  QueryRoleMember(params) {
    return api.get('QueryRoleMember', params)
  },
  QueryMember(params) {
    return api.get('QueryMember', params)
  },
  AddRoleMember(params) {
    return api.post('AddRoleMember', params)
  },
  DeleteRoleMember(params) {
    return api.post('DeleteRoleMember', params)
  },
  QueryRoleEmployee(params) {
    return api.get('QueryRoleEmployee', params)
  },
  QueryEmployeePermissions(params) {
    return api.get('QueryEmployeePermissions', params)
  },
  QueryUserSelect(params) {
    return api.get('QueryUserSelect', params)
  },
  QueryEmployeeSelect(params) {
    return api.get('QueryEmployeeSelect', params)
  }
}
