<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filter-container">
        <el-col :span="span">
          <el-input
            v-model="filter.code"
            clearable
            placeholder="模板编码"
            class="filter-item"
            @keyup.enter.native="search"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="filter.name"
            clearable
            placeholder="模板名称"
            class="filter-item"
            @keyup.enter.native="search"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="filter.rebateAgreementTemplateTypeName"
            clearable
            placeholder="模板类型"
            class="filter-item"
            @keyup.enter.native="search"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="filter.enumSigningPartiesType"
            class="filter-item"
            placeholder="签署方"
            clearable
          >
            <el-option
              v-for="item in signingPartiesTypeList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="filter.enumStatus"
            class="filter-item"
            placeholder="状态"
            clearable
          >
            <el-option
              v-for="item in agreementTemplateStatusList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'AgreementTemplate_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="search"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'AgreementTemplate_Button_Add')"
            type="primary"
            icon="el-icon-plus"
            @click="handleAddTemplate"
          >
            新增
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'AgreementTemplate_Button_Export')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="agreementTemplateList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              label="模板编码"
              sortable="custom"
              min-width="100px"
              header-align="center"
              align="center"
              prop="Code"
            >
              <template slot-scope="{ row }">
                <span>{{ row.code }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="模板名称"
              header-align="center"
              align="left"
              sortable="custom"
              min-width="140px"
              prop="Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="签署方"
              align="center"
              sortable="custom"
              min-width="100px"
              prop="EnumSigningPartiesType"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumSigningPartiesTypeDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="模板类型"
              align="center"
              sortable="custom"
              min-width="100px"
              prop="RebateAgreementTemplateType"
            >
              <template slot-scope="{ row }">
                <span>{{ row.rebateAgreementTemplateTypeName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="状态"
              align="center"
              sortable="custom"
              min-width="100px"
              prop="EnumStatus"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="120"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i
                  v-if="$isPermitted($store.getters.user, 'AgreementTemplate_Button_Edit') && row.enumStatus === 1"
                  class="el-icon-edit-outline eltablei"
                  title="编辑"
                  @click="handleEditTemplate(row)"
                />
                <i
                  v-if="($isPermitted($store.getters.user, 'AgreementTemplate_Button_Release')) && row.enumStatus === 1"
                  class="el-icon-circle-check eltablei"
                  title="发布"
                  @click="handleReleaseTemplate(row)"
                />
                <i
                  v-if="$isPermitted($store.getters.user, 'AgreementTemplate_Button_Release') && row.enumStatus === 2"
                  class="el-icon-circle-close eltablei"
                  title="取消发布"
                  @click="handleRevokeTemplate(row)"
                />
                <i
                  class="el-icon-document eltablei"
                  title="查看"
                  @click="handleReviewDetail(row)"
                />
                <i v-if="$isPermitted($store.getters.user, 'AgreementTemplate_Button_Del') && row.enumStatus === 1" class="el-icon-delete eltablei" title="删除" @click="handleDelete(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="filter.pageIndex"
            :limit.sync="filter.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>

    <DetailView ref="refDetailView" />
    <AddAgreementPolicyTemplate ref="refAddAgreementPolicyTemplate" @refresh="onRefresh()" />
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出协议模版"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

import AgreementService from '@/api/agreement'
import MasterDataService from '@/api/masterData'

import DetailView from './components/detailView'
import AddAgreementPolicyTemplate from './components/addAgreementTemplate'
import CustomExport from '@/components/Export/CustomExport'

export default {
  name: 'AgreementTemplateQuery',
  components: {
    Pagination,
    DetailView,
    AddAgreementPolicyTemplate,
    CustomExport
  },
  data() {
    return {
      span: 4,
      total: 0,
      filter: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      agreementTemplateList: [],
      agreementTemplateStatusList: [],
      signingPartiesTypeList: [],
      btnExportLoading: false,
      columnDictionary: {},
      showExportModal: false
    }
  },
  created() {
    this.initAgreementTemplateStatus()
    this.initAsigningPartiesType()
    this.search()
  },
  methods: {
    initAgreementTemplateStatus() {
      var param = {
        enumType: 'RebateAgreementTemplateStatus'
      }
      MasterDataService.GetEnumInfos(param)
        .then(result => {
          this.agreementTemplateStatusList = result.data.datas
          this.$forceupdate()
        })
        .catch(() => {})
    },
    initAsigningPartiesType() {
      var param = {
        enumType: 'SigningPartiesType'
      }
      MasterDataService.GetEnumInfos(param)
        .then(result => {
          this.signingPartiesTypeList = result.data.datas
          this.$forceupdate()
        })
        .catch(() => {})
    },
    handleAddTemplate() {
      this.$refs.refAddAgreementPolicyTemplate.init(null)
    },
    handleEditTemplate(row) {
      this.$refs.refAddAgreementPolicyTemplate.init(row.id)
    },
    handleReviewDetail(row) {
      this.$refs.refDetailView.init(row.id)
    },
    search() {
      this.filter.pageIndex = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      this.filter.quotaType = 1
      AgreementService.QueryAgreementTemplate(this.filter)
        .then(res => {
          this.listLoading = false
          this.agreementTemplateList = res.data.datas
          this.total = res.data.recordCount
          this.filter.pageIndex = res.data.pageIndex
        })
        .catch(() => {
          this.listLoading = true
        })
    },
    selectUpdate() {
      this.$forceUpdate()
    },
    indexMethod(index) {
      return (
        (this.filter.pageIndex - 1) * this.filter.pageSize + index + 1
      )
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.filter.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.filter.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.filter.pageSize = val
      this.search()
    },
    onRefresh() {
      this.getList()
    },
    handleReleaseTemplate(row) {
      this.listLoading = true
      AgreementService.ReleaseRebateAgreementTemplate({ id: row.id })
        .then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('模板发布成功', 'success')
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    handleRevokeTemplate(row) {
      this.listLoading = true
      AgreementService.RevokeRebateAgreementTemplate({ id: row.id })
        .then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('模板撤销发布成功', 'success')
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    handleDelete(row) {
      this.$confirm('确定删除此模板信息吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.listLoading = true
          AgreementService.DeleteRebateAgreementTemplate(row)
            .then((result) => {
              this.listLoading = false
              if (result.succeed) {
                this.getList()
                this.$notice.message('删除成功', 'success')
              }
            })
            .catch((error) => {
              this.listLoading = false
              console.log(error)
            })
        })
        .catch((error) => {
          this.listLoading = false
          if (!error.succeed) {
            this.$notice.message('取消删除', 'info')
          }
        })
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      AgreementService.GetRebateAgreementTemplateExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      this.filter.checkedColumns = checkColumns
      AgreementService.ExportRebateAgreementTemplate(this.filter)
        .then(result => {
          this.filter.checkedColumns = null
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '协议模版.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '协议模版.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    }
  }
}
</script>
<style scoped></style>
