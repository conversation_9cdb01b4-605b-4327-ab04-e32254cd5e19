import HttpApi from './libs/api.request'

const controller = 'Product'

const api = new HttpApi(controller)

export default {
  QueryProductAndSpecCascader(params) {
    return api.get('QueryProductAndSpecCascader', params)
  },
  QueryManufacturerProductAndSpecCascader(params) {
    return api.get('QueryManufacturerProductAndSpecCascader', params)
  },
  QueryManufacturerAndProductAndSpecCascader(params) {
    return api.get('QueryManufacturerAndProductAndSpecCascader', params)
  },
  QueryProductCommonNameAndSpecCascader(params) {
    return api.get('QueryProductCommonNameAndSpecCascader', params)
  },
  QueryProducts(params) {
    return api.get('QueryProducts', params)
  },
  AddProduct(params) {
    return api.post('AddProduct', params)
  },
  UpdateProduct(params) {
    return api.post('UpdateProduct', params)
  },
  DeleteProduct(params) {
    return api.post('DeleteProduct', params)
  },
  GetProduct(params) {
    return api.get('GetProduct', params)
  },
  QueryProductSpecs(params) {
    return api.get('QueryProductSpecs', params)
  },
  GetProductExportColumn() {
    return api.get('GetProductExportColumn')
  },
  ExportProducts(params) {
    return api.post('ExportProducts', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryImportProductMasterTemp(params) {
    return api.get('QueryImportProductMasterTemp', params)
  },
  ExportImportProductError(params) {
    return api.post('ExportImportProductError', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryImportProductError(params) {
    return api.get('QueryImportProductError', params)
  },
  AddProductSpec(params) {
    return api.post('AddProductSpec', params)
  },
  AddProductAndSpec(params) {
    return api.post('AddProductAndSpec', params)
  },
  UpdateProductSpec(params) {
    return api.post('UpdateProductSpec', params)
  },
  DeleteProductSpec(params) {
    return api.post('DeleteProductSpec', params)
  }
}
