<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filter-container">
        <el-col :span="8">
          <el-select
            v-model="filter.departmentIds"
            value-key="value"
            class="filter-item"
            placeholder="部门"
            multiple
            clearable
          >
            <el-option
              v-for="item in deptList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="filter.timeRange"
            clearable
            class="filter-item"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @keyup.enter.native="search"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'RebateAccruedExpense_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="search"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="rebateAccruedExpenseList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              label="部门"
              sortable="custom"
              min-width="150px"
              header-align="center"
              align="center"
              prop="departmentName"
            >
              <template slot-scope="{ row }">
                <span>{{ row.departmentName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="计算开始日期"
              align="center"
              sortable="custom"
              min-width="120px"
              prop="StartDate"
            >
              <template slot-scope="{ row }">
                <span v-if="row.startDate">{{
                  row.startDate | parseTime("{y}-{m}-{d}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="计算截止日期"
              align="center"
              sortable="custom"
              min-width="120px"
              prop="EndDate"
            >
              <template slot-scope="{ row }">
                <span>{{
                  row.endDate | parseTime("{y}-{m}-{d}")
                }}</span>
              </template>
            </el-table-column>

            <el-table-column
              label="总金额"
              min-width="150px"
              header-align="center"
              align="right"
              prop="TotalAmount"
            >
              <template slot-scope="{ row }">
                <span>{{ row.totalAmount | toMoney }}元</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="120"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i
                  v-if="$isPermitted($store.getters.user, 'RebateAccruedExpense_Button_Query')"
                  class="el-icon-document eltablei"
                  title="查看明细"
                  @click="handleReview(row)"
                />
                <i
                  v-if="$isPermitted($store.getters.user, 'RebateAccruedExpense_Button_ReCalculate')"
                  class="el-icon-refresh eltablei"
                  title="返利预提计算"
                  @click="handleReCalculateRebateAccruedExpense(row)"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="filter.pageIndex"
            :limit.sync="filter.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :close-on-click-modal="false"
      title="返利预提明细"
      :visible="dialogAccruedExpenseDetailVisible"
      width="80%"
      @close="btnAccruedExpenseDetailClose"
    >
      <div style="margin-bottom:20px;">
        <el-row>
          <el-col :span="24">
            <el-table
              ref="refTable"
              :data="accruedExpenseDetailList"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%;"
              :default-sort="{prop: 'createTime', order: 'descending'}"
              :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass"
              row-key="id"
              :tree-props="{children: 'rebateAccruedExpenseReceiverDetails'}"
              :expand-row-keys="expands"
              @row-click="clickRowHandle"
            >
              <!-- <el-table-column fixed label="序号" type="index" align="center" /> -->
              <el-table-column
                label="产品/规格"
                min-width="100px"
                header-align="center"
                align="left"
                prop="ProductAndSpec"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.productAndSpec }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="终端"
                min-width="150px"
                header-align="center"
                align="left"
                prop="ReceiverName"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.receiverName }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="CanBeSpentAmount" label="可支付总金额（元）" align="right" header-align="center" min-width="120px">
                <template slot-scope="{ row }">
                  <span>{{ row.canBeSpentAmount | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="PayableAmount" label="应付金额（元）" align="right" header-align="center" min-width="100px">
                <template slot-scope="{ row }">
                  <span>{{ row.payableAmount | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="ActualPaymentAmount" label="实际支付金额（元）" align="right" header-align="center" min-width="120px">
                <template slot-scope="{ row }">
                  <span>{{ row.actualPaymentAmount | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="PaiddRatio" label="已支付比例" align="right" header-align="center" min-width="120px">
                <template slot-scope="{ row }">
                  <span v-if="row.payableAmount === 0">-</span>
                  <span v-else>{{ row.paiddRatio }}%</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnAccruedExpenseDetailClose()"> 关闭 </el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      :visible="showExportModal"
      :close-on-click-modal="false"
      title="导出预提明细"
      width="800"
      @close="handleExportCancel"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>
<script>
import RebateService from '@/api/rebate'
import MaintenanceService from '@/api/maintenance'
import Pagination from '@/components/Pagination'
import CustomExport from '@/components/Export/CustomExport'

export default {
  name: 'RebateResult',
  components: {
    Pagination,
    CustomExport

  },
  data() {
    return {
      span: 4,
      total: 0,
      filter: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      rebateAccruedExpenseList: [],
      deptList: [],
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: {},
      dialogAccruedExpenseDetailVisible: false,
      accruedExpenseDetailList: [],
      expands: [],
      rebateAccruedExpenseFilter: {
        startDate: null,
        endDate: null,
        departmentCode: null
      }
    }
  },
  created() {
    this.initDept()
    this.search()
  },
  methods: {
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch(() => {
          this.deptLoading = false
        })
    },
    search() {
      this.filter.pageIndex = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      RebateService.QueryRebateAccruedExpense(this.filter)
        .then(res => {
          this.listLoading = false
          this.rebateAccruedExpenseList = res.data.datas
          this.total = res.data.recordCount
          this.filter.pageIndex = res.data.pageIndex
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    indexMethod(index) {
      return (this.filter.pageIndex - 1) * this.filter.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.filter.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.filter.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.filter.pageSize = val
      this.search()
    },
    handleReview(row) {
      this.listLoading = true
      RebateService.QueryRebateAccruedExpenseDetail({ rebateAccruedExpenseId: row.id })
        .then(res => {
          this.listLoading = false
          this.accruedExpenseDetailList = res
          this.dialogAccruedExpenseDetailVisible = true
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      RebateService.GetRebateAccruedExpenseColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      var exportParam = JSON.parse(JSON.stringify(this.filter))
      exportParam.checkedColumns = checkColumns
      RebateService.ExportRebateAccruedExpense(exportParam)
        .then(result => {
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '预提列表.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '预提列表.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    btnAccruedExpenseDetailClose() {
      this.dialogAccruedExpenseDetailVisible = false
      this.accruedExpenseDetailList = []
      this.expands = []
    },
    clickRowHandle(row, column, event) {
      if (this.expands.includes(row.id)) {
        this.expands = this.expands.filter((val) => val !== row.id)
      } else {
        this.expands.push(row.id)
      }
    },
    clearAccruedExpense() {
      this.rebateAccruedExpenseFilter = {
        startDate: null,
        endDate: null,
        departmentCode: null
      }
    },
    // 计算返利预提
    handleReCalculateRebateAccruedExpense(row) {
      this.rebateAccruedExpenseFilter.startDate = row.startDate
      this.rebateAccruedExpenseFilter.endDate = row.endDate
      this.rebateAccruedExpenseFilter.departmentCode = row.departmentCode

      RebateService.ReCalculateRebateAccruedExpense(this.rebateAccruedExpenseFilter)
        .then(result => {
          if (result.succeed) {
            this.$notice.message('返利预提已计算', 'success')
            this.clearAccruedExpense()
            this.search()
          } else {
            if (result.type !== -3) {
              this.$notice.resultTip(result)
            }
          }
        }).catch(error => {
          console.log(error)
        })
    }
  }
}
</script>
<style scoped>
.flexwarp {
  display: flex;
  flex-wrap: wrap;
}
</style>
