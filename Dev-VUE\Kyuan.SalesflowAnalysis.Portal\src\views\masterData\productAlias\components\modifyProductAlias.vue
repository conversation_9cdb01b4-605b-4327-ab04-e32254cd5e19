<template>
  <div>
    <el-dialog :title="title" width="50%" :close-on-click-modal="false" :visible="true" @close="cancle()">
      <el-form ref="dataForm" :rules="rules" :model="tempData.tempFormModel" label-position="right" label-width="100px" class="el-dialogform">
        <el-row :gutter="10">
          <el-col :span="span">
            <el-form-item label="厂商名称" prop="manufacturerId">
              <el-select
                v-model="tempData.tempFormModel.manufacturerId"
                :loading="manufacturerLoading"
                style="width:100%"
                placeholder="厂商名称"
                @change="manufacturerChange"
              >
                <el-option
                  v-for="item in manufacturerList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="产品/规格" prop="productAndSpecId">
              <el-cascader
                ref="refProductAndSpec"
                :key="cascaderKey"
                v-model="tempData.tempFormModel.productAndSpecId"
                :loading="productAndSpecLoading"
                :options="productAndSpecList"
                placeholder="产品/规格"
                style="width:100%"
                clearable
                class="filter-item"
                :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
                @change="productChange"
              /></el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="通用名" prop="commonName">
              <span>{{ tempData.tempFormModel.commonName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="通用名别名" prop="productAliasName">
              <el-input
                v-model="tempData.tempFormModel.productAliasName"
                placeholder="通用名别名"
                :disabled="viewModel"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="规格别名" prop="productSpecAlias">
              <el-input
                v-model="tempData.tempFormModel.productSpecAlias"
                placeholder="规格别名"
                :disabled="viewModel"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          :loading="btnSaveLoading"
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>

import ManufacturerService from '@/api/manufacturer'
import ProductService from '@/api/product'
import ProductAliasService from '@/api/productAlias'

export default {
  name: '',
  components: {
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    viewModel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validateSpec = (rule, value, callback) => {
      if (!this.tempData.tempFormModel.productAndSpecId.length) {
        callback(new Error('请选择产品/规格'))
      } else {
        callback()
      }
    }
    return {
      span: 24,
      cascaderKey: 0,
      rules: {
        manufacturerId: [
          { required: true, message: '请选择厂商', trigger: 'change' }
        ],
        productAndSpecId: [
          { type: 'array', required: true, validator: validateSpec, trigger: 'change' }
        ],
        productAliasName: [
          { required: true, message: '请输入通用名别名', trigger: 'blur' },
          { max: 50, message: '通用名别名不允许超过50个字符', trigger: 'blur' }
        ],
        productSpecAlias: [
          { required: true, message: '请输入规格别名', trigger: 'blur' },
          { max: 50, message: '规格别名不允许超过50个字符', trigger: 'blur' }
        ]
      },
      tempData: { tempFormModel: { manufacturerId: '' }},
      btnSaveLoading: false,
      manufacturerLoading: false,
      productAndSpecLoading: false,
      manufacturerList: [],
      productAndSpecList: []
    }
  },
  watch: {
    id(val) {
      this.tempData.tempFormModel.id = val
    }
  },
  mounted() {
  },
  created() {
    this.initManufacturer()
    this.init()
  },
  methods: {
    init() {
      if (!this.id) {
        this.clear()
        return
      }
      this.get(this.id)
    },
    initManufacturer() {
      this.manufacturerLoading = true
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturerLoading = false
          this.manufacturerList = result
        })
        .catch((error) => {
          this.manufacturerLoading = false
          console.log(error)
        })
    },
    initProductAndSpec() {
      ++this.cascaderKey
      this.productAndSpecLoading = true
      const para = { manufacturerId: this.tempData.tempFormModel.manufacturerId }
      ProductService.QueryProductAndSpecCascader(para)
        .then((result) => {
          this.cascaderKey = 0
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    manufacturerChange() {
      this.tempData.tempFormModel.productAndSpecId = []
      this.tempData.tempFormModel.commonName = ''
      this.initProductAndSpec()
    },
    productChange(value) {
      if (!value || value.length === 0) {
        this.tempData.tempFormModel.commonName = ''
        return
      } else {
        const para = { id: value[0] }
        ProductService.GetProduct(para)
          .then((result) => {
            this.tempData.tempFormModel.commonName = result.data.commonName
            this.$forceUpdate()
          })
          .catch((error) => {
            console.log(error)
          })
      }
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    timeRangeChange() {
      this.$forceUpdate()
    },
    get(id) {
      this.btnSaveLoading = true
      ProductAliasService.GetProductAlias({ id: id }).then(result => {
        this.tempData.tempFormModel = result.data
        this.tempData.tempFormModel.productAndSpecId = [result.data.productId, result.data.productSpecId]
        this.tempData.tempFormModel.timeRange = [new Date(result.data.startDate), new Date(result.data.endDate)]
        this.initProductAndSpec()
        this.btnSaveLoading = false
      }).catch(error => {
        this.btnSaveLoading = false
        console.log(error)
      })
    },
    save() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.tempData.tempFormModel.productSpecId = this.tempData.tempFormModel.productAndSpecId[1]
          if (!this.tempData.tempFormModel.id) {
            this.addNew()
          } else {
            this.update()
          }
        }
      })
    },
    addNew() {
      this.btnSaveLoading = true
      ProductAliasService.AddProductAlias(this.tempData.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempData.tempFormModel = result.data
          this.$notice.message('新增成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('新增失败。', 'error')
          }
        })
    },
    update() {
      this.btnSaveLoading = true
      ProductAliasService.UpdateProductAlias(this.tempData.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempData.tempFormModel = result.data
          this.$notice.message('修改成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('修改失败。', 'error')
          }
        })
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.tempData.tempFormModel = {}
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    }
  }

}
</script>
