import HttpApi from './libs/api.request'

const controller = 'Maintenance'

const api = new HttpApi(controller)

export default {
  NewGuid() {
    return api.get('NewGuid')
  },
  QueryOrganization(params) {
    return api.get('QueryOrganization', params)
  },
  QueryProvinces(params) {
    return api.get('QueryProvinces', params)
  },
  QueryEmployee(params) {
    return api.get('QueryEmployee', params)
  },
  GetEmployeeExportColumn() {
    return api.get('GetEmployeeExportColumn')
  },
  ExportEmployee(params) {
    return api.post('ExportEmployee', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryEmployeeInService(params) {
    return api.get('QueryEmployeeInService', params)
  },
  ResetEmployeePwd(params) {
    return api.post('ResetEmployeePwd', params)
  },
  QueryDept() {
    return api.get('QueryDept')
  },
  QueryAllDept() {
    return api.get('QueryAllDept')
  },
  QueryPosition(params) {
    return api.get('QueryPosition', params)
  },
  QueryProvinceSelect(params) {
    return api.get('QueryProvinceSelect', params)
  },
  QueryProductSelect(params) {
    return api.get('QueryProductSelect', params)
  },
  GetStation(params) {
    return api.get('GetStation', params)
  },
  AddStation(params) {
    return api.post('AddStation', params)
  },
  UpdateStation(params) {
    return api.post('UpdateStation', params)
  },
  DeleteStation(params) {
    return api.post('DeleteStation', params)
  },
  AddEmployeeStation(params) {
    return api.post('AddEmployeeStation', params)
  },
  StopEmployeeStation(params) {
    return api.post('StopEmployeeStation', params)
  },
  AddStationReceiver(params) {
    return api.post('AddStationReceiver', params)
  },
  BatchAddStationReceiver(params) {
    return api.post('BatchAddStationReceiver', params)
  },
  // 批量移岗
  BatchTransferStationReceiver(params) {
    return api.post('BatchTransferStationReceiver', params)
  },
  DeleteStationReceiver(params) {
    return api.post('DeleteStationReceiver', params)
  },
  BatchDeleteStationReceiver(params) {
    return api.post('BatchDeleteStationReceiver', params)
  },
  QueryStationReceiverByStationId(params) {
    return api.get('QueryStationReceiverByStationId', params)
  },
  QueryStation(params) {
    return api.get('QueryStation', params)
  },
  QueryEmployeeRoles(params) {
    return api.get('QueryEmployeeRoles', params)
  },
  QueryEmployeePermissions(params) {
    return api.get('QueryEmployeePermissions', params)
  },
  QueryReceiverTypeTree(params) {
    return api.get('QueryReceiverTypeTree', params)
  },
  GetReceiverType(params) {
    return api.get('GetReceiverType', params)
  },
  AddReceiverType(params) {
    return api.post('AddReceiverType', params)
  },
  UpdateReceiverType(params) {
    return api.post('UpdateReceiverType', params)
  },
  DeleteReceiverType(params) {
    return api.post('DeleteReceiverType', params)
  },
  QueryArea(params) {
    return api.get('QueryArea', params)
  },
  AddArea(params) {
    return api.post('AddArea', params)
  },
  UpdateArea(params) {
    return api.post('UpdateArea', params)
  },
  GetArea(params) {
    return api.get('GetArea', params)
  },
  GetAreaExportColumn() {
    return api.get('GetAreaExportColumn')
  },
  ExportArea(params) {
    return api.post('ExportArea', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  DeleteArea(params) {
    return api.post('DeleteArea', params)
  },
  GetReceiverTypeLevelOne(params) {
    return api.get('GetReceiverTypeLevelOne', params)
  },
  QueryReceiverTypeCascader(params) {
    return api.get('QueryReceiverTypeCascader', params)
  },
  GetAllAreas(params) {
    return api.get('QueryAllAreas', params)
  },
  QueryProvinceSelectByAreaIds(params) {
    return api.get('QueryProvinceSelectByAreaIds', params)
  },
  QueryAllCustomerCategory() {
    return api.get('QueryAllCustomerCategory')
  },
  GetReceiverTypeThreeByParent(params) {
    return api.get('GetReceiverTypeThreeByParent', params)
  },
  QueryAllProductSelect() {
    return api.get('QueryAllProductSelect')
  },
  QueryDepartmentForProject() {
    return api.get('QueryDepartmentForProject')
  },
  QueryDepartmentForTargetReceiver() {
    return api.get('QueryDepartmentForTargetReceiver')
  },
  QueryDepartmentForQuota() {
    return api.get('QueryDepartmentForQuota')
  },
  QueryStationSelectModel(params) {
    return api.get('QueryStationSelectModel', params)
  },
  GetCurrentStationByReceiverId(params) {
    return api.post('GetCurrentStationByReceiverId', params)
  },
  QueryOrganizationExcludeAssistant(params) {
    return api.get('QueryOrganizationExcludeAssistant', params)
  },
  QueryDepartmentForReport() {
    return api.get('QueryDepartmentForReport')
  },
  QueryAreasForReport() {
    return api.get('QueryAreasForReport')
  },
  QueryProvinceSelectByDepartmentAndAreas() {
    return api.get('QueryProvinceSelectByDepartmentAndAreas')
  }
}
