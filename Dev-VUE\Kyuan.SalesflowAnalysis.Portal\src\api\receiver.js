import HttpApi from './libs/api.request'

const controller = 'Receiver'

const api = new HttpApi(controller)

export default {
  QueryReceiverSelect(params) {
    return api.get('QueryReceiverSelect', params)
  },
  QueryReceiverList(params) {
    return api.get('QueryReceiverList', params)
  },
  QueryDistributorListForHandleSalesflow(params) {
    return api.get('QueryDistributorListForHandleSalesflow', params)
  },
  QueryStationReceiverList(params) {
    return api.get('QueryStationReceiverList', params)
  },
  QueryReceiverTerminalList(params) {
    return api.get('QueryReceiverTerminalList', params)
  },
  AddReceiverByHandleSalesFlow(params) {
    return api.post('AddReceiverByHandleSalesFlow', params)
  },
  QueryImportReceiverMasterTemp(params) {
    return api.get('QueryImportReceiverMasterTemp', params)
  },
  QueryImportReceiverError(params) {
    return api.get('QueryImportReceiverError', params)
  },
  ExportImportReceiverError(params) {
    return api.post('ExportImportReceiverError',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  QueryReceiver(params) {
    return api.get('QueryReceiver', params)
  },
  GetReceiver(params) {
    return api.get('GetReceiver', params)
  },
  AddReceiver(params) {
    return api.post('AddReceiver', params)
  },
  UpdateReceiver(params) {
    return api.post('UpdateReceiver', params)
  },
  DeleteReceiver(params) {
    return api.post('DeleteReceiver', params)
  },
  GetReceiverExportColumn() {
    return api.get('GetReceiverExportColumn')
  },
  ExportReceiver(params) {
    return api.post('ExportReceiver',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  CheckReceiverQuota(params) {
    return api.post('CheckReceiverQuota', params)
  },
  DisableReceiver(params) {
    return api.post('DisableReceiver', params)
  },

  EnableReceiver(params) {
    return api.post('EnableReceiver', params)
  },
  QueryHospitalOrDrugReceiverList(params) {
    return api.get('QueryHospitalOrDrugReceiverList', params)
  }
}
