const constDefinition = {
  errorSalesFlowType: {
    DistributorProvinceNotExist: 1,
    DistributorCityNotExist: 2,
    DistributorNotExist: 4,
    ReceiverrNotExist: 8,
    ManufacturerNotExist: 16,
    ProductNotExist: 32,
    ExpireDateError: 64,
    SaleDateError: 128,
    QuantityError: 256,
    SaleDateNotExist: 512,
    SaleDateRequired: 1024,
    QuantityRequired: 2048
  },
  calculateStatus: {
    WaitingReview: 30,
    DirectorReview: 33
  },
  rebateComputeCycleFlag: {
    Year: 1,
    Quarter: 2
  },
  rebateResultStatus: {
    Calculating: 1,
    Calculated: 2,
    Error: 9,
    Unconfirmed: 10,
    Rejected: 20,
    Confirmed: 30,
    Generated: 40,
    Paid: 50
  },
  rebateFeedbackStatus: {
    Untracked: 1, // 未上传跟踪文件
    Unreviewed: 2, // 待审核跟踪文件
    Rejected: 3, // 已拒绝跟踪文件
    Tracked: 4, // 已审核跟踪文件
    Pushed: 5, // 内勤已推送
    Paid: 6 // 已支付
  },
  rebateAgreementCertificateStatus:
  {
    DataEnter: 10,
    GenerateFile: 20
  },
  rebateAgreementStatus: {
    ImportPart: 1, // 部分导入
    Import: 5, // 已导入
    Normal: 10, // 未盖章
    InApproval: 20, // 审批中
    Sealed: 30, // 已盖章
    Obsolete: 90 // 已作废
  },
  formType: {
    AddRebateAgreement: 1, // 新增协议
    AddRebateMasterAgreement: 2, // 新增主协议
    UpdateRebateAgreement: 3, // 变更协议
    CancelRebateAgreement: 4, // 作废协议
    MergePayment: 5 // 合并支付
  },
  rebateAgreementTemplateType: {
    changeChannels: 'changeChannels'// 合并支付
  },
  departmentCode: {
    Marketing: '12',
    Commerce: '1201',
    Sales: '1206',
    Retail: '1204',
    Digital: '1207',
    Efficiency: '1202'
  },
  // 用于验证目标终端添加是否可以添加指标；
  customerCategory: {
    selfSupportCode: 'Normal',
    selfSupportDevelopCode: '科园自营-开发'
  },
  checkReceiverRange: {
    success: 0,
    notDistributor: 1, // 商务部只能选择经销商类型的客户
    notRetailAndMedical: 2, // 销售部只能选择医疗机构和零售类型的客户
    notRetail: 3 // 零售部仅能选择零售类型的客户
  },
  // 政策模版类型
  calculationTemplateType: {
    steppedType: 1, // 阶梯式返利
    quantityReached: 2, // 达量返利
    distribution: 3 // 开发门店
  },
  complaintsFormStatus: {
    request: 10,
    approval: 20,
    confirm: 30,
    reject: 40,
    cancel: 50
  },
  agreementImportStatus: {
    waitGenerateMaster: 1,
    waitGenerateSub: 2,
    completed: 3
  }
}
export default constDefinition
