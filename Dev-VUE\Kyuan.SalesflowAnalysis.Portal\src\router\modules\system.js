/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    permissions: ['admin','editor']    control the page permissions (you can set multiple permissions)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

import Layout from '@/layout'

const bonusRoutes = [
  {
    path: '/system',
    component: Layout,
    redirect: '/system/index',
    hidden: false,
    meta: {
      title: '系统管理',
      sort: 99,
      icon: 'setting'
    },
    children: [
      {
        name: 'Operate',
        path: 'operate',
        component: () => import('@/views/system/operate/index'),
        meta: {
          title: '操作日志',
          icon: 'list',
          sort: 1,
          noCache: false,
          permissions: ['System_ActionLog']
        }
      },
      {
        name: 'Exception',
        path: 'exception',
        component: () => import('@/views/system/exception/index'),
        meta: {
          title: '异常日志',
          icon: 'nested',
          sort: 2,
          noCache: false,
          permissions: ['System_ExceptionLog']
        }
      },
      {
        name: 'Dict',
        path: 'dict',
        component: () => import('@/views/system/dict/index'),
        meta: {
          title: '字典管理',
          icon: 'dict',
          sort: 3,
          noCache: false,
          permissions: ['System_Dict']
        }
      },
      {
        name: 'ReportSetting',
        path: 'reportSetting',
        component: () => import('@/views/system/reportSetting/index'),
        meta: {
          title: '报表设置',
          icon: 'table',
          sort: 3,
          noCache: false,
          permissions: ['System_ReportSetting']
        }
      },
      {
        name: 'WarningStopPurchase',
        path: 'warningStopPurchase',
        component: () => import('@/views/system/warningStopPurchase/index'),
        meta: {
          title: '停药警告处理',
          icon: 'table',
          sort: 3,
          noCache: false,
          permissions: ['System_WarningStopPurchase']
        }
      }
    ]
  }
]

export default bonusRoutes
