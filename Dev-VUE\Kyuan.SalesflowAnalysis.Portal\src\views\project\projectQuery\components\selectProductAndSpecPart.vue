<template>
  <div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            ref="dataTable"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              width="55"
            />
            <el-table-column
              sortable
              prop="Manufacturer.Name"
              label="厂商名称"
              min-width="100px"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.manufacturerName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable
              prop="ProductNameCn"
              label="产品名称"
              min-width="100px"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable
              prop="ProductCommonName"
              label="通用名"
              min-width="100px"
              header-align="center"
              align="left"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productCommonName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable
              prop="Spec"
              label="规格"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.spec }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>

export default {
  components: {

  },
  props: {
    showDialog: {
      type: Boolean,
      default: false
    },
    selected: {
      type: Array,
      default: () => []
    },
    listData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      span: 4,
      list: [],
      multipleSelection: []
    }
  },
  watch: {
    showDialog: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        if (val === true) {
          this.bingList(this.listData, this.selected)
        }
      }
    }
  },
  methods: {
    bingList(listData, selected) {
      this.list = listData
      this.$nextTick(() => {
        // 标记已选择的集合
        if (selected) {
          selected.forEach((row) => {
            this.$refs.dataTable.toggleRowSelection(row, true)
          })
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleCheck() {
      this.$emit('success', JSON.parse(JSON.stringify(this.multipleSelection)))
      this.list = []
    }
  }
}
</script>
<style scoped>

</style>
