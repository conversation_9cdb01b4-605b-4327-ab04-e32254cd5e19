<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="span">
          <el-date-picker
            v-model="listQuery.yearMonthSplicing"
            clearable
            class="filter-item"
            type="month"
            placeholder="流向月份"
            format="yyyy-MM"
            value-format="yyyy-MM"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.terminalChangeType"
            :loading="enumTerminalChangeTypeLoading"
            class="filter-item"
            placeholder="报表类型"
            clearable
          >
            <el-option
              v-for="item in enumTerminalChangeTypeList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProvinceCity"
            v-model="terminalProvinceCityId"
            :loading="provinceCityLoading"
            :options="provinceCityList"
            placeholder="终端省份/城市"
            clearable
            class="filter-item"
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            @change="closeProvinceCityCascader"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.receiverTypeLevelOneId"
            :loading="enumTypeLoading"
            class="filter-item"
            placeholder="终端类型"
            clearable
            @keyup.enter.native="handleFilter"
          >
            <el-option
              v-for="item in receiverTypeList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.name"
            clearable
            placeholder="终端名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'ReceiverReport_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'ReceiverReport_Button_Export')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-document-add"
            @click="handleGenerate"
          >
            生成报表
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'ReceiverReport_Button_Report')"
            :loading="btnExportLoading"
            class="filter-item-button"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column label="月份" align="center" sortable="custom" min-width="60px" prop="Cycle.YearMonthSplicing">
              <template slot-scope="{ row }">
                <span>{{ row.yearMonthSplicing }}</span>
              </template>
            </el-table-column>
            <el-table-column label="报表类型" align="center" sortable="custom" min-width="60px" prop="EnumTerminalChangeType">
              <template slot-scope="{ row }">
                <span>{{ row.enumTerminalChangeTypeDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="终端省份" align="center" sortable="custom" min-width="60px" prop="Terminal.Province.NameCn">
              <template slot-scope="{ row }">
                <span>{{ row.provinceName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="终端城市" align="center" sortable="custom" min-width="60px" prop="Terminal.City.NameCn">
              <template slot-scope="{ row }">
                <span>{{ row.cityName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="终端类型" align="center" sortable="custom" min-width="60px" prop="Terminal.ReceiverTypeLevelOne.Name">
              <template slot-scope="{ row }">
                <span>{{ row.typeLevelOneName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="终端名称" align="left" sortable="custom" min-width="150px" prop="Terminal.Name" header-align="center">
              <template slot-scope="{ row }">
                <span>{{ row.terminalName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="产品名称" align="center" sortable="custom" min-width="80px" prop="ProductSpec.Product.NameCn" header-align="center">
              <template slot-scope="{ row }">
                <span>{{ row.productNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column label="产品规格" align="center" sortable="custom" min-width="80px" prop="ProductSpec.Spec" header-align="center">
              <template slot-scope="{ row }">
                <span>{{ row.productSpecSpec }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出报表"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
    <generateTerminalChange
      v-if="generateDialogVisible"
      :title="generateDialogTitle"
      @hidden="onHidden()"
      @refresh="onRefresh()"
    />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import CustomExport from '@/components/Export/CustomExport'
import LocationService from '@/api/location'
import MasterDataService from '@/api/masterData'
import TerminalChangeReportService from '@/api/terminalChangeReport'
import generateTerminalChange from './components/generateTerminalChange'

export default {
  name: 'TerminalChangeQuery',
  components: {
    Pagination,
    CustomExport,
    generateTerminalChange
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        receiverType: '',
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      list: [],
      enumTypeLoading: false,
      enumTypeList: [],
      provinceCityLoading: false,
      provinceCityList: [],
      terminalProvinceCityId: null,
      btnExportLoading: false,
      showExportModal: false,
      enumTerminalChangeTypeList: [],
      columnDictionary: {},
      generateDialogTitle: '生成报表',
      generateDialogVisible: false,
      receiverTypeLoading: false,
      receiverTypeList: []
    }
  },
  created() {
    this.initTerminalChangeType()
    this.initProvinceCity()
    this.initReceiverTypeList()
    this.handleFilter()
  },
  methods: {
    initTerminalChangeType() {
      this.enumTerminalChangeTypeLoading = true
      MasterDataService.GetEnumInfos({ enumType: 'TerminalChangeType' })
        .then((result) => {
          this.enumTerminalChangeTypeLoading = false
          this.enumTerminalChangeTypeList = result.data.datas
        })
        .catch((error) => {
          this.enumTerminalChangeTypeLoading = false
          console.log(error)
        })
    },
    initProvinceCity() {
      this.provinceCityLoading = true
      LocationService.QueryProvinceCityCascader()
        .then((result) => {
          this.provinceCityList = result
          this.provinceCityLoading = false
        })
        .catch((error) => {
          this.provinceCityLoading = false
          console.log(error)
        })
    },
    initReceiverTypeList() {
      this.receiverTypeLoading = true
      MasterDataService.GetDicts({ parentCode: 'ReceiverTypeLevel1' }).then(res => {
        this.receiverTypeList = res.data
        this.receiverTypeLoading = false
      })
        .catch((error) => {
          this.receiverTypeLoading = false
          console.log(error)
        })
    },
    initReceiverType() {
      this.provinceCityLoading = true
      MasterDataService.GetEnumInfos({
        EnumType: 'ReceiverType'
      })
        .then((result) => {
          this.enumTypeList = result.data.datas
          this.enumTypeLoading = false
        })
        .catch((error) => {
          this.enumTypeLoading = false
          console.log(error)
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      if (this.terminalProvinceCityId) {
        const [terminalProvinceId, terminalCityId] = this.terminalProvinceCityId
        this.listQuery.provinceId = terminalProvinceId
        this.listQuery.cityId = terminalCityId
      }
      if (!this.listQuery.receiverType) {
        this.listQuery.receiverType = null
      }
      this.listLoading = true
      TerminalChangeReportService.QueryTerminalChangeReport(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      TerminalChangeReportService.GetTerminalChangeReportExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      if (this.terminalProvinceCityId) {
        const [terminalProvinceId, terminalCityId] = this.terminalProvinceCityId
        this.listQuery.provinceId = terminalProvinceId
        this.listQuery.cityId = terminalCityId
      }
      if (!this.listQuery.receiverType) {
        this.listQuery.receiverType = null
      }
      var exportParam = JSON.parse(JSON.stringify(this.listQuery))
      exportParam.checkedColumns = checkColumns
      TerminalChangeReportService.ExportTerminalChangeReport(exportParam)
        .then(result => {
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '新增、流失终端.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '新增、流失终端.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    handleGenerate() {
      this.generateDialogVisible = true
    },
    onHidden() {
      this.generateDialogVisible = false
    },
    onRefresh() {
      this.getList()
      this.generateDialogVisible = false
    },
    closeProvinceCityCascader() {
      this.$refs.refProvinceCity.dropDownVisible = false
    }
  }
}
</script>
