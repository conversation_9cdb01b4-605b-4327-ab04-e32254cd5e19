<template>
  <div>
    <el-card>
      <div slot="header" class="clearfix">
        <span>流向申诉待办</span>
      </div>
      <div>
        <el-row>
          <el-col :span="24">
            <el-table :data="list" style="width: 100%;height:215px;">
              <el-table-column prop="Title" label="待审核申诉" min-width="100px">
                <template slot-scope="{ row }">
                  <span class="link-type" @click="showBonus"> {{ row.applyNo }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="Title" label="申诉类型" min-width="100px">
                <template slot-scope="{ row }">
                  <span> {{ row.enumTypeDesc }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import SalesFlowComplaintsRequestService from '@/api/salesFlowComplaintsRequest'

export default {
  data() {
    return {
      list: [],
      announcementId: '',
      listQuery: {
        pageIndex: 1,
        pageSize: 5,
        order: '-CreateTime'
      }
    }
  },
  methods: {
    initPage(model) {
      if (model.isDataAdmin || model.isSysAdmin) {
        this.listQuery.complaintsFormStatus = 20
      }
      if (model.isAssistant) {
        this.listQuery.complaintsFormStatus = 10
      }

      this.fetchData()
    },
    fetchData() {
      SalesFlowComplaintsRequestService.QuerySalesFlowComplaintsRequest(this.listQuery).then(res => {
        this.list = res.data.datas
      }).catch(res => {})
    },
    showBonus() {
      this.$router.push({ name: 'SalesFlowApprove', query: { fromPage: 'Home', status: this.listQuery.complaintsFormStatus }})
    }
  }
}
</script>
