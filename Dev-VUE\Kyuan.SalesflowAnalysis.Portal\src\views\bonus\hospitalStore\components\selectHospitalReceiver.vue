<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filiter-container">
        <el-col :span="span">
          <el-cascader
            ref="refProvinceCity"
            v-model="listQuery.provinceCityId"
            :loading="provinceCityLoading"
            :options="provinceCityList"
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            placeholder="省份城市"
            clearable
            @change="provinceCitChange"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.receiverName"
            clearable
            placeholder="名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
            <el-table-column sortable="custom" prop="Name" label="名称" min-width="200px" align="left" header-align="center">
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="Employee.DisplayName" label="负责人" min-width="80px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.employeeDisplayName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="Province.NameCn" label="省份" min-width="80px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.provinceNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="City.NameCn" label="城市" min-width="80px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.cityNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column label="曾用名" min-width="180px" align="left" header-align="center">
              <template slot-scope="{ row }">
                <span>{{ row.receiverFormers }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="70"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i class="el-icon-circle-check eltablei" title="选择" @click="handleCheck(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import LocationService from '@/api/location'
import ReceiverService from '@/api/receiver'
import Pagination from '@/components/Pagination'

export default {
  components: {
    Pagination
  },
  props: {
    receiverOneLevelCodes: {
      type: Array,
      default: null
    },
    showDialog: {
      type: Boolean,
      default: false
    },
    isStopped: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      span: 4,
      provinceCityList: [],
      provinceCityLoading: false,
      listLoading: true,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      list: []
    }
  },
  watch: {
    showDialog: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        if (val === true) {
          this.initProvinceCity()
          this.getList()
        }
      }
    }
  },
  methods: {
    initProvinceCity() {
      this.provinceCityLoading = true
      LocationService.QueryProvinceCityCascader()
        .then((result) => {
          this.provinceCityList = result
          this.provinceCityLoading = false
        })
        .catch((error) => {
          this.provinceCityLoading = false
          console.log(error)
        })
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList() {
      this.listLoading = true
      this.listQuery.receiverOneLevelCodes = this.receiverOneLevelCodes
      if (this.isStopped != null) {
        this.listQuery.isStopped = this.isStopped
      }
      ReceiverService.QueryHospitalOrDrugReceiverList(this.listQuery).then(res => {
        this.listLoading = false
        this.list = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(res => {})
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleCheck(row) {
      this.$emit('success', row)
    },
    provinceCitChange() {
      if (this.listQuery.provinceCityId !== undefined && this.listQuery.provinceCityId !== null && this.listQuery.provinceCityId.length > 0) {
        this.listQuery.provinceId = this.listQuery.provinceCityId[0]
        if (this.listQuery.provinceCityId.length > 1) {
          this.listQuery.cityId = this.listQuery.provinceCityId[1]
        } else {
          this.listQuery.cityId = null
        }
      } else {
        this.listQuery.provinceId = null
        this.listQuery.cityId = null
      }
      this.$refs.refProvinceCity.dropDownVisible = false
    },
    clear() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
    }
  }
}
</script>
<style scoped>

</style>
