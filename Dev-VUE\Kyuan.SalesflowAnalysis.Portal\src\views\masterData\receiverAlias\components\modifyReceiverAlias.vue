<template>
  <div>
    <el-dialog :title="title" width="50%" :close-on-click-modal="false" :visible="true" @close="cancle()">
      <el-form ref="dataForm" :rules="rules" :model="tempData.tempFormModel" label-position="right" label-width="100px" class="el-dialogform">
        <el-row :gutter="10">
          <el-col :span="span">
            <el-form-item label="发货方名称" prop="distributorName">
              <el-col :span="20" style="padding-left: 0px; padding-right: 0px;">
                <el-input
                  v-model="tempData.tempFormModel.distributorName"
                  :readonly="true"
                  placeholder="发货方名称"
                />
              </el-col>
              <el-col :span="4">
                <el-button
                  icon="el-icon-search"
                  style="width: 100%; "
                  type="primary"
                  title="选择发货方"
                  :disabled="viewModel"
                  @click="handleSelectDistributor"
                />
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="收货方名称" prop="receiverName">
              <el-col :span="20" style="padding-left: 0px; padding-right: 0px;">
                <el-input
                  v-model="tempData.tempFormModel.receiverName"
                  :readonly="true"
                  placeholder="收货方名称"
                />
              </el-col>
              <el-col :span="4">
                <el-button
                  icon="el-icon-search"
                  style="width: 100%; "
                  type="primary"
                  title="选择收货方"
                  @click="handleSelectReceiver"
                />
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="别名" prop="alias">
              <el-input
                v-model="tempData.tempFormModel.alias"
                clearable
                placeholder="别名"
                :disabled="viewModel"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          :loading="btnSaveLoading"
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      custom-class="el-dialog-ls"
      append-to-body
      title="选择发货方"
      :close-on-click-modal="false"
      :visible="dialogDistributorVisible"
      width="90%"
      @close="closeDistributorDialog"
    >
      <SelectReceiver ref="refSelectDistributor" :show-dialog="dialogDistributorVisible" :distributor-types="distributorTypes" @success="selectDistributorSuccess" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeDistributorDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="选择收货方"
      :close-on-click-modal="false"
      :visible="dialogReceiverVisible"
      width="90%"
      class="popup-search"
      @close="closeReceiverDialog"
    >
      <SelectReceiver
        ref="refSelectReceiver"
        :show-dialog="dialogReceiverVisible"
        :distributor-types="distributorTypes"
        :is-stopped="false"
        @success="selectReceiverSuccess"
      />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeReceiverDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>

import SalesFlowTemplateMappingService from '@/api/salesFlowTemplateMapping'
import ReceiverAliasService from '@/api/receiverAlias'
import SelectReceiver from '@/views/components/selectReceiver'

export default {
  name: '',
  components: {
    SelectReceiver
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    viewModel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      span: 24,
      rules: {
        distributorName: [
          { required: true, message: '请选择发货方', trigger: 'change' }
        ],
        receiverName: [
          { required: true, message: '请选择收货方', trigger: 'change' }
        ],
        alias: [
          { required: true, message: '请输入别名', trigger: 'blur' },
          { max: 300, message: '别名不允许超过300个字符', trigger: 'blur' }
        ]
      },
      tempData: { tempFormModel: { distributorName: '', receiverName: '' }},
      btnSaveLoading: false,
      distributorTypes: [],
      dialogDistributorVisible: false,
      dialogReceiverVisible: false
    }
  },
  watch: {
    id(val) {
      this.tempData.tempFormModel.id = val
    }
  },
  mounted() {
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      if (!this.id) {
        this.clear()
        return
      }
      this.get(this.id)
    },
    get(id) {
      this.btnSaveLoading = true
      ReceiverAliasService.GetReceiverAlias({ id: id }).then(result => {
        this.tempData.tempFormModel = result.data
        this.tempData.tempFormModel.productAndSpecId = [result.data.productId, result.data.productSpecId]
        this.tempData.tempFormModel.timeRange = [new Date(result.data.startDate), new Date(result.data.endDate)]
        this.btnSaveLoading = false
      }).catch(error => {
        this.btnSaveLoading = false
        console.log(error)
      })
    },
    save() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.tempData.tempFormModel.id) {
            this.addNew()
          } else {
            this.update()
          }
        }
      })
    },
    addNew() {
      this.btnSaveLoading = true
      ReceiverAliasService.AddReceiverAlias(this.tempData.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempData.tempFormModel = result.data
          this.$notice.message('新增成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('新增失败。', 'error')
          }
        })
    },
    update() {
      this.btnSaveLoading = true
      ReceiverAliasService.UpdateReceiverAlias(this.tempData.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempData.tempFormModel = result.data
          this.$notice.message('修改成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('修改失败。', 'error')
          }
        })
    },
    handleSelectDistributor() {
      this.distributorTypes = [10, 20]
      this.dialogDistributorVisible = true
    },
    handleSelectReceiver() {
      this.distributorTypes = [10, 20, 30]
      this.dialogReceiverVisible = true
    },
    closeDistributorDialog() {
      this.dialogDistributorVisible = false
      this.$refs.refSelectDistributor.clear()
      this.distributorTypes = []
    },
    closeReceiverDialog() {
      this.dialogReceiverVisible = false
      this.$refs.refSelectReceiver.clear()
      this.distributorTypes = []
    },
    selectDistributorSuccess(val) {
      if (val.id === this.tempData.tempFormModel.receiverId) {
        this.$notice.message('发货方不能和收货方相同', 'error')
      } else {
        // 解决弹出框打开后，不输入任何值，只选择发货方名称时，验证失效的问题
        this.$set(this.tempData.tempFormModel, 'distributorName', val.name)
        // this.tempData.tempFormModel.distributorName = val.name
        this.tempData.tempFormModel.distributorId = val.id
        this.closeDistributorDialog()
      }
    },
    selectReceiverSuccess(val) {
      if (val.id === this.tempData.tempFormModel.distributorId) {
        this.$notice.message('收货方不能和发货方相同', 'error')
      } else {
        // 解决弹出框打开后，不输入任何值，只选择收货方名称时，验证失效的问题
        this.$set(this.tempData.tempFormModel, 'receiverName', val.name)
        // this.tempData.tempFormModel.receiverName = val.name
        this.tempData.tempFormModel.receiverId = val.id
        this.closeReceiverDialog()
      }
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.tempData.tempFormModel = {}
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    }
  }

}
</script>
