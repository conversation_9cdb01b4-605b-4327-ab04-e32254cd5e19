import HttpApi from './libs/api.request'

const controller = 'Rebate'

const api = new HttpApi(controller)

export default {
  QueryRebateResult(params) {
    return api.get('QueryRebateResult', params)
  },
  QueryRebateResultByAgreementId(params) {
    return api.get('QueryRebateResultByAgreementId', params)
  },
  QueryRebateResultTaskByAgreementId(params) {
    return api.get('QueryRebateResultTaskByAgreementId', params)
  },
  QueryRebateResultForTracking(params) {
    return api.get('QueryRebateResultForTracking', params)
  },
  GetRebateResultForTrackingColumn() {
    return api.get('GetRebateResultForTrackingColumn')
  },
  ExportRebateResultForTracking(params) {
    return api.post('ExportRebateResultForTracking', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  PushFieldConfirm(params) {
    return api.post('PushFieldConfirm', params)
  },
  PushFieldBatchConfirm() {
    return api.post('PushFieldBatchConfirm')
  },
  GetRebateResult(params) {
    return api.get('GetRebateResult', params)
  },
  ApprovalRebateResultFeedback(params) {
    return api.post('ApprovalRebateResultFeedback', params)
  },
  RejectRebateResultFeedback(params) {
    return api.post('RejectRebateResultFeedback', params)
  },
  ReCalculateRebateResult(params) {
    return api.post('ReCalculateRebateResult', params)
  },
  QueryRebateSalesFlow(params) {
    return api.get('QueryRebateSalesFlow', params)
  },
  ClearRebateResultWarning(params) {
    return api.post('ClearRebateResultWarning', params)
  },
  RebateResultAdjust(params) {
    return api.post('RebateResultAdjust', params)
  },
  QueryRebateAgent(params) {
    return api.get('QueryRebateAgent', params)
  },
  DownloadRebateTracking(params) {
    return api.post('DownloadRebateTracking', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryRebateResultDetail(params) {
    return api.get('QueryRebateResultDetail', params)
  },
  PaymentConfirm(params) {
    return api.post('PaymentConfirm', params)
  },
  QueryRebateAgreementProductSpec(params) {
    return api.get('QueryRebateAgreementProductSpec', params)
  },
  GetRebateResultExportColumn(params) {
    return api.get('GetRebateResultExportColumn', params)
  },
  ExportRebateResultReceiverDetail(params) {
    return api.post('ExportRebateResultReceiverDetail',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  GetRebateSalesFlowColumn(params) {
    return api.get('GetRebateSalesFlowColumn', params)
  },
  ExportRebateSalesFlow(params) {
    return api.post('ExportRebateSalesFlow',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  AddRebateResult(params) {
    return api.post('AddRebateResult', params)
  },
  QueryRebateAccruedExpense(params) {
    return api.get('QueryRebateAccruedExpense', params)
  },
  QueryRebateAccruedExpenseDetail(params) {
    return api.get('QueryRebateAccruedExpenseDetail', params)
  },
  ExportRebateAccruedExpense(params) {
    return api.post('ExportRebateAccruedExpense', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  GetRebateAccruedExpenseColumn() {
    return api.get('GetRebateAccruedExpenseColumn')
  },
  QueryRebateResultReceiver(params) {
    return api.get('QueryRebateResultReceiver', params)
  },
  ReCalculateRebateAccruedExpense(params) {
    return api.post('ReCalculateRebateAccruedExpense', params)
  },
  QueryToBeCalculatedRebateResultTask(params) {
    return api.get('QueryToBeCalculatedRebateResultTask', params)
  },
  CalculateRebateResultTask(params) {
    return api.post('CalculateRebateResultTask', params)
  },
  GetRebateResultChangeCertificate(params) {
    return api.get('GetRebateResultChangeCertificate', params)
  },
  QueryRebateResultPayReceiver(params) {
    return api.get('QueryRebateResultPayReceiver', params)
  }
}
