/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    permissions: ['admin','editor']    control the page permissions (you can set multiple permissions)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

import Layout from '@/layout'

const masterDataRoutes = [
  {
    path: '/masterData',
    component: Layout,
    redirect: '/masterData/index',
    hidden: false,
    meta: {
      title: '主数据管理',
      sort: 10,
      icon: 'documentation'
    },
    children: [
      {
        name: 'Manufacturer',
        path: 'manufacturer',
        component: () => import('@/views/masterData/manufacturer/index'),
        meta: {
          title: '厂商管理',
          icon: 'manufacturer',
          sort: 1,
          noCache: false,
          permissions: ['Maintenance_Manufacturer']
        }
      },
      {
        name: 'Product',
        path: 'product',
        component: () => import('@/views/masterData/product/index'),
        meta: {
          title: '产品信息',
          icon: 'list',
          sort: 2,
          noCache: false,
          permissions: ['Maintenance_Product']
        }
      },
      {
        name: 'Price',
        path: 'price',
        component: () => import('@/views/masterData/price/index'),
        meta: {
          title: '考核价管理',
          icon: 'money',
          sort: 3,
          noCache: false,
          permissions: ['Maintenance_AccountingPrice']
        }
      },
      {
        name: 'ProductAlias',
        path: 'productAlias',
        component: () => import('@/views/masterData/productAlias/index'),
        meta: {
          title: '产品别名管理',
          icon: 'nested',
          sort: 4,
          noCache: false,
          permissions: ['Maintenance_ProductAlias']
        }
      },
      {
        name: 'ReceiverType',
        path: 'receiverType',
        component: () => import('@/views/masterData/receiverType/index'),
        meta: {
          title: '收货方类型管理',
          icon: 'excel',
          sort: 5,
          noCache: false,
          permissions: ['Maintenance_ReceiverType']
        }
      },
      {
        name: 'Receiver',
        path: 'receiver',
        component: () => import('@/views/masterData/receiver/index'),
        meta: {
          title: '收货方管理',
          icon: 'receiver',
          sort: 6,
          noCache: false,
          permissions: ['Maintenance_Receiver']
        }
      },
      {
        name: 'TargetReceiver',
        path: 'targetReceiver',
        component: () => import('@/views/masterData/targetReceiver/index'),
        meta: {
          title: '目标客户管理',
          icon: 'target-hospital',
          sort: 7,
          noCache: false,
          permissions: ['Maintenance_TargetReceiver']
        }
      },
      {
        name: 'HospitalStore',
        path: 'hospitalStore',
        component: () => import('@/views/bonus/hospitalStore/index'),
        meta: {
          title: '关联药店管理',
          icon: 'store',
          sort: 8,
          noCache: false,
          permissions: ['Bonus_HospitalStore']
        }
      },
      {
        name: 'Channel',
        path: 'channel',
        component: () => import('@/views/masterData/channel/index'),
        meta: {
          title: '渠道管理',
          icon: 'receiver-alias',
          sort: 9,
          noCache: false,
          permissions: ['Maintenance_ProductChannel']
        }
      },
      {
        name: 'Area',
        path: 'area',
        component: () => import('@/views/masterData/area/index'),
        meta: {
          title: '大区管理',
          icon: 'location',
          sort: 10,
          noCache: false,
          permissions: ['Maintenance_Area']
        }
      },
      {
        name: 'City',
        path: 'city',
        component: () => import('@/views/masterData/city/index'),
        meta: {
          title: '城市信息管理',
          icon: 'international',
          sort: 11,
          noCache: false,
          permissions: ['Maintenance_City']
        }
      },
      {
        name: 'County',
        path: 'county',
        component: () => import('@/views/masterData/county/index'),
        meta: {
          title: '区县信息管理',
          icon: 'location',
          sort: 12,
          noCache: false,
          permissions: ['Maintenance_County']
        }
      }
    ]
  }
]

export default masterDataRoutes
