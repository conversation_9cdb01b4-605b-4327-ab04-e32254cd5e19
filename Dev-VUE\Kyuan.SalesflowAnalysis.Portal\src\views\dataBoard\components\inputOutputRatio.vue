<template>
  <div class="panel-group">
    <el-card style="height: 380px;">
      <div slot="header" class="clearfix cardHeader">
        <div>
          <span>目标客户投入产出比排名</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="handleMore">详情</el-button>
        </div>
      </div>
      <el-table
        :data="topList"
        stripe
        fit
        highlight-current-row
        style="width:100%;overflow-y:auto;"
        max-height="360"
      >
        <el-table-column label="部门" show-overflow-tooltip header-align="center" align="left">
          <template slot-scope="{ row }">
            <span>{{ row.departmentName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="客户名称" show-overflow-tooltip header-align="center" align="left">
          <template slot-scope="{ row }">
            <span>{{ row.receiverName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="品规" show-overflow-tooltip align="center">
          <template slot-scope="{ row }">
            <span>{{ row.productAndSpec }}</span>
          </template>
        </el-table-column>
        <el-table-column label="实际服务费收入" header-align="center" align="right" width="120">
          <template slot-scope="{ row }">
            <span>{{ row.actualServiceFeeIncome | toMoney }}</span>
          </template>
        </el-table-column>
        <el-table-column label="实际支出" header-align="center" align="right">
          <template slot-scope="{ row }">
            <span>{{ row.actualInvestment | toMoney }}</span>
          </template>
        </el-table-column>
        <el-table-column label="投入产出比" header-align="center" align="right">
          <template slot-scope="{ row }">
            <span>{{ row.returnOnInvestment | toTwoNum }}%</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <el-dialog append-to-body title="目标客户投入产出比排名" width="60%" :close-on-click-modal="false" :visible="showDialog" @close="cancleDialog()">
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="4">
            <el-select
              v-model="listQuery.departmentId"
              value-key="value"
              class="filter-item"
              placeholder="部门"
              style="width:100%"
              clearable
            >
              <el-option
                v-for="item in deptList"
                :key="item.key"
                :label="item.name"
                :value="item.key"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-input
              v-model="listQuery.receiverName"
              clearable
              placeholder="客户名称"
              class="filter-item"
            />
          </el-col>
          <el-col :span="8">
            <el-cascader
              ref="refProductAndSpec"
              v-model="manufacturerProductAndSpecId"
              :options="productAndSpecList"
              placeholder="厂商 / 产品 / 规格"
              style="width:100%"
              clearable
              class="filter-item"
              :props="{ multiple: true, checkStrictly: false ,expandTrigger: 'hover', emitPath: true }"
            />
          </el-col>
          <el-col :span="4">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-search"
              @click="handleFilter"
            >
              查询
            </el-button>
          </el-col>
        </el-row>
        <el-row type="flex" :gutter="10" justify="end">
          <el-col :span="3.5">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-download"
              @click="handleExport"
            >
              导出
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table
              :data="dataList"
              stripe
              fit
              border
              highlight-current-row
              style="width: 100%"
              :header-cell-class-name="'tableStyle'"
            >
              <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
              <el-table-column label="部门" show-overflow-tooltip header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.departmentName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="客户名称" show-overflow-tooltip header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.receiverName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="品规" show-overflow-tooltip header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.productAndSpec }}</span>
                </template>
              </el-table-column>
              <el-table-column label="实际服务费收入" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.actualServiceFeeIncome | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column label="返利支出" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.rebateActualInvestment | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column label="项目支出" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.projectActualInvestment | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column label="投入产出比" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.returnOnInvestment | toTwoNum }}%</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col v-if="totalCount > 0" class="el-colRight">
            <pagination v-show="totalCount > 0" :total="totalCount" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="search" />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleDialog()">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import Pagination from '@/components/Pagination'
import dashboardService from '@/api/dashboard'

export default {
  components: {
    Pagination
  },
  data() {
    return {
      showDialog: false,
      productAndSpecList: [],
      manufacturerProductAndSpecId: [],
      listQuery: {
        timeRange: [],
        productSpecIds: [],
        departmentId: null,
        receiverName: null,
        pageIndex: 1,
        pageSize: 10
      },
      deptList: [],
      filter: {},
      dataList: [],
      topList: [],
      totalCount: 0, // 数据总数
      topCount: 1
    }
  },

  methods: {
    initPage(filter, deptList, productAndSpecList, count) {
      this.filter = filter
      this.deptList = deptList
      this.manufacturerProductAndSpecId = this.filter.manufacturerProductAndSpecId
      this.productAndSpecList = productAndSpecList
      this.topCount = count

      this.getTopList()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getTopList() {
      this.filter.pageIndex = 1
      this.filter.pageSize = this.topCount
      dashboardService.QueryInputOutputRatioByReceiver(this.filter)
        .then((res) => {
          this.topList = res.data.datas
          this.filter.pageSize = 10
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.search()
    },
    search() {
      this.listQuery.timeRange = this.filter.timeRange
      if (this.manufacturerProductAndSpecId && this.manufacturerProductAndSpecId.length > 0) {
        this.listQuery.productSpecIds = this.manufacturerProductAndSpecId.map(element => element[2])
      }
      dashboardService.QueryInputOutputRatioByReceiver(this.listQuery)
        .then((res) => {
          this.dataList = res.data.datas
          this.totalCount = res.data.recordCount
        })
    },
    handleMore() {
      this.showDialog = true
      this.listQuery.productSpecIds = this.filter.productSpecIds
      this.listQuery.departmentId = this.filter.departmentId
      this.listQuery.areaIds = this.filter.areaIds
      this.listQuery.provinceIds = this.filter.provinceIds
      this.listQuery.isSelectedAllProvince = this.filter.isSelectedAllProvince
      this.listQuery.selectProvinceIds = this.filter.selectProvinceIds
      this.listQuery.pageIndex = 1
      this.search()
    },
    cancleDialog() {
      this.showDialog = false
      this.manufacturerProductAndSpecId = this.filter.manufacturerProductAndSpecId
      this.listQuery =
      {
        pageIndex: 1,
        pageSize: 10,
        receiverName: null,
        departmentId: null,
        productSpecIds: []
      }
    },
    handleExport() {
      this.listQuery.timeRange = this.filter.timeRange
      if (this.manufacturerProductAndSpecId && this.manufacturerProductAndSpecId.length > 0) {
        this.listQuery.productSpecIds = this.manufacturerProductAndSpecId.map(element => element[2])
      }
      dashboardService.ExportInputOutputRatioByReceiver(this.listQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '投入产出比排名.xlsx'

          fileDownload(result.data, filename)
        })
        .catch(() => {

        })
    }
  }
}
</script>
<style lang="scss" scoped>
.cardHeader {
      height: 10px;
    }

.el-card ::v-deep .el-card__header {
  padding: 18px 20px;
  border-bottom: 1px solid #e6ebf5;
}

.el-card ::v-deep .el-card__body {
  padding: 0px 10px 0px 10px;
}
</style>

