# just a flag
ENV = 'development'

# base api
VUE_APP_BASE_API = '/dev-api'

# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js

VUE_CLI_BABEL_TRANSPILE_MODULES = true

#以下的值webpack会根据不同的环境取得不同的值，使用方法：process.env.

#设定一个标题，代表这个环境是development
VUE_APP_TITLE = 'development'

# 打包后文件夹的名字
VUE_APP_FILE_Name = 'dist'