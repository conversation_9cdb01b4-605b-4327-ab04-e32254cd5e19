<template>
  <div>
    <div class="search-container-bg">
      <el-row type="flex" :gutter="10" class="filter-container">
        <el-col :span="span">
          <el-input
            v-model="listQuery.role"
            clearable
            placeholder="角色名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'Role_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Role_Button_Add')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-plus"
            @click="addRole"
          >
            新增
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="roleList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              header-align="center"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              sortable="custom"
              prop="name"
              label="角色名称"
              header-align="center"
              align="center"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="remark"
              label="角色描述"
              header-align="center"
              align="left"
              min-width="300px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.remark }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="100" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i v-if="$isPermitted($store.getters.user, 'Role_Button_Edit')" class="el-icon-edit-outline eltablei" title="编辑" @click="editRole(row)" />
                <i v-if="$isPermitted($store.getters.user, 'Role_Button_Del')" class="el-icon-delete eltablei" title="删除" @click="deleteRole(row)" />
                <i class="el-icon-document eltablei" title="查看" @click="review(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>

    <el-dialog
      :close-on-click-modal="false"
      :title="textMap[dialogStatus]"
      :visible="dialogRoleFormVisible"
      width="60%"
      @close="btnRoleClose"
    >
      <el-form
        ref="dataRoleForm"
        :rules="rules"
        :model="role"
        label-position="right"
        label-width="100px"
        class="el-role-dialogform"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input
            v-model="role.name"
            clearable
            placeholder="角色名称"
            maxlength="50"
          />
        </el-form-item>
        <el-form-item label="角色编码" prop="code">
          <el-input
            v-model="role.code"
            clearable
            placeholder="角色编码"
            maxlength="50"
          />
        </el-form-item>
        <el-form-item label="角色描述" prop="remark">
          <el-input
            v-model="role.remark"
            type="textarea"
            rows="2"
            clearable
            placeholder="角色描述"
            maxlength="500"
          />
        </el-form-item>
        <el-tree
          ref="tree"
          :data="datatree"
          node-key="id"
          show-checkbox
          :default-checked-keys="checkNode"
          :props="defaultProps"
          :render-content="renderContent"
          :check-on-click-node="true"
          :expand-on-click-node="false"
          :default-expand-all="true"
          @node-click="handleNodeClick"
        />
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnRoleClose()"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="dialogStatus === 'create' ? createRole() : updateRole()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      :title="textMap[dialogStatus]"
      :visible="dialogRoleMemberVisible"
      width="60%"
      @close="btnCloseRoleMember"
    >
      <el-row class="query-title filter-container" type="flex" :gutter="10">
        <el-col :span="24" style="margin-bottom:10px">
          <label>角色：{{ role.name }}</label>
        </el-col>
        <el-col :span="24">
          <label>角色描述：{{ role.remark }}</label>
        </el-col>
      </el-row>
      <el-tabs v-model="activeName" @tab-click="tabClick">
        <el-tab-pane label="角色成员" name="first">
          <el-row type="flex" :gutter="10" justify="end">
            <el-col :span="3.5">
              <el-button
                v-if="$isPermitted($store.getters.user, 'Role_Button_EditPerson')"
                class="filter-item"
                type="primary"
                icon="el-icon-plus"
                @click="btnAddRoleMember"
              >
                添加
              </el-button>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-table
                v-loading="roleMemberLoading"
                :data="roleMemberList"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%"
                :default-sort="{ prop: 'createTime', order: 'descending' }"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
                @sort-change="roleMemberSortChange"
              >
                <el-table-column
                  sortable="custom"
                  prop="EnumType"
                  label="类型"
                  align="center"
                  header-align="center"
                  min-width="100px"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.enumTypeDesc }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  sortable="custom"
                  prop="MemberId"
                  label="名称"
                  align="center"
                  header-align="center"
                  min-width="100px"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.memberName }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  fixed="right"
                  label="操作"
                  align="center"
                  header-align="center"
                  width="140"
                  class-name="small-padding fixed-width"
                >
                  <template slot-scope="{ row }">
                    <i v-if="$isPermitted($store.getters.user, 'Role_Button_EditPerson')" class="el-icon-delete eltablei" title="删除" @click="deleteRoleMember(row)" />
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col class="el-colRight">
              <pagination
                v-show="roleMemberTotal > 0"
                :total="roleMemberTotal"
                :page.sync="roleMemberListQuery.pageIndex"
                :limit.sync="roleMemberListQuery.pageSize"
                @pagination="getList"
              />
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="所属员工" name="second">
          <el-row class="query-title" type="flex" :gutter="10">
            <el-col :span="span">
              <el-input
                v-model="roleEmployeeListQuery.jobNo"
                clearable
                placeholder="工号"
                class="filter-item"
                @keyup.enter.native="getRoleEmployeeList"
              /></el-col>
            <el-col :span="span">
              <el-input
                v-model="roleEmployeeListQuery.displayName"
                clearable
                placeholder="姓名"
                class="filter-item"
                @keyup.enter.native="getRoleEmployeeList"
              >
                />
              </el-input></el-col>
            <el-col :span="span">
              <el-button class="filter-item-button" type="primary" icon="el-icon-search" @click="getRoleEmployeeList">
                查询
              </el-button>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-table
                :data="roleEmployeeList"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
                @sort-change="roleEmployeeSortChange"
              >
                <el-table-column fixed label="序号" type="index" align="center" :index="indexRoleMethod" />
                <el-table-column sortable="custom" prop="jobNo" label="工号" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.jobNo }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="displayName" label="姓名" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.displayName }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="departmentId" label="部门" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.departmentName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="岗位" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span v-if="row.employeeStation">
                      <el-col v-for="item in row.employeeStation" :key="item.id">{{ item.stationName }}</el-col>
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="职位" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span v-if="row.employeeStation">
                      <el-col v-for="item in row.employeeStation" :key="item.id">{{ item.positionName }}</el-col>
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col class="el-colRight">
              <pagination v-show="roleEmployeeTotal > 0" :total="roleEmployeeTotal" :page.sync="roleEmployeeListQuery.pageIndex" :limit.sync="roleEmployeeListQuery.pageSize" @pagination="getRoleEmployeeList" />
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnCloseRoleMember()"> 关闭 </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      title="角色成员"
      :visible="dialogEditRoleMemberVisible"
      width="40%"
      @close="btnCloseEditRoleMember"
    >
      <el-form
        ref="roleMemberForm"
        :rules="roleMemberRules"
        :model="roleMember"
        label-position="right"
        label-width="100px"
        class="el-dialogform"
      >
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="类型" prop="enumType">
              <el-select
                v-model="roleMember.enumType"
                class="filter-item"
                placeholder="成员类型"
                clearable
                @change="enumTypeChange"
              >
                <el-option
                  v-for="item in enumTypeList"
                  :key="item.value"
                  :label="item.desc"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="roleMember.enumType === 1" :span="24">
            <el-form-item label="成员" prop="memberId">
              <el-select
                v-model="roleMember.memberId"
                filterable
                remote
                reserve-keyword
                placeholder="请输入关键词"
                :remote-method="userChange"
                clearable
              >
                <el-option
                  v-for="item in userList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="roleMember.enumType === 2" :span="24">
            <el-form-item label="成员" prop="memberId">
              <el-select
                v-model="roleMember.memberId"
                filterable
                remote
                reserve-keyword
                placeholder="请输入关键词"
                :remote-method="employeeChange"
                clearable
              >
                <el-option
                  v-for="item in employeeList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="roleMember.enumType !== 2 && roleMember.enumType !== 1" :span="24">
            <el-form-item label="成员" prop="memberId">
              <el-select
                v-model="roleMember.memberId"
                class="filter-item"
                placeholder="成员"
                clearable
                filterable
              >
                <el-option
                  v-for="item in memberList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnCloseEditRoleMember()"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="createRoleMember()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import ManagementService from '@/api/management'
import MasterDataService from '@/api/masterData'

export default {
  name: 'Role',
  components: {
    Pagination
  },
  data() {
    return {
      span: 4,
      total: 0,
      roleMemberTotal: 0,
      roleEmployeeTotal: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      roleMemberListQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      roleEmployeeListQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      btnExportLoading: false,
      dialogEditFormVisible: false,
      dialogRoleFormVisible: false,
      dialogRoleMemberVisible: false,
      dialogEditRoleMemberVisible: false,
      deptLoading: false,
      dialogStatus: '',
      textMap: {
        create: '新增角色',
        update: '编辑角色',
        review: '查看人员'
      },
      role: {
        id: undefined,
        name: '',
        remark: '',
        code: '',
        enumFlags: 0,
        enumEditFlags: 0,
        permissions: []
      },
      rules: {
        name: [
          {
            required: true,
            type: 'string',
            message: '请输入角色名称',
            trigger: 'blur'
          }
        ],
        remark: [
          {
            required: true,
            type: 'string',
            message: '请输入角色描述',
            trigger: 'blur'
          }
        ],
        code: [
          {
            required: true,
            type: 'string',
            message: '请输入角色编码',
            trigger: 'blur'
          }
        ]
      },
      roleMemberRules: {
        enumType: [
          {
            required: true,
            message: '请选择类型',
            trigger: 'blur'
          }
        ],
        memberId: [
          {
            required: true,
            message: '请选择成员',
            trigger: 'blur'
          }
        ]
      },
      modifyDialogTitle: '',
      modifyDialogIsReadonly: false,
      itemId: null,
      roleList: [],
      columnDictionary: {},
      datatree: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      checkNode: [],
      roleMember: {
        id: undefined,
        roleid: '',
        enumType: '',
        memberId: ''
      },
      roleMemberList: [],
      activeName: 'first',
      roleMemberLoading: false,
      roleEmployeeLoading: false,
      roleEmployeeList: [],
      enumTypeList: [],
      memberList: [],
      userList: [],
      employeeList: [],
      queryName: '',
      testarr: [],
      permissions: {}
    }
  },
  created() {
    this.getList()
    this.getPermissions()
    this.initRoleMemberType()
  },
  mounted() {

  },
  methods: {
    renderContent(h, { node, data, store }) {
      let classname = ''
      // 由于项目中有三级菜单也有四级级菜单，就要在此做出判断
      if (data.isChildNode) {
        classname = 'childTree'
      }
      return h(
        'p',
        {
          class: classname
        },
        data.name
      )
    },
    changeCss() {
      var levelName = document.getElementsByClassName('childTree') // levelname是上面的最底层节点的名字
      for (var i = 0; i < levelName.length; i++) {
        // cssFloat 兼容 ie6-8  styleFloat 兼容ie9及标准浏览器
        levelName[i].parentNode.style.cssFloat = 'left' // 最底层的节点，包括多选框和名字都让他左浮动
        levelName[i].parentNode.style.styleFloat = 'left'
        levelName[i].parentNode.onmouseover = function() {
          this.style.backgroundColor = '#fff'
        }
      }
    },
    initRoleMemberType() {
      var param = {
        enumType: 'RoleMemberType'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.enumTypeList = result.data.datas
        })
        .catch((error) => {
          console.log(error)
        })
    },
    enumTypeChange(val) {
      this.userList = []
      this.employeeList = []
      this.queryName = ''
      this.roleMember.memberId = undefined
      if (val && val !== 1 && val !== 2) {
        ManagementService.QueryMember({ enumType: val }).then(result => {
          if (result) {
            this.memberList = result
          } else {
            this.$notice.resultTip(result)
          }
        }).catch(error => {
          console.log(error)
          this.listLoading = false
        })
      }
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleNodeClick() {

    },
    getList() {
      this.listLoading = true
      ManagementService.QueryRole(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.roleList = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    getRoleMemberList() {
      this.roleMemberLoading = true
      this.roleMemberListQuery.roleid = this.role.id
      ManagementService.QueryRoleMember(this.roleMemberListQuery).then(result => {
        if (result.succeed) {
          this.roleMemberLoading = false
          this.roleMemberList = result.data.datas
          this.roleMemberTotal = result.data.recordCount
          this.roleMemberListQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.roleMemberLoading = false
      })
    },
    getRoleEmployeeList() {
      this.roleEmployeeLoading = true
      this.roleEmployeeListQuery.roleid = this.role.id
      ManagementService.QueryRoleEmployee(this.roleEmployeeListQuery).then(result => {
        if (result.succeed) {
          this.roleEmployeeLoading = false
          this.roleEmployeeList = result.data.datas
          this.roleEmployeeTotal = result.data.recordCount
          this.roleEmployeeListQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.roleEmployeeLoading = false
      })
    },
    deptChange() {
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    indexRoleMethod(index) {
      return (this.roleEmployeeListQuery.pageIndex - 1) * this.roleEmployeeListQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    roleMemberSortChange(column, prop, order) {
      this.roleMemberListQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.roleMemberListQuery.order = orderSymbol + column.prop
      this.getRoleMemberList()
    },
    roleEmployeeSortChange(column, prop, order) {
      this.roleEmployeeListQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.roleEmployeeListQuery.order = orderSymbol + column.prop
      this.getRoleEmployeeList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    getPermissions() {
      ManagementService.QueryPermissions().then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.datatree = result.data
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    addRole() {
      this.dialogStatus = 'create'
      this.dialogRoleFormVisible = true
      this.$nextTick(() => {
        this.changeCss()
        this.resetRole()
        this.$refs['dataRoleForm'].clearValidate()
      })
    },
    resetRole() {
      this.role.id = undefined
      this.role.name = ''
      this.role.remark = ''
      this.role.code = ''
      this.$refs.tree.setCheckedKeys([])
    },
    createRole() {
      this.$refs['dataRoleForm'].validate((valid) => {
        if (valid) {
          this.role.permissions = this.$refs.tree.getCheckedNodes().concat(this.$refs.tree.getHalfCheckedNodes())
          ManagementService.AddRole(this.role)
            .then((result) => {
              if (result.succeed) {
                this.dialogRoleFormVisible = false
                this.showMessage('保存成功', 'success')
                this.getList()
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
            })
        }
      })
    },
    editRole(row) {
      this.dialogStatus = 'update'
      this.role = Object.assign({}, row)
      this.checkNode = []
      this.dialogRoleFormVisible = true
      this.$nextTick(() => {
        this.changeCss()
        this.$refs.tree.setCheckedKeys([])
        ManagementService.GetRolePermissions({ roleid: row.id }).then(result => {
          this.listLoading = false
          if (result.succeed) {
            result.data.forEach(e => {
              this.checkNode.push(e.id)
            })
          } else {
            this.$notice.resultTip(result)
          }
        }).catch(error => {
          console.log(error)
          this.listLoading = false
        })
      })
    },
    updateRole() {
      this.$refs['dataRoleForm'].validate((valid) => {
        if (valid) {
          this.role.permissions = this.$refs.tree.getCheckedNodes().concat(this.$refs.tree.getHalfCheckedNodes())
          ManagementService.UpdateRole(this.role)
            .then((result) => {
              if (result.succeed) {
                this.dialogRoleFormVisible = false
                this.showMessage('保存成功', 'success')
                this.getList()
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
            })
        }
      })
    },
    deleteRole(row) {
      this.$confirm('确定删除此角色吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.role = Object.assign({}, row)
          ManagementService.DeleteRole(this.role)
            .then((result) => {
              if (result.succeed) {
                this.getList()
                this.showMessage('删除成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
              this.loading = false
            })
        })
        .catch((error) => {
          if (!error.succeed) {
            this.showMessage('取消删除', 'info')
          }
        })
    },
    review(row) {
      this.role = Object.assign({}, row)
      this.roleMember.roleid = row.id
      this.activeName = 'first'
      this.getRoleMemberList()
      this.dialogStatus = 'review'
      this.dialogRoleMemberVisible = true
    },
    btnCloseRoleMember() {
      this.roleEmployeeListQuery = {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
      this.dialogRoleMemberVisible = false
    },
    btnRoleClose() {
      this.$nextTick(() => {
        this.$refs['dataRoleForm'].clearValidate()
      })
      this.dialogRoleFormVisible = false
    },
    tabClick(tab) {
      if (tab.name === 'second') {
        this.getRoleEmployeeList()
      }
    },
    btnAddRoleMember() {
      this.dialogEditRoleMemberVisible = true
      this.roleMember.enumType = ''
      this.roleMember.memberId = undefined
      this.$nextTick(() => {
        this.$refs['roleMemberForm'].clearValidate()
      })
    },
    deleteRoleMember(row) {
      this.$confirm('确定删除此角色成员吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var param = {
          id: row.id
        }
        ManagementService.DeleteRoleMember(param).then(result => {
          if (result.succeed) {
            this.getRoleMemberList()
            this.$notice.message('删除成功', 'success')
          } 
        })
          .catch(error => {
            console.log(error)
          })
      }).catch(error => {
        this.listLoading = false
        if (!error.succeed) {
          this.$notice.message('取消删除', 'info')
        }
      })
    },
    btnCloseEditRoleMember() {
      this.dialogEditRoleMemberVisible = false
    },
    createRoleMember() {
      this.$refs['roleMemberForm'].validate((valid) => {
        if (valid) {
          ManagementService.AddRoleMember(this.roleMember)
            .then((result) => {
              if (result.succeed) {
                this.dialogEditRoleMemberVisible = false
                this.showMessage('保存成功', 'success')
                this.getRoleMemberList()
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
            })
        }
      })
    },
    userChange(val) {
      if (val) {
        var param = {
          user: val
        }
        ManagementService.QueryUserSelect(param).then(result => {
          if (result) {
            this.userList = result
          } else {
            this.$notice.resultTip(result)
          }
        }).catch(error => {
          console.log(error)
        })
      }
    },
    employeeChange(val) {
      if (val) {
        var param = {
          displayName: val
        }
        ManagementService.QueryEmployeeSelect(param).then(result => {
          if (result) {
            this.employeeList = result
          } else {
            this.$notice.resultTip(result)
          }
        }).catch(error => {
          console.log(error)
        })
      }
    },
    selectChange(id) {
      if (id === '') {
        this.userList = []
        this.employeeList = []
        this.queryName = ''
      }
    },
    exprot() {

    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },
    onHidden() {
      this.dialogEditFormVisible = false
    },
    onRefresh() {
      this.getList()
      this.dialogEditFormVisible = false
    }

  }
}
</script>
<style>
.query-title{
  margin-bottom: 10px;
}
.filter-item{
  margin: 0 10px 5px 0;
}

.el-role-dialogform {
  width: 90%;
  margin-left: 50px;
}
</style>
