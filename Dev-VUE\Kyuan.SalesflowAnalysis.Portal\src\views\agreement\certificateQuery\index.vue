<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filter-container">
        <el-col :span="span">
          <el-input
            v-model="listQuery.code"
            clearable
            placeholder="编号"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.name"
            clearable
            placeholder="名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.collaborativeReceiverName"
            clearable
            placeholder="合作主体名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="listQuery.timeRange"
            clearable
            class="filter-item"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.paymentTypeId"
            class="filter-item"
            placeholder="支付方式"
            clearable
          >
            <el-option
              v-for="item in rebatePaymentTypeList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.payWithinDaysId"
            class="filter-item"
            placeholder="支付天数"
            clearable
          >
            <el-option
              v-for="item in rebatePayWithInDaysList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.enumStatus"
            class="filter-item"
            placeholder="状态"
            clearable
          >
            <el-option
              v-for="item in enumStatusList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'AgreementCertificate_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'AgreementCertificate_Button_Add')"
            type="primary"
            icon="el-icon-plus"
            @click="handleAddCertificate"
          >
            新增
          </el-button>
        </el-col>
      </el-row>
    </div>

    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="dataList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed="left"
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column label="编号" fixed="left" align="center" sortable="custom" min-width="120px" prop="Code">
              <template slot-scope="{ row }">
                <span>{{ row.code }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="名称"
              sortable="custom"
              fixed="left"
              min-width="250px"
              header-align="center"
              align="left"
              prop="Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column
              label="名称"
              sortable="custom"
              min-width="250px"
              header-align="center"
              align="left"
              prop="Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column> -->

            <el-table-column
              label="支付方式"
              min-width="100px"
              sortable="custom"
              align="center"
              prop="PaymentTypeId"
            >
              <template slot-scope="{ row }">
                <span>{{ row.paymentType }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="支付天数"
              min-width="100px"
              sortable="custom"
              align="center"
              prop="PayWithinDaysId"
            >
              <template slot-scope="{ row }">
                <span>{{ row.payWithinDays }}</span>
              </template>
            </el-table-column>
            <el-table-column label="起始日期" align="center" sortable="custom" min-width="100px" prop="StartDate">
              <template slot-scope="{ row }">
                <span v-if="row.startDate">{{ row.startDate |parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="截止日期" align="center" sortable="custom" min-width="100px" prop="EndDate">
              <template slot-scope="{ row }">
                <span v-if="row.endDate">{{ row.endDate |parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="签署日期" align="center" sortable="custom" min-width="100px" prop="SignDate">
              <template slot-scope="{ row }">
                <span v-if="row.signDate">{{ row.signDate |parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="状态"
              sortable="custom"
              min-width="80px"
              align="center"
              prop="EnumStatus"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="120" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i v-if="$isPermitted($store.getters.user, 'AgreementCertificate_Button_Query')" class="el-icon-document eltablei" title="查看明细" @click="reviewDetail(row)" />
                <i v-if="$isPermitted($store.getters.user, 'AgreementCertificate_Button_Edit') && row.enumStatus!=$constDefinition.rebateAgreementCertificateStatus.GenerateFile" class="el-icon-edit-outline eltablei" title="编辑" @click="handleEdit(row)" />
                <i v-if="$isPermitted($store.getters.user, 'AgreementCertificate_Button_MakeFile') && row.enumStatus!=$constDefinition.rebateAgreementCertificateStatus.GenerateFile" class="el-icon-circle-check eltablei" title="生成文件" @click="handleCreateFile(row.id)" />
                <i v-if=" row.enumStatus==$constDefinition.rebateAgreementCertificateStatus.GenerateFile" class="el-icon-download eltablei" title="下载文件" @click="handleDownLoadFile(row)" />
                <i v-if="$isPermitted($store.getters.user, 'AgreementCertificate_Button_Del') && row.enumStatus!=$constDefinition.rebateAgreementCertificateStatus.GenerateFile" class="el-icon-delete eltablei" title="删除" @click="handleDelete(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
    <addCertificate ref="addCertificate" @refreshData="getList" />
    <viewCertificate ref="viewCertificate" />

  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

import AgreementService from '@/api/agreement'
import MasterDataService from '@/api/masterData'
import addCertificate from './components/addCertificate.vue'
import viewCertificate from './components/viewCertificate.vue'
import FileService from '@/api/file'

export default {
  name: 'CertificateQuery',
  components: {
    Pagination,
    addCertificate,
    viewCertificate
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      dataList: [],
      deptLoading: false,
      deptList: [],
      provinceCityLoading: false,
      provinceCityList: [],
      productAndSpecList: [],
      rebateAgreementId: null,
      enumStatusList: [],
      dialogImportVisible: false,
      btnBatchGenerateContractLoading: false,
      rebatePaymentTypeList: [],
      rebatePayWithInDaysList: []
    }
  },
  created() {
    this.initAgreementStatus()
    this.handleFilter()
    this.queryRebatePaymentType()
    this.queryRebatePayWithInDays()
  },
  methods: {
    initAgreementStatus() {
      var param = {
        enumType: 'RebateAgreementCertificateStatus'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.enumStatusList = result.data.datas
          this.$forceupdate()
        })
        .catch(() => {
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      AgreementService.QueryRebateAgreementCertificate(this.listQuery).then(res => {
        this.listLoading = false
        this.dataList = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(() => {
        this.listLoading = true
      })
    },
    queryRebatePaymentType() {
      AgreementService.QueryRebatePaymentType().then(res => {
        this.rebatePaymentTypeList = res.data.datas
      }).catch(() => {
      })
    },
    queryRebatePayWithInDays() {
      AgreementService.QueryRebatePayWithInDays().then(res => {
        this.rebatePayWithInDaysList = res.data.datas
      }).catch(() => {
      })
    },
    selectUpdate() {
      this.$forceUpdate()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    reviewDetail(row) {
      this.$refs.viewCertificate.init(row)
    },
    handleEdit(row) {
      this.$refs.addCertificate.init(row)
    },
    handleDelete(row) {
      this.$confirm('确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        AgreementService.DeleteRebateAgreementCertificate(row).then(result => {
          if (result.succeed) {
            this.$notice.message('删除成功', 'success')
            this.handleFilter()
          }
        })
      }).catch(() => {

      })
    },
    handleCreateFile(id) {
      AgreementService.GenerateCertificate({ id: id })
        .then((result) => {
          if (result.succeed) {
            this.$notice.message('已生成', 'success')
            this.handleFilter()
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    handleDownLoadFile(row) {
      var objectType = 'MemorandumOfCooperation'
      FileService.downloadAttachmentBusiness(row.id, objectType).then(res => {
        const fileDownload = require('js-file-download')
        fileDownload(res, `${row.name}.pdf`)
      })
        .catch(() => {
        })
    },
    handleAddCertificate() {
      this.$refs.addCertificate.init()
    },
    closeImportDialog() {
      this.$refs.refImportAgreement.clear()
      this.dialogImportVisible = false
      this.handleFilter()
    }
  }
}
</script>
<style scoped>
.flexwarp {
    display: flex;
    flex-wrap: wrap;
}

</style>

