import HttpApi from './libs/api.request'

const controller = 'Location'

const api = new HttpApi(controller)

export default {
  QueryProvince() {
    return api.get('QueryProvinceSelect')
  },
  QueryCity() {
    return api.get('QueryCitySelect')
  },
  QueryCities(params) {
    return api.get('QueryCity', params)
  },
  QueryProvinceCityCascader() {
    return api.get('QueryProvinceCityCascader')
  },
  QueryCounties(params) {
    return api.get('QueryCounty', params)
  },
  QueryProvinceCityCountyCascader() {
    return api.get('QueryProvinceCityCountyCascader')
  },
  QueryProvinceSelectForDataReport(params) {
    return api.post('QueryProvinceSelectForDataReport', params)
  }
}
