<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex" style="margin-bottom:-20px;">
        <el-col :span="span">
          <el-select
            v-model="filter.ProvinceId"
            class="filter-item"
            placeholder="省份"
            clearable
          >
            <el-option
              v-for="item in province"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="filter.NameCn"
            clearable
            placeholder="城市名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'City_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              :index="indexMethod"
              type="index"
              align="center"
            />
            <el-table-column
              sortable="custom"
              prop="Province.NameCn"
              label="省份"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.provinceNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="NameCn"
              label="城市名称"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.nameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Code"
              label="Code编码"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.code }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="AliasName"
              label="别称"
              min-width="100px"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.aliasName }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="filter.pageIndex"
            :limit.sync="filter.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import LocationService from '@/api/location'

export default {
  name: 'Location',
  components: {
    Pagination
  },
  data() {
    return {
      span: 4,
      list: [],
      province: [],
      total: 0,
      listLoading: true,
      filter: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
    }
  },
  created() {
    this.queryProvince()
    this.getList()
  },
  methods: {
    indexMethod(index) {
      return (this.filter.pageIndex - 1) * this.filter.pageSize + index + 1
    },
    queryProvince() {
      LocationService.QueryProvince()
        .then((result) => {
          this.province = result
        })
        .catch((error) => {
          console.log(error)
          this.loading = false
        })
    },
    getList() {
      this.listLoading = true

      LocationService.QueryCities(this.filter)
        .then((result) => {
          this.listLoading = false

          this.list = result.data.datas
          this.total = result.data.recordCount
          this.filter.pageIndex = result.data.pageIndex
        })
        .catch((error) => {
          console.log(error)
          this.loading = false
        })
    },
    handleFilter() {
      this.filter.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.filter.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.filter.order = orderSymbol + column.prop
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    }
  }
}
</script>
