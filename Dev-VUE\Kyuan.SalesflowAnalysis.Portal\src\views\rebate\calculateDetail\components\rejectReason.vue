<template>
  <div>
    <el-dialog title="拒绝原因查看" width="60%" :close-on-click-modal="false" :visible="rejectReasonVisible" @close="handleCancle()">
      <el-form ref="dataForm" :model="rebateResultModel" label-position="right" label-width="120px" class="el-dialogform">
        <el-row type="flex" class="filter-container">
          <el-col :span="24">
            <el-form-item label="协议名称">
              {{ rebateResultModel.rebateAgreementName }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="返利接收方名称">
              {{ rebateResultModel.rebateReceiverName }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="产品/规格">
              {{ rebateResultModel.productAndSpec }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="返利金额">
              {{ rebateResultModel.rebateAmount | toMoney }}元
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="拒绝原因">
              {{ rebateResultModel.rejectReason }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleCancle()">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

export default {
  name: '',
  components: {
  },
  props: {
  },
  data() {
    return {
      rejectReasonVisible: false,
      span: 12,
      rebateResultModel: {}
    }
  },
  created() {
  },
  methods: {
    init(data) {
      this.rebateResultModel = data
      this.rejectReasonVisible = true
    },
    close() {
      this.rejectReasonVisible = false
      this.$refs['dataForm'].resetFields()
    },
    handleCancle() {
      this.close()
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    }
  }

}
</script>
<style lang="css">
</style>
