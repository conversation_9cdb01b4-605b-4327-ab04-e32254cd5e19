<template>
  <div class="app-container">
    <div>
      <el-row :gutter="20">
        <el-col :span="6" :xs="24">
          <el-card id="left-card" class="box-card" shadow="never">
            <div slot="header" class="clearfix">
              <span class="title" style="font-size:16px;"><i class="el-icon-info" /> {{ employee.displayName }}</span>
            </div>
            <el-form>
              <div class="title"><i class="el-icon-user" /> 用户名</div>
              <div class="content">{{ employee.username }}</div>

              <div class="title"><i class="el-icon-key" /> 唯一码</div>
              <div class="content">{{ employee.uid }}</div>

              <div v-if="employee.enumTypeDesc=='员工'">
                <div v-if="employee.jobNo!=''">
                  <div class="title"><i class="el-icon-c-scale-to-original" /> 工号</div>
                  <div class="content">{{ employee.jobNo }}</div>
                </div>
                <div v-if="employee.title!=''">
                  <div class="title"><svg-icon icon-class="job" /> 职务</div>
                  <div class="content">{{ employee.title }}</div>
                </div>

                <div class="title"><i class="el-icon-male" /> 性别</div>
                <div class="content">{{ employee.enumGenderDesc }}</div>

              </div>
              <div v-if="employee.mobile!=''">
                <div class="title"><svg-icon icon-class="mobile" /> 手机</div>
                <div class="content">{{ employee.mobile }}</div>
              </div>

              <div v-if="employee.email!=''">
                <div class="title"><i class="el-icon-message" /> 邮箱</div>
                <div class="content">{{ employee.email }}</div>
              </div>
              <div v-if="employee.enumTypeDesc=='员工'">
                <div v-if="employee.enumIdentityTypeDesc!=''">
                  <div class="title"><i class="el-icon-postcard" /> 证件类型</div>
                  <div class="content">{{ employee.enumIdentityTypeDesc }}</div>
                </div>
                <div v-if="employee.identityNumber!=''">
                  <div class="title"><svg-icon icon-class="id-card" /> 证件号码</div>
                  <div class="content">{{ employee.identityNumber }}</div>
                </div>
              </div>

              <div class="title"><svg-icon icon-class="status" /> 状态</div>
              <div v-if="employee.enumTypeDesc!='员工'" class="content">{{ employee.enumStatusDesc }}</div>
              <div v-if="employee.enumTypeDesc=='员工'" class="content">{{ employee.enumEmployeeStatusDesc }}</div>

              <div v-if="employee.enumTypeDesc=='员工'">
                <div v-if="employee.majorDeptName!=''">
                  <div class="title"><svg-icon icon-class="department" /> 主岗部门</div>
                  <div class="content">{{ employee.majorDeptName }}</div>
                </div>
                <div v-if="employee.majorPositionName!=''">
                  <div class="title"><svg-icon icon-class="title" />主岗职位</div>
                  <div class="content">{{ employee.majorPositionName }}</div>
                </div>
              </div>

              <el-form-item style="margin-top:20px;">
                <el-button type="primary" @click="btnEdit()">
                  <i class="el-icon-edit" /> 编辑
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
        <el-col :span="18" :xs="24">
          <el-card v-if="employee.enumTypeDesc=='员工'" class="box-card" shadow="never">
            <el-tabs v-model="activeName" type="card">
              <el-tab-pane label="岗位" name="first">
                <el-table :default-sort="{prop: 'positionGrade', order: 'descending'}" :data="empStationList" border stripe fit highlight-current-row style="width: 100%;">
                  <el-table-column label="部门" prop="deptName" />
                  <el-table-column label="职位" prop="positionName" />
                  <el-table-column label="是否主岗" prop="stationName" align="center" width="100">
                    <template slot-scope="{ row }">
                      <div v-if="row.isMajor"><i class="el-icon-check" /></div>
                    </template>
                  </el-table-column>

                </el-table>
              </el-tab-pane>
            </el-tabs>

          </el-card>
        </el-col>
      </el-row>
    </div>
    <el-dialog title="个人中心维护" :visible.sync="dialogFormVisible">

      <el-form
        ref="dataForm"
        :rules="rules"
        :model="employee1"
        label-position="right"
        label-width="100px"
        style="margin-right:20px;"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item style="margin-bottom: 20px !important;" label="唯一码" prop="uId">
              {{ employee1.uid }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="姓名" prop="displayName">
              {{ employee1.displayName }}
            </el-form-item>

          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="手机" prop="mobile">
              <el-input v-model="employee1.mobile" clearable placeholder="请输入手机" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item style="margin-bottom: 20px !important;" label="邮箱" prop="email">
              <el-input v-model="employee1.email" clearable placeholder="请输入邮箱" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col v-if="employee1.enumTypeDesc=='员工'" :span="12">
            <el-form-item label="性别" prop="enumGender">
              <el-select v-model="employee1.enumGender" style="width:100%;" class="filter-item" placeholder="请选择性别">
                <el-option v-for="item in genderOptions" :key="item.value" :label="item.desc" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="状态">
              <div v-if="employee1.enumTypeDesc!='员工'">{{ employee1.enumStatusDesc }}</div>
              <div v-if="employee1.enumTypeDesc=='员工'">{{ employee1.enumEmployeeStatusDesc }}</div>
            </el-form-item>

          </el-col>
        </el-row>

        <el-row v-if="employee1.enumTypeDesc=='员工'">
          <el-col :span="12">
            <el-form-item style="margin-bottom: 20px !important;" label="证件类型" prop="enumIdentityType">
              <el-select v-model="employee1.enumIdentityType" style="width:100%;" class="filter-item" placeholder="请选择">
                <el-option v-for="item in identityTypeOptions" :key="item.value" :label="item.desc" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证件号码" prop="identityNumber">
              <el-input v-model="employee1.identityNumber" placeholder="请输入证件号码" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="dialogFormVisible=false ">
          关闭
        </el-button>
        <el-button type="primary" icon="el-icon-check" @click="onSubmit()">
          保存
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>

import masterDataService from '@/api/masterData'
import homeService from '@/api/home'

export default {
  name: 'Profile',
  data() {
    return {
      activeName: 'first',
      dialogFormVisible: false,
      identityTypeOptions: [],
      employee1: {},
      employee: {
        id: '',
        displayName: '',
        uid: '',
        jobNo: '',
        title: '',
        email: '',
        mobile: '',
        enumIdentityType: '',
        identityNumber: '',
        enumStatus: '',
        enumGender: ''

      },
      rules: {
        enumGender: [
          { required: true, message: '请选择性别', trigger: 'change' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' }
        ]
      },
      empStationList: [],
      genderOptions: []

    }
  },
  created() {
    this.initEnums()
    this.getUserProfile()
  },
  methods: {
    initEnums() {
      masterDataService.GetEnumInfos({
        EnumType: 'Gender'
      }).then(res => {
        this.genderOptions = res.data.datas
      }).catch(res => {

      })
      masterDataService.GetEnumInfos({
        EnumType: 'IdentityType'
      }).then(res => {
        this.identityTypeOptions = res.data.datas
      }).catch(res => {

      })
    },
    getUserProfile() {
      homeService.GetUserProfile().then(res => {
        this.employee = res.data
        this.empStationList = res.data.employeeStations
      })
    },

    onSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          var regex = /^[A-Za-z0-9]+([-_.][A-Za-z0-9]+)*@([A-Za-z0-9]+[-.])+[A-Za-z0-9]{2,5}$/
          if (!regex.test(this.employee1.email.trim())) {
            this.$alert('邮箱格式不正确，请重新输入', '提示', {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确定',
              type: 'error'
            })
          } else {
            var para = Object.assign({}, this.employee1)
            if (para.enumIdentityType === '') {
              para.enumIdentityType = 0
            }
            homeService.UpdateUserProfile(para).then(res => {
              if (res.succeed) {
                this.getUserProfile()
                this.dialogFormVisible = false
                this.$message({
                  showClose: true,
                  message: '更新成功',
                  type: 'success',
                  duration: 1500
                })
              } else {
                this.$alert(res.messages.toString().replace(/,/g, '<br/>'), '提示', {
                  dangerouslyUseHTMLString: true,
                  confirmButtonText: '确定',
                  type: 'error'
                })
              }
            })
          }
        }
      })
    },
    btnEdit() {
      this.employee1 = Object.assign({}, this.employee)
      if (this.employee1.enumGender === 0) {
        this.employee1.enumGender = ''
      }
      if (this.employee1.enumIdentityType === 0) {
        this.employee1.enumIdentityType = ''
      }
      this.dialogFormVisible = true
    }

  }
}
</script>
<style>

#left-card{
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif !important;
  font-size: 14px;
  color: #606266;
}

#left-card .el-card__body{
  padding-top: 5px !important;
}
#left-card .el-card__header{
  padding-top: 8px !important;
  padding-bottom: 8px !important;
}

.title{
  line-height: 30px !important;
  padding-top: 5px;
  font-weight: 700;
}
#left-card .content{
  line-height: 20px !important;
  font-size: 15px;
}
</style>
