<template>
  <div>
    <el-dialog
      custom-class="el-dialog-s"
      :title="title"
      width="80%"
      append-to-body
      :close-on-click-modal="false"
      :visible="showAddDialog"
      @close="closeDialog"
    >
      <el-form
        ref="dataForm"
        :model="dataModel"
        label-position="right"
        label-width="100px"
        class="el-dialogform"
        style="width: 90%"
      >
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="甲方">
              {{ dataModel.rebatePartyName }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="乙方" prop="rebateReceiverName">
              <el-row>
                <el-col :span="24" style="padding-right: 5px">
                  {{ dataModel.rebateReceiverName }}
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="支付方式" prop="paymentTypeId">
              {{ dataModel.paymentTypeName }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="支付天数" prop="payWithinDaysId">
              {{ dataModel.payWithinDaysName }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="起始日期" prop="startDate">
              {{ dataModel.startDate | parseTime("{y}-{m}-{d}") }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="截止日期" prop="endDate">
              {{ dataModel.endDate | parseTime("{y}-{m}-{d}") }}
            </el-form-item>
          </el-col>

          <el-col :span="span">
            <el-form-item label="签署日期" prop="signDate">
              {{ dataModel.signDate | parseTime("{y}-{m}-{d}") }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-divider content-position="left">合作主体名单</el-divider>
      <el-row>
        <el-col :span="24">
          <el-table
            max-height="250"
            :data="dataModel.rebateAgreementCertificateReceivers"
            stripe
            border
            fit
            highlight-current-row
            :header-cell-class-name="'tableStyle'"
            style="width: 100%"
          >
            <el-table-column fixed label="序号" type="index" align="center" />
            <el-table-column
              label="主体名称"
              align="left"
              min-width="120px"
              prop="receiverName"
            />
            <el-table-column
              label="纳税人识别号"
              header-align="center"
              align="left"
            >
              <template slot-scope="{ row }">
                <span>{{
                  row.taxRegistrationNo === "NULL" ? "" : row.taxRegistrationNo
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="备注"
              header-align="center"
              align="left"
              prop="remark"
            />
          </el-table>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeDialog"> 关闭 </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import AgreementService from '@/api/agreement'
export default {
  data() {
    return {
      span: 12,
      title: '查看一级商名单备忘录',
      showAddDialog: false,
      dataModel: {

      }

    }
  },
  created() {
  },
  methods: {
    init(row) {
      AgreementService.GetRebateAgreementCertificateByID({ id: row.id }).then(res => {
        this.dataModel = res.data
      }).catch(() => {
      })
      this.showAddDialog = true
    },
    closeDialog() {
      this.showAddDialog = false
      this.dataModel = {}
    }
  }
}
</script>
<style scoped>
.el-collapse-item ::v-deep .el-collapse-item__header {
  font-size: 14px !important;
  font-weight: 600 !important;
}
</style>
