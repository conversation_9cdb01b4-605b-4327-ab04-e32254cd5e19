<template>
  <el-dialog
    title="配置ExcelSheet字段匹配关系"
    width="60%"
    :close-on-click-modal="false"
    :visible="showDialog"
    append-to-body
    @close="cancle()"
  >
    <div class="tips-remark">
      <span class="info"><i class="el-alert__icon el-icon-info" /><span>{{ remark }}</span></span>
    </div>
    <el-form ref="dataForm" :model="bonusResultEntityDetailMapping" label-position="right">
      <el-row>
        <el-col :span="24">
          <el-form-item>
            <el-table
              :data="bonusResultEntityDetailMappingList"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%;"
              :default-sort="{prop: 'createTime', order: 'descending'}"
              :header-cell-class-name="'tableStyle'"
            >
              <el-table-column
                prop="TemplateColumnName"
                label="Sheet字段名称"
                header-align="center"
                align="center"
                min-width="60px"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.templateColumnName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="对应数据库字段" min-width="60px" align="center">
                <template slot-scope="{ row }">
                  <el-select
                    v-model="row.bonusResultEntityDetailId"
                    style="width: 100%"
                    class="filter-item"
                    placeholder="对应数据库字段"
                    filterable
                    clearable
                    :disabled="row.tempEnable"
                    @change="variableDetailChange(row.bonusResultEntityDetailId,row)"
                  >
                    <el-option
                      v-for="item in bonusResultEntityDetails"
                      :key="item.id"
                      :label="item.title"
                      :value="item.id"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="数据库字段说明" min-width="140px" align="left" header-align="center">
                <template slot-scope="{ row }">
                  {{ row.remark }}
                </template>
              </el-table-column>
              <el-table-column
                fixed="right"
                label="操作"
                align="center"
                header-align="center"
                width="70"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="{ row }">
                  <i v-if="row.tempEnable" class="el-icon-delete eltablei" @click="handleDelete(row)" />
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" icon="el-icon-check" @click="handleSave">
        保存
      </el-button>
      <el-button icon="el-icon-close" @click="closeDialog">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import BonusService from '@/api/bonus'
export default {
  components: {},
  data() {
    return {
      span: 12,
      bonusResultEntityDetailMappingList: [],
      bonusResultEntityDetails: [],
      bonusResultEntityDetailMapping: {
        tempEnable: {
          type: Boolean,
          default: false
        },
        tempSave: {
          type: Boolean,
          default: false
        },
        remark: ''
      },
      showDialog: false,
      listLoading: false,
      remark: ''
    }
  },
  methods: {
    initPage(row) {
      this.showDialog = true
      this.initresultEntityDetailMappings(row.id)
      this.initresultEntityDetails(row.bonusResultEntityId)
    },
    initresultEntityDetailMappings(curBonusResultEntityMappingId) {
      const para = {
        bonusResultEntityMappingId: curBonusResultEntityMappingId
      }
      BonusService.QueryBonusResultEntityDetailMapping(para)
        .then((result) => {
          if (result.succeed) {
            this.bonusResultEntityDetailMappingList = result.data
            var isEdit = this.bonusResultEntityDetailMappingList.filter(item => item.bonusResultEntityDetailId !==
                undefined && item.bonusResultEntityDetailId !== '' && item.bonusResultEntityDetailId !== null)
            if (isEdit.length > 0) {
              this.bonusResultEntityDetailMappingList.forEach((item) => {
                item.tempEnable = true
                if (item.bonusResultEntityDetailRemark !== undefined && item.bonusResultEntityDetailDataName
                  .length > 0) {
                  item.remark = '数据库字段：' + item.bonusResultEntityDetailDataName + '；'
                }
                if (item.bonusResultEntityDetailRemark !== undefined && item.bonusResultEntityDetailRemark
                  .length > 0) {
                  item.remark += '说明：' + item.bonusResultEntityDetailRemark + '；'
                }
              })
            } else {
              this.bonusResultEntityDetailMappingList.forEach((item) => {
                item.tempEnable = false
              })
            }
          } else {
            this.$notice.resultTip(result)
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    initresultEntityDetails(curbonusResultEntityId) {
      const para = {
        bonusResultEntityId: curbonusResultEntityId
      }
      BonusService.QueryBonusResultEntityDetailSelect(para).then((res) => {
        this.bonusResultEntityDetails = res
        this.remark = '需要完整配置数据库字段'
        this.bonusResultEntityDetails.forEach((item) => {
          this.remark += '[' + item.title + ']、'
        })
        this.remark = this.remark.substr(0, this.remark.length - 1)
        this.remark += '与Sheet字段名称关系，其他Sheet字段无需配置。'
      })
    },
    cancle() {
      this.showDialog = false
    },
    variableDetailChange(selectId, row) {
      if (selectId !== undefined && selectId != null && selectId !== '') {
        let resultEntityDetail = {}
        resultEntityDetail = this.bonusResultEntityDetails.find((item) => {
          return selectId === item.id
        })
        row.bonusResultEntityDetailRemark = resultEntityDetail.remark
        row.bonusResultEntityDetailDataName = resultEntityDetail.dataName
        row.tempSave = true
        row.remark = '数据库字段：' + row.bonusResultEntityDetailDataName + '；'
        if (row.bonusResultEntityDetailRemark.length > 0) {
          row.remark += '说明：' + row.bonusResultEntityDetailRemark + '；'
        }
      } else {
        row.bonusResultEntityDetailRemark = ''
        row.bonusResultEntityDetailDataName = ''
        row.tempSave = true
        row.remark = ''
      }
    },
    handleSave() {
      this.listLoading = true
      var saveData = this.bonusResultEntityDetailMappingList.filter(item => item.tempSave === true)
      if (saveData.length === 0) {
        this.$notice.message('没有要保存的选项', 'error')
        return
      }
      BonusService.UpdateBonusResultEntityDetailMapping(saveData)
        .then((result) => {
          this.listLoading = false
          if (result.succeed) {
            this.$notice.message('保存成功', 'success')
            this.scoped()
          } else {
            this.$notice.message('保存失败，请联系管理员', 'error')
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    handleDelete(row) {
      if (row.bonusResultEntityDetailId !== undefined) {
        row.bonusResultEntityDetailId = undefined
      }

      row.bonusResultEntityDetailDataName = ''
      row.bonusResultEntityDetailRemark = ''
      row.tempEnable = false
      row.tempSave = true
      row.remark = ''
    },
    closeDialog() {
      this.bonusResultEntityDetails = []
      this.showDialog = false
      this.remark = ''
      this.$emit('success')
    }
  }
}

</script>
<style scoped>
  .el-dialog-s {
    z-index: 12;
  }

  .el-dialog-ls {
    z-index: 13;
  }

  .tips-remark {
    border: 1px solid #dfe6ec;
    margin-bottom: 10px;
    padding: 5px ;
  }
  .info {
  width: 100%;
  /* padding: 8px 16px; */
  margin: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 4px;
  line-height:1.2;
  position: relative;
  overflow: hidden;
  opacity: 1;
  -webkit-transition: opacity .2s;
  transition: opacity .2s;
  color: #909399;
  }

</style>
