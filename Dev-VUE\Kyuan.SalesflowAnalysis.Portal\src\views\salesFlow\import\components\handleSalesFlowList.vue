<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10">
        <el-col :span="span">
          <el-select
            v-model="listQuery.isHandled"
            style="width: 100%"
            class="filter-item"
            placeholder="是否已处理"
            clearable
            @input="isHandledChange"
          >
            <el-option
              v-for="item in handleList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.enumErrorSalesFlowType"
            style="width: 100%"
            class="filter-item"
            placeholder="错误描述"
            clearable
          >
            <el-option
              v-for="item in errorSalesFlowTypeList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" justify="end" :gutter="10">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Import_Button_ErrorDataExport')"
            class="filter-item-button"
            type="primary"
            :loading="btnExportLoading"
            icon="el-icon-download"
            @click="handleExportError"
          >
            错误数据导出
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Import_Button_BatchUpdate')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-refresh"
            @click="handleBatchUpdate"
          >
            批量更新
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row type="flex" justify="end" :gutter="10">
        <el-col :span="3.5" />
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
            <el-table-column sortable="custom" prop="DistributorProvinceName" label="发货方省份" align="center" min-width="110px">
              <template slot-scope="{ row }">
                <span>{{ row.distributorProvinceName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="DistributorCityName" label="发货方城市" align="center" min-width="110px">
              <template slot-scope="{ row }">
                <span>{{ row.distributorCityName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="DistributorName" label="发货方名称" align="left" header-align="center" min-width="200px">
              <template slot-scope="{ row }">
                <span>{{ row.distributorName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="ReceiverProvinceName" label="收货方省份" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.receiverProvinceName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="ReceiverCityName" label="收货方城市" align="center" min-width="110px">
              <template slot-scope="{ row }">
                <span>{{ row.receiverCityName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="ReceiverName" label="收货方名称" min-width="200px" align="left" header-align="center">
              <template slot-scope="{ row }">
                <span>{{ row.receiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="ManufacturerName" label="厂商名称" min-width="100px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.manufacturerName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="ProductName" label="产品名称" min-width="120px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.productName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="CommonName" label="通用名" min-width="150px" align="center" header-align="center">
              <template slot-scope="{ row }">
                <span>{{ row.commonName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="SpecificationName" label="规格" min-width="100px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.specificationName }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="BatchNumber" label="批次" min-width="100px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.batchNumber }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="ExpireDate" label="产品效期" min-width="100px" align="center">
              <template slot-scope="{ row }">
                <span v-if="row.expireDate">{{ row.expireDate }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="SaleDate" label="销售日期" min-width="100px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.saleDate }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="Quantity" label="销售数量" min-width="100px" align="right" header-align="center">
              <template slot-scope="{ row }">
                <span>{{ row.quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" sortable="custom" prop="EnumErrorSalesFlowType" label="错误描述" min-width="200px" align="left" header-align="center">
              <template slot-scope="{ row }">
                <span>{{ row.enumErrorSalesFlowTypeDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" sortable="custom" prop="IsHandled" label="是否处理" min-width="100px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.isHandledDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="100"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i v-if="!row.isHandled" class="el-icon-edit eltablei" title="处理" @click="handleError(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <HandleSalesFlow ref="refHandleSalesFlow" @success="handelSuccess" />
  </div>

</template>
<script>
import SalesFlowService from '@/api/salesFlow'
import MasterDataService from '@/api/masterData'
import Pagination from '@/components/Pagination'
import HandleSalesFlow from './handleSalesFlow'

export default {
  components: {
    Pagination,
    HandleSalesFlow
  },
  props: {
    importSalesFlowMasterTempId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      span: 4,
      handleList: [{
        value: 'true',
        label: '是'
      }, {
        value: 'false',
        label: '否'
      }],
      salesFlowList: [],
      listLoading: true,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      list: [],
      errorSalesFlowTypeList: [],
      importMasterTempId: '',
      showExportModal: false,
      columnDictionary: {},
      btnExportLoading: false

    }
  },
  watch: {
    importSalesFlowMasterTempId: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        if (val !== '') {
          this.importMasterTempId = val
          this.listQuery.isHandled = 'false'
          if (this.listQuery.enumErrorSalesFlowType) {
            delete this.listQuery.enumErrorSalesFlowType
          }
          this.listQuery = {
            pageIndex: 1,
            pageSize: 10,
            order: '-CreateTime'
          }
          this.initErrorSalesFlowType()
          this.getList()
        }
      }
    }
  },
  methods: {
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    initErrorSalesFlowType() {
      var param = {
        enumType: 'ErrorSalesFlowType'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.errorSalesFlowTypeList = result.data.datas
        })
        .catch(() => {
        })
    },
    getList() {
      this.listLoading = true
      this.listQuery.importSalesFlowMasterTempId = this.importMasterTempId
      SalesFlowService.QueryImportSalesFlowHandle(this.listQuery).then(res => {
        this.listLoading = false
        this.list = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(res => {})
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleError(row) {
      this.$refs.refHandleSalesFlow.initPage(row.id)
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handelSuccess() {
      this.handleFilter()
    },
    // 确认导出
    handleExportError() {
      SalesFlowService.ExportImportSalesFlowHandle(this.listQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '流向错误数据.xlsx'

          fileDownload(result.data, filename)
        })
        .catch(() => {
        })
    },
    handleBatchUpdate() {
      this.$confirm('确定批量更新?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          SalesFlowService.BatchHandleSalesFlow({ id: this.importMasterTempId }).then(res => {
            this.handleFilter()
          }).catch(res => {})
        })
        .catch((error) => {
          if (!error.succeed) {
            this.showMessage('取消批量更新', 'info')
          }
        })
    },
    isHandledChange() {
      this.$forceUpdate()
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    }
  }
}
</script>
<style scoped>
  .el-dialog-s {
  z-index: 11;
  }
</style>
