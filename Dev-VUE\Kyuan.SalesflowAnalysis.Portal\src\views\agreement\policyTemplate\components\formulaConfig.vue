<template>
  <el-dialog
    :title="title"
    width="60%"
    :close-on-click-modal="false"
    :visible="showFormulaConfig"
    @close="cancle()"
  >
    <el-form>
      <el-form-item label="" :label-width="formLabelWidth">
        <div
          id="dialogTxtCon"
          ref="dialogTxtCon"
          class="inputsx"
          placeholder="请配置公式"
          contentEditable="true"
          :v-html="htmlText"
          @click="handleEditClick"
          @keyup="keyUpVariable"
        />
      </el-form-item>
    </el-form>
    <el-row :gutter="10" style="margin-top: 15px">
      <el-col :span="8">
        <el-card
          class="wait-task-user-box-card"
          shadow="never"
          :body-style="{ padding: '5px' }"
          style="height:330px;"
        >
          <template v-slot:header>
            <span class="font-small">基础运算符</span>
          </template>
          <el-row :gutter="5">
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addNumber('1')"
            >1</el-button></el-col>
            <el-col
              align="center"
              :span="span"
            ><el-button
              class="numberButton"
              @click="addNumber('2')"
            >2</el-button></el-col>
            <el-col
              align="center"
              :span="span"
            ><el-button
              class="numberButton"
              @click="addNumber('3')"
            >3</el-button></el-col>
            <el-col
              align="center"
              :span="span"
            ><el-button
              class="numberButton"
              @click="addNumber('4')"
            >4</el-button></el-col>
            <el-col
              align="center"
              :span="span"
            ><el-button
              class="numberButton"
              @click="addNumber('5')"
            >5</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addNumber('6')"
            >6</el-button></el-col>
            <el-col
              align="center"
              :span="span"
            ><el-button
              class="numberButton"
              @click="addNumber('7')"
            >7</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addNumber('8')"
            >8</el-button></el-col>
            <el-col
              align="center"
              :span="span"
            ><el-button
              class="numberButton"
              @click="addNumber('9')"
            >9</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addNumber('0')"
            >0</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addNumber('.')"
            >.</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('+')"
            >+</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('-')"
            >-</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('×')"
            >×</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('÷')"
            >÷</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addBracket('(')"
            >(</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addBracket(')')"
            >)</el-button></el-col>
            <el-col
              v-if="displayCondition"
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('>')"
            >></el-button></el-col>
            <el-col
              v-if="displayCondition"
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('<')"
            >&lt;</el-button></el-col>
            <el-col
              v-if="displayCondition"
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('≥')"
            >≥</el-button></el-col>
            <el-col
              v-if="displayCondition"
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('≤')"
            >≤</el-button></el-col>
            <el-col
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('=')"
            >=</el-button></el-col>
            <el-col
              v-if="displayCondition"
              :span="span"
              align="center"
            ><el-button
              class="numberButton"
              @click="addSymbol('≠')"
            >≠</el-button></el-col>
            <el-col
              v-if="displayCondition"
              :span="span"
              align="center"
            >
              <el-button
                class="numberButton formulaFontSize"
                @click="addFunction('[and]')"
              >
                And
              </el-button>
            </el-col>
            <el-col
              v-if="displayCondition"
              :span="span"
              align="center"
            >
              <el-button
                class="numberButton formulaFontSize"
                @click="addFunction('[or]')"
              >
                Or
              </el-button>
            </el-col>
            <el-col
              v-if="displayCondition"
              :span="12"
              align="center"
            >
              <el-button
                class="numberButton backspaceColor formulaFontSize"
                @click="handleBackspace"
              >
                BackSpace
              </el-button>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card
          class="wait-task-user-box-card"
          shadow="never"
          :body-style="{ padding: '10px' }"
          style="height:120px;"
        >
          <template v-slot:header>
            <span class="font-small">数学函数</span>
          </template>
          <el-row :gutter="5">
            <el-col
              :span="funSpan"
              align="center"
            >
              <el-button
                class="functionButton"
                @click="addFunction('[Int]')"
              >
                Int
              </el-button>
            </el-col>
            <el-col
              :span="funSpan"
              align="center"
            >
              <el-button
                class="functionButton"
                @click="addFunction('[Round]')"
              >
                Round
              </el-button>
            </el-col>
          </el-row>
        </el-card>
        <el-card
          class="wait-task-user-box-card"
          shadow="never"
          :body-style="{ padding: '10px' }"
          style="margin-top:10px;height:200px;"
        >
          <template v-slot:header>
            <span class="font-small">变量说明</span>
          </template>
          <el-row :gutter="5">
            <div class="font-small" style="height:15px;color:grey">
              {{ variableRemark }}
            </div>
          </el-row>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card
          shadow="never"
          class="wait-task-user-box-card card"
          :body-style="{ padding: '10px 2px' }"
          style="height:330px;"
        >
          <template v-slot:header>
            <span class="font-small">系统变量</span>
          </template>
          <el-collapse v-for="(item,index) in rebateEnvironmentVariableList" :key="index">
            <el-collapse-item :title="item.variableGroupTypeDesc" :name="index" class="process-collapse">
              <el-table
                :data="item.rebateEnvironmentVariables"
                :show-header="false"
                border
                fit
                highlight-current-row
                style="width: 100%;"
                @cell-mouse-enter="handleMouseEnter"
                @cell-mouse-leave="handleMouselLeave"
              >
                <el-table-column
                  prop="name"
                  label="变量名"
                  header-align="center"
                  align="left"
                >
                  <template slot-scope="{ row }">
                    <el-button
                      size="small"
                      style="width:99%"
                      @click="addVariable(row.name)"
                    >{{ row.name }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </el-card>
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button icon="el-icon-close" @click="cancle()"> 关闭 </el-button>
      <el-button
        type="primary"
        icon="el-icon-check"
        @click="handleSaveFormula()"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import AgreementSevices from '@/api/agreement'
export default {
  props: {
    displayCondition: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formula: '',
      formulaDisplay: '',
      formulaHtml: '',
      rebateEnvironmentVariableList: [],
      variableRemark: '请把光标移动到右侧变量上',
      span: 6,
      funSpan: 8,
      formLabelWidth: '240',
      htmlText: '',
      oldInputValue: '',
      showFormulaConfig: false,
      title: '',
      // 定义鼠标光标值
      lastEditRange: null
    }
  },
  methods: {
    init(titleStr, formulaStr) {
      this.initRebateEnvironmentVariable()
      if (formulaStr !== undefined && formulaStr !== '' && formulaStr !== null && formulaStr.length > 0) {
        this.$refs.dialogTxtCon.innerHTML = formulaStr
      }
      this.title = titleStr
      this.showFormulaConfig = true
    },
    initRebateEnvironmentVariable() {
      AgreementSevices.GetRebateEnvironmentVariables()
        .then((result) => {
          this.rebateEnvironmentVariableList = JSON.parse(JSON.stringify(result.data))
        })
        .catch((error) => {
          console.log(error)
        })
    },
    addNumber(key) {
      this.initFormula(key)
    },
    addSymbol(key) {
      this.initFormula(` ${key} `)
    },
    addBracket(key) {
      this.initFormula(` ${key} `)
    },
    addFunction(key) {
      if (key === 'Round') {
        const html = `<span contentEditable="false"> ${key}</span>( , ) `
        this.initFormula(html)

        this.variableRemark = 'Round是四舍五入函数，请在函数后边对应括号中输入参数：参数1，要进行四舍五入的数据；参数2，四舍五入保留小数位数；'
      } else if (key === 'Int') {
        const html = `<span contentEditable="false"> ${key}</span>( ) `
        this.initFormula(html)

        this.variableRemark = 'Int是取整数函数，请在函数后边对应括号中输入参数'
      } else {
        const html = `<span contentEditable="false"> ${key} </span>`
        this.initFormula(html)
      }
    },
    addVariable(name) {
      const html = `<span contentEditable="false">【${name}】</span>`
      this.initFormula(html)
    },
    initFormula(html) {
      const sel = window.getSelection()
      if (this.lastEditRange != null) {
        // 清除所有选区
        sel.removeAllRanges()
        // 添加最后光标还原之前的状态
        sel.addRange(this.lastEditRange)
      } else {
        this.$refs.dialogTxtCon.focus()
      }

      // Selection 选区对象，表示用户选择的文本范围或光标的当前位置
      if (sel.getRangeAt && sel.rangeCount) {
        let range = sel.getRangeAt(0) // 包含当前选区内容的区域对象
        range.deleteContents() // Range.deleteContents(): 移除来自 Document 的 Range 内容
        const el = document.createElement('div') // 生成一个 div
        el.innerHTML = html // div 里的内容为上面定义的要添加的元素
        const frag = document.createDocumentFragment()
        let node, lastNode
        while ((node = el.firstChild)) {
          // 把生成 div 里的内容，即 html，移到 frag 里
          lastNode = frag.appendChild(node)
        }
        range.insertNode(frag)

        if (lastNode) {
          range = range.cloneRange()
          range.setStartAfter(lastNode)
          range.collapse(true)
          sel.removeAllRanges()
          sel.addRange(range)
        }
        // 记录最后光标对象
        this.lastEditRange = sel.getRangeAt(0)
      }

      this.oldInputValue = this.$refs.dialogTxtCon.innerHTML
    },
    handleSaveFormula() {
      this.formulaDisplay = this.$refs.dialogTxtCon.innerText.replace(/\s+/g, '')
      this.formulaHtml = this.$refs.dialogTxtCon.innerHTML
      this.formula = this.formulaDisplay
      this.rebateEnvironmentVariableList.forEach(element => {
        element.rebateEnvironmentVariables.forEach(item => {
          if (this.formula.includes(item.name)) {
            this.formula = this.formula.replaceAll('【' + item.name + '】', item.code)
          }
        })
      })
      this.formula = this.formula.replaceAll('=', '==').replaceAll('≥', '>=').replaceAll('≤', '<=').replaceAll('[and]', '&&').replaceAll('[or]', '||').replaceAll('≠', '!=').replaceAll('[Int]', 'int.Parse').replaceAll('[Round]', 'Math.Round').replaceAll('×', '*').replaceAll('÷', '/')

      if (this.formulaDisplay.length <= 0) {
        this.$notice.message('请输入公式后再保存', 'warning')
        return
      }

      if (!this.formulaVerify(this.formulaDisplay)) {
        this.$notice.message('运算公式配置不正确', 'error')
        return
      }
      const resultFormula = this.formulaDisplay + '|' + this.formula + '|' + this.formulaHtml
      this.$emit('success', resultFormula)
      this.cancle()
    },
    cancle() {
      this.showFormulaConfig = false
      this.htmlText = ''
      this.oldInputValue = ''
      this.$refs.dialogTxtCon.innerText = ''
      this.$refs.dialogTxtCon.innerHTML = ''
      this.rebateEnvironmentVariableList = null
    },
    // 限制键盘输入，除了删除键
    keyUpVariable(e) {
      var numberCodes = ['Digit0', 'Digit1', 'Digit2', 'Digit3', 'Digit4', 'Digit5', 'Digit6', 'Digit7', 'Digit8', 'Digit9', 'Period']
      if (e.code !== 'Backspace' && !numberCodes.includes(e.code)) {
        e.target.innerHTML = this.oldInputValue

        this.$refs.dialogTxtCon.focus()
        document.execCommand('selectAll', false, null)
        document.getSelection().collapseToEnd()

        this.setCursor()
        return false
      } else {
        this.oldInputValue = e.target.innerHTML
        this.setCursor()
        return true
      }
    },
    formulaVerify(formulaStr) {
      formulaStr = formulaStr.replaceAll('[and]', 'and').replaceAll('[or]', 'or').replaceAll('[Int]', 'Int').replaceAll('[Round]', 'Round');
      // 错误情况，( 后面是运算符
      if (/\([\+\-\×\÷\>\<\≥\≤]/.test(formulaStr)) {
        return false
      }
      // 错误情况，) 前面是运算符
      if (/[\+\-\×\÷\>\<\≥\≤]\)/.test(formulaStr)) {
        return false
      }
      // 错误情况，( 前面不是运算符 或 空
      if (/[^\+\-\×\÷\>\<\≥\≤\(\and\or\Int\Round\(\s]\(/.test(formulaStr)) {
        return false
      }
      // 错误情况，) 后面不是运算符 或 空
      if (/\)[^\+\-\×\÷\>\<\≥\≤\)\and\or\,\s]/.test(formulaStr)) {
        return false
      }
      // 错误情况，运算符号不能在首末位
      if (/^[\+\-\×\÷\>\<\≥\≤.]|[\+\-\*\/\and\or\>\<\≥\≤.]$/.test(formulaStr)) {
        return false
      }
      // 错误情况，运算符连续
      if (/[\+\-\×\÷\>\<\≥\≤]{2,}/.test(formulaStr)) {
        return false
      }
      // 错误情况，【】后面不是 运算符或 ) 或 ''
      if (/\【.+\】[^\+\-\×\÷\>\<\≥\≤\)\and\or\,)\s]/.test(formulaStr)) {
        return false
      }
      // 错误情况，【】前面不是 运算符或 ( 或 ''
      if (/[^\+\-\×\÷\>\<\≥\≤\and\or\(\s]\【.+\】/.test(formulaStr)) {
        return false
      }
      return true
    },
    handleMouseEnter(row) {
      this.variableRemark = row.remark
    },
    handleMouselLeave() {
      this.variableRemark = ''
    },
    handleEditClick() {
      this.setCursor()
    },
    handleBackspace() {
      if (this.$refs.dialogTxtCon.childNodes.length > 0) {
        this.$refs.dialogTxtCon.removeChild(this.$refs.dialogTxtCon.childNodes[this.$refs.dialogTxtCon.childNodes.length - 1])
      }
    },
    setCursor() {
      // 获取选定对象
      const selection = getSelection()
      // 设置最后光标对象
      this.lastEditRange = selection.getRangeAt(0)
    }
  }
}
</script>
<style scoped>
.numberButton {
  width: 100%;
  height: 40px;
  margin-bottom:2px;
  font-size: large !important;
}

.functionButton {
  width: 100%;
  height: 40px;
  margin-bottom:2px;
}

.formulaFontSize {
  font-size: 13px !important;
  font-weight: 500 !important;
}
.backspaceColor {
  background-color: #409eff;
}
.el-card ::v-deep .el-card__header {
  background-color: #f5f7fa;
  padding: 3px !important;
}
.font-small {
  font-size: 12px;
}
.inputsx {
    background-color: #FFFFFF;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #DCDFE6;
    box-sizing: border-box;
    color: #606266;
    font-size: inherit;
    height: 120px;
    line-height: 40px;
    outline: none;
    padding: 0 15px;
    width: 100%;
}

.inputsx:empty::before {
            content: attr(placeholder);
        }

/deep/ .el-table__body-wrapper {
            height: 130px; /* 滚动条整体高 必须项 */
            border-right: none;
            overflow-y: scroll;/* overflow-y为了不出现水平滚动条*/
        }
        /deep/ .el-table__body-wrapper::-webkit-scrollbar {
            width: 5px;/* 滚动条的宽高 必须项 */
            height: 5px;
        }

       /deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
          border-radius: 10px; /*滚动条的圆角*/
   			  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    		  background-color: #409eff; /*滚动条的背景颜色*/
        }

.card{
  overflow-y:auto  /* 开启滚动显示溢出内容 */
 }
</style>
