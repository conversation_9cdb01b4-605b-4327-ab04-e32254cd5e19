<template>
  <div>
    <el-dialog
      custom-class="el-dialog-s"
      title="查看单据"
      width="80%"
      append-to-body
      :close-on-click-modal="false"
      :visible="showAddDialog"
      @close="closeAgreementViewDialog"
    >
      <el-form
        :model="model"
        label-position="left"
        label-width="110px"
        class="el-dialogform"
        style="width:95%"
      >
        <el-row type="flex" class="filter-container">
          <el-col :span="24" style="color:red;">
            <el-form-item v-if="rebateAgreement.isExceedBasePrice" label="警告：">
              <label>{{ rebateAgreement.exceedBasePriceWaringMessage }}</label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="申请单编号：">
              {{ model.requestCode }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="申请单类型：">
              {{ model.enumFormTypeDesc }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="协议类型：">
              {{ rebateAgreement.rebateAgreementTemplateName }}
            </el-form-item>
          </el-col>
          <el-col v-if="!rebateAgreement.isMaster&&rebateAgreement.rebateAgreementTemplateTypeCode!=='changeChannels'" :span="24">
            <el-form-item label="主协议：">
              {{ rebateAgreement.masterRebateAgreementName }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="协议名称：">
              <label>{{ rebateAgreement.name }}</label>
            </el-form-item>
          </el-col>
          <el-col v-if="!rebateAgreement.isMaster&&rebateAgreement.rebateAgreementTemplateTypeCode!=='changeChannels'" :span="24">
            <el-form-item label="项目名称：">
              <label>{{ rebateAgreement.rebateProjectName }}</label>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="甲方：">
              {{ rebateAgreement.partyAName }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="乙方：">
              {{ rebateAgreement.rebateReceiverName }}
            </el-form-item>
          </el-col>
          <el-col v-if="rebateAgreement.enumSigningPartiesType >=2" :span="span">
            <el-form-item :label="rebateAgreement.enumSigningPartiesType ===3 || rebateAgreement.enumSigningPartiesType ===6?'丙方1：':'丙方：'">
              {{ rebateAgreement.partyCNameOne }}
            </el-form-item>
          </el-col>
          <el-col v-if="rebateAgreement.enumSigningPartiesType === 3||rebateAgreement.enumSigningPartiesType === 6" :span="12">
            <el-form-item label="丙方2：">
              {{ rebateAgreement.partyCNameTwo }}
            </el-form-item>
          </el-col>
          <el-col v-if="rebateAgreement.enumSigningPartiesType >=4" :span="span">
            <el-form-item :label="rebateAgreement.enumSigningPartiesType ===5?'丁方1：':'丁方：'">
              {{ rebateAgreement.partyDNameOne }}
            </el-form-item>
          </el-col>
          <el-col v-if="rebateAgreement.enumSigningPartiesType ===5" :span="span">
            <el-form-item label="丁方2：">
              {{ rebateAgreement.partyDNameTwo }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="起始日期：">
              <label>{{
                rebateAgreement.startDate | parseTime("{y}-{m}-{d}")
              }}</label>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="截止日期：">
              <label>{{
                rebateAgreement.endDate | parseTime("{y}-{m}-{d}")
              }}</label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="申请人：">
              <label>{{ rebateAgreement.principalDisplayName }}({{
                rebateAgreement.principalJobNo
              }})</label>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="部门：">
              <label>{{ rebateAgreement.departmentName }}</label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="filter-container">
          <el-col v-if="!rebateAgreement.isMaster&&rebateAgreement.rebateAgreementTemplateTypeCode!=='changeChannels'" :span="span">
            <el-form-item label="支付周期：">
              <label>{{ rebateAgreement.paymentCycleName }}</label>
            </el-form-item>
          </el-col>
          <el-col v-if="rebateAgreement.rebateAgreementTemplateTypeCode!=='changeChannels'" :span="span">
            <el-form-item label="支付方式：">
              <label>{{ rebateAgreement.paymentTypeName }}</label>
            </el-form-item>
          </el-col>
          <el-col v-if="rebateAgreement.rebateAgreementTemplateTypeCode!=='changeChannels'" :span="span">
            <el-form-item label="支付天数：">
              <label>{{ rebateAgreement.payWithinDaysName }}</label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="filter-container">
          <el-col
            v-if="model.enumFormType === $constDefinition.formType.MergePayment"
            :span="24"
          >
            <el-form-item label="双方协议名称：">
              <label>{{ rebateAgreement.mergeRebateAgreementAName }}</label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="filter-container">
          <el-col
            v-if="model.enumFormType === $constDefinition.formType.MergePayment"
            :span="24"
          >
            <el-form-item label="多方协议名称：">
              <label>{{ rebateAgreement.mergeRebateAgreementBName }}</label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="model.enumFormType === $constDefinition.formType.MergePayment" type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="指标销量统计：">
              <label>{{ rebateAgreement.enumMergeSalesStatisticsTypeDesc }}</label>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="支付方式：">
              <label>{{ rebateAgreement.enumMergePaymentTypeDesc }}</label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="filter-container">
          <el-col
            v-if="model.enumFormType!= $constDefinition.formType.MergePayment && (rebateAgreement.enumSigningPartiesType === 2 ||rebateAgreement.enumSigningPartiesType === 3 ||
              rebateAgreement.enumSigningPartiesType === 4 ||rebateAgreement.enumSigningPartiesType === 5 || rebateAgreement.enumSigningPartiesType === 6)"
            :span="span"
          >
            <el-form-item label="丙方代付：">
              <label>{{ rebateAgreement.partyCPaymentWithinDaysName }}</label>
            </el-form-item>
          </el-col>
          <el-col
            v-if="model.enumFormType!= $constDefinition.formType.MergePayment &&
              (rebateAgreement.enumSigningPartiesType === 4 || rebateAgreement.enumSigningPartiesType === 5 || rebateAgreement.enumSigningPartiesType === 6)
            "
            :span="span"
          >
            <el-form-item label="丁方代付：">
              <label>{{ rebateAgreement.partyDPaymentWithinDaysName }}</label>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row type="flex" class="filter-container">
          <el-form-item label="合同文件下载：">
            <i class="el-icon-download eltablei" title="协议下载" @click="handleDownLoadTemplate(rebateAgreement)" />
          </el-form-item>
        </el-row> -->
        <el-row v-if="model.formStatus===2" type="flex" class="filter-container">
          <el-form-item label="拒绝原因：">
            <label>{{ model.rejectedReason }}</label>
          </el-form-item>
        </el-row>
        <el-row v-if="rebateAgreement.rebateAgreementTemplateTypeCode==='purchase' || rebateAgreement.rebateAgreementTemplateTypeCode==='pureSales'">
          <el-col :span="24">
            <el-form-item label="政策条款：">
              <label v-show="rebateAgreement.agreementPolicyList[0].enumCalculationTemplateType=== $constDefinition.calculationTemplateType.steppedType">
                当达成率大于等于{{ rebateAgreement.agreementPolicyList[0].upperLimitAchievementRateStr }}%时,补偿系数为1;
                当{{ rebateAgreement.agreementPolicyList[0].lowerLimitAchievementRateStr }}%≤达成率＜
                {{ rebateAgreement.agreementPolicyList[0].upperLimitAchievementRateStr }}%时,补偿系数为：实际达成率；
                当达成率小于{{ rebateAgreement.agreementPolicyList[0].lowerLimitAchievementRateStr }}%时，补偿系数为0;
              </label>
              <label v-show="rebateAgreement.agreementPolicyList[0].enumCalculationTemplateType=== $constDefinition.calculationTemplateType.quantityReached">
                当达成率大于等于100%时,补偿系数为1;
                当{{ rebateAgreement.agreementPolicyList[0].lowerLimitAchievementRateStr }}%≤达成率＜
                100%时,补偿系数为：实际达成率；
                当达成率小于{{ rebateAgreement.agreementPolicyList[0].lowerLimitAchievementRateStr }}%时，补偿系数为0;
              </label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="rebateAgreement.rebateAgreementTemplateTypeCode==='cover'">
          <el-col :span="24">
            <el-form-item label="政策条款：">
              <label>
                （1）至少完成【{{ rebateAgreement.agreementPolicyList[0].numberOfStores }} 】家门店的销售；
                （2）至少完成【{{ rebateAgreement.agreementPolicyList[0].numberOfSpecPerStore }}】个规格产品的销售；
                （3）各品规{{ rebateAgreement.agreementPolicyList[0].enumDistributionQuantityStatisticalTypeDesc }}销售量不低于【{{ rebateAgreement.agreementPolicyList[0].distributionQuantity }}】盒。
              </label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="rebateAgreement.rebateAgreementTemplateTypeCode==='pureSales'||rebateAgreement.rebateAgreementTemplateTypeCode==='purchase'||rebateAgreement.rebateAgreementTemplateTypeCode==='cover'" style="margin-top: 10px" class="table">
          <el-col
            v-if="
              rebateAgreement.agreementPolicyList[0]
                .productSpecList
            "
            :span="24"
          >
            <el-form-item label="产品指标">
              <el-table
                :data="
                  rebateAgreement.agreementPolicyList[0]
                    .productSpecList
                "
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%"
                :header-cell-class-name="'tableStyle'"
              >
                <el-table-column
                  fixed
                  label="序号"
                  type="index"
                  align="center"
                />
                <el-table-column
                  label="产品(规格)"
                  prop="productNameCn"
                  align="center"
                  min-width="100px"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.productNameCn }}({{ row.spec }})</span>
                  </template>
                </el-table-column>
                <el-table-column
                  label="补偿单价（元）"
                  prop="calculationPrice"
                  min-width="120px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.rebateUnitPrice | toTwoNum }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode!=='QuarterPayment'"
                  label="年度指标"
                  prop="totalQuota"
                  min-width="80px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.totalQuota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode==='QuarterPayment'"
                  label="一季度指标"
                  prop="q1Quota"
                  min-width="100px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.q1Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode==='QuarterPayment'"
                  label="二季度指标"
                  prop="q2Quota"
                  min-width="100px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.q2Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode==='QuarterPayment'"
                  label="三季度指标"
                  prop="q3Quota"
                  min-width="100px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.q3Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode==='QuarterPayment'"
                  label="四季度指标"
                  prop="q4Quota"
                  min-width="100px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.q4Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  label="是否超出项目补偿单价"
                  min-width="160px"
                  align="center"
                  prop="isExceedBasePrice"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.isExceedBasePrice?'是': '否' }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="rebateAgreement.rebateAgreementTemplateTypeCode==='pureSales'" style="margin-top: 10px">
          <el-col :span="24">
            <el-form-item label="目标终端">
              <el-table
                :data="
                  rebateAgreement.agreementPolicyList[0]
                    .targetReceiverList
                "
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%"
                :default-sort="{ prop: 'createTime', order: 'descending' }"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
              >
                <el-table-column
                  fixed
                  label="序号"
                  type="index"
                  align="center"
                />
                <el-table-column
                  label="产品(规格)"
                  prop="productNameCn"
                  align="center"
                  min-width="100px"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.productNameCn }}({{ row.spec }})</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode!=='QuarterPayment'"
                  label="年度指标"
                  prop="totalQuota"
                  min-width="80px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.totalQuota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode==='QuarterPayment'"
                  label="一季度指标"
                  prop="q1Quota"
                  min-width="100px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.q1Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode==='QuarterPayment'"
                  label="二季度指标"
                  prop="q2Quota"
                  min-width="100px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.q2Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode==='QuarterPayment'"
                  label="三季度指标"
                  prop="q3Quota"
                  min-width="100px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.q3Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="rebateAgreement.paymentCycleCode==='QuarterPayment'"
                  label="四季度指标"
                  prop="q4Quota"
                  min-width="100px"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.q4Quota }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  label="终端名称"
                  align="center"
                  min-width="130px"
                >
                  <template slot-scope="{ row }">
                    <span>{{ row.targetReceiverName }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="审批截图">
              <div class="imgbox">
                <div v-for="(url, index) in imageList" :key="index" class="block">
                  <el-image
                    style="width: 200px; height: 200px;object-fit: cover;"
                    :src="url"
                    :preview-src-list="getSrcList(index)"
                  />
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="margin-top:10px">
          <el-col v-if="model.approvalHistoryList" :span="24">
            <el-form-item label="审批历史">
              <el-table
                :data="model.approvalHistoryList"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
              >
                <el-table-column
                  fixed
                  label="序号"
                  type="index"
                  align="center"
                />
                <el-table-column label="操作人" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.approverDisplayName }}({{ row.approverJobNo }})</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作时间" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.approvalTime | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.enumFormStatusDesc }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="审批说明" align="center" min-width="100px">
                  <template slot-scope="{ row }">
                    <span>{{ row.approvalDescription }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeAgreementViewDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import RequestFormRebateService from '@/api/requestFormRebate'

export default {
  components: {
  },
  data() {
    return {
      span: 12,
      showAddDialog: false,
      model: {
        rebateAgreement: { masterRebateAgreementName: '' }
      },
      rebateAgreement: {},
      imageList: []
    }
  },

  methods: {
    initPage(formId) {
      const para = { id: formId }
      RequestFormRebateService.GetRequestFormRebateAgreement(para)
        .then((result) => {
          this.model = result.data
          this.rebateAgreement = this.model.rebateAgreement
          for (var i = 0; i < this.model.mailImageList.length; i++) {
            this.imageList.push('data:image/png;base64,' + this.model.mailImageList[i])
          }
        })
        .catch((error) => {
          console.log(error)
        })

      this.showAddDialog = true
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (row.mergeCell.includes(column.property)) {
        const rowspan = row[`rowspan_${column.property}`]
        if (rowspan) {
          return { rowspan: rowspan, colspan: 1 }
        } else {
          return { rowspan: 0, colspan: 0 }
        }
      }
    },
    closeAgreementViewDialog() {
      this.showAddDialog = false
      this.model = { rebateAgreement: { masterRebateAgreementName: '' }}
      this.imageList = []
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    getSrcList(index) {
      return this.imageList.slice(index).concat(this.imageList.slice(0, index))
    }
  }

}
</script>

<style   scoped>

</style>

