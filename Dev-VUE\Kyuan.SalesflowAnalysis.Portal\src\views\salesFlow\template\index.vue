<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="span">
          <el-input
            v-model="listQuery.name"
            clearable
            placeholder="模板名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'Template_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'Template_Button_Add')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-plus"
            @click="handleCreate"
          >
            新增
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column label="模板名称" align="left" header-align="center" sortable="custom" min-width="200px" prop="Name">
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="创建人" align="center" sortable="custom" min-width="100px" prop="Creator">
              <template slot-scope="{ row }">
                <span>{{ row.creator }}</span>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" sortable="custom" min-width="100px" prop="CreateTime">
              <template slot-scope="{ row }">
                <span>{{ row.createTime | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i v-if="$isPermitted($store.getters.user, 'Template_Button_Edit')" class="el-icon-edit-outline eltablei" @click="handleUpdate(row)" />
                <i v-if="$isPermitted($store.getters.user, 'Template_Button_Del')" class="el-icon-delete eltablei" @click="handleDelete(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
    <addSalesFlowTemplate
      v-if="dialogAddFormVisible"
      :id="itemId"
      :title="modifyDialogTitle"
      @hidden="onHidden()"
      @refresh="onRefresh()"
    />
    <modifySalesFlowTemplate
      v-if="dialogEditFormVisible"
      :id="itemId"
      :title="modifyDialogTitle"
      @hidden="onModifyHidden()"
    />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import SalesFlowTemplateMappingService from '@/api/salesFlowTemplateMapping'
import addSalesFlowTemplate from './components/addSalesFlowTemplate'
import modifySalesFlowTemplate from './components/modifySalesFlowTemplate'

export default {
  name: 'Template',
  components: {
    Pagination,
    addSalesFlowTemplate,
    modifySalesFlowTemplate
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      list: [],
      dialogAddFormVisible: false,
      dialogEditFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑映射模板',
        create: '新增映射模板'
      },
      modifyDialogTitle: '',
      itemId: null
    }
  },
  created() {
    this.handleFilter()
  },
  mounted() {

  },
  methods: {
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      SalesFlowTemplateMappingService.QuerySalesFlowTemplateMapping(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    handleCreate() {
      this.itemId = null
      this.dialogAddFormVisible = true
      this.dialogStatus = 'create'
      this.modifyDialogTitle = this.textMap[this.dialogStatus]
    },
    handleUpdate(row) {
      this.itemId = row.id
      this.dialogEditFormVisible = true
      this.dialogStatus = 'update'
      this.modifyDialogTitle = this.textMap[this.dialogStatus]
    },
    handleDelete(row) {
      this.$confirm('确定删除此映射模板吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        SalesFlowTemplateMappingService.DeleteSalesFlowTemplateMapping(row).then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('删除成功', 'success')
          } 
        })
          .catch(error => {
            this.listLoading = false
            console.log(error)
          })
      }).catch(error => {
        this.listLoading = false
        if (!error.succeed) {
          this.$notice.message('取消删除', 'info')
        }
      })
    },
    onHidden() {
      this.dialogAddFormVisible = false
    },
    onRefresh() {
      this.getList()
      this.dialogAddFormVisible = false
    },
    onModifyHidden() {
      this.dialogEditFormVisible = false
    }
  }
}
</script>
