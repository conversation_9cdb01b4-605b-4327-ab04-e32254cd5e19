<template>
  <div>
    <el-dialog :title="title" width="80%" :close-on-click-modal="false" :visible="true" @close="cancle()">
      <el-form ref="dataForm" :rules="rules" :model="tempData.tempFormModel" label-position="right" label-width="110px" class="el-dialogform-modify">
        <el-row :gutter="10" class="filter-container" type="flex">
          <el-col :span="span">
            <el-form-item label="发货方省份/城市" prop="distributorProvinceCityId">
              <el-cascader
                v-model="tempData.tempFormModel.distributorProvinceCityId"
                disabled
                style="width: 100%; "
                :options="provinceCityList"
                placeholder="发货方省份/城市"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="发货方名称" prop="distributorName">
              <el-col :span="20" style="padding-left: 0px; padding-right: 0px;">
                <el-input
                  v-model="tempData.tempFormModel.distributorName"
                  style="width: 100%; "
                  clearable
                  :readonly="true"
                  :disabled="!tempData.tempFormModel.allowEdit"
                  placeholder="发货方名称"
                />
              </el-col>
              <el-col :span="4">
                <el-button icon="el-icon-search" :disabled="!tempData.tempFormModel.allowEdit" style="width: 100%; " type="primary" title="选择发货方" @click="handleSelectDistributor" />
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="收货方省份/城市" prop="receiverProvinceCityId">
              <el-cascader
                v-model="tempData.tempFormModel.receiverProvinceCityId"
                disabled
                :options="provinceCityList"
                style="width: 100%"
                placeholder="收货方省份/城市"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="收货方名称" prop="receiverName">
              <el-col :span="20" style="padding-left: 0px; padding-right: 0px;">
                <el-input
                  v-model="tempData.tempFormModel.receiverName"
                  clearable
                  :readonly="true"
                  style="width: 100%"
                  placeholder="收货方名称"
                  :disabled="!tempData.tempFormModel.allowEdit"
                />
              </el-col>
              <el-col :span="4">
                <el-button icon="el-icon-search" :disabled="!tempData.tempFormModel.allowEdit" style="width: 100%; " type="primary" title="选择收货方" @click="handleSelectReceiver" />
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="厂商" prop="manufacturerId">
              <el-select
                v-model="tempData.tempFormModel.manufacturerId"
                :loading="manufacturerLoading"
                clearable
                style="width: 100%"
                placeholder="厂商"
                :disabled="!tempData.tempFormModel.allowEdit"
                @change="manufacturerChange"
              >
                <el-option
                  v-for="item in manufacturerList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="产品/规格" prop="productAndSpecId">
              <el-cascader
                ref="refProductAndSpec"
                :key="tempData.tempFormModel.manufacturerId"
                v-model="tempData.tempFormModel.productAndSpecId"
                :loading="productAndSpecLoading"
                :options="productAndSpecList"
                placeholder="产品/规格"
                clearable
                :disabled="!tempData.tempFormModel.allowEdit"
                style="width: 100%"
                :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
                @change="productAndSpecChange"
              /></el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="销售日期" prop="saleDate">
              <el-date-picker
                v-model="tempData.tempFormModel.saleDate"
                style="width: 100%"
                clearable
                type="date"
                placeholder="销售日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :disabled="!tempData.tempFormModel.allowEdit"
                @blur="timeChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="销售数量" prop="quantity">
              <el-input
                v-model="tempData.tempFormModel.quantity"
                clearable
                placeholder="销售数量"
                maxlength="14"
                :disabled="!tempData.tempFormModel.allowEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="批次" prop="batchNumber">
              <el-input
                v-model="tempData.tempFormModel.batchNumber"
                max="14"
                clearable
                placeholder="批次"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="产品效期" prop="expireDate">
              <el-date-picker
                v-model="tempData.tempFormModel.expireDate"
                style="width: 100%"
                clearable
                type="date"
                placeholder="产品效期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                @blur="timeChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          v-if="!viewModel"
          :loading="btnSaveLoading"
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      custom-class="el-dialog-ls"
      append-to-body
      title="选择发货方"
      :close-on-click-modal="false"
      :visible="dialogDistributorVisible"
      width="80%"
      class="popup-search"
      @close="closeDistributorDialog"
    >
      <SelectReceiver ref="refDistributor" :show-dialog="dialogDistributorVisible" :distributor-types="distributorTypes" @success="selectDistributorSuccess" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeDistributorDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="选择收货方"
      :close-on-click-modal="false"
      :visible="dialogReceiverVisible"
      width="80%"
      class="popup-search"
      @close="closeReceiverDialog"
    >
      <SelectReceiver ref="refReceiver" :show-dialog="dialogReceiverVisible" :distributor-types="distributorTypes" @success="selectReceiverSuccess" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeReceiverDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>

import ManufacturerService from '@/api/manufacturer'
import ProductService from '@/api/product'
import LocationService from '@/api/location'
import SalesFlowService from '@/api/salesFlow'
import SelectReceiver from '@/views/components/selectReceiver'

export default {
  name: '',
  components: {
    SelectReceiver
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    viewModel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validateSpec = (rule, value, callback) => {
      if (!this.tempData.tempFormModel.productAndSpecId.length) {
        callback(new Error('请选择产品/规格'))
      } else {
        callback()
      }
    }
    return {
      span: 12,
      provinceCityLoading: false,
      provinceCityList: [],
      distributorProvinceCityId: null,
      receiverProvinceCityId: null,
      rules: {
        saleDate: [
          { required: true, message: '请选择销售日期', trigger: 'change' }
        ],
        distributorName: [
          { required: true, message: '请选择发货方', trigger: 'change' }
        ],
        receiverName: [
          { required: true, message: '请选择收货方', trigger: 'change' }
        ],
        productAndSpecId: [
          { type: 'array', required: true, validator: validateSpec, trigger: 'change' }
        ],
        batchNumber: [
          { required: false, max: 50, message: '批次最多只允许50个字符', trigger: 'blur' }
        ],
        quantity: [
          {
            required: true, trigger: 'blur', message: '请填写销售数量'
          },
          { pattern: /^-*\d+(\.\d{0,2}){0,1}$/, message: '销售数量仅支持两位小数的数字' }
        ]
      },
      tempData: { tempFormModel: {}, originalTempFormModel: {}},
      btnSaveLoading: false,
      manufacturerLoading: false,
      productAndSpecLoading: false,
      manufacturerList: [],
      productAndSpecList: [],
      distributorTypes: [],
      dialogDistributorVisible: false,
      dialogReceiverVisible: false
    }
  },
  watch: {
    id(val) {
      this.tempData.tempFormModel.id = val
    }
  },
  mounted() {
  },
  created() {
    this.initManufacturer()
    this.initProductAndSpec()
    this.initProvinceCity()
    this.init()
  },
  methods: {
    init() {
      if (!this.id) {
        this.clear()
        return
      }
      this.get(this.id)
    },
    initManufacturer() {
      this.manufacturerLoading = true
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturerLoading = false
          this.manufacturerList = result
        })
        .catch((error) => {
          this.manufacturerLoading = false
          console.log(error)
        })
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      const para = { manufacturerId: this.tempData.tempFormModel.manufacturerId }
      ProductService.QueryProductAndSpecCascader(para)
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    initProvinceCity() {
      this.provinceCityLoading = true
      LocationService.QueryProvinceCityCascader()
        .then((result) => {
          this.provinceCityList = result
          this.provinceCityLoading = false
        })
        .catch((error) => {
          this.provinceCityLoading = false
          console.log(error)
        })
    },
    manufacturerChange() {
      this.tempData.tempFormModel.productAndSpecId = null
      this.initProductAndSpec()
    },
    timeChange() {
      this.$forceUpdate()
    },
    get(id) {
      this.btnSaveLoading = true
      SalesFlowService.GetSalesFlow({ id: id }).then(result => {
        this.tempData.originalTempFormModel = JSON.parse(JSON.stringify(result.data))
        this.tempData.tempFormModel = result.data
        this.tempData.tempFormModel.productAndSpecId = [result.data.productId, result.data.productSpecId]
        if (result.data.distributorCityId) {
          this.tempData.tempFormModel.distributorProvinceCityId = [result.data.distributorProvinceId, result.data.distributorCityId]
        } else {
          this.tempData.tempFormModel.distributorProvinceCityId = [result.data.distributorProvinceId]
        }
        if (result.data.receiverCityId) {
          this.tempData.tempFormModel.receiverProvinceCityId = [result.data.receiverProvinceId, result.data.receiverCityId]
        } else {
          this.tempData.tempFormModel.receiverProvinceCityId = [result.data.receiverProvinceId]
        }
        this.btnSaveLoading = false
      }).catch(error => {
        this.btnSaveLoading = false
        console.log(error)
      })
    },
    save() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.tempData.tempFormModel.productId = this.tempData.tempFormModel.productAndSpecId[0]
          this.tempData.tempFormModel.productSpecId = this.tempData.tempFormModel.productAndSpecId[1]
          this.update()
        }
      })
    },
    update() {
      this.btnSaveLoading = true
      SalesFlowService.UpdateSalesFlow({ originalSalesFlow: this.tempData.originalTempFormModel, updatedSalesFlow: this.tempData.tempFormModel }).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempData.tempFormModel = result.data
          this.$notice.message('修改成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('修改失败。', 'error')
          }
        })
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.tempData.tempFormModel = {}
    },
    handleSelectDistributor() {
      this.distributorTypes = [10, 20]
      this.dialogDistributorVisible = true
    },
    handleSelectReceiver() {
      this.distributorTypes = [20, 30]
      this.dialogReceiverVisible = true
    },
    closeDistributorDialog() {
      this.dialogDistributorVisible = false
      this.$refs.refDistributor.clear()
      this.distributorTypes = []
    },
    closeReceiverDialog() {
      this.dialogReceiverVisible = false
      this.$refs.refReceiver.clear()
      this.distributorTypes = []
    },
    selectDistributorSuccess(val) {
      if (val.id === this.tempData.tempFormModel.receiverId) {
        this.$notice.message('发货方不能和收货方相同', 'error')
      } else {
        this.tempData.tempFormModel.distributorName = val.name
        this.tempData.tempFormModel.distributorId = val.id
        if (val.cityId) {
          this.tempData.tempFormModel.distributorProvinceCityId = [val.provinceId, val.cityId]
        } else {
          this.tempData.tempFormModel.distributorProvinceCityId = [val.provinceId]
        }
        this.closeDistributorDialog()
      }
    },
    selectReceiverSuccess(val) {
      if (val.id === this.tempData.tempFormModel.distributorId) {
        this.$notice.message('收货方不能和发货方相同', 'error')
      } else {
        this.tempData.tempFormModel.receiverName = val.name
        this.tempData.tempFormModel.receiverId = val.id
        if (val.cityId) {
          this.tempData.tempFormModel.receiverProvinceCityId = [val.provinceId, val.cityId]
        } else {
          this.tempData.tempFormModel.receiverProvinceCityId = [val.provinceId]
        }
        this.closeReceiverDialog()
      }
    },
    productAndSpecChange() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    }
  }

}
</script>
<style scoped>
.el-dialogform-modify {
  width: 90%;
  margin-left: 50px;
}
</style>
