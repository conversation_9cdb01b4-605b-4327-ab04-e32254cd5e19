<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filter-container minusBottom">
        <el-col :span="8">
          <el-date-picker
            v-model="listQuery.timeRange"
            clearable
            class="filter-item"
            style="width:100%"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            format="yyyy-MM"
            value-format="yyyy-MM"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.bonusCategoryId"
            :loading="calculateCategoryLoading"
            class="filter-item"
            placeholder="奖励类型"
            clearable
            @change="selectUpdate"
          >
            <el-option
              v-for="item in calculateCategoryQueryList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="listQuery.displayName"
            clearable
            placeholder="员工姓名"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'MyBonus_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'MyBonus_Button_Export')"
            :loading="btnExportLoading"
            class="filter-item-button"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              header-align="center"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              sortable="custom"
              prop="Employee.DisplayName"
              label="员工姓名"
              header-align="center"
              align="center"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.employeeDisplayName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="Employee.JobNo"
              label="工号"
              header-align="center"
              align="center"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.employeeJobNo }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="奖励计算周期"
              header-align="center"
              align="center"
              min-width="120px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.bonusResultStartDate | parseTime('{y}.{m}') }}-{{ row.bonusResultEndDate | parseTime('{y}.{m}') }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="BonusResult.EnumCalculateType"
              label="奖励类型"
              header-align="center"
              align="right"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.BonusCategoryName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="BonusAmount"
              label="奖励计算金额（元）"
              header-align="center"
              align="right"
              min-width="120px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.bonusAmount | toTwoNum | toThousandFilter }}</span>
              </template>
            </el-table-column>
            <el-table-column
              sortable="custom"
              prop="ActualAmount"
              label="实得金额（元）"
              header-align="center"
              align="right"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.actualAmount | toTwoNum | toThousandFilter }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="调整原因"
              header-align="center"
              align="left"
              min-width="100px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.adjustReason }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="120" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i class="el-icon-document eltablei" title="查看明细" @click="reviewDetail(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>

    <el-dialog
      :close-on-click-modal="false"
      title="人员奖励明细"
      :visible="dialogPersonalDetailFormVisible"
      width="80%"
      @close="btnPersonalDetailsClose"
    >
      <el-row class="query-title" type="flex" :gutter="10">
        <el-col :span="5">
          <label>工号：{{ jobNo }}</label>
        </el-col>
        <el-col :span="6">
          <label>员工姓名：{{ employeeDisplayName }}</label>
        </el-col>
      </el-row>

      <el-row style="margin-bottom:20px;">
        <el-col :span="24">
          <el-table
            :data="bonusResultEmployeeDetailList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
          >
            <el-table-column fixed label="序号" type="index" align="center" />

            <el-table-column label="计算月份" align="center" min-width="120px">
              <template slot-scope="{ row }">
                <span>{{ row.bonusResultStartDate | parseTime('{y}.{m}') }}-{{ row.bonusResultEndDate | parseTime('{y}.{m}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="潜力金额" align="right" header-align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span v-if="row.enumBonusWaringFlag != 0 && row.enumBonusRule !== 11">-</span>
                <span v-else>{{ row.quotaPrice | toTwoNum | toThousandFilter }}元</span>
              </template>
            </el-table-column>
            <el-table-column label="销售金额" align="right" header-align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span v-if="row.enumBonusWaringFlag != 0 && row.enumBonusRule !== 11">-</span>
                <span v-else>{{ row.salePrice | toTwoNum | toThousandFilter }}元</span>
              </template>
            </el-table-column>
            <el-table-column label="潜力进度" align="right" header-align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span v-if="row.enumBonusWaringFlag != 0 && row.enumBonusRule !== 11">-</span>
                <span v-else>{{ row.quotaRate }}%</span>
              </template>
            </el-table-column>
            <el-table-column label="职位" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span>{{ row.positionName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="岗位" align="center" min-width="120px">
              <template slot-scope="{ row }">
                <span>{{ row.stationNames }}</span>
              </template>
            </el-table-column>
            <el-table-column label="上岗时间" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span v-if="row.stationStartDate">{{ row.stationStartDate | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="离岗时间" align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span v-if="row.stationEndDate">{{ row.stationEndDate | parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="奖励金额" align="right" header-align="center" min-width="100px">
              <template slot-scope="{ row }">
                <span>{{ row.bonusAmount | toTwoNum | toThousandFilter }}元</span>
              </template>
            </el-table-column>
            <el-table-column label="警告原因" align="center" min-width="150px">
              <template slot-scope="{ row }">
                <span>{{ row.enumBonusWaringFlagDesc }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="btnPersonalDetailsClose()"> 关闭 </el-button>
      </div>
    </el-dialog>

    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出报表"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import CustomExport from '@/components/Export/CustomExport'
import ManufacturerService from '@/api/manufacturer'
import MaintenanceService from '@/api/maintenance'
import MasterDataService from '@/api/masterData'
import BonusService from '@/api/bonus'

export default {
  name: 'MyBonusQuery',
  components: {
    Pagination,
    CustomExport
  },
  data() {
    return {
      span: 4,
      total: 0,
      bonusDetailTotal: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      list: [],
      bonusResultEmployeeDetailList: [],
      bonusResultEmployeeIncrementList: [],
      dialogPersonalDetailFormVisible: false,
      manufacturerLoading: false,
      deptLoading: false,
      dialogStatus: '',
      bonusCalculation: {
        months: '',
        deptId: undefined,
        isIncrement: false
      },
      itemId: null,
      manufacturerList: [],
      deptList: [],
      calculateStatusList: [],
      isDisabled: false,
      bonusSummaryDetailList: [],
      adjust: {
        id: undefined,
        money: 0,
        reason: ''
      },
      activeName: 'first',
      salesBonusList: [],
      incrementalBonusList: [],
      jobNo: '',
      employeeDisplayName: '',
      enumCalculateType_SalesBonus: 10, // 销量奖励
      enumCalculateType_IncrementBonus: 20, // 增量奖励
      bonusResultEmployee: null,
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: {},
      calculateCategoryLoading: false,
      calculateCategoryQueryList: []
    }
  },
  created() {
    this.initManufacturer()
    this.getList()
    this.initDept()
    this.initCalculateStatus()
    this.initCalculateType()
  },
  mounted() {

  },
  methods: {
    initManufacturer() {
      this.manufacturerLoading = true
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturerLoading = false
          this.manufacturerList = result
        })
        .catch(() => {
          this.manufacturerLoading = false
        })
    },

    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch(() => {
          this.deptLoading = false
        })
    },
    initCalculateType() {
      this.calculateCategoryLoading = true
      BonusService.QueryBonusCategorySelect()
        .then((result) => {
          this.calculateCategoryLoading = false
          this.calculateCategoryQueryList = JSON.parse(JSON.stringify(result))
        })
        .catch((error) => {
          this.calculateCategoryLoading = false
          console.log(error)
        })
    },
    initCalculateStatus() {
      var param = {
        enumType: 'CalculateStatus'
      }
      MasterDataService.GetEnumInfos(param)
        .then((result) => {
          this.calculateStatusList = result.data.datas
        })
        .catch(() => {
        })
    },
    getList() {
      this.listLoading = true
      BonusService.QueryPersonalBonusResultEmployee(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(() => {
        this.listLoading = false
      })
    },
    getSalesBonusList(id) {
      this.listLoading = true
      BonusService.QueryPersonalBonusResultEmployeeDetail({ bonusResultEmployeeId: id }).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.bonusResultEmployeeDetailList = result.data.datas
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(() => {
        this.listLoading = false
      })
    },
    getIncrementalBonusList(id) {
      this.listLoading = true
      BonusService.QueryBonusResultEmployeeIncrement({ bonusResultEmployeeId: id }).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.bonusResultEmployeeIncrementList = result.data.datas
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(() => {
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    resetBonus() {
      this.bonusCalculation = {
        months: '',
        deptId: undefined,
        isIncrement: false
      }
    },
    handleCreate() {
      this.resetBonus()
      this.dialogEditFormVisible = true
      this.$nextTick(() => {
        this.$refs['bonusCalculationForm'].clearValidate()
      })
    },
    btnClose() {
      this.dialogEditFormVisible = false
    },
    timeRangeChange() {
      if (this.bonusCalculation.months) {
        const startArr = this.bonusCalculation.months[0].split('-')
        const endArr = this.bonusCalculation.months[1].split('-')
        const startMonth = startArr[0] * 12 + startArr[1]
        const endMonth = endArr[0] * 12 + endArr[1]
        this.isDisabled = endMonth - startMonth < 3
      }
    },
    reviewDetail(row) {
      this.bonusResultEmployee = row
      this.dialogPersonalDetailFormVisible = true
      this.jobNo = row.employeeJobNo
      this.employeeDisplayName = row.employeeDisplayName
      this.$nextTick(() => {
        this.getSalesBonusList(row.id)
        this.getIncrementalBonusList(row.id)
      })
    },
    reviewPersonalDetails(row) {
      this.dialogPersonalDetailFormVisible = true
      this.$nextTick(() => {
        this.getSalesBonusList()
        this.getIncrementalBonusList()
      })
    },
    btnPersonalDetailsClose() {
      this.dialogPersonalDetailFormVisible = false
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },
    onHidden() {
      this.dialogEditFormVisible = false
    },
    onRefresh() {
      this.getList()
      this.dialogEditFormVisible = false
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      BonusService.GetMyBonusExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      var exportParam = JSON.parse(JSON.stringify(this.listQuery))
      exportParam.checkedColumns = checkColumns
      BonusService.ExportMyBonus(exportParam)
        .then(result => {
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '我的奖励.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '我的奖励.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    selectUpdate() {
      this.$forceUpdate()
    }
  }
}
</script>
<style>
.query-title{
  margin-bottom: 12px;
}
.filter-item{
  margin: 0 10px 5px 0;
}
.table-title{
  font-weight:bold;
  font-size:13px;
  margin-bottom: 20px;
}
</style>
