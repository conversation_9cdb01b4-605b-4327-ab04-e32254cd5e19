export function decryptCycle(json) {
  if (typeof json === 'string') {
    json = JSON.parse(json)
  }

  const objById = {} // all objects by id
  const refs = [] // references to objects that could not be resolved
  json = (function recurse(obj, prop, parent) {
    if (typeof obj !== 'object' || !obj) { // a primitive value
      return obj
    }

    if ('$ref' in obj) { // a reference
      const ref = obj.$ref
      if (ref in objById) {
        return objById[ref]
      }
      // else we have to make it lazy:
      refs.push([parent, prop, ref])
      return
    } else if ('$id' in obj) {
      const id = obj.$id
      delete obj.$id
      if ('$values' in obj) { // an array
        obj = obj.$values.map(recurse)
      } else { // a plain object
        for (const p in obj) {
          obj[p] = recurse(obj[p], p, obj)
        }
        objById[id] = obj
      }
    }

    return obj
  })(json) // run it!

  for (let i = 0; i < refs.length; i++) { // resolve previously unknown references
    var ref = refs[i]
    ref[0][ref[1]] = objById[refs[2]]
    // Notice that this throws if you put in a reference at top-level
  }
  return json
}

export function encryptCycle(json) {
  if (typeof json === 'string') {
    json = JSON.parse(json)
  }
  // todo
  return json
}

