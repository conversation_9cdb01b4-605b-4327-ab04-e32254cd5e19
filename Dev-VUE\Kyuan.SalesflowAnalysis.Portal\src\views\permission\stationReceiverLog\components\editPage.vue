<template>
  <div>
    <el-dialog
      custom-class="el-dialog-s"
      :title="title"
      width="80%"
      append-to-body
      :close-on-click-modal="false"
      :visible="showAddDialog"
      @close="closeDialog"
    >
      <el-form
        ref="dataForm"
        :model="dataModel"
        label-position="right"
        label-width="100px"
        class="el-dialogform"
        :rules="rules"
        style="width: 90%"
      >
        <el-row type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="部门">
              {{ dataModel.deptName }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="上级岗位">
              <el-row>
                <el-col :span="24" style="padding-right: 5px">
                  {{ dataModel.stationParentName }}
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="岗位名称">
              {{ dataModel.stationName }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="负责省份">
              {{ dataModel.responsibleProvinceName }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="终端名称">
              {{ dataModel.receiverName }}
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="挂岗时间" prop="startDate">
              <el-date-picker
                v-model="dataModel.startDate"
                type="month"
                style="width: 100%"
                format="yyyy-MM"
                value-format="yyyy-MM-dd"
                placeholder="挂岗时间"
                :picker-options="datePickerOptions"
              />
            </el-form-item>
          </el-col>

          <el-col :span="span">
            <el-form-item label="撤岗时间">
              <span v-if="dataModel.endDate">{{ dataModel.endDate | parseTime('{y}-{m}-{d}') }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeDialog"> 关闭 </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          :loading="btnSaveLoading"
          @click="submitForm"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import StationReceiverService from '@/api/stationReceiver'
export default {
  data() {
    return {
      span: 12,
      title: '修改挂岗时间',
      showAddDialog: false,
      btnSaveLoading: false,
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6
        }
      },
      dataModel: { },
      rules: {
        startDate: [
          {
            required: true,
            message: '请选择挂岗时间',
            trigger: 'change'
          }
        ]
      }

    }
  },
  created() {
  },
  methods: {
    init(row) {
      StationReceiverService.GetStationReceiverByID({ id: row.id }).then(res => {
        this.dataModel = res.data
      }).catch(() => {
      })
      this.showAddDialog = true
    },
    submitForm() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.btnSaveLoading = true
          StationReceiverService.UpdateStationReceiver(this.dataModel).then(result => {
            this.btnSaveLoading = false
            if (result.succeed) {
              this.$notice.message('编辑成功', 'success')
              this.$emit('refreshData')
              this.closeDialog()
            } else {
              if (result.type !== -3) {
                this.$notice.resultTip(result)
              }
            }
          }).catch(() => {
            this.btnSaveLoading = false
          })
        }
      })
    },
    closeDialog() {
      this.showAddDialog = false
      this.dataModel = {}
      this.$refs.dataForm.resetFields()
    }
  }
}
</script>
<style scoped>
.el-collapse-item ::v-deep .el-collapse-item__header {
  font-size: 14px !important;
  font-weight: 600 !important;
}
</style>
