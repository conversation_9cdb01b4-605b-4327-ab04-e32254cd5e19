/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    permissions: ['admin','editor']    control the page permissions (you can set multiple permissions)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

import Layout from '@/layout'

const permissionRoutes = [
  {
    path: '/permission',
    component: Layout,
    redirect: '/permission/index',
    hidden: false,
    meta: {
      title: '权限管理',
      sort: 80,
      icon: 'eye-open'
    },
    children: [
      {
        name: 'Employee',
        path: 'employee',
        component: () => import('@/views/permission/employee/index'),
        meta: {
          title: '员工管理',
          icon: 'peoples',
          sort: 1,
          noCache: false,
          permissions: ['Permission_User']
        }
      },
      {
        name: 'Role',
        path: 'role',
        component: () => import ('@/views/permission/role/index'),
        meta: {
          title: '角色管理',
          icon: 'skill',
          sort: 2,
          noCache: false,
          permissions: ['Permission_Role']
        }
      },
      {
        name: 'Position',
        path: 'position',
        component: () => import ('@/views/permission/position/index'),
        meta: {
          title: '组织架构管理',
          icon: 'tree',
          sort: 3,
          noCache: false,
          permissions: ['Permission_Station']
        }
      },
      {
        name: 'EmployeeStationLog',
        path: 'employeeStationLog',
        component: () => import ('@/views/permission/employeeStationLog/index'),
        meta: {
          title: '人员在岗日志',
          icon: 'list',
          sort: 4,
          noCache: false,
          permissions: ['Permission_StationEmployee']
        }
      },
      {
        name: 'StationReceiverLog',
        path: 'stationReceiverLog',
        component: () => import ('@/views/permission/stationReceiverLog/index'),
        meta: {
          title: '终端在岗日志',
          icon: 'nested',
          sort: 4,
          noCache: false,
          permissions: ['Permission_StationReceiver']
        }
      },
      {
        name: 'StationLog',
        path: 'stationLog',
        component: () => import ('@/views/permission/stationLog/index'),
        meta: {
          title: '岗位创建日志',
          icon: 'tree-table',
          sort: 4,
          noCache: false,
          permissions: ['Permission_StationLog']
        }
      }
    ]
  }
]

export default permissionRoutes
