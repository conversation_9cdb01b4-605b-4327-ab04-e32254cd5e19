<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="8">
          <el-date-picker
            v-model="filter.checkedMonths"
            clearable
            class="filter-item"
            style="width:100%"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            format="yyyy-MM"
            value-format="yyyy-MM"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.receiverName"
            clearable
            placeholder="终端名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="filter.manufacturerId"
            :loading="manufacturerLoading"
            class="filter-item"
            placeholder="厂商"
            clearable
            @change="manufacturerChange"
          >
            <el-option
              v-for="item in manufacturerList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProductAndSpec"
            v-model="productAndSpecId"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            placeholder="产品/规格"
            clearable
            class="filter-item"
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            @change="closeProductCascader"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'SalesTracking_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>

      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'SalesTracking_Button_GenerateByCommercial')"
            type="primary"
            icon="el-icon-upload2"
            @click="handleGenerateByCommercial"
          >
            商务生成问卷
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'SalesTracking_Button_GenerateExcel')"
            type="primary"
            icon="el-icon-upload2"
            @click="handleGenerateSalesTracking"
          >
            生成问卷
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'SalesTracking_Button_BatchImport')"
            type="primary"
            icon="el-icon-upload2"
            @click="handleImport"
          >
            批量导入
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'SalesTracking_Button_Export')"
            :loading="btnExportLoading"
            class="filter-item-button"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column fixed label="流向月份" align="center" sortable="custom" min-width="100px" prop="Cycle.YearMonthSplicing">
              <template slot-scope="{ row }">
                <span>{{ row.cycleYearMonthSplicing }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="终端名称" align="left" header-align="center" sortable="custom" min-width="140px" prop="Receiver.Name">
              <template slot-scope="{ row }">
                <span>{{ row.receiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="负责人" align="center" header-align="center" sortable="custom" min-width="110px" prop="ReceiverEmployee.DisplayName">
              <template slot-scope="{ row }">
                <span>{{ row.receiverEmployeeDisplayName }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="员工编号" align="center" header-align="center" sortable="custom" min-width="110px" prop="ReceiverEmployee.JobNo">
              <template slot-scope="{ row }">
                <span>{{ row.receiverEmployeeJobNo }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="厂商" align="center" sortable="custom" min-width="110px" prop="ProductSpec.Product.Manufacturer.Name">
              <template slot-scope="{ row }">
                <span>{{ row.manufacturerName }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="产品名称" align="center" sortable="custom" min-width="110px" header-align="center" prop="ProductSpec.Product.NameCn">
              <template slot-scope="{ row }">
                <span>{{ row.productNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column label="通用名" align="center" sortable="custom" min-width="110px" prop="ProductSpec.Product.CommonName">
              <template slot-scope="{ row }">
                <span>{{ row.productCommonName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="规格" align="center" sortable="custom" min-width="110px" header-align="center" prop="ProductSpec.Spec">
              <template slot-scope="{ row }">
                <span>{{ row.productSpec }}</span>
              </template>
            </el-table-column>
            <el-table-column label="指标数量" align="center" header-align="center" sortable="custom" min-width="110px" prop="Quota">
              <template slot-scope="{ row }">
                <span>{{ row.quota }}</span>
              </template>
            </el-table-column>
            <el-table-column label="累积销量" align="center" header-align="center" sortable="custom" min-width="110px" prop="SalesQuantity">
              <template slot-scope="{ row }">
                <span>{{ row.salesQuantity }}</span>
              </template>
            </el-table-column>
            <el-table-column label="终端销量预估" align="center" header-align="center" sortable="custom" min-width="120px" prop="SalesQuantity">
              <template slot-scope="{ row }">
                <span>{{ row.estimatedSalesQuantity }}</span>
              </template>
            </el-table-column>
            <el-table-column label="配送商业" align="right" header-align="center" sortable="custom" min-width="220px" prop="DistributorEmployee.DisplayName">
              <template slot-scope="{ row }">
                <span>{{ row.distributorName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="商业销量预估" align="center" header-align="center" sortable="custom" min-width="120px" prop="DeliveringQuantity">
              <template slot-scope="{ row }">
                <span>{{ row.deliveringQuantity }}</span>
              </template>
            </el-table-column>
            <el-table-column label="商业库存" align="center" header-align="center" sortable="custom" min-width="110px" prop="DeliveringQuantity">
              <template slot-scope="{ row }">
                <span>{{ row.deliveringEstimatedQuantity }}</span>
              </template>
            </el-table-column>
            <el-table-column label="商业负责人" align="center" header-align="center" sortable="custom" min-width="130px" prop="DistributorEmployee.DisplayName">
              <template slot-scope="{ row }">
                <span>{{ row.distributorEmployeeDisplayName }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="filter.pageIndex" :limit.sync="filter.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :visible="showExportModal"
      :before-close="handleExportCancel"
      :close-on-click-modal="false"
      title="导出"
      width="800"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
    <!--商务生成销售跟踪-->
    <el-dialog
      title="生成商务销售跟踪问卷"
      :close-on-click-modal="false"
      :visible="dialogGenerateByCommercialVisible"
      width="50%"
      @close="closeGenerateByCommercialDialog"
    >
      <el-form
        ref="generateByCommercialForm"
        :model="generateByCommercial"
        label-position="right"
        label-width="90px"
        class="el-dialogform"
      >
        <el-form-item label="计算月份" prop="months">
          <el-date-picker
            v-model="generateByCommercial.generateDate"
            clearable
            class="filter-item"
            style="width:100%"
            type="month"
            format="yyyy-MM"
            value-format="yyyy-MM"
            :picker-options="datePickerOptions"
          />
        </el-form-item>

        <el-form-item label="选择产品" prop="products">
          <el-checkbox v-model="checkAllProduct" :indeterminate="isIndeterminateProduct" @change="handleCheckAllProductChange">全选</el-checkbox>
          <el-col v-for="(pro,index) in products" :key="index">
            <el-checkbox v-model="pro.checkAll" :indeterminate="pro.isIndeterminate" @change="pro => { handleCheckedSpecChange(pro,index) }">{{ pro.nameCn }}</el-checkbox>
            <el-checkbox-group v-model="pro.checkProductSpecs" style="margin-left:10px;" @change="pro => { handleCheckedProductChange(pro,index) }">
              <el-checkbox v-for="sp in pro.specification" :key="sp.id" :label="sp.id">{{ sp.spec }}</el-checkbox>
            </el-checkbox-group>
          </el-col>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeGenerateByCommercialDialog">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="saveGenerateByCommercial()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' //  secondary package based on el-pagination
import ManufacturerService from '@/api/manufacturer'
import MaintenanceService from '@/api/maintenance'
import ProductService from '@/api/product'
import SalesTrackingService from '@/api/salesTracking'
import CustomExport from '@/components/Export/CustomExport'

export default {
  name: 'SalesTrackQuery',
  components: {
    Pagination,
    CustomExport
  },
  data() {
    return {
      span: 4,
      total: 0,
      filter: {
        pageIndex: 1,
        pageSize: 10,
        order: '-Cycle.YearMonthSplicing'
      },
      listLoading: false,
      list: [],
      manufacturerLoading: false,
      productAndSpecLoading: false,
      manufacturerList: [],
      productAndSpecList: null,
      productAndSpecId: [],
      provinceCityLoading: false,
      btnExportLoading: false,
      dialogStatus: '',
      showExportModal: false,
      columnDictionary: {},
      productAndSpecKey: 0,
      dialogGenerateByCommercialVisible: false,
      generateByCommercial: {},
      checkAllProduct: false,
      checkedProduct: [],
      products: [],
      isIndeterminateProduct: false,
      datePickerOptions: {
        disabledDate(time) {
          const current = new Date()
          const lastMonth = new Date(current.getFullYear(), current.getMonth() + 1, 1)
          return time.getTime() < current || time.getTime() > lastMonth
        }
      }
    }
  },
  created() {
    this.initManufacturer()
    this.initProductAndSpec()
    this.handleFilter()
    this.getProducts()
  },
  methods: {
    //  厂商下拉框
    initManufacturer() {
      this.manufacturerLoading = true
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturerLoading = false
          this.manufacturerList = result
        })
        .catch((error) => {
          this.manufacturerLoading = false
          console.log(error)
        })
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      const para = { manufacturerId: this.filter.manufacturerId }
      ProductService.QueryProductAndSpecCascader(para)
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    manufacturerChange() {
      this.productAndSpecId = []
      ++this.productAndSpecKey
      this.initProductAndSpec()
    },
    timeRangeChange() {
      this.$forceUpdate()
    },
    handleFilter() {
      this.filter.pageIndex = 1
      this.getList()
    },
    //  加载数据
    getList() {
      if (this.productAndSpecId) {
        const [productId, productSpecId] = this.productAndSpecId
        this.filter.productId = productId
        this.filter.productSpecId = productSpecId
      }
      this.listLoading = true
      SalesTrackingService.QuerySalesTracking(this.filter).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.filter.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.filter.pageIndex - 1) * this.filter.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.filter.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.filter.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.filter.pageSize = val
      this.handleFilter()
    },
    closeProductCascader() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      SalesTrackingService.GetSalesTrackingExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      this.filter.checkedColumns = checkColumns
      SalesTrackingService.ExportSalesTrackings(this.filter)
        .then(result => {
          this.filter.checkedColumns = null
          const fileDownload = require('js-file-download')
          var filename = '销售跟踪.xlsx'

          fileDownload(result.data, filename)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    handleGenerateByCommercial() {
      this.dialogGenerateByCommercialVisible = true
    },
    saveGenerateByCommercial() {
      this.getCheckProductSpecs()
      this.$refs['generateByCommercialForm'].validate((valid) => {
        if (valid) {
          SalesTrackingService.GenerateSalesTrackingByCommercial(this.generateByCommercial)
            .then((result) => {
              if (result.succeed) {
                this.dialogGenerateByCommercialVisible = false
                this.showMessage('生成成功', 'success')
                this.getList()
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
              this.loading = false
            })
        }
      })
    },
    closeGenerateByCommercialDialog() {
      this.generateByCommercial = {}
      this.dialogGenerateByCommercialVisible = false
    },
    handleCheckAllProductChange(val) {
      this.products.forEach((item, index) => {
        this.products[index].checkAll = val
        this.handleCheckedSpecChange(val, index)
      })
    },
    handleCheckedSpecChange(val, index) {
      this.products[index].checkProductSpecs = (val
        ? this.products[index].specification
        : []
      ).map(item => item.id)
      this.products[index].isIndeterminate = false
      this.topCheckBoxCheck()
    },
    handleCheckedProductChange(value, index) {
      const checkedCount = value.length
      this.products[index].checkAll =
        checkedCount === this.products[index].specification.length
      this.products[index].isIndeterminate =
        checkedCount > 0 &&
        checkedCount < this.products[index].specification.length
      this.topCheckBoxCheck()
    },
    topCheckBoxCheck() {
      const allSelectLen = this.products.filter(item => item.checkAll)
        .length
      if (allSelectLen === this.products.length) {
        this.checkAllProduct = true
        this.isIndeterminateProduct = false
      } else {
        this.checkAllProduct = false
        this.isIndeterminateProduct =
          this.products.findIndex(item => item.isIndeterminate) >= 0 ||
          this.products.findIndex(item => item.checkAll) >= 0
      }
    },
    getProducts() {
      MaintenanceService.QueryProductSelect().then(result => {
        if (result) {
          const tempArr = []
          result.data.forEach(item => {
            var specification = item.specification.map(e => {
              return e.id
            })
            var isCheck = false
            var i = 0
            var newProducts = []
            tempArr.push({
            // 子项的全选状态
              checkAll: i === specification.length,
              // 子项的默认选中的checkbox
              checkProductSpecs: isCheck ? newProducts : [],
              isIndeterminate: i > 0 && specification.length > i,
              id: item.id,
              nameCn: item.nameCn,
              specification: item.specification
            })
          })
          this.products = tempArr

          this.$nextTick(() => {
            this.handleCheckAllProductChange(true)
          })
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    getCheckProductSpecs() {
      const res = this.products.map(item => {
        if (item.checkProductSpecs && item.checkProductSpecs.length > 0) {
          return item.checkProductSpecs
        }
      })
      var products = []
      res.flat().filter(item => {
        if (item) {
          return { item }
        }
      }).forEach(e => {
        products.push(e)
      })
      this.generateByCommercial.productSpecIds = products
    }
  }

}
</script>

