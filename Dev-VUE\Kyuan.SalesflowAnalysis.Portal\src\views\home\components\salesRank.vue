/* eslint-disable space-before-blocks */
<template>
  <div>
    <el-row>
      <el-col :span="8">
        <el-cascader
          ref="refProductAndSpec"
          v-model="rankProductAndSpecId"
          :options="rankProductAndSpecList"
          placeholder="产品/规格"
          class="filter-item"
          :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
          @change="productChange"
        />
      </el-col>
    </el-row>
    <el-row style="padding-top:5px">
      <el-col style="width: 100%;overflow-y:auto;height: 215px">
        <el-table :data="listRank" border height="200px">
          <el-table-column label="终端名称" header-align="center">
            <template slot-scope="{ row }">
              <span :title="row.receiverName"> {{ row.receiverName | ellipsis }}</span>
            </template>
          </el-table-column>
          <el-table-column label="产品/规格" width="130px" header-align="center">
            <template slot-scope="{ row }">
              <span> {{ row.productNameCn }}/{{ row.spec }}</span>
            </template>
          </el-table-column>
          <el-table-column label="流向" width="80px" prop="saleQty" align="center" />
          <el-table-column label="进度" width="80px" prop="achievedRate" align="center">
            <template slot-scope="{ row }">
              <span> {{ row.achievedRate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="排名" width="60px" prop="salesQtyRank" align="center" />
        </el-table>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import HomeService from '@/api/home'
import ProductService from '@/api/product'

export default {
  filters: {
    ellipsis(value) {
      if (!value) return ''
      if (value.length > 13) {
        return value.slice(0, 15) + '...'
      }
      return value
    }
  },
  data() {
    return {
      listRank: [],
      listRankQuery: {
        pageIndex: 1,
        pageSize: 20
      },
      rankProductAndSpecId: [],
      rankProductAndSpecList: []
    }
  },
  created() {
    this.initProductAndSpec()
  },
  methods: {
    initProductAndSpec() {
      ProductService.QueryProductAndSpecCascader()
        .then((result) => {
          this.rankProductAndSpecList = result
          this.rankProductAndSpecList.some(p => {
            if (p.children) {
              if (p.children[0]) {
                this.$nextTick(() => {
                  this.rankProductAndSpecId = [p.value, p.children[0].value]
                  this.$emit('specChange', this.rankProductAndSpecId)
                  this.fetchData()
                })
                return true
              }
            }
          })
        })
        .catch((error) => {
          console.log(error)
        })
    },
    productChange() {
      this.fetchData()
    },
    fetchData() {
      this.listRankQuery.productSpecId = this.rankProductAndSpecId[1]
      this.$emit('specChange', this.rankProductAndSpecId)
      HomeService.QuerySalesRanking(this.listRankQuery).then(res => {
        this.listRank = res
      }).catch(res => {})
    },
    indexMethod(index) {
      return (this.listRankQuery.pageIndex - 1) * this.listRankQuery.pageSize + index + 1
    }
  }
}
</script>
