<template>
  <div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="异常流向" name="warning">
        <div class="search-container-bg" style="padding: 15px 10px 10px 10px">
          <el-row :gutter="10" class="filter-container" type="flex">
            <el-col :span="8">
              <el-date-picker
                v-model="warningListQuery.salesDateRange"
                clearable
                class="filter-item"
                type="daterange"
                range-separator="至"
                start-placeholder="销售开始日期"
                end-placeholder="销售结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              />
            </el-col>
            <el-col :span="span">
              <el-select
                v-model="warningListQuery.manufacturerId"
                :loading="manufacturerLoading"
                class="filter-item"
                placeholder="厂商"
                clearable
                @change="manufacturerChange"
              >
                <el-option
                  v-for="item in manufacturerList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-col>
            <el-col :span="span">
              <el-cascader
                ref="refProductAndSpec"
                v-model="productAndSpecId"
                :loading="productAndSpecLoading"
                :options="productAndSpecList"
                placeholder="产品/规格"
                class="filter-item"
                :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
                clearable
                @change="productAndSpecChange"
              />
            </el-col>
            <el-col :span="span">
              <el-input
                v-model="warningListQuery.distributorName"
                clearable
                placeholder="发货方名称"
                class="filter-item"
                @keyup.enter.native="handleWarningFilter"
              />
            </el-col>
            <el-col :span="span">
              <el-input
                v-model="warningListQuery.receiverName"
                clearable
                placeholder="收货方名称"
                class="filter-item"
                @keyup.enter.native="handleWarningFilter"
              />
            </el-col>
            <el-col :span="span">
              <el-select
                v-model="warningListQuery.warningSalesFlowType"
                class="filter-item"
                placeholder="错误描述"
                style="width:100%"
                clearable
              >
                <el-option
                  v-for="item in enumWarningSalesFlowTypes"
                  :key="item.value"
                  :label="item.desc"
                  :value="item.value"
                />
              </el-select>
            </el-col>
            <el-col :span="span">
              <el-button
                v-if="$isPermitted($store.getters.user, 'Warning_Button_SalesFlawQuery')"
                type="primary"
                class="filter-item-button"
                icon="el-icon-search"
                @click="handleWarningFilter"
              >
                查询
              </el-button>
            </el-col>
          </el-row>
          <el-row type="flex" justify="end" :gutter="10">
            <el-col :span="3.5">
              <el-button
                v-if="$isPermitted($store.getters.user, 'Warning_Button_Matching')"
                class="filter-item-button"
                type="primary"
                :loading="btnUpdateLoading"
                icon="el-icon-refresh"
                @click="handleMatching"
              >
                匹配地理主数据
              </el-button>
            </el-col>
            <el-col :span="3.5">
              <el-button
                v-if="$isPermitted($store.getters.user, 'Warning_Button_Export')"
                class="filter-item-button"
                type="primary"
                icon="el-icon-download"
                @click="handleSalesflowWarningExport"
              >
                导出
              </el-button>
            </el-col>
          </el-row>
        </div>
        <div class="list-container">
          <el-row>
            <el-col :span="24">
              <el-table
                v-loading="listLoading"
                :data="warningList"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
                @sort-change="warningSortChange"
              >
                <el-table-column fixed label="序号" :index="indexWarningMethod" type="index" align="center" />
                <el-table-column sortable="custom" prop="Distributor.Province.NameCn" label="发货方省份" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span v-if="row.enumWarningSalesFlowType & 2">{{ row.warningDistributorProvinceName }}</span>
                    <span v-else>{{ row.distributorProvinceName }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="Distributor.City.NameCn" label="发货方城市" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span v-if="row.enumWarningSalesFlowType & 2 || row.enumWarningSalesFlowType & 4 ">{{ row.warningDistributorCityName }}</span>
                    <span v-else>{{ row.distributorCityName }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="Distributor.Name" label="发货方" min-width="200px" align="left" header-align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.distributorName }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="Receiver.Name" label="收货方" min-width="200px" align="left" header-align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.receiverName }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="Manufacturer.Name" label="厂商" min-width="100px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.manufacturerName }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="Product.NameCn" label="产品名称" min-width="100px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.productNameCn }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="Product.CommonName" label="通用名" min-width="150px" align="left" header-align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.productCommonName }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="ProductSpec.Spec" label="包装规格" min-width="100px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.specification }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="BatchNumber" label="批次" min-width="100px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.batchNumber }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="ExpireDate" label="产品效期" min-width="100px" align="center">
                  <template slot-scope="{ row }">
                    <span v-if="row.expireDate !== undefined">{{ row.expireDate | parseTime('{y}-{m}-{d}') }}</span>
                    <span v-else />
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="SaleDate" label="销售日期" min-width="100px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.saleDate | parseTime('{y}-{m}-{d}') }}</span>
                  </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="Quantity" label="销售数量" min-width="100px" align="right" header-align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.quantity }}</span>
                  </template>
                </el-table-column>
                <el-table-column fixed="right" sortable="custom" prop="EnumWarningSalesFlowType" label="错误描述" min-width="150px" align="left" header-align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.enumWarningSalesFlowTypeDesc }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  fixed="right"
                  label="操作"
                  align="center"
                  header-align="center"
                  width="100"
                  class-name="small-padding fixed-width"
                >
                  <template slot-scope="{ row }">
                    <i v-if="(row.enumWarningSalesFlowType & 1) && $isPermitted($store.getters.user, 'Warning_Button_IgnoreRepeat')" class="el-icon-circle-close eltablei" title="忽略重复" @click="handleIgnoreRepeat(row)" />
                    <i v-if="((row.enumWarningSalesFlowType & 2) || (row.enumWarningSalesFlowType & 4)) && $isPermitted($store.getters.user, 'Warning_Button_IgnoreError')" class="el-icon-remove-outline eltablei" title="忽略错误" @click="handleIgnoreWarning(row)" />
                    <i v-if="(row.enumWarningSalesFlowType & 1) && $isPermitted($store.getters.user, 'Warning_Button_Del')" class="el-icon-delete eltablei" title="删除流向" @click="handleDelete(row)" />
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col class="el-colRight">
              <pagination
                v-show="warningTotal > 0"
                :total="warningTotal"
                :page.sync="warningListQuery.pageIndex"
                :limit.sync="warningListQuery.pageSize"
                @pagination="getWarningList"
              />
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
      <el-tab-pane label="异常客户数据" name="custom">
        <div class="search-container-bg" style="padding: 15px 10px 10px 10px">
          <el-row :gutter="10" class="filter-container minusBottom" type="flex">
            <el-col :span="span">
              <el-date-picker
                v-model="customListQuery.checkedMonth"
                style="width:100%"
                type="month"
                placeholder="流向月份"
                value-format="yyyy-MM"
                class="filter-item"
              />
            </el-col>
            <el-col :span="span">
              <el-cascader
                ref="refprovince"
                v-model="customListQuery.provinceCityId"
                :loading="provinceCityLoading"
                :options="provinceCityList"
                placeholder="省份/城市"
                clearable
                class="filter-item"
                :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
                @change="provinceCitChange"
              />
            </el-col>
            <el-col :span="span">
              <el-input
                v-model="customListQuery.distributorName"
                clearable
                placeholder="发货方名称"
                class="filter-item"
                @keyup.enter.native="handleCustomFilter"
              />
            </el-col>
            <el-col :span="span">
              <el-button
                v-if="$isPermitted($store.getters.user, 'Warning_Button_SalesFlawQuery')"
                class="filter-item-button"
                type="primary"
                icon="el-icon-search"
                @click="handleCustomFilter"
              >
                查询
              </el-button>
            </el-col>
          </el-row>
          <el-row type="flex" justify="end" :gutter="10">
            <el-col :span="3.5">
              <el-button
                v-if="$isPermitted($store.getters.user, 'Warning_Button_Export')"
                class="filter-item-button"
                type="primary"
                icon="el-icon-download"
                @click="handleWarningCustomerExport"
              >
                导出
              </el-button>
            </el-col>
          </el-row>
        </div>
        <div class="list-container">
          <el-row>
            <el-col :span="24">
              <el-table
                v-loading="listLoading"
                :data="customList"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
                @sort-change="customSortChange"
              >
                <el-table-column fixed label="序号" :index="indexCustomMethod" type="index" align="center" />
                <el-table-column label="流向月份" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.salesFlowMonth }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="发货方省份" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.distributorProvinceName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="发货方城市" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.distributorCityName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="发货方" min-width="200px" align="left" header-align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.distributorName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="异常描述" min-width="100px" align="left" header-align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.warningMessage }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col class="el-colRight">
              <pagination
                v-show="customTotal > 0"
                :total="customTotal"
                :page.sync="customListQuery.pageIndex"
                :limit.sync="customListQuery.pageSize"
                @pagination="getCustomList"
              />
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
      <el-tab-pane label="异常终端数据" name="terminal">
        <div class="search-container-bg" style="padding: 15px 10px 10px 10px">
          <el-row :gutter="10" class="filter-container minusBottom" type="flex">
            <el-col :span="span">
              <el-date-picker
                v-model="terminalListQuery.checkedMonth"
                style="width:100%"
                type="month"
                placeholder="流向月份"
                value-format="yyyy-MM"
                class="filter-item"
              />
            </el-col>
            <el-col :span="span">
              <el-cascader
                ref="refProvinceCity"
                v-model="terminalListQuery.provinceCityId"
                :loading="provinceCityLoading"
                :options="provinceCityList"
                placeholder="省份城市"
                clearable
                class="filter-item"
                :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
                @change="terminalProvinceCitChange"
              />
            </el-col>
            <el-col :span="span">
              <el-input
                v-model="terminalListQuery.receiverName"
                clearable
                placeholder="收货方名称"
                class="filter-item"
                @keyup.enter.native="handleTerminalFilter"
              />
            </el-col>
            <el-col :span="span">
              <el-button
                v-if="$isPermitted($store.getters.user, 'Warning_Button_SalesFlawQuery')"
                class="filter-item-button"
                type="primary"
                icon="el-icon-search"
                @click="handleTerminalFilter"
              >
                查询
              </el-button>
            </el-col>
          </el-row>
          <el-row type="flex" justify="end" :gutter="10">
            <el-col :span="3.5">
              <el-button
                v-if="$isPermitted($store.getters.user, 'Warning_Button_Export')"
                class="filter-item-button"
                type="primary"
                icon="el-icon-download"
                @click="handleWarningTerminalExport"
              >
                导出
              </el-button>
            </el-col>
          </el-row>
        </div>
        <div class="list-container">
          <el-row>
            <el-col :span="24">
              <el-table
                v-loading="listLoading"
                :data="terminalList"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
                @sort-change="terminalSortChange"
              >
                <el-table-column fixed label="序号" :index="indexTerminalMethod" type="index" align="center" />
                <el-table-column label="流向月份" min-width="120px" header-align="center" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.salesFlowMonth }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="收货方省份" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.receiverProvinceName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="收货方城市" min-width="120px" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.receiverCityName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="收货方" min-width="200px" align="left" header-align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.receiverName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="异常描述" min-width="100px" align="left" header-align="center">
                  <template>
                    <span>当月存在发货数据</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col class="el-colRight">
              <pagination
                v-show="terminalTotal > 0"
                :total="terminalTotal"
                :page.sync="terminalListQuery.pageIndex"
                :limit.sync="terminalListQuery.pageSize"
                @pagination="getTerminalList"
              />
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import ProductService from '@/api/product'
import CycleService from '@/api/cycle'
import SalesFlowService from '@/api/salesFlow'
import ManufacturerService from '@/api/manufacturer'
import LocationService from '@/api/location'
import MasterDataService from '@/api/masterData'
import Pagination from '@/components/Pagination'

export default {
  name: 'SalesFlowWarning',
  components: {
    Pagination
  },
  data() {
    return {
      span: 4,
      activeName: 'warning',
      productAndSpecId: [],
      manufacturerList: [],
      productAndSpecList: [],
      provinceCityList: [],
      enumWarningSalesFlowTypes: [],
      listLoading: true,
      warningTotal: 0,
      customTotal: 0,
      terminalTotal: 0,
      warningListQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      customListQuery: {
        checkedMonth: '',
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      terminalListQuery: {
        checkedMonth: '',
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      warningList: [],
      customList: [],
      terminalList: [],
      btnUpdateLoading: false,
      manufacturerLoading: false,
      productAndSpecLoading: false,
      provinceCityLoading: false,
      prevCycle: {}
    }
  },
  created() {
    this.getCycle()
    this.initManufacturer()
    this.initProductAndSpec()
    this.initProvinceCity()
    this.initWarningSalesFlowType()
    this.getWarningList()
  },
  methods: {
    getCycle() {
      CycleService.GetTheLatestWaitingForApprovalCycle()
        .then((result) => {
          this.prevCycle = result.data
          this.customListQuery.checkedMonth = this.prevCycle.yearMonthSplicing
          this.terminalListQuery.checkedMonth = this.prevCycle.yearMonthSplicing
        })
        .catch(() => {
        })
    },
    initManufacturer() {
      this.manufacturerLoading = true
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturerLoading = false
          this.manufacturerList = result
        })
        .catch((error) => {
          this.manufacturerLoading = false
          console.log(error)
        })
    },
    manufacturerChange() {
      this.initProductAndSpec()
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      const para = { manufacturerId: this.warningListQuery.manufacturerId }
      ProductService.QueryProductAndSpecCascader(para)
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    initProvinceCity() {
      this.provinceCityLoading = true
      LocationService.QueryProvinceCityCascader()
        .then((result) => {
          this.provinceCityList = result
          this.provinceCityLoading = false
        })
        .catch((error) => {
          this.provinceCityLoading = false
          console.log(error)
        })
    },
    indexCustomMethod(index) {
      return (
        (this.customListQuery.pageIndex - 1) * this.customListQuery.pageSize + index + 1
      )
    },
    indexWarningMethod(index) {
      return (
        (this.warningListQuery.pageIndex - 1) * this.warningListQuery.pageSize + index + 1
      )
    },
    indexTerminalMethod(index) {
      return (
        (this.terminalListQuery.pageIndex - 1) * this.terminalListQuery.pageSize + index + 1
      )
    },
    getWarningList() {
      if (this.productAndSpecId) {
        const [productId, productSpecId] = this.productAndSpecId
        this.warningListQuery.productId = productId
        this.warningListQuery.productSpecId = productSpecId
      }
      this.listLoading = true
      SalesFlowService.QueryWarningSalesFlow(this.warningListQuery).then(res => {
        this.listLoading = false
        this.warningList = res.data.datas
        this.warningTotal = res.data.recordCount
        this.warningListQuery.pageIndex = res.data.pageIndex
      }).catch(res => { this.listLoading = false })
    },
    handleWarningFilter() {
      this.warningListQuery.pageIndex = 1
      this.getWarningList()
    },
    handleCustomFilter() {
      this.customListQuery.pageIndex = 1
      this.getCustomList()
    },
    handleTerminalFilter() {
      this.terminalListQuery.pageIndex = 1
      this.getTerminalList()
    },
    handleSalesflowWarningExport() {
      if (this.productAndSpecId) {
        const [productId, productSpecId] = this.productAndSpecId
        this.warningListQuery.productId = productId
        this.warningListQuery.productSpecId = productSpecId
      }
      SalesFlowService.ExportWarningSalesFlow(this.warningListQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '异常流向.xlsx'

          fileDownload(result.data, filename)
        })
        .catch(() => {

        })
    },
    handleWarningCustomerExport() {
      if (this.customListQuery.provinceCityId) {
        const [provinceId, cityId] = this.customListQuery.provinceCityId
        this.customListQuery.distributorProvinceId = provinceId
        this.customListQuery.distributorCityId = cityId
      }

      SalesFlowService.ExportWarningCustomer(this.customListQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '异常客户数据.xlsx'

          fileDownload(result.data, filename)
        })
        .catch(() => {

        })
    },
    handleWarningTerminalExport() {
      if (this.terminalListQuery.provinceCityId) {
        const [provinceId, cityId] = this.terminalListQuery.provinceCityId
        this.terminalListQuery.receiverProvinceId = provinceId
        this.terminalListQuery.receiverCityId = cityId
      }

      SalesFlowService.ExportWarningTerminal(this.terminalListQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '异常终端数据.xlsx'

          fileDownload(result.data, filename)
        })
        .catch(() => {

        })
    },
    warningSortChange(column, prop, order) {
      this.warningListQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.warningListQuery.order = orderSymbol + column.prop
      this.getWarningList()
    },
    customSortChange(column, prop, order) {
      this.customListQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.customListQuery.order = orderSymbol + column.prop
      this.getCustomList()
    },
    terminalSortChange(column, prop, order) {
      this.terminalListQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.terminalListQuery.order = orderSymbol + column.prop
      this.getTerminalList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleMatching() {
      this.$confirm('确定匹配地理主数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.btnUpdateLoading = true
          if (this.productAndSpecId) {
            const [productId, productSpecId] = this.productAndSpecId
            this.warningListQuery.productId = productId
            this.warningListQuery.productSpecId = productSpecId
          }
          SalesFlowService.BatchUpdateWarningSalesFlow(this.warningListQuery).then(res => {
            this.getWarningList()
            this.btnUpdateLoading = false
          }).catch(res => {})
        })
        .catch((error) => {
          if (!error.succeed) {
            this.showMessage('取消匹配地理主数据', 'info')
          }
        })
    },
    handleIgnoreRepeat(row) {
      this.$confirm('确定忽略该条重复流向吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const tempRow = Object.assign({}, row)

          SalesFlowService.IgnoreRepeatSalesFlow(tempRow)
            .then((result) => {
              if (result.succeed) {
                this.getWarningList()
                this.showMessage('忽略成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
            })
        })
        .catch((error) => {
          if (!error.succeed) {
            this.showMessage('取消忽略', 'info')
          }
        })
    },
    handleIgnoreWarning(row) {
      this.$confirm('确定忽略该条异常流向吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const tempRow = Object.assign({}, row)

          SalesFlowService.IgnoreWarningSalesFlow(tempRow)
            .then((result) => {
              if (result.succeed) {
                this.getWarningList()
                this.showMessage('忽略成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
            })
        })
        .catch((error) => {
          if (!error.succeed) {
            this.showMessage('取消忽略', 'info')
          }
        })
    },
    handleDelete(row) {
      this.$confirm('确定删除此条流向吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.temp = Object.assign({}, row)

          SalesFlowService.DeleteSalesFlow(this.temp)
            .then((result) => {
              if (result.succeed) {
                this.getWarningList()
                this.showMessage('删除成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch((error) => {
              console.log(error)
            })
        })
        .catch((error) => {
          if (!error.succeed) {
            this.showMessage('取消删除', 'info')
          }
        })
    },
    getCustomList() {
      if (this.customListQuery.provinceCityId) {
        const [provinceId, cityId] = this.customListQuery.provinceCityId
        this.customListQuery.distributorProvinceId = provinceId
        this.customListQuery.distributorCityId = cityId
      }
      this.listLoading = true
      SalesFlowService.QueryWarningCustomer(this.customListQuery).then(res => {
        this.listLoading = false
        this.customList = res.data.datas
        this.customTotal = res.data.recordCount
        this.customListQuery.pageIndex = res.data.pageIndex
      }).catch(() => { this.listLoading = false })
    },
    getTerminalList() {
      if (this.terminalListQuery.provinceCityId) {
        const [provinceId, cityId] = this.terminalListQuery.provinceCityId
        this.terminalListQuery.receiverProvinceId = provinceId
        this.terminalListQuery.receiverCityId = cityId
      }
      this.listLoading = true
      SalesFlowService.QueryWarningTerminal(this.terminalListQuery).then(res => {
        this.listLoading = false
        this.terminalList = res.data.datas
        this.terminalTotal = res.data.recordCount
        this.terminalListQuery.pageIndex = res.data.pageIndex
      }).catch(res => { this.listLoading = false })
    },
    handleClick(tab, event) {
      if (tab.name === 'warning') {
        this.getWarningList()
      }
      if (tab.name === 'custom') {
        this.getCustomList()
      }
      if (tab.name === 'terminal') {
        this.getTerminalList()
      }
    },
    provinceCitChange() {
      if (this.customListQuery.provinceCityId !== undefined && this.customListQuery.provinceCityId !== null && this.customListQuery.provinceCityId.length > 0) {
        this.customListQuery.provinceId = this.customListQuery.provinceCityId[0]
        if (this.customListQuery.provinceCityId.length > 1) {
          this.customListQuery.cityId = this.customListQuery.provinceCityId[1]
        } else {
          this.customListQuery.cityId = null
        }
      } else {
        this.customListQuery.provinceId = null
        this.customListQuery.cityId = null
      }
      this.$refs.refprovince.dropDownVisible = false
    },
    terminalProvinceCitChange() {
      if (this.terminalListQuery.provinceCityId !== undefined && this.terminalListQuery.provinceCityId !== null && this.terminalListQuery.provinceCityId.length > 0) {
        this.terminalListQuery.provinceId = this.terminalListQuery.provinceCityId[0]
        if (this.terminalListQuery.provinceCityId.length > 1) {
          this.terminalListQuery.cityId = this.terminalListQuery.provinceCityId[1]
        } else {
          this.terminalListQuery.cityId = null
        }
      } else {
        this.terminalListQuery.provinceId = null
        this.terminalListQuery.cityId = null
      }
      this.$refs.refProvinceCity.dropDownVisible = false
    },
    productAndSpecChange() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    initWarningSalesFlowType() {
      MasterDataService.GetEnumInfos({ enumType: 'WarningSalesFlowType' })
        .then((result) => {
          this.enumWarningSalesFlowTypes = result.data.datas
        })
        .catch((error) => {
          this.enumReceiverFlagLoading = false
          console.log(error)
        })
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    }
  }
}
</script>
