<template>
  <div class="list-container">
    <el-row type="flex" justify="end" :gutter="10" style="margin-bottom:10px">
      <el-col :span="3.5">
        <el-button
          v-if="$isPermitted($store.getters.user, 'Product_Button_Export')"
          :loading="btnExportLoading"
          class="filter-item"
          type="primary"
          icon="el-icon-download"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-table
          :data="list"
          stripe
          border
          fit
          highlight-current-row
          style="width: 100%;"
          :default-sort="{prop: 'createTime', order: 'descending'}"
          :header-cell-class-name="'tableStyle'"
          :row-class-name="handleRowClass"
          @sort-change="sortChange"
        >
          <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
          <el-table-column sortable="custom" prop="ManufacturerName" align="center" label="厂商名称" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.manufacturerName }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="ProductNameCn" align="center" label="产品名称" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.productNameCn }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="CommonName" align="left" header-align="center" label="通用名" min-width="150px">
            <template slot-scope="{ row }">
              <span>{{ row.commonName }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="ProductNameEn" label="英文名称" align="center" min-width="100px">
            <template slot-scope="{ row }">
              <span>{{ row.productNameEn }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="SpecCode" label="规格编码" min-width="100px" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.specCode }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="Specification" label="包装规格" min-width="100px" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.specification }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="Unit" label="包装单位" min-width="100px" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.unit }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="DosageFormName" label="剂型" min-width="80px" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.dosageFormName }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="ErrorMessage" label="错误信息" align="left" header-align="center" min-width="150px">
            <template slot-scope="{ row }">
              <span>{{ row.errorMessage }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col class="el-colRight">
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.pageIndex"
          :limit.sync="listQuery.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
  </div>
</template>
<script>
import ProductService from '@/api/product'
import Pagination from '@/components/Pagination'

export default {
  components: {
    Pagination
  },
  props: {
    importMasterTempId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      btnExportLoading: false
    }
  },
  watch: {
    importMasterTempId: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function(val) {
        this.listQuery.importMasterTempId = val
        this.getList()
      }
    }
  },
  methods: {
    getList() {
      ProductService.QueryImportProductError(this.listQuery).then(res => {
        this.list = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(res => {})
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    // 确认导出
    handleExport() {
      this.btnExportLoading = true
      ProductService.ExportImportProductError(this.listQuery)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '产品错误数据.xlsx'

          fileDownload(result.data, filename)
          this.btnExportLoading = false
        })
        .catch((error) => {
          console.log(error)
          this.btnExportLoading = false
        })
    }
  }
}
</script>
<style scoped>
  .el-dialog-s {
  z-index: 12;
}
</style>
