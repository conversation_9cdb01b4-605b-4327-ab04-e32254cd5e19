import HttpApi from './libs/api.request'

const controller = 'SalesFlow'

const api = new HttpApi(controller)

export default {
  QueryImportSalesFlowMasterTemp(params) {
    return api.get('QueryImportSalesFlowMasterTemp', params)
  },
  QueryImportSalesFlowHandle(params) {
    return api.get('QueryImportSalesFlowHandle', params)
  },
  GetImportSalesFlowHandle(params) {
    return api.get('GetImportSalesFlowHandle', params)
  },
  QuerySalesFlow(params) {
    return api.get('QuerySalesFlow', params)
  },
  QueryMySalesFlow(params) {
    return api.get('QueryMySalesFlow', params)
  },
  GetSalesFlow(params) {
    return api.get('GetSalesFlow', params)
  },
  UpdateSalesFlow(params) {
    return api.post('UpdateSalesFlow', params)
  },
  GetSalesFlowExportColumn() {
    return api.get('GetSalesFlowExportColumn')
  },
  GetMySalesFlowExportColumn() {
    return api.get('GetMySalesFlowExportColumn')
  },
  ExportSalesFlow(params) {
    return api.post('ExportSalesFlow', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryManufacturerSalesFlow(params) {
    return api.get('QueryManufacturerSalesFlow', params)
  },
  HandleSalesFlow(params) {
    return api.post('HandleSalesFlow', params)
  },
  ExportImportSalesFlowHandle(params) {
    return api.post('ExportImportSalesFlowHandle', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryWarningSalesFlow(params) {
    return api.get('QueryWarningSalesFlow', params)
  },
  ExportWarningSalesFlow(params) {
    return api.post('ExportWarningSalesFlow', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  BatchUpdateWarningSalesFlow(params) {
    return api.post('BatchUpdateWarningSalesFlow', params)
  },
  BatchHandleSalesFlow(params) {
    return api.post('BatchHandleSalesFlow', params)
  },
  DeleteSalesFlow(params) {
    return api.post('DeleteSalesFlow', params)
  },
  IgnoreRepeatSalesFlow(params) {
    return api.post('IgnoreRepeatSalesFlow', params)
  },
  IgnoreWarningSalesFlow(params) {
    return api.post('IgnoreWarningSalesFlow', params)
  },
  QueryWarningCustomer(params) {
    return api.get('QueryWarningCustomer', params)
  },
  ExportWarningCustomer(params) {
    return api.post('ExportWarningCustomer', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryWarningTerminal(params) {
    return api.get('QueryWarningTerminal', params)
  },
  ExportWarningTerminal(params) {
    return api.post('ExportWarningTerminal', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  ExportSalesFlowByPermission(params) {
    return api.post('ExportSalesFlowByPermission', {
      data: params,
      responseType: 'arraybuffer'
    })
  },
  QueryWatingReleaseMonths() {
    return api.get('QueryWatingReleaseMonths')
  },
  SalesFlowRelease(params) {
    return api.post('SalesFlowRelease', params)
  },
  QuerySalesFlowVersion() {
    return api.get('QuerySalesFlowVersion')
  },
  HandleSalesFlowIsOtherCycle(params) {
    return api.post('HandleSalesFlowIsOtherCycle', params)
  },
  ManualGenerateSalesFlowDataToMongo(params) {
    return api.get('ManualGenerateSalesFlowDataToMongo', params)
  },
  QueryGenerateSalesFlowToMongoRecord(params) {
    return api.get('QueryGenerateSalesFlowToMongoRecord', params)
  },
  GetGenerateStartDate(params) {
    return api.get('GetGenerateStartDate', params)
  }
}
