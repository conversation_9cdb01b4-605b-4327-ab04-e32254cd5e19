<template>
  <div>
    <el-dialog
      :title="title"
      width="60%"
      :close-on-click-modal="false"
      :visible="showAddDialog"
      @close="cancle()"
    >
      <el-form ref="dataForm" :rules="rules" :model="requestFormRebateAgreementTerm" label-position="right" label-width="80px" class="el-dialogform">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="条件" prop="agreementTermDescription">
              <el-col :span="18" style="padding-left: 0px; padding-right: 0px;">
                <el-input
                  v-model="requestFormRebateAgreementTerm.agreementTermDescription"
                  :readonly="true"
                  placeholder="请配置条件公式"
                />
              </el-col>
              <el-col :span="3">
                <el-button
                  icon="el-icon-plus"
                  style="width:100%;"
                  type="primary"
                  title="编辑条件公式"
                  @click="handelEditCondition"
                >编辑
                </el-button>
              </el-col>
              <el-col :span="3">
                <el-button
                  icon="el-icon-plus"
                  style="width:100%;"
                  type="primary"
                  title="清空条件公式"
                  @click="handelClearCondition"
                >清空
                </el-button>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="结果" prop="rebateCalculateDescription">
              <el-col :span="18" style="padding-left: 0px; padding-right: 0px;">
                <el-input
                  v-model="requestFormRebateAgreementTerm.rebateCalculateDescription"
                  :readonly="true"
                  placeholder="请配置结果公式"
                />
              </el-col>
              <el-col :span="3">
                <el-button
                  icon="el-icon-plus"
                  style="width:100%;"
                  type="primary"
                  title="编辑结果公式"
                  @click="handelEditResult"
                >编辑
                </el-button>
              </el-col>
              <el-col :span="3">
                <el-button
                  icon="el-icon-plus"
                  style="width:100%;"
                  type="primary"
                  title="清空结果公式"
                  @click="handelClearResult"
                >清空
                </el-button>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="requestFormRebateAgreementTerm.remark"
                type="textarea"
                :rows="2"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handlerSave()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <!--编辑公式-->
    <formulaConfig
      ref="refFormulaConfig"
      :display-condition="displayCondition"
      @success="formulaConfigSuccess"
    />
  </div>
</template>
<script>
import formulaConfig from './formulaConfig'
export default {
  components: {
    formulaConfig
  },
  data() {
    return {
      span: 24,
      rules: {
        condition: [
          {
            required: true,
            type: 'string',
            message: '请配置条件公式',
            trigger: 'change'
          }
        ],
        result: [
          {
            required: true,
            type: 'string',
            message: '请配置结果公式',
            trigger: 'change'
          }
        ]
      },
      requestFormRebateAgreementTerm: {
        agreementTermDescription: '',
        agreementTermFormula: '',
        agreementTermFormulaHtml: '',
        rebateCalculateDescription: '',
        rebateCalculateFormula: '',
        rebateCalculateFormulaHtml: ''
      },
      dialogFormulaConfigVisible: false,
      displayCondition: false,
      showAddDialog: false,
      title: '',
      agreementTermFormulaStr: '',
      rebateCalculateFormulaStr: ''
    }
  },
  mounted() {
  },
  methods: {
    init(dialogTitle, rowData) {
      this.title = dialogTitle
      if (rowData !== null) {
        this.requestFormRebateAgreementTerm = rowData
      }
      this.showAddDialog = true
    },
    cancle() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.requestFormRebateAgreementTerm = {}
      this.showAddDialog = false
    },
    handlerSave() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 保存
          if (this.requestFormRebateAgreementTerm.id === null || this.requestFormRebateAgreementTerm.id === undefined) {
            this.$emit('add', JSON.parse(JSON.stringify(this.requestFormRebateAgreementTerm)))
          } else {
            this.$emit('update', JSON.parse(JSON.stringify(this.requestFormRebateAgreementTerm)))
          }

          this.cancle()
        }
      })
    },
    handelEditCondition() {
      this.displayCondition = true
      this.$refs.refFormulaConfig.init('条件配置', this.requestFormRebateAgreementTerm.agreementTermFormulaHtml)
    },
    handelEditResult() {
      this.$refs.refFormulaConfig.init('结果配置', this.requestFormRebateAgreementTerm.rebateCalculateFormulaHtml)
      this.displayCondition = false
    },
    formulaConfigSuccess(val) {
      if (val.length <= 0) { return }
      this.formulaStr = val
      const formulas = val.split('|')
      if (this.displayCondition) {
        this.agreementTermFormulaStr = val
        this.requestFormRebateAgreementTerm.agreementTermDescription = formulas[0]
        this.requestFormRebateAgreementTerm.agreementTermFormula = formulas[1]
        this.requestFormRebateAgreementTerm.agreementTermFormulaHtml = formulas[2]
      } else {
        this.rebateCalculateFormulaStr = val
        this.requestFormRebateAgreementTerm.rebateCalculateDescription = formulas[0]
        this.requestFormRebateAgreementTerm.rebateCalculateFormula = formulas[1]
        this.requestFormRebateAgreementTerm.rebateCalculateFormulaHtml = formulas[2]
      }
    },
    handelClearCondition() {
      this.requestFormRebateAgreementTerm.agreementTermDescription = ''
      this.requestFormRebateAgreementTerm.agreementTermFormula = ''
      this.requestFormRebateAgreementTerm.agreementTermFormulaHtml = ''
    },
    handelClearResult() {
      this.requestFormRebateAgreementTerm.rebateCalculateDescription = ''
      this.requestFormRebateAgreementTerm.rebateCalculateFormula = ''
      this.requestFormRebateAgreementTerm.rebateCalculateFormulaHtml = ''
    }
  }
}
</script>
