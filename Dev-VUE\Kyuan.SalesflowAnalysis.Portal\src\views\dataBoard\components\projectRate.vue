<!--项目达成情况-->
<template>
  <div class="panel-group">
    <el-card style="height: 310px;">
      <div slot="header" class="clearfix cardHeader">
        <span>项目达成情况</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleMore">详情</el-button>
      </div>
      <div ref="projectChart" style="width: 100%; height: 220px;" />
    </el-card>
    <!--弹出dialog:详情页-->
    <el-dialog append-to-body title="项目达成情况查看" width="60%" :close-on-click-modal="false" :visible="showDialog" @close="cancleDialog()">
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="8">
            <el-input
              v-model="filter.projectName"
              clearable
              placeholder="项目名称"
              class="filter-item"
              @input="$forceUpdate()"
            />
          </el-col>
          <el-col :span="4">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-search"
              @click="search"
            >
              查询
            </el-button>
          </el-col>
        </el-row>
        <el-row type="flex" :gutter="10" justify="end">
          <el-col :span="3.5">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-download"
              @click="handleExport"
            >
              导出
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table
              :data="queryList"
              stripe
              fit
              border
              highlight-current-row
              style="width: 100%"
              :header-cell-class-name="'tableStyle'"
            >
              <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
              <el-table-column label="项目名称" min-width="150" header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.projectName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="预计收入" width="120" header-align="center" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.estimatedIncome | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column label="实际支出" width="120" prop="storesCount" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.actualInvestment | toMoney }}</span>
                </template>
              </el-table-column>
              <el-table-column label="实际收入" width="120" prop="storesCount" align="right">
                <template slot-scope="{ row }">
                  <span>{{ row.actualIncome | toMoney }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleDialog()">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import echarts from 'echarts'
import DashboardService from '@/api/dashboard'

export default {
  data() {
    return {
      filter: {
        projectName: '',
        topList: []
      },
      projectData: [],
      projectedTotalIncomeData: [], // 预计收入
      actualTotalIncomeData: [], // 实际收入
      actualTotalInvestmentData: [], // 支出
      showDialog: false,
      dataList: [],
      queryList: [],
      topCount: 1
    }
  },
  methods: {
    initPage(param, count) {
      this.filter = param
      this.topCount = count
      this.queryProjectAchieved()
    },
    queryProjectAchieved() {
      DashboardService.QueryProjectAchieved(this.filter)
        .then((res) => {
          this.dataList = res
          this.projectData = []
          this.projectedTotalIncomeData = []
          this.actualTotalIncomeData = []// 实际收入
          this.actualTotalInvestmentData = [] // 支出

          this.topList = res.slice(0, this.topCount)

          this.topList.forEach(element => {
            this.projectData.push(element.projectName)
            this.projectedTotalIncomeData.push(element.estimatedIncome)
            this.actualTotalIncomeData.push(element.actualIncome)
            this.actualTotalInvestmentData.push(this.changeSign(element.actualInvestment))
          })
          this.initChart()
        })
        .catch(() => {

        })
    },
    initChart() {
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            // params 是一个数组，数组中包含每个系列的数据信息
            let result = `${params[0].name}<br/>`
            params.forEach(function(item) {
              // item 是每一个系列的数据
              const seriesName = item.seriesName // 系列名称
              const value = Math.abs(item.value) // 数据值
              const marker = item.marker // 标志图形
              result += `${marker}${seriesName}: ${value.toFixed(2)}<br/>`
            })
            return result
          }
        },
        legend: {},
        grid: {
          top: '20%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: this.projectData
        },
        series: [
          {
            name: '预计收入',
            type: 'bar',
            data: this.projectedTotalIncomeData
          },
          {
            name: '实际支出',
            type: 'bar',
            data: this.actualTotalInvestmentData
          },
          {
            name: '实际收入',
            type: 'bar',
            data: this.actualTotalIncomeData
          }
        ]
      }
      const myChartExpenditure = echarts.init(this.$refs.projectChart)
      myChartExpenditure.setOption(option)
    },
    cancleDialog() {
      this.showDialog = false
      this.queryList = []
      this.filter.projectName = ''
    },
    search() {
      this.queryList = this.dataList.filter(item => {
        return item.projectName.includes(this.filter.projectName)
      })
    },
    handleExport() {
      DashboardService.ExportProjectAchieved(this.filter)
        .then((result) => {
          const fileDownload = require('js-file-download')
          var filename = '项目达成情况.xlsx'

          fileDownload(result.data, filename)
        })
        .catch(() => {

        })
    },
    indexMethod(index) {
      return (
        index + 1
      )
    },
    handleMore() {
      this.showDialog = true
      this.queryList = this.dataList
    },
    changeSign(value) {
      return -1 * value
    }
  }
}
</script>

<style lang="scss" scoped>
.cardHeader {
      height: 5px;
    }
</style>
