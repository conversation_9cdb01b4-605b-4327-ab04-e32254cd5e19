import HttpApi from './libs/api.request'

const controller = 'MasterData'

const api = new HttpApi(controller)

export default {
  NewGuid() {
    return api.get('NewGuid')
  },
  GetEnumInfos(params) {
    return api.get('GetEnumInfos', params)
  },
  GetDicts(params) {
    return api.get('GetDictsByParent', params)
  },
  QueryDict(params) {
    return api.get('QueryDict', params)
  },
  DeleteDict(params) {
    return api.post('DeleteDict', params)
  },
  AddDict(params) {
    return api.post('AddDict', params)
  },
  UpdateDict(params) {
    return api.post('UpdateDict', params)
  },
  GetDict(params) {
    return api.get('GetDict', params)
  },
  GetAllProvince(params) {
    return api.get('QueryAllProvinces', params)
  },
  GetEnumInfoList(params) {
    return api.get('GetEnumInfoList', params)
  }
}
