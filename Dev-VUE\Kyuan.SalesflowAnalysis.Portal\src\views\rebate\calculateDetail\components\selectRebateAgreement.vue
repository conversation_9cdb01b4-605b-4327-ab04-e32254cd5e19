<!--选择协议-->
<template>
  <el-dialog custom-class="el-dialog-s" title="选择协议" width="90%" append-to-body :close-on-click-modal="false" :visible="selectRebateAgreementVisible" @close="handleCancle()">
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filiter-container">
        <el-col :span="span">
          <el-input
            v-model="listQuery.code"
            clearable
            placeholder="协议编号"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.name"
            clearable
            placeholder="协议名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.rebateReceiverName"
            clearable
            placeholder="返利接收方"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.departmentId"
            value-key="value"
            :loading="deptLoading"
            class="filter-item"
            placeholder="部门"
            clearable
          >
            <el-option
              v-for="item in deptList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProductAndSpec"
            v-model="manufacturerProductAndSpecId"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            placeholder="厂商 / 产品 / 规格"
            style="width:100%"
            clearable
            class="filter-item"
            :props="{ multiple: true, checkStrictly: false ,expandTrigger: 'hover', emitPath: true }"
            @change="handleProductChange"
          />
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="listQuery.timeRange"
            style="width: 100%;padding: 0"
            clearable
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
              min-width="380px"
            />
            <el-table-column label="协议编号" align="center" sortable="custom" min-width="80px" prop="code">
              <template slot-scope="{ row }">
                <span>{{ row.code }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="协议名称"
              sortable="custom"
              min-width="150px"
              header-align="center"
              align="left"
              prop="Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="返利接收方" align="center" sortable="custom" min-width="100px" prop="RebateReceiver.Name">
              <template slot-scope="{ row }">
                <span>{{ row.rebateReceiverName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="部门" align="center" sortable="custom" min-width="100px" prop="Department.Name">
              <template slot-scope="{ row }">
                <span>{{ row.departmentName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="品规"
              min-width="150px"
              align="center"
              prop="ProductSpecDesc"
            >
              <template slot-scope="{ row }">
                <span>{{ row.productSpecDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="起始日期" align="center" sortable="custom" min-width="100px" prop="startDate">
              <template slot-scope="{ row }">
                <span v-if="row.startDate">{{ row.startDate |parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="截止日期" align="center" sortable="custom" min-width="100px" prop="endDate">
              <template slot-scope="{ row }">
                <span v-if="row.endDate">{{ row.endDate |parseTime('{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="120" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <i class="el-icon-circle-check eltablei" title="选择" @click="handleCheck(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
  </el-dialog>
</template>
<script>
import AgreementService from '@/api/agreement'
import MaintenanceService from '@/api/maintenance'
import ProductService from '@/api/product'
import Pagination from '@/components/Pagination'

export default {
  components: {
    Pagination
  },
  data() {
    return {
      span: 3,
      listLoading: true,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      list: [],
      deptLoading: false,
      deptList: [],
      productAndSpecLoading: false,
      productAndSpecList: [],
      manufacturerProductAndSpecId: null,
      selectRebateAgreementVisible: false
    }
  },
  methods: {
    initPage() {
      this.selectRebateAgreementVisible = true
      this.handleFilter()
      this.initDept()
      this.initProductAndSpec()
    },
    initDept() {
      this.deptLoading = true
      MaintenanceService.QueryDept()
        .then((result) => {
          this.deptLoading = false
          this.deptList = result
        })
        .catch(() => {
          this.deptLoading = false
        })
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch(() => {
          this.productAndSpecLoading = false
        })
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList() {
      this.listLoading = true
      AgreementService.QuerySupplyRebateAgreement(this.listQuery).then(res => {
        this.listLoading = false
        this.list = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(res => {})
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleCheck(row) {
      this.$emit('success', row)
    },
    close() {
      this.selectRebateAgreementVisible = false
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
    },
    handleProductChange() {},
    handleCancle() {
      this.selectRebateAgreementVisible = false
    }
  }
}
</script>
<style scoped>

</style>
