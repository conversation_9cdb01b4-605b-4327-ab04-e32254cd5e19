<template>
  <div>
    <el-dialog title="编辑关联药店" width="80%" :close-on-click-modal="false" :visible="true" @close="cancle()">
      <el-form ref="dataForm" :rules="rules" :model="tempFormModel" label-position="right" label-width="130px" class="el-dialogform">
        <el-row :gutter="10" type="flex" class="filter-container">
          <el-col :span="span">
            <el-form-item label="月份" prop="cycleYearMonthSplicing">
              <el-date-picker
                v-model="tempFormModel.cycleYearMonthSplicing"
                type="month"
                style="width: 100%"
                placeholder="选择月份"
                format="yyyy-MM"
                value-format="yyyy-MM"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="医院名称" prop="hospitalName">
              <el-col :span="20" style="padding-left: 0px; padding-right: 0px;">
                <el-input
                  v-model="tempFormModel.hospitalName"
                  :readonly="true"
                  placeholder="医院名称"
                />
              </el-col>
              <el-col :span="4">
                <el-button
                  icon="el-icon-search"
                  style="width: 100%; "
                  type="primary"
                  title="选择医院"
                  :disabled="viewModel"
                  @click="handleSelectHospital"
                />
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="医院编码" prop="hospitalCode">
              <el-input
                v-model="tempFormModel.hospitalCode"
                placeholder="医院编码"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="省份/城市" prop="provinceCityId">
              <el-cascader
                ref="refProvinceCity"
                v-model="tempFormModel.provinceCityId"
                :loading="provinceCityLoading"
                :options="provinceCityList"
                :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
                placeholder="省份 / 城市"
                disabled
                style="width:100%"
                @change="closeProvinceCityCascader"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="药店名称" prop="hospitalName">
              <el-col :span="20" style="padding-left: 0px; padding-right: 0px;">
                <el-input
                  v-model="tempFormModel.drugStoreName"
                  :readonly="true"
                  placeholder="药店名称"
                />
              </el-col>
              <el-col :span="4">
                <el-button
                  icon="el-icon-search"
                  style="width: 100%; "
                  type="primary"
                  title="选择药店"
                  :disabled="viewModel"
                  @click="handleSelectDrug"
                />
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="药店编码" prop="drugStoreCode">
              <el-input
                v-model="tempFormModel.drugStoreCode"
                placeholder="药店编码"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="产品/规格" prop="productAndSpecId">
              <el-cascader
                ref="refProductAndSpec"
                v-model="tempFormModel.productAndSpecId"
                :loading="productAndSpecLoading"
                :options="productAndSpecList"
                placeholder="产品/规格"
                style="width:100%"
                clearable
                class="filter-item"
                :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
                @change="productChange"
              /></el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="距离" prop="distance">
              <el-input
                v-model="tempFormModel.distance"
                placeholder="距离"
              />
            </el-form-item>
          </el-col>
          <el-col :span="span">
            <el-form-item label="关联类型" prop="hospitalStoreTypeId">
              <el-select
                v-model="tempFormModel.hospitalStoreTypeId"
                style="width: 100%"
                class="filter-item"
                placeholder="关联类型"
              >
                <el-option
                  v-for="item in dosageFormList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          v-if="!viewModel"
          :loading="btnSaveLoading"
          type="primary"
          icon="el-icon-check"
          @click="handlerSave()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
    <!--选择医院-->
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="选择医院"
      :close-on-click-modal="false"
      :visible="dialogReceiverVisible"
      width="70%"
      class="popup-search"
      @close="closeReceiverDialog"
    >
      <SelectReceiver ref="refSelectReceiver" :show-dialog="dialogReceiverVisible" :receiver-one-level-codes="receiverOneLevelCodes" @success="selectReceiverSuccess" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeReceiverDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <!--选择药店-->
    <el-dialog
      custom-class="el-dialog-s"
      append-to-body
      title="选择药店"
      :close-on-click-modal="false"
      :visible="dialogDrugReceiverVisible"
      width="70%"
      class="popup-search"
      @close="closeDrugReceiverDialog"
    >
      <SelectReceiver ref="refSelectDrugReceiver" :show-dialog="dialogDrugReceiverVisible" :receiver-one-level-codes="receiverOneLevelCodes" @success="selectDrugReceiverSuccess" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeDrugReceiverDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import LocationService from '@/api/location'
import SelectReceiver from './selectHospitalReceiver'
import HospitalStoreService from '@/api/hospitalStore'
import ProductService from '@/api/product'
import MasterDataService from '@/api/masterData'
export default {
  components: {
    SelectReceiver
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    viewModel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validateSpec = (rule, value, callback) => {
      if (!this.tempFormModel.productAndSpecId.length) {
        callback(new Error('请选择产品/规格'))
      } else {
        callback()
      }
    }
    return {
      span: 12,
      rules: {
        cycleYearMonthSplicing: [
          {
            required: true,
            type: 'string',
            message: '请选择月份',
            trigger: 'change'
          }
        ],
        hospitalName: [
          {
            required: true,
            type: 'string',
            message: '请选择医院名称',
            trigger: 'change'
          }
        ],
        productAndSpecId: [
          { type: 'array', required: true, validator: validateSpec, trigger: 'change' }
        ]
      },
      dialogReceiverVisible: false,
      dialogDrugReceiverVisible: false,
      distributorTypes: [],
      receiverOneLevelCodes: [],
      tempFormModel: { enumType: null, provinceId: null },
      provinceCityLoading: false,
      provinceCityList: [],
      productAndSpecLoading: false,
      productAndSpecList: [],
      dosageFormList: []
    }
  },
  watch: {
    id(val) {
      this.tempFormModel.id = val
    }
  },
  mounted() {
  },
  created() {
    this.initProvinceCity()
    this.initProductAndSpec()
    this.initDict()
    this.init()
  },
  methods: {
    initProvinceCity() {
      this.provinceCityLoading = true
      LocationService.QueryProvinceCityCascader()
        .then((result) => {
          this.provinceCityList = result
          this.provinceCityCLoading = false
        })
        .catch((error) => {
          this.provinceCityLoading = false
          console.log(error)
        })
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      ProductService.QueryProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    initDict() {
      MasterDataService.GetDicts({ parentCode: 'HospitalStoreType' }).then(res => {
        this.dosageFormList = res.data
      })
    },
    init() {
      if (!this.id) {
        this.clear()
        return
      }
      this.get(this.id)
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.tempFormModel = {}
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    },
    handleSelectHospital() {
      this.receiverOneLevelCodes = ['Medical_Institution']
      this.dialogReceiverVisible = true
    },
    selectReceiverSuccess(val) {
      this.tempFormModel.hospitalId = val.id
      this.tempFormModel.hospitalName = val.name
      this.tempFormModel.hospitalCode = val.code
      this.$refs.refSelectReceiver.clear()
      this.dialogReceiverVisible = false
    },
    closeReceiverDialog() {
      this.$refs.refSelectReceiver.clear()
      this.dialogReceiverVisible = false
    },
    get(id) {
      this.btnSaveLoading = true
      HospitalStoreService.GetHospitalStore({ id: id }).then(result => {
        this.tempFormModel = result.data
        this.tempFormModel.provinceCityId = [result.data.Hospital.provinceId, result.data.Hospital.cityId]
        this.tempFormModel.productAndSpecId = [result.data.productId, result.data.productSpecId]
        this.tempFormModel.timeRange = [new Date(result.data.startDate), new Date(result.data.endDate)]
        this.btnSaveLoading = false
      }).catch(error => {
        this.btnSaveLoading = false
        console.log(error)
      })
    },
    closeProvinceCityCascader() {
      this.$refs.refProvinceCity.dropDownVisible = false
    },
    productChange(value) {
      if (!value || value.length === 0) {
        this.tempData.tempFormModel.commonName = ''
        return
      } else {
        const para = { id: value[0] }
        ProductService.GetProduct(para)
          .then((result) => {
            this.tempData.tempFormModel.commonName = result.data.commonName
            this.$forceUpdate()
          })
          .catch((error) => {
            console.log(error)
          })
      }
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    handleSelectDrug() {
      this.receiverOneLevelCodes = ['Retail']
      this.dialogDrugReceiverVisible = true
    },
    selectDrugReceiverSuccess(val) {
      this.tempFormModel.drugStoreId = val.id
      this.tempFormModel.drugStoreName = val.name
      this.tempFormModel.drugStoreCode = val.code
      this.$refs.refSelectDrugReceiver.clear()
      this.dialogDrugReceiverVisible = false
    },
    closeDrugReceiverDialog() {
      this.$refs.refSelectDrugReceiver.clear()
      this.dialogDrugReceiverVisible = false
    },
    handlerSave() {
      // 保存
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          HospitalStoreService.UpdateHospitalStore(this.tempFormModel)
            .then((result) => {
              if (result.succeed) {
                this.showAddDialog = false
                this.$notice.message('修改成功', 'success')
                this.$emit('refresh')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
            })
        }
      })
    }
  }
}
</script>
