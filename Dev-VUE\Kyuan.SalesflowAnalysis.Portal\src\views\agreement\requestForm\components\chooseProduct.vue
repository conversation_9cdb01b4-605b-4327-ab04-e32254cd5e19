<template>
  <div>
    <el-dialog :title="title" width="70%" :close-on-click-modal="false" :visible="showAddDialog" @close="cancle()">
      <el-form ref="dataForm" :rules="rules" :model="tempFormModel" label-position="right" label-width="120px">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix" style="height: 4px; font-size: 12px;">
            <span>添加产品</span>
          </div>
          <div>
            <el-row :gutter="10" type="flex" style="flex-wrap:wrap">
              <el-col :span="12">
                <el-form-item label="政策模板">
                  <el-input
                    v-model="tempFormModel.agreementPolicyTypeName"
                    disabled
                    placeholder="政策模板"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="厂商/产品/规格" prop="manufacturerProductAndSpecId">
                  <el-cascader
                    ref="refProductAndSpec"
                    v-model="manufacturerProductAndSpecId"
                    :loading="productAndSpecLoading"
                    :options="productAndSpecList"
                    placeholder="厂商/产品/规格"
                    style="width:100%"
                    clearable
                    class="filter-item"
                    :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
                    @change="productChange"
                  /></el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="供货价" prop="SupplyPrice">
                  <el-input
                    v-model="tempFormModel.supplyPrice"
                    clearable
                    placeholder="供货价"
                    maxlength="14"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="建议零售价" prop="retailPrice">
                  <el-input
                    v-model="tempFormModel.retailPrice"
                    clearable
                    placeholder="建议零售价"
                    maxlength="14"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="协议数量" prop="minimumPurchaseQuantity">
                  <el-input
                    v-model="tempFormModel.minimumPurchaseQuantity"
                    clearable
                    placeholder="协议数量"
                    maxlength="14"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="销售折扣" prop="rebateUnitPrice">
                  <el-input
                    v-model="tempFormModel.rebateUnitPrice"
                    clearable
                    placeholder="补偿单价"
                    maxlength="14"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="返利比例" prop="rebateProportion">
                  <el-input
                    v-model="tempFormModel.rebateProportion"
                    clearable
                    placeholder="返利比例"
                    maxlength="14"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="税率(%)" prop="taxRate">
                  <el-input
                    v-model="tempFormModel.taxRate"
                    clearable
                    placeholder="税率(%)"
                    maxlength="14"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-card class="box-card" shadow="never" style="margin-top: 10px;">
          <div slot="header" class="clearfix" style="height: 4px; font-size: 12px;">
            <span>指标信息</span>
          </div>
          <div>
            <el-row :gutter="10" type="flex" style="flex-wrap:wrap">
              <el-col :span="12">
                <el-form-item label="指标数量" prop="quotaQuantity">
                  <el-input
                    v-model="tempFormModel.quotaQuantity"
                    clearable
                    placeholder="指标数量"
                    maxlength="14"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="一季度指标数量" prop="q1Quota">
                  <el-input
                    v-model="tempFormModel.q1Quota"
                    clearable
                    placeholder="一季度指标数量"
                    maxlength="14"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="二季度指标数量" prop="q2Quota">
                  <el-input
                    v-model="tempFormModel.q2Quota"
                    clearable
                    placeholder="二季度指标数量"
                    maxlength="14"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="三季度指标数量" prop="q3Quota">
                  <el-input
                    v-model="tempFormModel.q3Quota"
                    clearable
                    placeholder="三季度指标数量"
                    maxlength="14"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="四季度指标数量" prop="q4Quota">
                  <el-input
                    v-model="tempFormModel.q4Quota"
                    clearable
                    placeholder="四季度指标数量"
                    maxlength="14"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-card class="box-card" shadow="never" style="margin-top: 10px;">
          <div slot="header" class="clearfix" style="height: 4px; font-size: 12px; ">
            <span>满赠信息</span>
          </div>
          <div>
            <el-row :gutter="10" type="flex" style="flex-wrap:wrap">
              <el-col :span="12">
                <el-form-item label="满赠标准" prop="giftStandard">
                  <el-input
                    v-model="tempFormModel.giftStandard"
                    clearable
                    placeholder="满赠标准"
                    maxlength="14"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="赠送数量" prop="giftQuantity">
                  <el-input
                    v-model="tempFormModel.giftQuantity"
                    clearable
                    placeholder="赠送数量"
                    maxlength="14"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="满赠厂商/产品/规格" prop="giftManufacturerProductAndSpecId">
                  <el-cascader
                    ref="refGiftProductAndSpec"
                    v-model="giftManufacturerProductAndSpecId"
                    style="width: 100%"
                    :loading="productAndSpecLoading"
                    :options="productAndSpecList"
                    placeholder="满赠厂商/产品/规格"
                    clearable
                    class="filter-item"
                    :props="{ checkStrictly: false ,expandTrigger: 'hover' }"
                    @change="giftProductChange"
                  /></el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="满赠是否参与计算" prop="giftNeedCompute">
                  <el-switch v-model="tempFormModel.giftNeedCompute" class="drawer-switch" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="销售组数量" prop="salesGroupQuantity">
                  <el-input
                    v-model="tempFormModel.salesGroupQuantity"
                    clearable
                    placeholder="销售组数量"
                    maxlength="14"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="销售组返利金额" prop="salesGroupRebate">
                  <el-input
                    v-model="tempFormModel.salesGroupRebate"
                    clearable
                    placeholder="销售组返利金额"
                    maxlength="14"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="save()"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>

import ProductService from '@/api/product'

export default {
  name: '',
  components: {
  },
  data() {
    const validateSpec = (rule, value, callback) => {
      if (!this.manufacturerProductAndSpecId.length) {
        callback(new Error('请选择厂商/产品/规格'))
      } else {
        callback()
      }
    }
    return {
      rules: {
        supplyPrice: [
          { pattern: /((^[1-9]\d*))(\.\d{0,4}){0,1}$/, message: '仅支持正数，最多4位小数', trigger: 'change' }
        ],
        manufacturerProductAndSpecId: [
          { type: 'array', required: true, validator: validateSpec, trigger: 'change' }
        ],
        minimumPurchaseQuantity: [
          { pattern: /((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/, message: '应为大于等于0的数字，支持两位小数', trigger: 'change' }
        ],
        retailPrice: [
          { pattern: /((^[1-9]\d*))(\.\d{0,4}){0,1}$/, message: '仅支持正数，最多4位小数', trigger: 'change' }
        ],
        rebateUnitPrice: [
          { pattern: /((^[1-9]\d*))(\.\d{0,4}){0,1}$/, message: '仅支持正数，最多4位小数', trigger: 'change' }
        ],
        rebateProportion: [
          { pattern: /((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/, message: '应为大于等于0的数字，支持两位小数', trigger: 'change' }
        ],
        taxRate: [
          { pattern: /((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/, message: '应为大于等于0的数字，支持两位小数', trigger: 'change' }
        ],
        quotaQuantity: [
          { pattern: /((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/, message: '应为大于等于0的数字，支持两位小数', trigger: 'change' }
        ],
        q1Quota: [
          { pattern: /((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/, message: '应为大于等于0的数字，支持两位小数', trigger: 'change' }
        ],
        q2Quota: [
          { pattern: /((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/, message: '应为大于等于0的数字，支持两位小数', trigger: 'change' }
        ],
        q3Quota: [
          { pattern: /((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/, message: '应为大于等于0的数字，支持两位小数', trigger: 'change' }
        ],
        q4Quota: [
          { pattern: /((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/, message: '应为大于等于0的数字，支持两位小数', trigger: 'change' }
        ]
      },
      rebateAgreementProductId: null,
      tempFormModel: {},
      showAddDialog: false,
      productAndSpecLoading: false,
      productAndSpecList: [],
      manufacturerProductAndSpecId: [],
      giftManufacturerProductAndSpecId: [],
      title: ''
    }
  },
  created() {
    this.initManufacturerAndProductAndSpec()
  },
  methods: {
    init(rebateAgreementProduct) {
      this.showAddDialog = true
      if (rebateAgreementProduct === null || rebateAgreementProduct === undefined) {
        this.title = '新增品规'
        this.tempFormModel = {}
      } else if (
        rebateAgreementProduct.id === undefined || rebateAgreementProduct.id === null) {
        this.title = '新增品规'
        this.tempFormModel = rebateAgreementProduct
      } else {
        this.title = '修改品规'
        this.rebateAgreementProductId = rebateAgreementProduct.id
        this.getRebateAgreementProduct(rebateAgreementProduct.id)
      }
    },
    initManufacturerAndProductAndSpec() {
      this.productAndSpecLoading = true

      ProductService.QueryManufacturerAndProductAndSpecCascader()
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    productChange(value) {
      if (!value || value.length === 0) {
        this.tempFormModel.productSpecId = null
        this.tempFormModel.productNameCn = ''
        this.tempFormModel.spec = ''
        this.tempFormModel.manufacturerName = ''
        return
      } else {
        this.tempFormModel.productSpecId = value[2]
        const m = this.productAndSpecList.find(item => { return item.value === value[0] })
        this.tempFormModel.manufacturerName = m.label
        const p = m.children.find(item => { return item.value === value[1] })
        this.tempFormModel.productNameCn = p.label
        const s = p.children.find(item => { return item.value === value[2] })
        this.tempFormModel.spec = s.label
      }
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    giftProductChange(value) {
      if (!value || value.length === 0) {
        this.tempFormModel.giftProductSpecId = null
        return
      }
      this.tempFormModel.giftProductSpecId = value[2]
      this.$refs.refGiftProductAndSpec.dropDownVisible = false
    },
    getRebateAgreementProduct(id) {

    },
    save() {
      if (this.tempFormModel.supplyPrice !== undefined) {
        this.tempFormModel.supplyPrice = parseFloat(this.tempFormModel.supplyPrice)
      }
      if (this.tempFormModel.minimumPurchaseQuantity !== undefined) {
        this.tempFormModel.minimumPurchaseQuantity = parseFloat(this.tempFormModel.minimumPurchaseQuantity)
      }
      if (this.tempFormModel.rebateProportion !== undefined) {
        this.tempFormModel.rebateProportion = parseFloat(this.tempFormModel.rebateProportion)
      }
      if (this.tempFormModel.quotaQuantity !== undefined) {
        this.tempFormModel.quotaQuantity = parseFloat(this.tempFormModel.quotaQuantity)
      }
      if (this.tempFormModel.q1Quota !== undefined) {
        this.tempFormModel.q1Quota = parseFloat(this.tempFormModel.q1Quota)
      }
      if (this.tempFormModel.q2Quota !== undefined) {
        this.tempFormModel.q2Quota = parseFloat(this.tempFormModel.q2Quota)
      }
      if (this.tempFormModel.q3Quota !== undefined) {
        this.tempFormModel.q3Quota = parseFloat(this.tempFormModel.q3Quota)
      }
      if (this.tempFormModel.q4Quota !== undefined) {
        this.tempFormModel.q4Quota = parseFloat(this.tempFormModel.q4Quota)
      }
      if (this.tempFormModel.retailPrice !== undefined) {
        this.tempFormModel.retailPrice = parseFloat(this.tempFormModel.retailPrice)
      }
      if (this.tempFormModel.taxRate !== undefined) {
        this.tempFormModel.taxRate = parseFloat(this.tempFormModel.taxRate)
      }
      if (this.tempFormModel.giftStandard !== undefined) {
        this.tempFormModel.giftStandard = parseFloat(this.tempFormModel.giftStandard)
      }
      if (this.tempFormModel.giftQuantity !== undefined) {
        this.tempFormModel.giftQuantity = parseFloat(this.tempFormModel.giftQuantity)
      }
      if (this.tempFormModel.salesGroupQuantity !== undefined) {
        this.tempFormModel.salesGroupQuantity = parseFloat(this.tempFormModel.salesGroupQuantity)
      }
      if (this.tempFormModel.salesGroupRebate !== undefined) {
        this.tempFormModel.salesGroupRebate = parseFloat(this.tempFormModel.salesGroupRebate)
      }
      if (this.tempFormModel.rebateUnitPrice !== undefined) {
        this.tempFormModel.rebateUnitPrice = parseFloat(this.tempFormModel.rebateUnitPrice)
      }
      if (this.tempFormModel.id === null || this.tempFormModel.id === undefined) {
        this.$emit('add', JSON.parse(JSON.stringify(this.tempFormModel)))
      } else {
        this.$emit('update', JSON.parse(JSON.stringify(this.tempFormModel)))
      }
      this.clear()
    },
    cancle() {
      this.clear()
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.showAddDialog = false
      this.tempFormModel = {}
      this.manufacturerProductAndSpecId = null
      this.giftManufacturerProductAndSpecId = null
    }
  }
}
</script>
