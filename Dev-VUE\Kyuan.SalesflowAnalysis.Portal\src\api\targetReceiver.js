import HttpApi from './libs/api.request'

const controller = 'TargetReceiver'

const api = new HttpApi(controller)

export default {
  QueryTargetReceiver(params) {
    return api.get('QueryTargetReceiver', params)
  },
  GetTargetReceiver(params) {
    return api.get('GetTargetReceiver', params)
  },
  AddTargetReceiver(params) {
    return api.post('AddTargetReceiver', params)
  },
  UpdateTargetReceiver(params) {
    return api.post('UpdateTargetReceiver', params)
  },
  DeleteTargetReceiver(params) {
    return api.post('DeleteTargetReceiver', params)
  },
  GetTargetReceiverExportColumn(params) {
    return api.get('GetTargetReceiverExportColumn', params)
  },
  ExportTargetReceivers(params) {
    return api.post('ExportTargetReceivers',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  QueryImportTargetReceiverMasterTemp(params) {
    return api.get('QueryImportTargetReceiverMasterTemp', params)
  },
  QueryImportTargetReceiverError(params) {
    return api.get('QueryImportTargetReceiverError', params)
  },
  ExportImportTargetReceiverError(params) {
    return api.post('ExportImportTargetReceiverError',
      {
        data: params,
        responseType: 'arraybuffer'
      }
    )
  },
  QueryCustomerCategorySelect() {
    return api.get('QueryCustomerCategorySelect')
  },
  CheckDepartmentOfReceiverRange(params) {
    return api.post('CheckDepartmentOfReceiverRange', params)
  },
  GetTargetReceiverQuota(params) {
    return api.post('GetTargetReceiverQuota', params)
  }
}
