<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" type="flex" class="filter-container">
        <el-col :span="span">
          <el-input
            v-model="listQuery.code"
            clearable
            placeholder="模板编码"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.name"
            clearable
            placeholder="模板名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.enumStatus"
            class="filter-item"
            placeholder="状态"
            clearable
          >
            <el-option
              v-for="item in agreementPolicyTemplateStatusList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-button
            v-if="$isPermitted($store.getters.user, 'AgreementPolicyTemplate_Button_Query')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'AgreementPolicyTemplate_Button_Add')"
            type="primary"
            icon="el-icon-plus"
            @click="handleAddTemplate"
          >
            新增
          </el-button>
        </el-col>
        <!-- <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'AgreementPolicyTemplatee_Button_Export')"
            class="filter-item-button"
            type="primary"
            icon="el-icon-download"
          >
            导出
          </el-button>
        </el-col> -->
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="agreementPolicyTemplateList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              label="模板编码"
              sortable="custom"
              min-width="100px"
              header-align="center"
              align="left"
              prop="Code"
            >
              <template slot-scope="{ row }">
                <span>{{ row.code }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="模板名称"
              align="center"
              sortable="custom"
              min-width="140px"
              prop="Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="适用范围"
              align="center"
              sortable="custom"
              min-width="100px"
              prop="Scope"
            >
              <template slot-scope="{ row }">
                <span>{{ row.scopeTemplateTypeNames }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="状态"
              align="center"
              sortable="custom"
              min-width="100px"
              prop="EnumStatusDesc"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enumStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="120"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i
                  v-if="row.enumStatus === 1 && $isPermitted($store.getters.user, 'AgreementPolicyTemplate_Button_Edit') "
                  class="el-icon-edit-outline eltablei"
                  title="编辑"
                  @click="handleEditTemplate(row)"
                />
                <i
                  v-if="row.enumStatus === 1 && $isPermitted($store.getters.user, 'AgreementPolicyTemplate_Button_ReleaseStop')"
                  class="el-icon-circle-check eltablei"
                  title="发布"
                  @click="handleReleaseTemplate(row)"
                />
                <i
                  v-if="row.enumStatus === 2 && $isPermitted($store.getters.user, 'AgreementPolicyTemplate_Button_ReleaseStop')"
                  class="el-icon-circle-close eltablei"
                  title="停用"
                  @click="handleRevokeTemplate(row)"
                />
                <i v-if="row.enumStatus === 2"
                  class="el-icon-document eltablei"
                  title="查看"
                  @click="handleReviewDetail(row)"
                />
                <i v-if="row.enumStatus === 1 && $isPermitted($store.getters.user, 'AgreementPolicyTemplate_Button_Del')" class="el-icon-delete eltablei" title="删除" @click="handleDelete(row)" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <EditTemplate ref="refEditTemplate" @refresh="onRefresh()" />
    <TemplateView ref="refReviewTemplate" />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

import AgreementService from '@/api/agreement'
import MasterDataService from '@/api/masterData'

import EditTemplate from './components/editTemplate'
import TemplateView from './components/templateView'

export default {
  name: 'TemplateQuery',
  components: {
    Pagination,
    EditTemplate,
    TemplateView
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      agreementPolicyTemplateList: [],
      agreementPolicyTemplateStatusList: []
    }
  },
  created() {
    this.initAgreementPolicyTemplateStatus()
    this.handleFilter()
  },
  methods: {
    initAgreementPolicyTemplateStatus() {
      var param = {
        enumType: 'RebateAgreementPolicyTemplateStatus'
      }
      MasterDataService.GetEnumInfos(param)
        .then(result => {
          this.agreementPolicyTemplateStatusList = result.data.datas
          this.$forceupdate()
        })
        .catch(() => {})
    },
    handleAddTemplate() {
      this.$refs.refEditTemplate.init(null)
    },
    handleEditTemplate(row) {
      this.$refs.refEditTemplate.init(row.id)
    },
    handleReviewDetail(row) {
      this.$refs.refReviewTemplate.init(row.id)
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      this.listQuery.quotaType = 1
      AgreementService.QueryAgreementPolicyTemplate(this.listQuery)
        .then(res => {
          this.listLoading = false
          this.agreementPolicyTemplateList = res.data.datas
          this.total = res.data.recordCount
          this.listQuery.pageIndex = res.data.pageIndex
        })
        .catch(() => {
          this.listLoading = true
        })
    },
    selectUpdate() {
      this.$forceUpdate()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    onRefresh() {
      this.getList()
    },
    handleReleaseTemplate(row) {
      this.listLoading = true
      AgreementService.ReleaseRebateAgreementPolicyTemplate({ id: row.id })
        .then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('已发布政策模板', 'success')
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    handleRevokeTemplate(row) {
      this.listLoading = true
      AgreementService.RevokeRebateAgreementPolicyTemplate({ id: row.id })
        .then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('已撤销政策模板', 'success')
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    handleDelete(row) {
      this.$confirm('确定删除此模板信息吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.listLoading = true
          AgreementService.DeleteRebateAgreementPolicyTemplate(row)
            .then((result) => {
              this.listLoading = false
              if (result.succeed) {
                this.getList()
                this.$notice.message('删除成功', 'success')
              }
            })
            .catch((error) => {
              this.listLoading = false
              console.log(error)
            })
        })
        .catch((error) => {
          this.listLoading = false
          if (!error.succeed) {
            this.$notice.message('取消删除', 'info')
          }
        })
    }
  }
}
</script>
<style scoped></style>
