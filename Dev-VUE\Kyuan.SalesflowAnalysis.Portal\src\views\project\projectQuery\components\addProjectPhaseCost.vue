<template>
  <div>
    <el-dialog
      custom-class="el-dialog-s"
      :title="enumTypeValue === 1 ? '预计成本管理' : '实际成本管理'"
      width="80%"
      append-to-body
      :close-on-click-modal="false"
      :visible="editRebateProjectPhaseCostVisible"
      @close="handleCancle()"
    >
      <el-row type="flex" justify="end">
        <el-col :span="15" />
        <el-col :span="3" />
        <el-col :span="3" />
      </el-row>
      <div class="top-btn">
        <el-button
          v-if="enumTypeValue === 2"
          name="inp"
          class="filter-item"
          type="primary"
          icon="el-icon-upload2"
          @click="showImport"
        >
          批量导入
        </el-button>
        <el-button
          name="inp"
          class="filter-item"
          type="primary"
          icon="el-icon-plus"
          @click="handleAddCost"
        >
          添加成本项
        </el-button>
      </div>
      <el-row>
        <el-col :span="24">
          <el-table
            :data="rebateProjectPhaseCostList"
            max-height="400"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :header-cell-class-name="'tableStyle'"
          >
            <el-table-column fixed label="序号" type="index" align="center" />
            <el-table-column
              label="部门"
              header-align="center"
              align="left"
              width="150px"
            >
              <template slot-scope="{ row, $index }">
                <el-select
                  v-model="row.departmentId"
                  placeholder="部门"
                  style="width: 100%"
                  :class="row.dept_error ? 'error-border' : ''"
                  @change="changeDepartment($event, $index)"
                >
                  <el-option
                    v-for="dept in initData.departmentList"
                    :key="dept.key"
                    :label="dept.value"
                    :value="dept.key"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column
              v-if="enumTypeValue === 2"
              label="省份"
              header-align="center"
              align="left"
              width="150px"
            >
              <template slot-scope="{ row, $index }">
                <el-select
                  v-model="row.provinceId"
                  placeholder="省份"
                  style="width:100%"
                  :class="row.province_error ? 'error-border' : ''"
                  @change="changeProvince($event, $index)"
                >
                  <el-option
                    v-for="item in provinceList"
                    :key="item.key"
                    :label="item.value"
                    :value="item.key"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column
              v-if="enumTypeValue === 2"
              label="工号"
              header-align="center"
              align="left"
              width="150px"
            >
              <template slot-scope="{ row, $index }">
                <chooseEmployee
                  :input-value="row.jobNo"
                  :class="row.employee_error ? 'error-border' : ''"
                  show-type="input"
                  choose-type="single"
                  show-column="jobNo"
                  @change="changeEmployee($event, row, $index)"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-if="enumTypeValue === 2"
              label="员工姓名"
              header-align="center"
              align="left"
              width="150px"
            >
              <template slot-scope="{ row, $index }">
                <chooseEmployee
                  :class="row.employee_error ? 'error-border' : ''"
                  :input-value="row.employeeDisplayName"
                  show-type="input"
                  choose-type="single"
                  show-column="displayName"
                  @change="changeEmployee($event, row, $index)"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-if="enumTypeValue === 2"
              label="客户code"
              header-align="center"
              align="left"
              width="150px"
            >
              <template slot-scope="{ row, $index }">
                <chooseReceiver
                  :input-value="row.receiverCode"
                  :class="row.receiver_error ? 'error-border' : ''"
                  :distributor-types="distributorTypes"
                  title="选择客户"
                  show-type="input"
                  choose-type="single"
                  show-column="code"
                  @change="changeReceiver($event, row, $index)"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-if="enumTypeValue === 2"
              label="客户名称"
              header-align="center"
              align="left"
              min-width="200px"
            >
              <template slot-scope="{ row, $index }">
                <chooseReceiver
                  :input-value="row.receiverName"
                  title="选择客户"
                  show-type="input"
                  choose-type="single"
                  :class="row.receiver_error ? 'error-border' : ''"
                  :distributor-types="distributorTypes"
                  show-column="name"
                  @change="changeReceiver($event, row, $index)"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-if="enumTypeValue === 2"
              label="费用日期"
              header-align="center"
              align="center"
              min-width="180px"
            >
              <template slot-scope="{ row, $index }">
                <el-date-picker
                  v-model="row.costDate"
                  :class="row.costDate_error ? 'error-border' : ''"
                  style="width: 150px !important"
                  type="date"
                  :clearable="false"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期"
                  :picker-options="datePickerOptions"
                  @change="changeCostDate($event, $index)"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-if="enumTypeValue === 2"
              label="大区"
              header-align="center"
              align="left"
              width="150px"
            >
              <template slot-scope="{ row }">
                {{ row.areaName }}
              </template>
            </el-table-column>
            <el-table-column
              v-if="enumTypeValue === 2"
              label="岗位"
              header-align="center"
              align="left"
              width="150px"
            >
              <template slot-scope="{ row }">
                {{ row.stationName }}
              </template>
            </el-table-column>
            <el-table-column
              label="成本科目"
              header-align="center"
              align="left"
              min-width="180px"
            >
              <template slot-scope="{ row, $index }">
                <el-select
                  v-model="row.costTypeId"
                  :class="row.costType_error ? 'error-border' : ''"
                  placeholder="请选择成本科目"
                  @change="changeCostType($event, $index)"
                >
                  <el-option
                    v-for="item in costTypeList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column
              label="成本金额(元)"
              header-align="center"
              align="right"
              min-width="130px"
            >
              <template slot-scope="{ row, $index }">
                <el-input
                  v-model="row.amount"
                  :class="row.amount_error ? 'error-border' : ''"
                  placeholder="请输入成本金额"
                  @input="productItemChange($event, $index)"
                />
              </template>
            </el-table-column>
            <el-table-column label="备注" align="left" min-width="150px">
              <template slot-scope="{ row }">
                <el-input
                  v-model="row.remark"
                  header-align="center"
                  clearable
                  placeholder="请输入备注"
                />
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="70"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i
                  class="el-icon-delete eltablei"
                  title="删除"
                  @click="handleDeleteCost(row)"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="handleCancle()">
          关闭
        </el-button>
        <el-button type="primary" icon="el-icon-check" @click="handleSave()">
          保存
        </el-button>
      </div>
    </el-dialog>

    <importProjectPhaseCost
      ref="importProjectPhaseCost"
      @successData="successData"
    />
  </div>
</template>
<script>
import MasterDataService from '@/api/masterData'
import importProjectPhaseCost from './importProjectPhaseCost.vue'
import chooseEmployee from '@/components/ChooseEmployee'
import chooseReceiver from '@/components/ChooseReceiver'
import LocationService from '@/api/location'
import ProjectService from '@/api/project'
import moment from 'moment'
export default {
  name: '',
  components: {
    importProjectPhaseCost,
    chooseEmployee,
    chooseReceiver
  },
  props: {
  },
  data() {
    return {
      editRebateProjectPhaseCostVisible: false,
      costTypeList: [],
      totalAmount: 0,
      enumTypeValue: 0,
      rebateProjectPhaseCostList: [{
        costTypeId: null,
        amount: null,
        enumType: 0,
        remark: null,
        areaName: ''
      }],
      initData: {},
      distributorTypes: [10, 20, 30],
      provinceList: [],
      filter: {
        provinceIds: []
      }
    }
  },
  computed: {
    datePickerOptions: function() {
      var _this = this
      return {
        disabledDate(time) {
          return time.getTime() <= moment(_this.initData.startTime).add('day', -1) || time.getTime() >= moment(_this.initData.endTime).add('day', 1)
        }
      }
    }
  },
  methods: {
    init(data) {
      this.enumTypeValue = data.type
      this.rebateProjectPhaseCostList = data.costList
      this.editRebateProjectPhaseCostVisible = true
      this.initData = data
      this.initCostType()
      // 实际成本
      if (this.enumTypeValue === 2) {
        this.queryProvinceByDepartmentAndAreas()
      }
    },
    initCostType() {
      MasterDataService.GetDicts({ parentCode: 'ProjectCostType' })
        .then((result) => {
          this.costTypeList = result.data
        })
        .catch((error) => {
          console.log(error)
        })
    },
    handleSave() {
      if (this.checkData()) {
        this.$message.error('请填写必填信息')
        return false
      }
      this.rebateProjectPhaseCostList.forEach(element => {
        element.enumType = this.enumTypeValue
      })
      const data = { type: this.enumTypeValue, list: JSON.parse(JSON.stringify(this.rebateProjectPhaseCostList)) }
      this.$emit('success', data)
      this.close()
    },
    close() {
      this.editRebateProjectPhaseCostVisible = false
      this.rebateProjectPhaseCostList = []
    },
    handleCancle() {
      this.close()
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },
    handleAddCost() {
      var rebateProjectPhaseCost = {
        enumType: this.enumTypeValue,
        isAdd: true,
        dept_error: false,
        employee_error: false,
        receiver_error: false,
        costDate_error: false,
        costType_error: false,
        amount_error: false,
        province_error: false
      }
      this.rebateProjectPhaseCostList.push(rebateProjectPhaseCost)
    },
    handleDeleteCost(row) {
      this.$confirm('确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.rebateProjectPhaseCostList.indexOf(row)
        if (index !== -1) {
          this.rebateProjectPhaseCostList.splice(index, 1)
        }
      }).catch(() => {

      })
    },
    showImport() {
      this.initData.costTypeList = this.costTypeList
      this.$refs.importProjectPhaseCost.initPage(this.initData)
    },
    successData(data) {
      if (data && data.length > 0) {
        data.map(item => {
          this.rebateProjectPhaseCostList.push(item)
        })
      }
    },
    changeEmployee(val, row, rowIndex) {
      if (val) {
        row.employeeId = val.id
        row.employeeDisplayName = val.displayName
        row.jobNo = val.jobNo
        this.$set(this.rebateProjectPhaseCostList, rowIndex, row)
        this.rebateProjectPhaseCostList[rowIndex].employee_error = false
      } else {
        this.rebateProjectPhaseCostList[rowIndex].employee_error = true
      }
      this.getStation(this.rebateProjectPhaseCostList[rowIndex], rowIndex)
    },
    changeReceiver(val, row, rowIndex) {
      if (val) {
        row.receiverId = val.id
        row.receiverName = val.name
        row.receiverCode = val.code
        this.$set(this.rebateProjectPhaseCostList, rowIndex, row)
        this.rebateProjectPhaseCostList[rowIndex].receiver_error = false
      } else {
        this.rebateProjectPhaseCostList[rowIndex].receiver_error = true
      }
      this.getStation(this.rebateProjectPhaseCostList[rowIndex], rowIndex)
    },
    changeDepartment(val, rowIndex) {
      this.getAreaByDepartmentAndProvince(this.rebateProjectPhaseCostList[rowIndex], rowIndex)
      this.getStation(this.rebateProjectPhaseCostList[rowIndex], rowIndex)
      if (val) {
        this.rebateProjectPhaseCostList[rowIndex].dept_error = false
      } else {
        this.rebateProjectPhaseCostList[rowIndex].dept_error = true
      }
    },
    changeCostDate(val, rowIndex) {
      if (val && moment(this.initData.startTime) <= moment(val) && moment(this.initData.endTime) >= moment(val)) {
        this.rebateProjectPhaseCostList[rowIndex].costDate_error = false
      } else {
        this.rebateProjectPhaseCostList[rowIndex].costDate_error = true
      }
      this.getStation(this.rebateProjectPhaseCostList[rowIndex], rowIndex)
    },
    changeCostType(val, rowIndex) {
      if (val) {
        this.rebateProjectPhaseCostList[rowIndex].costType_error = false
      } else {
        this.rebateProjectPhaseCostList[rowIndex].costType_error = true
      }
    },
    productItemChange(v, rowIndex) {
      var amount = Number(v)
      const regex = /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/
      if (!regex.test(amount)) {
        this.$message.error('成本金额只能是两位小数或整数')
        this.rebateProjectPhaseCostList[rowIndex].amount_error = true
      } else {
        this.rebateProjectPhaseCostList[rowIndex].amount_error = false
      }
    },
    checkData() {
      this.rebateProjectPhaseCostList.map(item => {
        if (item.isAdd) {
          if (!item.departmentId) {
            item.dept_error = true
          }
          if (!item.employeeId && this.enumTypeValue === 2) {
            item.employee_error = true
          }
          if (!item.costDate && this.enumTypeValue === 2) {
            item.costDate_error = true
          }
          if (!item.costTypeId) {
            item.costType_error = true
          }
          if (!item.amount) {
            item.amount_error = true
          }
          if (!item.provinceId && this.enumTypeValue === 2) {
            item.province_error = true
          }
        }
        return item
      })
      return this.rebateProjectPhaseCostList.some(item => {
        return item.dept_error || item.employee_error || item.receiver_error || item.costDate_error || item.costType_error || item.amount_error || item.province_error
      })
    },
    queryProvinceByDepartmentAndAreas() {
      LocationService.QueryProvinceSelectForDataReport(this.filter)
        .then((result) => {
          this.provinceList = result
        })
        .catch((error) => {
          console.log(error)
        })
    },
    changeProvince(val, rowIndex) {
      if (val) {
        this.rebateProjectPhaseCostList[rowIndex].province_error = false
      } else {
        this.rebateProjectPhaseCostList[rowIndex].province_error = true
      }

      this.getAreaByDepartmentAndProvince(this.rebateProjectPhaseCostList[rowIndex], rowIndex)
      this.getStation(this.rebateProjectPhaseCostList[rowIndex], rowIndex)
    },
    getAreaByDepartmentAndProvince(row, index) {
      if (row.departmentId && row.provinceId) {
        var areaFilter = {
          departmentId: row.departmentId,
          provinceId: row.provinceId
        }
        ProjectService.GetAreaByDepartmentAndProvince(areaFilter).then(rel => {
          row.areaName = rel.data.name
          row.areaId = rel.data.id
          this.$set(this.rebateProjectPhaseCostList, index, row)
        }).catch((error) => {
          console.log(error)
        })
      }
    },
    getStation(row, index) {
      if (row.departmentId && row.costDate && ((row.provinceId && row.employeeId) || row.receiverId)) {
        var stationFilter = {
          deptId: row.departmentId,
          receiverId: row.receiverId,
          startDate: row.costDate,
          provinceId: row.provinceId,
          employeeId: row.employeeId
        }

        ProjectService.GetStation(stationFilter).then(rel => {
          row.stationName = rel.data.name
          row.stationId = rel.data.id
          this.$set(this.rebateProjectPhaseCostList, index, row)
        }).catch((error) => {
          console.log(error)
        })
      }
    }
  }
}
</script>
<style lang="css">
.uploadCss {
  float: right;
  color: #516e92;
  padding: 3px 0px;
  font-size: 12px;
  font-weight: 400;
  background: transparent;
  border-color: transparent;
}
.error-border .el-input__inner {
  border: 1px solid red;
  border-radius: 5px;
}

.top-btn {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  margin-bottom: 15px;
}
[name="inp"] {
  margin-right: 10px;
}

[name="inp"]:last-child {
  margin-right: 0px;
}
</style>
