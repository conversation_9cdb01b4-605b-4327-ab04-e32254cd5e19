<template>
  <div>
    <el-dialog title="编辑字典" width="80%" append-to-body :close-on-click-modal="false" :visible="showEditDialog" @close="closeEditDialog">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="180px"
        class="el-dialogform"
      >
        <div style="padding-bottom:10px">
          <el-row type="flex" class="filter-container">
            <el-col :span="span">
              <el-form-item label="字典编码" prop="code">
                <el-input
                  v-model="temp.code"
                  readonly
                  placeholder="字典编码"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="字典名称" prop="name">
                <el-input
                  v-model="temp.name"
                  readonly
                  placeholder="字典名称"
                  maxlength="200"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" justify="end" :gutter="10">
            <el-col :span="3.5">
              <el-button
                class="filter-item"
                type="primary"
                icon="el-icon-plus"
                @click="handleAddDict"
              >
                添加子项
              </el-button>
            </el-col>
          </el-row>
        </div>
        <el-row>
          <el-col :span="24">
            <el-form-item label="字典子项">
              <el-table
                :data="childDicts"
                stripe
                border
                fit
                highlight-current-row
                style="width: 100%;"
                :default-sort="{prop: 'createTime', order: 'descending'}"
                :header-cell-class-name="'tableStyle'"
                :row-class-name="handleRowClass"
              >
                <el-table-column
                  fixed
                  label="序号"
                  type="index"
                  align="center"
                />
                <el-table-column label="字典编码" min-width="150px" align="center">
                  <template slot-scope="{ row }">
                    <el-input
                      v-model="row.code"
                      clearable
                      placeholder="字典编码"
                      maxlength="50"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="字典名称" min-width="150px" align="center">
                  <template slot-scope="{ row }">
                    <el-input
                      v-model="row.name"
                      clearable
                      placeholder="字典名称"
                      maxlength="200"
                    />
                  </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70" class-name="small-padding fixed-width">
                  <template slot-scope="row">
                    <i class="el-icon-delete eltablei" @click="handleDeleteQuota(row.$index)" />
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
            <el-col />
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeEditDialog">
          关闭
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handlerSave"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import MasterDataService from '@/api/masterData'

export default {
  data() {
    return {
      span: 12,
      showEditDialog: false,
      temp: { code: '', name: '' },
      childDicts: [],
      rules: {
        code: [
          {
            required: true,
            type: 'string',
            message: '字典编码',
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            type: 'string',
            message: '字典名称',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    initPage(id) {
      MasterDataService.GetDict({ id: id }).then((result) => {
        this.temp = result.data
        this.childDicts = result.data.children
        this.showEditDialog = true
      })
    },
    handlerSave() {
      if (this.childDicts != null && this.childDicts.length > 0) {
        for (var i = 0; i < this.childDicts.length; i++) {
          if (this.childDicts[i].code === undefined || this.childDicts[i].name === undefined) {
            this.showMessage('请将字典子项填写完整', 'error')
            return false
          }
        }

        var codes = this.childDicts.map(function(item) { return item.code })
        if (new Set(codes).size !== codes.length) {
          this.showMessage('不能添加重复的字典编码', 'error')
          return false
        }

        var names = this.childDicts.map(function(item) { return item.name })
        if (new Set(names).size !== names.length) {
          this.showMessage('不能添加重复的字典名称', 'error')
          return false
        }
      }
      // 保存
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.children = this.childDicts

          MasterDataService.UpdateDict(this.temp)
            .then((result) => {
              if (result.succeed) {
                this.showEditDialog = false
                this.$emit('success')
                this.showMessage('创建成功', 'success')
              } else {
                this.ShowTip(result)
              }
            })
            .catch(() => {
            })
        }
      })
    },
    closeEditDialog() {
      this.showEditDialog = false
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleAddDict() {
      var temp = {}
      this.childDicts.push(temp)
    },
    handleDeleteQuota(index) {
      this.$confirm('确定删除此字典项吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.childDicts.splice(index, 1)
      }).catch(error => {
        if (!error.succeed) {
          this.showMessage('取消删除', 'info')
        }
      })
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    }
  }
}
</script>
<style scoped>
  .el-dialog-s {
  z-index: 12;
  }
  .el-dialog-ls {
  z-index: 13;
  }
</style>
