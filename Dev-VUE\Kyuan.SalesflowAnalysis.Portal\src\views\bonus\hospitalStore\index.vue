<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="span">
          <el-date-picker
            v-model="listQuery.yearMonthSplicing"
            class="filter-item"
            placeholder="月份"
            type="month"
            clearable
            format="yyyy-MM"
            value-format="yyyy-MM"
            @change="timeChange"
            @blur="timeChange"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.manufacturerId"
            :loading="manufacturerLoading"
            class="filter-item"
            placeholder="厂商名称"
            clearable
            @change="manufacturerChange"
          >
            <el-option
              v-for="item in manufacturerList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-cascader
            ref="refProductAndSpec"
            :key="productAndSpecKey"
            v-model="productAndSpecId"
            :loading="productAndSpecLoading"
            :options="productAndSpecList"
            placeholder="产品/规格"
            class="filter-item"
            clearable
            :props="{ checkStrictly: true ,expandTrigger: 'hover' }"
            @change="productChange"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.hospitalName"
            clearable
            placeholder="医院名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-input
            v-model="listQuery.drugStoreName"
            clearable
            placeholder="药店名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-button v-if="$isPermitted($store.getters.user, 'HospitalStore_Button_Query')" class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" :gutter="10" justify="end">
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'HospitalStore_Button_Import')"
            type="primary"
            icon="el-icon-upload2"
            @click="handleImport"
          >
            批量导入
          </el-button>
        </el-col>
        <el-col :span="3.5">
          <el-button
            v-if="$isPermitted($store.getters.user, 'HospitalStore_Button_Export')"
            :loading="btnExportLoading"
            type="primary"
            icon="el-icon-download"
            @click="onShowExportModal"
          >
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%;"
            :default-sort="{prop: 'createTime', order: 'descending'}"
            :header-cell-class-name="'tableStyle'"
            :row-class-name="handleRowClass"
            @sort-change="sortChange"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              label="月份"
              sortable="custom"
              min-width="80px"
              header-align="center"
              align="center"
              prop="Cycle.YearMonthSplicing"
            >
              <template slot-scope="{ row }">
                <span>{{ row.yearMonthSplicing }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="医院名称"
              sortable="custom"
              min-width="200px"
              header-align="center"
              align="left"
              prop="Hospital.Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.hospitalName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="医院编码"
              sortable="custom"
              min-width="100px"
              header-align="center"
              align="center"
              prop="Hospital.Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.hospitalCode }}</span>
              </template>
            </el-table-column>
            <el-table-column label="所属省份" sortable="custom" min-width="100px" prop="Hospital.Province.NameCn" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.hospitalProvinceName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="所属城市" sortable="custom" min-width="100px" prop="Hospital.City.NameCn" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.hospitalCityName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="目标客户" min-width="100px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.isTargetReceiver }}</span>
              </template>
            </el-table-column>
            <el-table-column label="客户类型" min-width="100px" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.customerType }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="药店名称"
              sortable="custom"
              min-width="200px"
              header-align="center"
              align="left"
              prop="DrugStore.Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.drugStoreName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="药店编码"
              sortable="custom"
              min-width="100px"
              header-align="center"
              align="center"
              prop="DrugStore.Name"
            >
              <template slot-scope="{ row }">
                <span>{{ row.drugStoreCode }}</span>
              </template>
            </el-table-column>
            <el-table-column label="产品名称" sortable="custom" min-width="100px" prop="ProductSpec.Product.NameCn" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.productNameCn }}</span>
              </template>
            </el-table-column>
            <el-table-column label="规格" sortable="custom" min-width="100px" prop="ProductSpec.Spec" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.spec }}</span>
              </template>
            </el-table-column>
            <el-table-column label="距离" sortable="custom" min-width="80px" prop="Distance" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.distance }}</span>
              </template>
            </el-table-column>
            <el-table-column label="关联类型" sortable="custom" min-width="100px" prop="HospitalStoreType.Name" header-align="center" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.hospitalStoreTypeName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="60"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <i v-if="$isPermitted($store.getters.user, 'HospitalStore_Button_Edit')" class="el-icon-edit-outline eltablei" title="编辑" @click="handleUpdate(row)" />
                <i
                  v-if="$isPermitted($store.getters.user, 'HospitalStore_Button_Delete')"
                  class="el-icon-delete eltablei"
                  title="删除"
                  @click="handleDelete(row)"
                />
              </template>
            </el-table-column></el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :visible="showExportModal"
      :close-on-click-modal="false"
      title="导出关联药店"
      width="800"
      @close="handleExportCancel"
    >
      <CustomExport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      />
      <div slot="footer" class="dialog-footer" />
    </el-dialog>
    <el-dialog
      title="导入关联药店"
      :close-on-click-modal="false"
      :visible="dialogImportVisible"
      width="80%"
      class="popup-search"
      @close="closeImportDialog"
    >
      <ImportHospitalStore :show-import="dialogImportVisible" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeImportDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <editHospitalStore
      v-if="dialogEditFormVisible"
      :id="itemId"
      @hidden="onHidden()"
      @refresh="onRefresh()"
    />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import CustomExport from '@/components/Export/CustomExport'
import ManufacturerService from '@/api/manufacturer'
import ProductService from '@/api/product'
import HospitalStoreService from '@/api/hospitalStore'
import ImportHospitalStore from './components/importHospitalStore'
import EditHospitalStore from './components/editHospitalStore'

export default {
  name: 'Price',
  components: {
    Pagination,
    CustomExport,
    ImportHospitalStore,
    EditHospitalStore
  },
  data() {
    return {
      span: 4,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      listLoading: false,
      list: [],
      manufacturerLoading: false,
      productAndSpecLoading: false,
      manufacturerList: [],
      productAndSpecList: null,
      productAndSpecId: [],
      btnExportLoading: false,
      showExportModal: false,
      columnDictionary: {},
      dialogImportVisible: false,
      productAndSpecKey: 0,
      dialogEditFormVisible: false
    }
  },
  created() {
    this.initManufacturer()
    this.initProductAndSpec()
    this.handleFilter()
  },
  methods: {
    initManufacturer() {
      this.manufacturerLoading = true
      ManufacturerService.QueryManufacturerSelect()
        .then((result) => {
          this.manufacturerLoading = false
          this.manufacturerList = result
        })
        .catch((error) => {
          this.manufacturerLoading = false
          console.log(error)
        })
    },
    initProductAndSpec() {
      this.productAndSpecLoading = true
      const para = { manufacturerId: this.listQuery.manufacturerId }
      ProductService.QueryProductAndSpecCascader(para)
        .then((result) => {
          this.productAndSpecList = result
          this.productAndSpecLoading = false
        })
        .catch((error) => {
          this.productAndSpecLoading = false
          console.log(error)
        })
    },
    manufacturerChange() {
      this.productAndSpecId = []
      ++this.productAndSpecKey
      this.initProductAndSpec()
    },
    timeChange() {
      this.$forceUpdate()
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      if (this.productAndSpecId) {
        const [productId, productSpecId] = this.productAndSpecId
        this.listQuery.productId = productId
        this.listQuery.productSpecId = productSpecId
      }
      this.listLoading = true
      HospitalStoreService.QueryHospitalStore(this.listQuery).then(result => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    // 显示导出model
    onShowExportModal() {
      this.btnExportLoading = true
      HospitalStoreService.GetHospitalStoreExportColumn().then(result => {
        this.btnExportLoading = false
        if (result.data && result.data.$id) {
          delete result.data.$id
        }
        this.columnDictionary = result.data
        this.showExportModal = true
      }).catch(error => {
        this.btnExportLoading = false
        console.log(error)
      })
    },
    // 确认导出
    handleExport(checkColumns) {
      this.showExportModal = false
      if (this.productAndSpecId && this.productAndSpecId.length > 0) {
        if (this.productAndSpecId.length === 1) {
          this.listQuery.productId = this.productAndSpecId[0]
        } if (this.productAndSpecId.length > 1) {
          this.listQuery.productSpecId = this.productAndSpecId[1]
        }
      } else {
        this.listQuery.productId = null
        this.listQuery.productSpecId = null
      }
      this.listQuery.checkedColumns = checkColumns
      HospitalStoreService.ExportHospitalStore(this.listQuery)
        .then(result => {
          this.listQuery.checkedColumns = null
          const fileDownload = require('js-file-download')
          let fileName = ''
          if (result.headers['content-type'].indexOf('application/octet-stream') >= 0
          ) {
            fileName = '关联药店.xlsx'
          } else if (
            result.headers['content-type'].indexOf('application/ms-txt') >= 0
          ) {
            fileName = '关联药店.csv'
          }
          fileDownload(result.data, fileName)
        }).catch(error => {
          console.log(error)
        })
    },
    handleExportCancel() {
      this.showExportModal = false
    },
    handleImport() {
      this.dialogImportVisible = true
    },
    closeImportDialog() {
      this.handleFilter()
      this.dialogImportVisible = false
    },
    productChange() {
      this.$refs.refProductAndSpec.dropDownVisible = false
    },
    handleUpdate(row) {
      this.itemId = row.id
      this.dialogEditFormVisible = true
    },
    onHidden() {
      this.dialogEditFormVisible = false
    },
    onRefresh() {
      this.getList()
      this.dialogEditFormVisible = false
    },
    handleDelete(row) {
      this.$confirm('确定删除此关联药店吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        HospitalStoreService.DeleteHospitalStore(row).then(result => {
          this.listLoading = false
          if (result.succeed) {
            this.getList()
            this.$notice.message('删除成功', 'success')
          }
        })
          .catch(() => {
            this.listLoading = false
          })
      }).catch(error => {
        this.listLoading = false
        if (!error.succeed) {
          this.$notice.message('取消删除', 'info')
        }
      })
    }
  }
}
</script>
