<template>
  <div style="background-color: #f5f6f8; min-height: 75vh; height: auto">
    <el-form
      ref="dataForm"
      :rules="settingRules"
      :model="setting"
      label-position="right"
      label-width="100px"
      class="el-dialogform-setting"
    >
      <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
        <el-tab-pane label="报表设置" name="report">
          <el-row type="flex"><el-col :span="24">
            <el-card class="box-card">
              <div slot="header" class="clearfix">
                <span>终端新增/流失报表设置</span>
              </div>
              <div>
                <el-row>
                  <el-col :span="span">
                    <el-form-item label="新增终端对比月份" prop="terminalChangeReportSetting.newTerminalNumberOfMonth" label-width="150px"><el-input
                      v-model.number="setting.terminalChangeReportSetting.newTerminalNumberOfMonth"
                      type="number"
                      clearable
                      placeholder="新增终端对比月份"
                    />
                    </el-form-item>
                  </el-col>
                  <el-col :span="span">
                    <el-form-item label="流失终端对比月份" prop="terminalChangeReportSetting.loseTerminalNumberOfMonth" label-width="150px">
                      <el-input
                        v-model.number="setting.terminalChangeReportSetting.loseTerminalNumberOfMonth"
                        type="number"
                        clearable
                        placeholder="流失终端对比月份"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="24">
              <el-card class="box-card">
                <div slot="header" class="clearfix">
                  <span>客户分析报表销量异常设置</span>
                </div>
                <el-row>
                  <el-col :span="span">
                    <el-form-item label="平均销量" prop="abnormalSalesVolumeReportSetting.numberOfMonths" label-width="150px">
                      <el-input v-model.number="setting.abnormalSalesVolumeReportSetting.numberOfMonths" type="number" clearable placeholder="请输入多少个月">
                        <template slot="prepend">过去</template>
                        <template slot="append">个月的平均销量</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="span">
                    <el-form-item label="销量过低预警" prop="abnormalSalesVolumeReportSetting.percentageOfAverage" label-width="150px">
                      <el-input v-model.number="setting.abnormalSalesVolumeReportSetting.percentageOfAverage" type="number" clearable placeholder="请输入低于平均销量的百分之几">
                        <template slot="prepend">低于平均销量的</template>
                        <template slot="append">%</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="span">
                    <el-form-item label="销量过高预警" prop="abnormalSalesVolumeReportSetting.multiplesOfAverage" label-width="150px">
                      <el-input v-model.number="setting.abnormalSalesVolumeReportSetting.multiplesOfAverage" type="number" clearable placeholder="请输入高于平均销量的几倍"><template slot="prepend">高于平均销量的</template>
                        <template slot="append">倍</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24" class="el-colRight">
                    <el-button
                      type="primary"
                      icon="el-icon-plus"
                      @click="handleAddProduct"
                    >
                      选择产品
                    </el-button>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="例外品种">
                      <el-table
                        :data="setting.abnormalSalesVolumeReportSetting.exceptedProductSpecs"
                        stripe
                        border
                        fit
                        highlight-current-row
                        style="width: 100%;"
                        :header-cell-class-name="'tableStyle'"
                      >
                        <el-table-column
                          fixed
                          label="序号"
                          type="index"
                          align="center"
                        />
                        <el-table-column label="厂商" align="center" min-width="100px">
                          <template slot-scope="{ row }">
                            <span>{{ row.manufacturerName }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="产品名称" align="center" min-width="100px" prop="productNameCn">
                          <template slot-scope="{ row }">
                            <span>{{ row.productNameCn }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="规格" align="center" min-width="100px">
                          <template slot-scope="{ row }">
                            <span>{{ row.spec }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column fixed="right" label="操作" align="center" header-align="center" width="90" class-name="small-padding fixed-width">
                          <template slot-scope="{ row }">
                            <i class="el-icon-delete eltablei" title="删除" @click="handleDelete(row)" />
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </el-col>
          </el-row>
          <el-row type="flex"><el-col :span="24">
            <el-card class="box-card">
              <div slot="header" class="clearfix">
                <span>客户分析报表停药预警设置</span>
              </div>
              <div>
                <el-row>
                  <el-col :span="span">
                    <el-form-item label="计算停药风险月份数量" prop="stopPurchaseReportSetting.lowPurchaseNumberOfMonth" label-width="150px"><el-input
                      v-model.number="setting.stopPurchaseReportSetting.lowPurchaseNumberOfMonth"
                      type="number"
                      clearable
                      placeholder="连续低于平均销量的月份数量"
                    />
                    </el-form-item>
                  </el-col>
                  <el-col :span="span">
                    <el-form-item label="计算停药风险的百分比" prop="stopPurchaseReportSetting.lowPurchasePercentageOfAverage" label-width="150px">
                      <el-input
                        v-model.number="setting.stopPurchaseReportSetting.lowPurchasePercentageOfAverage"
                        type="number"
                        clearable
                        placeholder="低于平均销量的百分比"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="span">
                    <el-form-item label="计算停药警告月份数量" prop="stopPurchaseReportSetting.stopPurchaseNumberOfMonth" label-width="150px">
                      <el-input
                        v-model.number="setting.stopPurchaseReportSetting.stopPurchaseNumberOfMonth"
                        type="number"
                        clearable
                        placeholder="连续采购数量低于10盒的月份数量"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :span="24">
                  <el-alert
                    title="停药预警的计算逻辑"
                    type="info"
                    :description="stopPurchaseDiscription"
                    show-icon
                  />
                </el-row>
              </div>
            </el-card>
          </el-col>
          </el-row>

        </el-tab-pane>
        <el-tab-pane label="数据看板设置" name="dashBoard">
          <el-row type="flex"><el-col :span="24">
            <el-card class="box-card">
              <div slot="header" class="clearfix">
                <span>数据看板设置</span>
              </div>
              <div>
                <el-row>
                  <el-col :span="span">
                    <el-form-item label="目标客户销量下滑百分比" prop="dashBoardSetting.targetReceiverSlidingPercentage" label-width="160px">
                      <el-input v-model.number="setting.dashBoardSetting.targetReceiverSlidingPercentage" type="number" clearable placeholder="请输入目标客户达成低于百分比">
                        <template slot="prepend">目标客户销量下滑超过</template>
                        <template slot="append">%预警</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="span">
                    <el-form-item label="岗位产值下限金额设置" prop="dashBoardSetting.productionLowerAmount" label-width="150px"><el-input
                      v-model.number="setting.dashBoardSetting.productionLowerAmount"
                      type="number"
                      clearable
                      placeholder="代表岗位产值下限金额设置"
                    />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24" class="el-colRight">
                    <el-button
                      type="primary"
                      icon="el-icon-plus"
                      @click="handleAddMinSalesProduct"
                    >
                      新增产品
                    </el-button>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="品种下限设置">
                      <el-table
                        :data="setting.dashBoardSetting.productSpecsMinSalesDashBoards"
                        stripe
                        border
                        fit
                        highlight-current-row
                        style="width: 100%;"
                        :header-cell-class-name="'tableStyle'"
                      >
                        <el-table-column
                          fixed
                          label="序号"
                          type="index"
                          align="center"
                        />
                        <el-table-column label="厂商" align="center" min-width="100px">
                          <template slot-scope="{ row }">
                            <span>{{ row.manufacturerName }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="产品名称" align="center" min-width="100px" prop="productNameCn">
                          <template slot-scope="{ row }">
                            <span>{{ row.productNameCn }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="规格" align="center" min-width="100px">
                          <template slot-scope="{ row }">
                            <span>{{ row.spec }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="销售下限" align="center" min-width="90px">
                          <template slot-scope="{ row,$index }">
                            <el-input
                              v-model="row.minQuantity"
                              min="0"
                              placeholder="销售下限"
                              size="mini"
                              @input="productItemChange(row,$index)"
                            />
                          </template>
                        </el-table-column>
                        <el-table-column fixed="right" label="操作" align="center" header-align="center" width="90" class-name="small-padding fixed-width">
                          <template slot-scope="{ row }">
                            <i class="el-icon-delete eltablei" title="删除" @click="handleDeleteMinSalesProduct(row)" />
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
      <el-row>
        <el-col :span="24" class="el-colCenter">
          <el-button
            v-if="$isPermitted($store.getters.user, 'ReportSetting_Button_Save')"
            type="primary"
            icon="el-icon-check"
            @click="handlerSave"
          >
            保存
          </el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-dialog
      custom-class="el-dialog-ls"
      append-to-body
      title="选择产品"
      :close-on-click-modal="false"
      :visible="dialogSelectProductAndSpecVisible"
      width="60%"
      class="popup-search"
      @close="handleCloseProductDialog"
    >
      <SelectProductAndSpec ref="refSelectProductAndSpec" :show-dialog="dialogSelectProductAndSpecVisible" :selected="setting.abnormalSalesVolumeReportSetting.exceptedProductSpecs" @success="chooseProduct_callback" />
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleChooseProductAndSpec"
        >
          确认
        </el-button>
        <el-button icon="el-icon-close" @click="handleCloseProductDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="el-dialog-ls"
      append-to-body
      title="选择产品"
      :close-on-click-modal="false"
      :visible="dialogSelectMinSalesProductAndSpecVisible"
      width="60%"
      class="popup-search"
      @close="handleCloseMinSalesProductDialog"
    >
      <SelectProductAndSpec ref="refSelectMinSalesProductAndSpec" :show-dialog="dialogSelectMinSalesProductAndSpecVisible" :selected="setting.dashBoardSetting.productSpecsMinSalesDashBoards " @success="chooseMinSalesProduct_callback" />
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleChooseMinSalesProductAndSpec"
        >
          确认
        </el-button>
        <el-button icon="el-icon-close" @click="handleCloseMinSalesProductDialog">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import ReportSevice from '@/api/report'
import SelectProductAndSpec from '@/views/components/selectProductAndSpec'

export default {
  components: {
    SelectProductAndSpec
  },
  data() {
    return {
      span: 12,
      setting: {
        abnormalSalesVolumeReportSetting: {
          numberOfMonths: 0,
          percentageOfAverage: 0,
          multiplesOfAverage: 0,
          exceptedProductSpecs: []
        },
        terminalChangeReportSetting: {
          newTerminalNumberOfMonth: 0,
          loseTerminalNumberOfMonth: 0
        },
        stopPurchaseReportSetting: {
          lowPurchaseNumberOfMonth: 0,
          lowPurchasePercentageOfAverage: 0,
          stopPurchaseNumberOfMonth: 0
        },
        dashBoardSetting: {
          targetReceiverSlidingPercentage: 0,
          productSpecsMinSalesDashBoards: []
        }
      },
      settingRules: {
        'terminalChangeReportSetting.newTerminalNumberOfMonth': [
          { required: true, trigger: 'blur', message: '请输入统计新增终端所需对比月份' },
          { pattern: /^[1-9]\d*$/, message: '新增终端所需对比月份必须是大于0的整数' }
        ],
        'terminalChangeReportSetting.loseTerminalNumberOfMonth': [
          { required: true, trigger: 'blur', message: '请输入统计流失终端所需对比月份' },
          { pattern: /^[1-9]\d*$/, message: '流失终端所需对比月份必须是大于0的整数' }
        ],
        'abnormalSalesVolumeReportSetting.numberOfMonths': [
          {
            required: true,
            message: '请输入计算平均销量的月份',
            trigger: 'blur'
          },
          { pattern: /^(([1-9]\d+)|([2-9]))$/, message: '计算平均销量的月份数必须是大于1的整数' }
        ],
        'abnormalSalesVolumeReportSetting.percentageOfAverage': [
          {
            required: true,
            message: '请输入计算过低预警的百分比',
            trigger: 'blur'
          },
          { pattern: /^[1-9]\d*$/, message: '计算销量过低预警的百分比必须是大于等于0的整数' }
        ],
        'abnormalSalesVolumeReportSetting.multiplesOfAverage': [
          {
            required: true,
            message: '请输入计算过高预警的倍数',
            trigger: 'blur'
          },
          { pattern: /^[1-9]\d*$/, message: '计算销量过高预警的倍数必须是大于0的整数' }
        ],
        'stopPurchaseReportSetting.lowPurchaseNumberOfMonth': [
          { required: true, trigger: 'blur', message: '请输入连续低于平均销量的月份数量' },
          { pattern: /^(([1-9]\d+)|([2-9]))$/, message: '计算连续低于平均销量的月份必须是大于1的整数' }
        ],
        'stopPurchaseReportSetting.stopPurchaseNumberOfMonth': [
          { required: true, trigger: 'blur', message: '请输入连续采购数量低于10盒的月份数量' },
          { pattern: /^(([1-9]\d+)|([2-9]))$/, message: '计算停药的月份必须是大于1的整数' }
        ],
        'stopPurchaseReportSetting.lowPurchasePercentageOfAverage': [
          { required: true, trigger: 'blur', message: '请输入计算停药风险的百分比' },
          { pattern: /^[1-9]\d*$/, message: '计算停药风险的百分比必须是大于0的整数' }
        ],
        'dashBoardSetting.targetReceiverSlidingPercentage': [
          {
            required: true,
            message: '请输入目标客户销量下滑百分比',
            trigger: 'blur'
          },
          { pattern: /^[1-9]\d*$/, message: '目标客户销量下滑百分比必须是大于等于0的整数' }
        ],
        'dashBoardSetting.productionLowerAmount': [
          {
            required: true,
            message: '请输入岗位产值下限金额',
            trigger: 'blur'
          },
          { pattern: /^[1-9]\d*$/, message: '岗位产值下限金额必须是大于等于0的整数' }
        ]
      },
      dialogSelectProductAndSpecVisible: false,
      stopPurchaseDiscription: '',
      activeName: 'report',
      dialogSelectMinSalesProductAndSpecVisible: false
    }
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      ReportSevice.GetReportSetting().then(result => {
        if (result.succeed) {
          this.setting = result.data

          this.stopPurchaseDiscription = '当某品规的采购数量连续' + this.setting.stopPurchaseReportSetting.lowPurchaseNumberOfMonth + '个月低于平均销量的' + this.setting.stopPurchaseReportSetting.lowPurchasePercentageOfAverage + '%，视为存在停药风险。当某品规的采购数量连续' + this.setting.stopPurchaseReportSetting.stopPurchaseNumberOfMonth + '个月的采购数量为个位数或者0则视为该终端已停止采购此品规。'
        }
      })
    },
    handlerSave() {
      for (let index = 0; index < this.setting.dashBoardSetting.productSpecsMinSalesDashBoards.length; index++) {
        const element = this.setting.dashBoardSetting.productSpecsMinSalesDashBoards[index]
        if (element.minQuantity === null || element.minQuantity === '') {
          this.showMessage('请填写最低销售数量', 'error')
          return
        }
        const reg = /((^[1-9]\d*))(\.\d{0,2}){0,1}$/
        if ((!reg.test(element.minQuantity))) {
          this.showMessage('最低销售数量仅支持正数，最多2位小数', 'error')
          return
        }
      }

      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          ReportSevice.SaveReportSetting(this.setting).then(result => {
            if (result.succeed) {
              this.initPage()
              this.showMessage('保存成功', 'success')
            } else {
              this.ShowTip(result)
            }
          })
        }
      })
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    },
    handleAddProduct() {
      this.dialogSelectProductAndSpecVisible = true
    },
    handleDelete(row) {
      this.$confirm('确定从例外品规列表中移除此产品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.setting.abnormalSalesVolumeReportSetting.exceptedProductSpecs.forEach((element, index) => {
          if (element.id === row.id) {
            this.setting.abnormalSalesVolumeReportSetting.exceptedProductSpecs.splice(index, 1)
          }
        })
      }).catch(error => {
        // this.listLoading = false
        if (!error.succeed) {
          this.$notice.message('取消删除', 'info')
        }
      })
    },
    handleDeleteMinSalesProduct(row) {
      this.$confirm('确定从品种下限设置中移除此产品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.setting.dashBoardSetting.productSpecsMinSalesDashBoards.forEach((element, index) => {
          if (element.productSpecId === row.productSpecId) {
            this.setting.dashBoardSetting.productSpecsMinSalesDashBoards.splice(index, 1)
          }
        })
      }).catch(error => {
        // this.listLoading = false
        if (!error.succeed) {
          this.$notice.message('取消删除', 'info')
        }
      })
    },
    indexMethod(index) {
      return (this.productList.pageIndex - 1) * this.productList.pageSize + index + 1
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.productList.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.productList.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.productList.pageSize = val
      this.handleFilter()
    },
    handleChooseProductAndSpec() {
      this.$refs.refSelectProductAndSpec.handleCheck()
    },

    handleCloseProductDialog() {
      this.dialogSelectProductAndSpecVisible = false
      this.$refs.refSelectProductAndSpec.clear()
    },

    chooseProduct_callback(data) {
      this.setting.abnormalSalesVolumeReportSetting.exceptedProductSpecs = data
      this.handleCloseProductDialog()
    },
    handleClick(tab, event) {
      this.initPage()
    },
    handleAddMinSalesProduct() {
      this.dialogSelectMinSalesProductAndSpecVisible = true
    },
    handleChooseMinSalesProductAndSpec() {
      this.$refs.refSelectMinSalesProductAndSpec.handleCheck()
    },

    handleCloseMinSalesProductDialog() {
      this.dialogSelectMinSalesProductAndSpecVisible = false
      this.$refs.refSelectMinSalesProductAndSpec.clear()
    },
    chooseMinSalesProduct_callback(data) {
      // 原来的品规数据集合
      if (this.setting.dashBoardSetting.productSpecsMinSalesDashBoards === undefined || this.setting.dashBoardSetting.productSpecsMinSalesDashBoards === null) {
        this.setting.dashBoardSetting.productSpecsMinSalesDashBoards = []
      }
      const oldProductSpecsMinSalesDashBoards = this.setting.dashBoardSetting.productSpecsMinSalesDashBoards
      this.setting.dashBoardSetting.productSpecsMinSalesDashBoards = []
      // 遍历当前选择的
      data.forEach(element => {
        var findIndex = oldProductSpecsMinSalesDashBoards.findIndex(item => item.productSpecId === element.id)

        // 原来集合中不包含的添加
        if (findIndex === -1) {
          const productSpec = {
            id: element.id,
            manufacturerName: element.manufacturerName,
            productNameCn: element.productNameCn,
            productSpecId: element.id,
            spec: element.spec,
            minQuantity: 0
          }
          this.setting.dashBoardSetting.productSpecsMinSalesDashBoards.push(productSpec)
        } else {
          // 包含的取原来的集合数据
          const oldProductSpec = oldProductSpecsMinSalesDashBoards.find(item => item.productSpecId === element.id)
          this.setting.dashBoardSetting.productSpecsMinSalesDashBoards.push(oldProductSpec)
        }
      })

      this.handleCloseMinSalesProductDialog()
    },
    productItemChange(row, index) {
      var quantity = Number(row.quantity)
      const regex = /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/
      if (row.quantity && !regex.test(quantity)) {
        this.$message.error('销售下限只能是两位小数或整数')
        row.accountingBase_error = true
      } else {
        row.accountingBase_error = false
      }
    }
  }
}
</script>
<style scoped>

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both
  }

.el-dialogform-setting {
    width: 100%;
    padding-right: 10px;
}
  .box-card {
    width: 96%;
    margin-bottom: 10px;
    margin-left: 30px;
  }
</style>
